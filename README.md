## File structure update (3/5/2024) 

- check the [file-structure.md](./docs/file-structure.md) for the new file structure on screens and features

## Screens

- Break down the screen components (at least around 3 main parts)
- the `AnyScreen/index.ts` is the entry file for the screen
- the screen components under `AnyScreen/components` are the puzzles for the `AnyScreen.tsx`

# Local environments

- install direnv using the curl below or visit https://direnv.net/docs/installation.html for details <br/>
  `curl -sfL https://direnv.net/install.sh | bash`

### Add .envrc by script:

- `yarn applyEnv [build]` for example, `yarn applyEnv phDev`

### Manually add .envrc

- create an .envrc in root directory from root:

```bash
cp .envrc.sample .envrc
```

then config the params in `.envrc`

### ZSH

add following to the end of the ~/.zshrc file:

```bash
eval "$(direnv hook zsh)"
```

- run `direnv allow .` whenever changes has made to the `.envrc`

# Libraries

## Cube-ui-components

- expose the root container style of each component
- use meaningful props for the critical style control, for example, the size of a button
- extract the style to the theme data
- in the component level, reuse the style in the base level

remark: currently we use theme from core for devlopment before the cube-ui-components pipeline is ready

# App Circle Release Group

|    | DEV | SIT | UAT | STG | PRD |
|----|:---:|:---:|:---:|:---:|:---:|
| JP |![](./assets/qr/appcircle/CUBE_JP_DEV.png) | ![](./assets/qr/appcircle/CUBE_JP_SIT.png) | ![](./assets/qr/appcircle/CUBE_JP_UAT.png) | | ![](./assets/qr/appcircle/CUBE_JP_PRD.png) |
| FIB |![](./assets/qr/appcircle/CUBE_FIB_DEV.png) [DEV](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_FIB_SIT.png) [SIT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_FIB_UAT.png) [UAT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_FIB_STG.png) [STG](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_FIB_PRD.png) [PRD](https://dist.appcircle.io/home/<USER>
| MY |![](./assets/qr/appcircle/CUBE_MY_DEV.png) [DEV](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_MY_SIT.png) [SIT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_MY_UAT.png) [UAT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_MY_PRD.png) [PRD](https://dist.appcircle.io/home/<USER>
| PH |![](./assets/qr/appcircle/CUBE_PH_DEV.png) [DEV](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_PH_SIT.png) [SIT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_PH_UAT.png) [UAT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_PH_PRD.png) [PRD](https://dist.appcircle.io/home/<USER>
| ID |![](./assets/qr/appcircle/CUBE_ID_DEV.png) [DEV](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_ID_SIT.png) [SIT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_ID_UAT.png) [UAT](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_ID_STG.png) [STG](https://dist.appcircle.io/home/<USER>/assets/qr/appcircle/CUBE_ID_PRD.png) [PRD](https://dist.appcircle.io/home/<USER>


# GitFlow Feature Branch Naming Guidelines

This document outlines the guidelines for naming feature branches in GitFlow to ensure consistency, clarity, and alignment with our JIRA ticketing system.

## Purpose

Feature branch naming conventions are crucial for:

- Improving collaboration and communication among team members.
- Simplifying branch management and integration.
- Linking code changes directly to JIRA tickets for better traceability.

## Naming Convention

All feature branch names must adhere to the following format:

```
feature/<JIRA-TICKET-NUMBER>-<short-description>
```

### Components:

1. **`feature/`**: Prefix indicating the branch is a feature branch.
2. **`<JIRA-TICKET-NUMBER>`**: The unique identifier of the JIRA ticket, e.g., `CUBEMY-1234`.
3. **`<short-description>`**: A concise, hyphen-separated description of the feature or task. This part is optional but recommended for clarity.

### Examples:

- `feature/CUBEMY-1234-add-login-page`
- `feature/CUBEMY-5678-improve-dashboard-performance`
- `feature/CUBEMY-9101-fix-user-profile-bug`

## Rules and Best Practices

1. **Mandatory JIRA Ticket Number**:

   - Every feature branch must start with a valid JIRA ticket number (e.g., `CUBEMY-1234`).
   - Ensure the ticket number is accurate and corresponds to the task being worked on.

2. **Descriptive Short Description**:

   - Use lowercase letters and hyphens (`-`) to separate words.
   - Keep the description concise (ideally under 50 characters).
   - Avoid using special characters, spaces, or underscores.

3. **No Special Characters**:

   - Branch names should not include characters such as `@`, `#`, `!`, or spaces.

4. **Consistency**:

   - Follow the format strictly to maintain uniformity across the repository.

5. **Validation**:

   - Ensure branch names are reviewed for compliance during code reviews or automated checks.

6. **Azure DevOps Pull Request**:

   - Include the JIRA ticket number in the title or description of the pull request to ensure traceability.

## Workflow Example

### Creating a New Feature Branch

1. Identify the JIRA ticket number for your task (e.g., `CUBEMY-1234`).
2. Draft a short description of the feature (e.g., `add-login-page`).
3. Create the branch using the format:
   ```
   git checkout -b feature/CUBEMY-1234-add-login-page
   ```

### Pushing the Branch

After creating the branch, push it to the remote repository:

```bash
git push origin feature/CUBEMY-1234-add-login-page
```

## Troubleshooting: Android Emulator Memory Allocation Error

### Potential Issue

You may encounter the following error when running the app on an Android emulator:

```bash
Failed to allocate a <byte-size> byte allocation ... giving up on allocation because <1% of heap free after GC.
```

### Suggested Solution

This issue is often caused by insufficient memory settings in the emulator. To resolve it:

1. **Increase emulator resources**:
   - Set **RAM** to **at least 4 GB or higher**
   - Set **VM heap size** to **at least 4 GB**

   You can update these settings in: **Android Studio > Device Manager > Edit > Additional settings**

2. **Update Gradle memory settings**:
   Open `android/gradle.properties` and update the `org.gradle.jvmargs` value:

   ```properties
   org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m