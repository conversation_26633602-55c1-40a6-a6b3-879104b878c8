module.exports = function (api) {
  api.cache(true);
  console.log('process.env.COUNTRY', process.env.COUNTRY);

  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            assets: './assets',
            firebaseConfig: './firebaseConfig',
            '@regionSpecificNavigator':
              './src/navigation/StackNavigator/' + process.env.COUNTRY,
          },
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.png'],
        },
      ],
      [
        'react-native-reanimated/plugin',
        {
          processNestedWorklets: true,
        },
      ],
      ['react-native-worklets-core/plugin'],
    ],
  };
};
