# Seller Experience File Structure (May 2024)

## Screens

The `screens` directory contains the following files:

- `[screen-name]`: This folder contains the screen files.
  - `index.ts`: This is the entry point of the screen.
  - `[screen-name].phone.tsx`: This is the version of the screen that will be rendered on phones. switch case for different regions.
  - `[screen-name].tablet.tsx`: This is the version of the screen that will be rendered on tablets.
  - `[screen-name].tsx`: This is the main screen to separate between phone and tablet.

## Features

The `features` directory contains the following files:

- `[feature-name]` : This folder contains the feature files.
  - `assets`: This folder contains images and icons.
  - `components`: This folder contains following:
    - `[sub-feature]`: folder for the common component of the sub-feature.
      - `[component].tsx`: This is the common component of the sub-feature.
    - `[component].tsx`: This is the common component of the feature.
  - `[region]`: This folder contains the following:
    - `tablet`: Contains files specific to tablet view.
      - `[sub-feature]`: folder for the region specific component of the sub-feature will be rendered on tablets.
        - `[sub-feature].tsx`: This is the region specific sub-feature.
      - `[feature-name].tsx`: This is the version of the feature that will be rendered on tablets.
    - `phone`: Contains files specific to phone view.
      - `[sub-feature]`: folder for the region specific component of the sub-feature will be rendered on phones.
        - `[sub-feature].tsx`: This is the region specific sub-feature.
      - `[feature-name].tsx`: This is the version of the feature that will be rendered on phones.
    - `validation`: Contains files for validation.
  - `hooks`: This folder contains hooks.
  - `translation`: This folder contains translation files.
    - `[region]`: This folder contains translation files for the region.
  - `utils`: This folder contains utility functions and files.

---

```
📦src
┣ 📂screens
┃ ┣ 📂[screen-name]
┃ ┃┣ 📜index.ts
┃ ┃┣ 📜[screen-name].phone.tsx
┃ ┃┣ 📜[screen-name].tablet.tsx
┃ ┃┗ 📜[screen-name].tsx
┃
┃
┣ 📂features
┃ ┣ 📂[feature-name]
┃ ┃ ┣ 📂assets
┃ ┃ ┃ ┃ ┗ 📜[SVG].tsx
┃ ┃ ┣ 📂components
┃ ┃ ┃ ┣ 📂[sub-feature]
┃ ┃ ┃ ┃ ┗ 📜[component].tsx
┃ ┃ ┃ ┗ 📜[component].tsx
┃ ┃ ┣ 📂[region]
┃ ┃ ┃ ┣ 📂tablet
┃ ┃ ┃ ┃ ┣ 📂components
┃ ┃ ┃ ┃ ┣ 📂[sub-feature]
┃ ┃ ┃ ┃ ┃ ┗ 📜[sub-feature-name].tsx
┃ ┃ ┃ ┃ ┗ 📜[feature-name].tsx
┃ ┃ ┃ ┣ 📂phone
┃ ┃ ┃ ┃ ┣ 📂components
┃ ┃ ┃ ┃ ┣ 📂[sub-feature]
┃ ┃ ┃ ┃ ┃ ┗ 📜[sub-feature-name].tsx
┃ ┃ ┃ ┃ ┗ 📜[feature-name].tsx
┃ ┃ ┃ ┗ 📂validation
┃ ┃ ┃ ┃ ┗ 📜[schema].ts
┃ ┃ ┣ 📂hooks
┃ ┃ ┃ ┗ 📜[hook].ts
┃ ┃ ┣ 📂translation
┃ ┃ ┃ ┗ 📂[region]
┃ ┃ ┃ ┃ ┗ 📜[feature-name].[locale].ts
┃ ┃ ┗ 📂utils
┃ ┃ ┃ ┗ 📜[util].ts
┃ ┗
┗
```
