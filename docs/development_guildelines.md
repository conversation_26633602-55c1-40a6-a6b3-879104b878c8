# CUBE DEVELOPMENT GUIDELINES

## 1. Styling

We suggest using [@emotion/native](https://emotion.sh/docs/@emotion/native) to customize component look and feel.

```tsx
import { styled } from '@emotion/native';
import { View } from 'react-native';
import { TextField } from 'cube-ui-components';

const Container = styled(View)({
  padding: 16,
  height: 200,
});

const FirstNameInput = styled(TextField)({
  marginTop: 4,
  marginBottom: 8,
});

export default function App() {
  return (
    <Container>
      <FirstNameInput />
    </Container>
  );
}
```

## 2. Theme

Cube app has predefined design token including (color, spacing, typography)

### Spacing & Sizing

For `margin`, `padding` in component's style, use `space`
For `height`, `width` in component's style, use `sizes`
In default theme, spacing and sizing are sets of numbers multiplied by <b>4</b>. So `space[1]` will be `4`, `space[2]` will be `8`.

```tsx
import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import { TextField } from 'cube-ui-components';

// Theme config is injected in style function
const FirstNameInput = styled(TextField)(({ theme: { space } }) => ({
  marginTop: space[1],
  marginBottom: space[2],
}));

export default function App() {
  // Get theme config by using `useTheme` hook
  const theme = useTheme();

  return (
    <View style={{ padding: theme.space[4], height: theme.sizes[50] }}>
      <FirstNameInput />
    </View>
  );
}
```

### Color

FWD Color Palette
| Family | Name | Hex |
| ----------------- | ------------------ | --------------------------------------------------------------------- |
| Orange | fwdOrange[100] | <font style=" color: black; background-color: #E87722">#E87722</font> |
| | fwdOrange[50] | <font style=" color: black; background-color: #F3BB90">#F3BB90</font> |
| | fwdOrange[20] | <font style=" color: black; background-color: #FAE4D3">#FAE4D3</font> |
| | fwdOrange[5] | <font style=" color: black; background-color: #FEF9F4">#FEF9F4</font> |
| Dark Green | fwdDarkGreen[100] | <font style=" color: white; background-color: #183028">#183028</font> |
| | fwdDarkGreen[50] | <font style=" color: black; background-color: #859D99">#859D99</font> |
| | fwdDarkGreen[20] | <font style=" color: black; background-color: #CED8D6">#CED8D6</font> |
| White | white | <font style=" color: black; background-color: #FFFFFF">#FFFFFF</font> |
| Black | black | <font style=" color: white; background-color: #000000">#000000</font> |
| Grey | fwdGrey[100] | <font style=" color: black; background-color: #DBDFE1">#DBDFE1</font> |
| | fwdGrey[50] | <font style=" color: black; background-color: #EDEFF0">#EDEFF0</font> |
| | fwdGrey[20] | <font style=" color: black; background-color: #F8F9F9">#F8F9F9</font> |
| Grey Dark | fwdGreyDark | <font style=" color: black; background-color: #B3B6B8">#B3B6B8</font> |
| Grey Darker | fwdGreyDarker | <font style=" color: black; background-color: #8B8E8F">#8B8E8F</font> |
| Grey Darkest | fwdGreyDarkest | <font style=" color: white; background-color: #636566">#636566</font> |
| Yellow | fwdYellow[100] | <font style=" color: black; background-color: #FED141">#FED141</font> |
| | fwdYellow[50] | <font style=" color: black; background-color: #FEE8A0">#FEE8A0</font> |
| | fwdYellow[20] | <font style=" color: black; background-color: #FFF6D9">#FFF6D9</font> |
| Light Green | fwdLightGreen[100] | <font style=" color: black; background-color: #6ECEB2">#6ECEB2</font> |
| | fwdLightGreen[50] | <font style=" color: black; background-color: #B6E6D8">#B6E6D8</font> |
| | fwdLightGreen[20] | <font style=" color: black; background-color: #E2F5F0">#E2F5F0</font> |
| Blue | fwdBlue[100] | <font style=" color: black; background-color: #0097A9">#0097A9</font> |
| | fwdBlue[50] | <font style=" color: black; background-color: #7FCBD4">#7FCBD4</font> |
| | fwdBlue[20] | <font style=" color: black; background-color: #CCEAEE">#CCEAEE</font> |
| Alert Red | alertRed | <font style=" color: white; background-color: #B30909">#B30909</font> |
| Alert Red Light | alertRedLight | <font style=" color: black; background-color: #FEF3F3">#FEF3F3</font> |
| Alert Green | alertGreen | <font style=" color: white; background-color: #03824F">#03824F</font> |
| Alert Green Light | alertGreenLight | <font style=" color: black; background-color: #F1F8F6">#F1F8F6</font> |
| Brown | fwdBrown[100] | <font style=" color: black; background-color: #391900">#391900</font> |
| Alternative Orange | fwdAlternativeOrange[100] | <font style=" color: black; background-color: #B74701">#B74701</font> |
| | fwdAlternativeOrange[50] | <font style=" color: black; background-color: #FF6816">#FF6816</font> |
| | fwdAlternativeOrange[20] | <font style=" color: black; background-color: #FF9B0A">#FF9B0A</font> |

Theme color mapping
| Theme name | Palette name | Hex |
| ----------------- | ------------------ | --------------------------------------------------------------------- |
| primary | fwdOrange[100] | <font style=" color: black; background-color: #E87722">#E87722</font> |
| primaryVariant | fwdOrange[50] | <font style=" color: black; background-color: #F3BB90">#F3BB90</font> |
| primaryVariant2 | fwdOrange[20] | <font style=" color: black; background-color: #FAE4D3">#FAE4D3</font> |
| primaryVariant3 | fwdOrange[5] | <font style=" color: black; background-color: #FEF9F4">#FEF9F4</font> |
| secondary | fwdDarkGreen[100] | <font style=" color: white; background-color: #183028">#183028</font> |
| secondaryVariant | fwdDarkGreen[50] | <font style=" color: black; background-color: #859D99">#859D99</font> |
| background | white | <font style=" color: black; background-color: #FFFFFF">#FFFFFF</font> |
| surface | fwdGrey[50] | <font style=" color: black; background-color: #EDEFF0">#EDEFF0</font> |
| error | alertRed | <font style=" color: white; background-color: #B30909">#B30909</font> |
| onPrimary | white | <font style=" color: black; background-color: #FFFFFF">#FFFFFF</font> |
| onSecondary | white | <font style=" color: black; background-color: #FFFFFF">#FFFFFF</font> |
| onSurface | fwdDarkGreen[100] | <font style=" color: white; background-color: #183028">#183028</font> |
| onBackground | fwdDarkGreen[100] | <font style=" color: white; background-color: #183028">#183028</font> |
| onError | white | <font style=" color: black; background-color: #FFFFFF">#FFFFFF</font> |
| placeholder | fwdGreyDarker | <font style=" color: white; background-color: #8B8E8F">#8B8E8F</font> |

<i><b>Note</b>:

- Try to use theme color name first (primary, secondary, background,...), if the color doesn't exist in theme mapping, use palette color instead</i><i>
- Common pattern is if container's `backgroundColor={colors.primary}` then content inside should be `color={colors.onPrimary}`</i>

```tsx
import { useTheme } from '@emotion/react';
import { Pressable, Text } from 'react-native';

export default function App() {
  const theme = useTheme();
  return (
    <Pressable style={{ backgroundColor: theme.colors.primary }}>
      <Text style={{ color: theme.colors.onPrimary }}>Apply</Text>
    </Pressable>
  );
}
```

### Typography

#### Font size

| Name              | Font size | line height |
| ----------------- | --------- | ----------- |
| H1                | 61        | 76.25       |
| H2                | 49        | 61.25       |
| H3                | 39        | 48.75       |
| H4                | 31        | 38.75       |
| H5                | 25        | 31.25       |
| H6                | 20        | 25          |
| H7                | 16        | 20          |
| H8                | 14        | 17.5        |
| Extra Large Body  | 20        | 30          |
| Large Body        | 16        | 24          |
| Body              | 14        | 21          |
| Small Body        | 12        | 18          |
| Large Label       | 16        | 20          |
| Label             | 14        | 17.5        |
| Small Label       | 12        | 15          |
| Extra Small Label | 10        | 12.5        |

<i><b>Note:</b>

- Body type (Extra large body, large body, body, small body) has line-height factor of 1.5, the other types have 1.25 factor </i><i>
- font-size and line-height always come together when using theme config</i>

```tsx
import { useTheme } from '@emotion/react';
import { TextInput } from 'react-native';

export default function App() {
  const theme = useTheme();
  return (
    <TextInput
      style={{
        fontSize: theme.typography.label.size,
        lineHeight: theme.typography.label.lineHeight,
      }}
    />
  );
}
```

#### Font family

| Font Weight | Font Family   |
| ----------- | ------------- |
| 450         | Book (normal) |
| 500         | Medium        |
| 700         | Bold          |
| 800         | Black         |

<i><b>Note:</b>

- cube-ui-components has built-in Text components to support typography out of the box </i><i>

```tsx
import { H1, H2, Body, Label } from 'cube-ui-components';

export default function App() {
  return (
    <>
      <H1>H1</H1>

      <H2 fontWeight="medium">H2 with medium weight</H2>

      <Body fontWeight="bold" italic>
        Body with bold weight, italic style
      </Body>

      <Label fontWeight="black" color="#FF0000">
        Label with black weight, red color
      </Label>
    </>
  );
}
```

- In case we want to customize react-native Text/TextInput component, <b>fontFamily</b> is required</i>

```tsx
import { styled } from '@emotion/native';
import { fonts } from 'constants';

const MyTextInput = styled(TextInput)(({ theme }) => ({
  fontFamily: fonts.FWDCircularTT.Book,
  fontSize: theme.typography.label.size,
  lineHeight: theme.typography.label.lineHeight,
}));

export default function App() {
  return <MyTextInput />;
}
```

### Border radius

| Name    | Value |
| ------- | ----- |
| none    | 0     |
| x-small | 4     |
| small   | 8     |
| medium  | 12    |
| large   | 16    |
| x-large | 20    |
| full    | 9999  |

## 3. Translation

Translation files should be put in each feature folder and combined to 1 file

```tsx
// 1. feature-based translation file:
// src/features/savedProposal/translation/ph/savedProposals.en.ts
export default {
  name: 'Saved Proposals',
};

// 2. translation master
// src/utils/translation/ph/en.js
import savedProposals from 'features/savedProposals/translation/ph/savedProposals.en';

const en = {
  ...
  savedProposals,
}

export default en;

// 3. type def
// src/utils/translation/i18next.d.ts
declare module 'i18next' {
  interface CustomTypeOptions {
    ...
    resources: {
      ...
      savedProposals: typeof en.savedProposals;
    };
  }
}
```

## 4. API

Cube project uses [React-query](https://tanstack.com/query/v4/docs/react/overview) to manage api requests.

```tsx
// 1. Define api function by domain in src/api
// src/api/caseApi.ts
...
const CASE_API = '/exp/case';

export async function getCaseById(caseId: string) {
  return await cubeClient.get<Case>(`${CASE_API}/${caseId}`);
}
...

// 2. Define a query hook to wrap api function
// src/api/useGetCase.ts
...
export const getCaseByIdKey = (caseId: string) => ['/case', caseId]; // query key function

export function useGetCase(
  caseId: string,
  options?: Omit<
    UseQueryOptions<Case, unknown, Case, QueryKey>,
    'queryKey' | 'queryFn' | 'initialData'
  >,
) {
  return useQuery<Case>(
    getCaseByIdKey(caseId),
    () => getCaseById(caseId),
    options,
  );
}
...

// 3. Usage
const Component = () => {
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj, isFetching } = useGetCase(caseId || '');

  if (isFetching) return <LoadingIndicator />;

  return (
    <Case data={caseObj} />
  )
}
```

## 5. State management

For global state management, we use [Zustand](https://github.com/pmndrs/zustand)

<b>App global state</b>

```tsx
// 1. src/utils/zustand/newsSlice.ts
import { NewsSlice, NewsState, StoreSlice } from 'types';
import { StateCreator } from 'zustand';

export const createNewsSlice: StateCreator<
  StoreSlice,
  [],
  [],
  NewsSlice
> = set => ({
  news: { ...initialState },
  newsActions: {
    updateBookmarkedNewsIdMap: (id, markAs) =>
      set(state => {
        state.news.bookmarkedNewsIdMap[id] = markAs;
        return state;
      }),
  },
});

const initialState: NewsState = {
  bookmarkedNewsIdMap: {},
};

// 2. src/utils/zustand/index.ts
export * from './newsSlice';

// 3. src/hooks/useBoundStore.tsx
...
const useBoundStore = create<
  StoreSlice,
  [['zustand/immer', never], ['zustand/persist', unknown]]
>(
  zustandFlipperWrapper(
    immer(
      persist(
        (...a) => ({
          ...
          ...createNewsSlice(...a), // add this line
          ...
        })
      )
    )
  )
);
...
```

<b>Feature specific store</b>

```tsx
// src/features/eApp/utils/store/eAppStore.ts
import { ObjectUtil } from 'utils';
import { StateCreator, create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export enum EAPP_STEP {
  APPLICATION_DETAILS = 'application-details',
  CONSENTS = 'consents',
}

export interface EAppState {
  progressStep: EAPP_STEP;
}

export interface EAppStore extends EAppState {
  updateProgressStep: (step: EAPP_STEP) => void;
}

export const createEAppStore: StateCreator<EAppStore> = set => ({
  ...ObjectUtil.cloneDeep(initialState),
  updateProgressStep: (step: EAPP_STEP) =>
    set(() => ({
      progressStep: step,
    })),
});

const initialState: EAppState = {
  progressStep: EAPP_STEP.APPLICATION_DETAILS,
};

export const useEAppStore = create(immer(devtools(createEAppStore)));
```

<b>Important notes:</b>

- For combined state selector, use `shallow` to avoid unnecessary re-renders

```tsx
import { shallow } from 'zustand/shallow';

// single state selector, no need shallow
const caseId = useBoundStore(state => state.case.caseId);

// combined state selector, should use shallow
const { caseId, setActiveCase } = useBoundStore(
  state => ({
    caseId: state.case.caseId,
    setActiveCase: state.caseActions.setActiveCase,
  }),
  shallow,
);
```

- Use ref + subscriber pattern to improve performance for some cases that only need to pass data through (e.g. animation)

```tsx
const useScratchStore = create(set => ({ scratches: 0, ... }))

const Component = () => {
  // Fetch initial state
  const scratchRef = useRef(useScratchStore.getState().scratches)
  // Connect to the store on mount, disconnect on unmount, catch state-changes in a reference
  useEffect(() => useScratchStore.subscribe(
    state => (scratchRef.current = state.scratches)
  ), [])
  ...
```

## 6. Form

Cube uses [react-hook-form](https://react-hook-form.com/get-started/) for form validation

```tsx
import * as yup from 'yup';
import { TextField, Dropdown, DatePicker, Button } from 'cube-ui-components';
import Input from 'components/Input';
import useYupValidationResolver from 'hooks/useYupValidationResolver';

// define schema
const addLeadSchema = yup.object({
  customerType: yup.string(),
  title: yup.string(),
  firstName: yup.string().required('Required'),
  middleName: yup.string(),
  lastName: yup.string().required('Required'),
  gender: yup.string().oneOf(['M', 'F']).nullable(),
  extensionName: yup.string(),
  dob: yup.date().nullable(),
});

// Schema type
type AddLeadFormType = yup.InferType<typeof addLeadSchema>;

// default value
const addLeadFormDefaultValue: AddLeadFormType = {
  customerType: 'Individual',
  title: 'Mrs',
  firstName: '',
  lastName: '',
  middleName: '',
  gender: null,
  extensionName: '',
  dob: null,
  occupation: '',
  reviewHQ: false,
  smoking: null,
};

const AddLeadForm = () => {
  const resolver = useYupValidationResolver(addLeadSchema);

  const {
    watch,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: addLeadFormDefaultValue,
    resolver,
  });

  // Use Input wrapper component to work with hook form easier
  // Passing actual component in 'as' prop
  // Available components in cube-ui for 'as' prop are:
  // - TextField
  // - Dropdown
  // - DatePicker
  // - Checkbox
  // - Switch
  // - RadioGroup
  // - Picker
  // - ToggleGroup

  const customerTypes = ['Individual'];

  const onSubmit = () => {
    console.log('validated');
  };

  return (
    <>
      <Input
        control={control}
        as={TextField}
        name="firstName"
        label="First Name"
        error={errors.firstName?.message}
      />
      <Input
        control={control}
        as={Dropdown<string, string>}
        name="customerType"
        label="Customer Type"
        data={customerTypes}
        getItemValue={item => item}
        getItemLabel={item => item}
      />
      <Input
        control={control}
        as={DatePicker}
        name="dob"
        label="Date of Birth"
        modalTitle="Date of Birth"
      />
      <Button text="Submit" onPress={handleSubmit(onSubmit)} />
    </>
  );
};
```

## 7. Icons

cube-ui-components provides icon collections that being used in Cube

- Icon: can customize width, height (`size` prop), color (`fill` prop)
- PictogramIcon: can customize width, height (`size` prop), color is static

```tsx
import { useTheme } from '@emotion/react';
import { Icon, PictogramIcon } from 'cube-ui-components';

export default function App() {
  const theme = useTheme();
  return (
    <>
      <Icon.ArrowDown />
      <Icon.ArrowUp size={18} fill={theme.colors.placeholder} />
      <PictogramIcon.Accident size={56} />
    </>
  );
}
```
