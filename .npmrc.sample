registry=https://pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/registry/
                        
always-auth=true

; begin auth token https://learn.microsoft.com/en-us/azure/devops/artifacts/npm/npmrc?view=azure-devops&tabs=linux%2Cclassic#credentials-setup
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/registry/:username=USERNAME
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/registry/:_password=[PASSWORD]
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/registry/:email=EMAIL
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/:username=USERNAME
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/:_password=[PASSWORD]
//pkgs.dev.azure.com/FWDGODevOps/_packaging/FWDGODevOps/npm/:email=EMAIL
; end auth token
