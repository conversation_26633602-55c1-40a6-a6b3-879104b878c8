const createExpoWebpackConfigAsync = require('@expo/webpack-config');
const path = require('path');

module.exports = async function (env, argv) {
  const config = await createExpoWebpackConfigAsync(
    {
      ...env,
    },
    argv,
  );

  config.module.rules.forEach(rule => {
    if (rule.oneOf) {
      rule.oneOf.forEach(o => {
        if (o.use && o.use.loader && o.use.loader.includes('babel-loader')) {
          o.include = [
            path.resolve('.'),
            path.resolve('node_modules/cube-ui-components/src'),
          ];
        }
      });
    }
  });

  return config;
};
