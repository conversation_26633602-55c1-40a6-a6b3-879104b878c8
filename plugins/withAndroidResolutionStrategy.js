/* eslint-disable @typescript-eslint/no-var-requires */
const { withAppBuildGradle } = require('@expo/config-plugins');

/**
 * A config plugin that adds a resolutionStrategy to resolve Android dependency conflicts
 */
module.exports = function withAndroidResolutionStrategy(config) {
  return withAppBuildGradle(config, config => {
    // Check if the build.gradle content already contains our resolution strategy to avoid duplication
    if (config.modResults.contents.includes('// CUSTOM RESOLUTION STRATEGY')) {
      return config;
    }

    // Define the code block we want to add
    const resolutionStrategyBlock = `
    // CUSTOM RESOLUTION STRATEGY
    configurations.all {
        resolutionStrategy {
            force "androidx.core:core:1.13.1"
            force "androidx.versionedparcelable:versionedparcelable:1.1.1"
            force 'androidx.appcompat:appcompat:1.6.1' // Ensure consistent AndroidX versions
            force 'androidx.fragment:fragment:1.6.2' // Ensure consistent AndroidX versions

            // Exclude all modules from the old support library group.
            // Jetifier should handle migrating usages to AndroidX.
            exclude group: 'com.android.support'
        }
    }`;

    // Look for the android { block to insert our code inside it
    const androidBlockRegex = /android\s*\{/;

    // Insert our resolution strategy block right after the android { opening
    if (androidBlockRegex.test(config.modResults.contents)) {
      config.modResults.contents = config.modResults.contents.replace(
        androidBlockRegex,
        `android {${resolutionStrategyBlock}`,
      );
    }

    return config;
  });
};
