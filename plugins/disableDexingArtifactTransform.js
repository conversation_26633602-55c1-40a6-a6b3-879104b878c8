// disableDexingArtifactTransform key update for web rtc for gradle version >= 8.2
/* eslint-disable @typescript-eslint/no-var-requires */
const { withGradleProperties } = require('@expo/config-plugins');

const disableDexingArtifactTransform = config => {
  return withGradleProperties(config, config => {
    config.modResults = config.modResults.map(buildProperty => {
      if (
        buildProperty.key == 'android.enableDexingArtifactTransform.desugaring'
      ) {
        return {
          ...buildProperty,
          key: 'android.useFullClasspathForDexingTransform',
        };
      }

      if (buildProperty.key == 'android.minSdkVersion') {
        return {
          ...buildProperty,
          value: '31',
        };
      }

      return buildProperty;
    });

    return config;
  });
};

module.exports = disableDexingArtifactTransform;
