import Constants, { ExecutionEnvironment } from 'expo-constants';
import { FirebaseAnalyticsTypes } from '@react-native-firebase/analytics';

class Analytics {
  static get isExpoGo() {
    const isExpoGo =
      Constants.executionEnvironment === ExecutionEnvironment.StoreClient;
    return isExpoGo;
  }

  private static get module(): FirebaseAnalyticsTypes.Module {
    if (this.isExpoGo) {
      throw Error('Firebase analytics does not support expo go');
    }
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const fbAnalyticsModule = require('@react-native-firebase/analytics').default;
    return fbAnalyticsModule();
  }

  static async logCustomEvent(name: string)  {
    await this.module.logEvent(name);
  }
}

export default Analytics;
