trigger:
  - none
variables:
  - group: CUBE_PH_DEV
  - name: androidTarget
    value: '$(DEVELOP_ANDROID)'
  - name: iosTarget
    value: '$(DEVELOP_IOS)'
  - name: iOSProvCert
    value: 'DEVphcomfwdcube.mobileprovision'
  - name: Build_Env
    value: 'phDev'

pool:
  name: 'Cube Frontend Pipelines'

stages:
  - stage: Prepare
    displayName: Prepare
    # condition: false
    jobs:
      - job: Prepare
        displayName: Prepare
        steps:
          - checkout: self
            persistCredentials: true
            clean: true
          - task: NodeTool@0
            displayName: 'Install Node'
            inputs:
              versionSpec: '16.20.0'
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: 'npm install --global yarn && yarn --version'
            displayName: Install Yarn
          - script: |
              git checkout $(Build.SourceBranchName)
              commitId=$(git rev-parse HEAD)
              echo "##vso[task.setvariable variable=Commit_ID;isOutput=true]$commitId"
              echo "##vso[task.setvariable variable=Commit_Message;isOutput=true]$(Build.SourceVersionMessage)"
            displayName: 'Get commit info'
            name: 'GetVersion'

  # ========================== Android build task: START =============================
  - stage: BuildAPK
    displayName: Build APK
    dependsOn:
      - Prepare
    # condition: false
    jobs:
      - job: BuildAPK
        timeoutInMinutes: 0
        displayName: Build APK
        variables:
          - name: Commit_ID
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_ID'] ]
          - name: Commit_Message
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_Message'] ]

        steps:
          - task: UseNode@1
            inputs:
              version: '16.20.2' 
          - task: DownloadSecureFile@1
            name: npmrc
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'phDev.npmrc'
              retryCount: '3'
          - bash: |
              echo Copying $(npmrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(npmrc.secureFilePath) $(System.DefaultWorkingDirectory)/.npmrc
            displayName: 'Copy npmrc'
          - script: |
              yarn cache clean
              npm install -g eas-cli@3.9.3
              yarn install --network-timeout 1000000
            retryCountOnTaskFailure: 5
            displayName: 'Install Dependencies'
          - script: |
              echo "Verify project info ✅"
              npx expo-env-info
            retryCountOnTaskFailure: 5
            displayName: 'Project Info'
          - script: |
              echo Update build Number
              sed -i .bak -e "s/versionCode: 1/versionCode: $(Build.BuildId)/g" app.config.js
            displayName: 'Set Build number'

          - bash: |
              echo Check for development client build
              if grep -q firebase package.json; then
                sed -i .bak -e "s/\"developmentClient\": false/\"developmentClient\": true/g" eas.json
                echo firebase found
                echo "##vso[task.setvariable variable=DevClientBuild]Yes"
              else
                echo firebase not found
                echo "##vso[task.setvariable variable=DevClientBuild]No"
              fi
            displayName: 'Set DevClient build'

          # - task: JavaToolInstaller@0
          #   displayName: 'Set Java 11'
          #   inputs:
          #     versionSpec: '11'
          #     jdkArchitectureOption: 'x64'
          #     jdkSourceOption: 'PreInstalled'
          - task: DownloadSecureFile@1
            name: yarnrc
            displayName: 'Downlaod yarnrc.yml'
            inputs:
              secureFile: 'yarnrc.yml'
              retryCount: '3'
          - bash: |
              echo Copying $(yarnrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(yarnrc.secureFilePath) $(System.DefaultWorkingDirectory)/yarnrc.yml
            displayName: 'Verify yarnrc.yml'
          - task: DownloadSecureFile@1
            name: credentials
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'credentials.json'
              retryCount: '3'
          - bash: |
              echo Copying $(credentials.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(credentials.secureFilePath) $(System.DefaultWorkingDirectory)/credentials.json
            displayName: 'Verify credentials.json'

          - task: DownloadSecureFile@1
            name: androidKeystore
            displayName: 'Downlaod keystore file'
            inputs:
              secureFile: 'my-upload-key.keystore'
              retryCount: '3'
          - bash: |
              echo Copying $(androidKeystore.secureFilePath) to $(System.DefaultWorkingDirectory)/certs/
              cp $(androidKeystore.secureFilePath) $(System.DefaultWorkingDirectory)/certs/my-upload-key.keystore
              echo "Verify certs folder ✅"
              ls -al $(System.DefaultWorkingDirectory)/certs/
            displayName: 'Verify certs'
          - bash: |
              export ANDROID_HOME=$(Build.SourcesDirectory)/android
              mkdir $ANDROID_HOME
              curl -o cmdtools.zip https://dl.google.com/android/repository/commandlinetools-mac-9477386_latest.zip
              unzip cmdtools.zip -d $ANDROID_HOME

              yes | $ANDROID_HOME/cmdline-tools/bin/sdkmanager --licenses --sdk_root=$ANDROID_HOME
              $ANDROID_HOME/cmdline-tools/bin/sdkmanager --install "platforms;android-33" --sdk_root=$ANDROID_HOME

            displayName: Install Android SDK
          - bash: |
              node -v
              echo "SET TOKEN"
              export EXPO_TOKEN=$(EXPO_TOKEN)
              export ANDROID_HOME=$(Build.SourcesDirectory)/android
              rm $(Build.SourcesDirectory)/.npmrc
              echo Build.SourcesDirectory = $(Build.SourcesDirectory)
              ls -l $(Build.SourcesDirectory)

              echo Build_Env = $(Build_Env) 
              echo DevClientBuild = $(DevClientBuild) 
              echo "⏰⏰⏰ Start building standalone APK"
              npx eas-cli build --local --platform android --profile $(Build_Env) --no-wait --clear-cache --non-interactive

              echo "🥱🥱🥱 Check APK exist❓❓❓"
              export APK_FILE=$(ls *.apk)
              if [ ! -f "$APK_FILE" ];
              then
                echo "❌❌❌ APK file not found 💔💔💔"
                echo "##vso[task.setvariable variable=APK_Build]false"
                exit 1
              else
                echo "✅✅✅ APK File exists 👍👍👍"
                echo "##vso[task.setvariable variable=androidArtifact]$APK_FILE"
                echo "##vso[task.setvariable variable=APK_Build]true"
                echo "File name to publish Android"
                echo $APK_FILE
              fi
            displayName: 'Build APK'
          - task: PublishBuildArtifacts@1
            condition: eq(variables.APK_Build, 'true')
            displayName: 'Publish APK to artifacts'
            inputs:
              PathtoPublish: '$(androidArtifact)'
              ArtifactName: 'android'
              publishLocation: 'Container'
          - task: AppCenterDistribute@3
            condition: eq(variables.APK_Build, 'true')
            displayName: 'Upload APK to AppCenter'
            retryCountOnTaskFailure: 5
            inputs:
              serverEndpoint: 'App Center'
              appSlug: '$(APK_SLUG)'
              appFile: '$(androidArtifact)'
              releaseNotesOption: 'input'
              releaseNotesInput: |+
                Android APK Distribution
                ---

                - **Dev Client Build**  : $(DevClientBuild)
                - **Build Number**      : $(Build.BuildId)
                - **Source Branch**     : $(Build.sourceBranch)
                - **Commit SHA**        : $(Commit_ID)
                - **Release Notes**     : $(Commit_Message)
              isMandatory: true
              destinationType: 'groups'
              distributionGroupId: '$(androidTarget)'
              isSilent: true

  # ========================== Android build task: END ===========================

  # ========================== iOS build task: START =============================
  - stage: BuildIPA
    displayName: Build IPA
    dependsOn:
      - Prepare
    # condition: false
    jobs:
      - job: BuildIPA
        timeoutInMinutes: 0
        displayName: Build IPA
        variables:
          - name: Commit_ID
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_ID'] ]
          - name: Commit_Message
            value: $[ stageDependencies.Prepare.Prepare.outputs['GetVersion.Commit_Message'] ]

        steps:
          # - script: |
          #     cp $(System.DefaultWorkingDirectory)/certs/cubeph.npmrc $(System.DefaultWorkingDirectory)/.npmrc
          #   displayName: 'Set npmrc'
          - task: UseNode@1
            inputs:
              version: '16.20.2' 
          - task: DownloadSecureFile@1
            name: npmrc
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'phDev.npmrc'
              retryCount: '3'
          - bash: |
              echo Copying $(npmrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(npmrc.secureFilePath) $(System.DefaultWorkingDirectory)/.npmrc
            displayName: 'Copy npmrc'
          - script: |
              yarn cache clean
              npm install -g eas-cli@3.9.3
              yarn install --network-timeout 1000000
            retryCountOnTaskFailure: 5
            displayName: 'Install Dependencies'
          - script: |
              echo "Verify project info ✅"
              npx expo-env-info
            retryCountOnTaskFailure: 5
            displayName: 'Project Info'
          - script: |
              echo Update build Number
              sed -i .bak -e "s/buildNumber: '1'/buildNumber: '$(Build.BuildId)'/g" app.config.js
            displayName: 'Set Build number'

          - bash: |
              echo Check for development client build
              if grep -q firebase package.json; then
                sed -i .bak -e "s/\"developmentClient\": false/\"developmentClient\": true/g" eas.json
                echo firebase found
                echo "##vso[task.setvariable variable=DevClientBuild]Yes"
              else
                echo firebase not found
                echo "##vso[task.setvariable variable=DevClientBuild]No"
              fi
            displayName: 'Set DevClient build'

          - task: DownloadSecureFile@1
            name: yarnrc
            displayName: 'Downlaod yarnrc.yml'
            inputs:
              secureFile: 'yarnrc.yml'
              retryCount: '3'
          - bash: |
              echo Copying $(yarnrc.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(yarnrc.secureFilePath) $(System.DefaultWorkingDirectory)/yarnrc.yml
            displayName: 'Verify yarnrc.yml'

          - task: DownloadSecureFile@1
            name: credentials
            displayName: 'Downlaod credentials'
            inputs:
              secureFile: 'credentials.json'
              retryCount: '3'
          - bash: |
              echo Copying $(credentials.secureFilePath) to $(System.DefaultWorkingDirectory)/
              cp $(credentials.secureFilePath) $(System.DefaultWorkingDirectory)/credentials.json
            displayName: 'Verify credentials.json'

          - task: DownloadSecureFile@1
            name: p12Cert
            displayName: 'Download P12 cert'
            inputs:
              secureFile: 'DistCert.p12'
              retryCount: '3'
          - task: DownloadSecureFile@1
            name: iOSProvCert
            displayName: 'Get mobileprovision cert'
            inputs:
              secureFile: '$(iOSProvCert)'
              retryCount: '3'
          - bash: |
              echo Copying DistCert and mobile provision to $(System.DefaultWorkingDirectory)/certs/
              cp $(p12Cert.secureFilePath) $(System.DefaultWorkingDirectory)/certs/DistCert.p12
              cp $(iOSProvCert.secureFilePath) $(System.DefaultWorkingDirectory)/certs/profile.mobileprovision
              echo "Verify certs folder ✅"
              ls -al $(System.DefaultWorkingDirectory)/certs/
            displayName: 'Verify certs'
          - bash: |
              node -v
              echo "SET TOKEN"
              export PATH="/usr/local/bin:/usr/local/opt/ruby/bin:$PATH"
              export EXPO_TOKEN=$(EXPO_TOKEN)
              rm $(Build.SourcesDirectory)/.npmrc
              echo Build.SourcesDirectory = $(Build.SourcesDirectory)
              ls -l $(Build.SourcesDirectory)

              echo Build_Env = $(Build_Env) 
              echo DevClientBuild = $(DevClientBuild) 
              echo "⏰⏰⏰ Start building standalone IPA"
              npx eas-cli build --local --platform ios --profile $(Build_Env) --no-wait --clear-cache --non-interactive

              echo "🥱🥱🥱 Check IPA exist❓❓❓"
              export IPA_FILE=$(ls *.ipa)
              if [ ! -f "$IPA_FILE" ];
              then
                echo "❌❌❌ IPA file not found 💔💔💔"
                echo "##vso[task.setvariable variable=IPA_Build]false"
                exit 1
              else
                echo "✅✅✅ IPA File exists 👍👍👍"
                echo "##vso[task.setvariable variable=iosArtifact]$IPA_FILE"
                echo "##vso[task.setvariable variable=IPA_Build]true"
                echo "File name to publish iOS"
                echo $IPA_FILE
              fi
            displayName: 'Build IPA'
          - task: PublishBuildArtifacts@1
            condition: eq(variables.IPA_Build, 'true')
            displayName: 'Publish IPA to artifacts'
            inputs:
              PathtoPublish: '$(iosArtifact)'
              ArtifactName: 'ios'
              publishLocation: 'Container'
          - task: AppCenterDistribute@3
            condition: eq(variables.IPA_Build, 'true')
            retryCountOnTaskFailure: 5
            displayName: 'Upload IPA to AppCenter'
            inputs:
              serverEndpoint: 'App Center'
              appSlug: '$(IPA_SLUG)'
              appFile: '$(iosArtifact)'
              releaseNotesOption: 'input'
              releaseNotesInput: |+
                iOS IPA Distribution
                ---

                - **Dev Client Build**  : $(DevClientBuild)
                - **Build Number**      : $(Build.BuildId)
                - **Source Branch**     : $(Build.sourceBranch)
                - **Commit SHA**        : $(Commit_ID)
                - **Release Notes**     : $(Commit_Message)
              isMandatory: true
              destinationType: 'groups'
              distributionGroupId: '$(iosTarget)'
              isSilent: true
  # ============================ iOS build task: END =============================
