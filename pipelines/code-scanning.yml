trigger: none

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: SharedPipeline
      type: git
      name: GO_DevSecOps/shared-pipeline
      ref: 'main'

variables:
  SERVICE: $(Build.Repository.Name)
  BRANCH_NAME: $[ replace(variables['Build.SourceBranch'], 'refs/heads/', '') ]
  DEPLOY_PATH: $(SERVICE)/$(SERVICE)-web

parameters:
  - name: runCodeQualityScanning
    displayName: 'Run Code Quality Scanning'
    type: boolean
    default: true

  - name: runCodeScanning
    displayName: 'Run Code Scanning'
    type: boolean
    default: true

  - name: runDependenciesScanning
    displayName: 'Run Dependencies Scanning'
    type: boolean
    default: false

extends:
  template: cicd/stage-ci-v1.yml@SharedPipeline
  parameters:
    # 1. General
    devsecopsAzureRmServiceConn: 'devsecops_azure_rm_01-FWD_Cube_Frontend' # string | required | Provide the service connection name for Azure key vault connection. Could be found in "Project Settings > Service connections" | Example: "devsecops_azure_rm_01"
    # pipelinePreparationSteps:                       # stepList | optional | Run additional azure pipeline step(s) in the preparation stage of the pipeline (before any setup)
    # 2. Build
    buildVersionExtractType: 'nodejs' # string enum ["custom", "maven", "nodejs"] | required | Determine logic to extract the application version, e.g. pom.xml for maven, package.json for nodejs:
    buildType: 'custom' # string enum ["custom", "maven", "nodejs"] | required | Determine logic for building the application, e.g. referring package.json for nodejs
    customBuildVersionExtractSteps: [] # stepList | optional | Run custom azure pipeline step(s) for extracting the application version, is ignore if `buildVersionExtractType` not custom
    buildJobTimeoutInMinutes: 180
    # buildDirNoTrailingSlash: '$(SERVICE)'                      # string | optional | Determine the folder location to start build in. Default is root level (./)
    # scanDirNoTrailingSlash: '.'                       # string | optional | Determine the folder location to start scanning in, if different with buildDirNoTrailingSlash. Default is same as buildDirNoTrailingSlash
    # buildCommandExtraArgument: ''                     # string | optional | Append extra argument or flag to the build command (e.g. npm run build/mvn package)
    # preBuildSteps: []                                   # stepList | optional | Run additional azure pipeline step(s) in the pre-build stage of the pipeline (before build happen)
    # customBuildSteps: []                              # stepList | optional | Run custom azure pipeline step(s) for building the application, is ignore if `buildType` not "custom"
    # buildGoalOverride: ''              # string | optional | Replace default build goal, useful when you need multiple step to complete the build | Example: clean package
    # buildToolVersion: ''                   # string | optional | Specify the build tool version to use (jdk 11/nodejs 20) | Example: 16.x
    # buildCommandJvmArgument: ''                       # string | optional | Append extra argument to the build-time jvm | Example: -Xmx3072m
    # isEnableBuildCaching: false                       # boolean | optional | Determine if build time caching should be applied, if apply, successful built artifact from last run will be restored (.m2 for maven, .npm for npm)

    # 3. Unit Test
    isRunUnitTest: false # boolean | optional | Determine if unit test should be run (maven test/ npm test)
    # unitTestResultPathGlob: ''                        # string | optional | Provide the unit test result file location, required for nodejs to see test result in pipeline summary | Example: '**/junit.xml'
    # codeCoverageResultPathGlob: ''                    # string | optional | Provide the coverage result file location, required for nodejs to see coverage report in pipeline summary | Example: '**/coverage/cobertura-coverage.xml'

    # 4. Code Quality Scan
    isEnableCodeScanSonarqube: ${{ parameters.runCodeQualityScanning }} # boolean | optional | Determine if SonarQube should be run for code quality check
    sonarServiceConn: devsecops_sonarqube_01-FWD_Cube_Frontend # string | required if `isEnableCodeScanSonarqube`: true  | Provided the service connection name for SonarQube | Example: "devsecops_sonarqube_01"
    # sonarExtraProperties: ''                          # string | optional | Append extra SonarQube key value pair for initial project configuration

    # 5. Secret Scan
    isEnableCodeScanOrca: ${{ parameters.runCodeScanning }} # boolean | optional | Determine if Orca secret scanning should be ran
    # orcaProjectKey: 'go-devsecops-cicd-general'       # string | optional | Provide the project key for Orca scanning

    # 6. Static Analysis Security Test
    isEnableCodeScanCoverity: ${{ parameters.runCodeScanning }} # boolean | optional | Determine if Coverity SAST should be ran
    coverityServiceConn: 'devsecops_coverity_01-FWD_Cube_Frontend' # string | required if `isEnableCodeScanCoverity`: true   | Provide the service connection name for Coverity SAST | Example: "devsecops_coverity_01"
    # coverityHost: 'https://fwd.polaris.synopsys.com'  # string | optional | Provide the host of Coverity SAST
    coverityCliExtraConfig: '--co project.name=${scm.git.repo} --co project.branch=${scm.git.branch} --co project.revision.name=${scm.git.commit} --co project.revision.date=${scm.git.commit.date}' # string | optional | Append extra flag or argument for running Coverity scan

    # 7. Software Composition Analysis
    isEnableCodeScanBlackduck: ${{ parameters.runDependenciesScanning }} # boolean | optional | Determine if Buckduck SCA should be ran
    blackduckServiceConn: 'devsecops_blackduck_01-FWD_Cube_Frontend' # string | required if `isEnableCodeScanBlackduck`: true | Provide the service connection name for Backduck SCA | Example: "devsecops_blackduck_01"
    # blackduckExtraSynopsysDetectProps: ''             # string | optional | Append extra flag or argument for running Blackduck scan
    # isGenerateBlackduckDetailedReport: true           # boolean | optional | Determine if detail scanning report should be generated

    # 8. Release
    releaseType: 'custom' # string enum ["custom", "docker"] | required | Determine the type of release to produce, for example using dockerfile to create docker image
    dockerRegistryServiceConn: null # string | required if `releaseType`: docker | Provide the service connection name for docker registry | Example: "devsecops_dockerregistry_demo_01"
    customReleaseSteps: [] # stepList | optional | Run custom azure pipeline step(s) for producing the application release artifact, is ignore if `releaseType` not custom
    #   - checkout: CubeSharedResources # replace with your repository details
    #     displayName: 'Checkout shared cube-devops-deployments'

    # # Since we are building from cube-devops-deployments Dockerfile context, we only have access to file in that directory.
    # # This is to allow the shared cube-devops-deployments to use generic relative path without worrying which context would it be built in
    # - task: CopyFiles@2
    #   inputs:
    #     SourceFolder: '$(DEPLOY_PATH)/target'
    #     Contents: '$(SERVICE)-$(APP_VERSION_SHORT).jar'
    #     TargetFolder: 'cube-devops-deployments'
    #   displayName: 'Copy JAR file to cube-devops-deployments directory'
    # - script: |
    #     curl -fSL "https://github.com/genuinetools/img/releases/download/v0.5.11/img-linux-amd64" -o "/usr/local/bin/img" && chmod a+x "/usr/local/bin/img"
    #
    #     img build \
    #     --build-arg SNAPSHOT_PATH=. \
    #     --build-arg JAR_FILE=$(SERVICE)-$(APP_VERSION_SHORT).jar \
    #     -t $(Build.Repository.Name):$(APP_VERSION) \
    #     -f $(System.DefaultWorkingDirectory)/cube-devops-deployments/Dockerfile.devsecops \
    #     cube-devops-deployments

    #     img save -o $(System.DefaultWorkingDirectory)/cube-devops-deployments/$(SERVICE).tar $(Build.Repository.Name):$(APP_VERSION)
    #   displayName: 'Download IMG and Build Container Image'
    # # By design, all build goes to dev ECR, for CD, pull the required image from Dev ECR and push to the target ECR
    # - template: ./steps/img-sync.yml
    #   parameters:
    #     imageName: $(Build.Repository.Name)
    #     appVersion: $(APP_VERSION)
    #     action: push
    #     env: ${{ parameters.imageRegistryEnv }}
    #     imgVersion: '0.5'

    # dockerfileFilename: 'Dockerfile'                  # string | optional | Provide the name of dockerfile of your release
    # dockerRegistryCustomImageRepo: ''                 # string | optional | Provide a custom image repository to use
    # postReleaseSteps: []                              # stepList | optional | Run additional azure pipeline step(s) in the post release stage of the pipeline (after release artifact is created)
    # isPublishDeployPackage: true                      # boolean | optional | Determine if deployment related artifact (e.g. k8s deployment yaml) should be published
    # deploymentPackageStorageType: 'custom'            # string enum ["custom", "azdoFeed"] | required | Determine logic of storing deployment artifact, azdoFeed means storing under azure devops feed
    # customUploadDeploymentPackageSteps: []            # stepList | optional | Run custom azure pipeline step(s) for uploading deployment artifact (e.g. upload k8s deployment to some namespace)
    # deployPackagePath: null                           # string | required if `isPublishDeployPackage`: true AND `deploymentPackageStorageType`: `azdoFeed` | Locate the deployment related artifact in this path | Example: ./deployment
    # deployPackageFeedId: null                         # string | required if either `isPublishDeployPackage`: true AND `deploymentPackageStorageType`: `azdoFeed` | Provide the Azure DevOps Feed Id to be used for storage, ignored if `deploymentPackageStorageType` is not "azdoFeed"

    # # 9. Image Scan
    # isEnableImageScanCrowdstrike: false                # boolean | optional | Determine if CloudStrike container image scanning should be ran
    # isEnableImageScanOrca: false
    # # dockerRegistryHost: ''                            # string | required if either of the above is true | Provide the docker registry host for performing image scanning purpose, should match your service connection of docker | Example: "acrhkgeaspocsnq01.azurecr.io"
    # orcaOciImagePath: 'cube-devops-deployments/$(SERVICE).tar'
