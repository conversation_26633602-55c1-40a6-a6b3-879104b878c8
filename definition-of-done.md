# GitFlow Feature Branch Naming Guidelines

This document outlines the guidelines for naming feature branches in GitFlow to ensure consistency, clarity, and alignment with our JIRA ticketing system.

## Purpose

Feature branch naming conventions are crucial for:

- Improving collaboration and communication among team members.
- Simplifying branch management and integration.
- Linking code changes directly to JIRA tickets for better traceability.

## Naming Convention

All feature branch names must adhere to the following format:

```
feature/<JIRA-TICKET-NUMBER>-<short-description>
```

### Components:

1. **`feature/`**: Prefix indicating the branch is a feature branch.
2. **`<JIRA-TICKET-NUMBER>`**: The unique identifier of the JIRA ticket, e.g., `CUBEMY-1234`.
3. **`<short-description>`**: A concise, hyphen-separated description of the feature or task. This part is optional but recommended for clarity.

### Examples:

- `feature/CUBEMY-1234-add-login-page`
- `feature/CUBEMY-5678-improve-dashboard-performance`
- `feature/CUBEMY-9101-fix-user-profile-bug`

## Rules and Best Practices

1. **Mandatory JIRA Ticket Number**:

   - Every feature branch must start with a valid JIRA ticket number (e.g., `CUBEMY-1234`).
   - Ensure the ticket number is accurate and corresponds to the task being worked on.

2. **Descriptive Short Description**:

   - Use lowercase letters and hyphens (`-`) to separate words.
   - Keep the description concise (ideally under 50 characters).
   - Avoid using special characters, spaces, or underscores.

3. **No Special Characters**:

   - Branch names should not include characters such as `@`, `#`, `!`, or spaces.

4. **Consistency**:

   - Follow the format strictly to maintain uniformity across the repository.

5. **Validation**:

   - Ensure branch names are reviewed for compliance during code reviews or automated checks.

6. **Azure DevOps Pull Request**:

   - Include the JIRA ticket number in the title or description of the pull request to ensure traceability.

## Workflow Example

### Creating a New Feature Branch

1. Identify the JIRA ticket number for your task (e.g., `CUBEMY-1234`).
2. Draft a short description of the feature (e.g., `add-login-page`).
3. Create the branch using the format:
   ```
   git checkout -b feature/CUBEMY-1234-add-login-page
   ```

### Pushing the Branch

After creating the branch, push it to the remote repository:

```bash
git push origin feature/CUBEMY-1234-add-login-page
```
