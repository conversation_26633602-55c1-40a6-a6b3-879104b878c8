#!/bin/bash
export LANG="en_US.UTF-8"

cert_path=$1
cert_pass=$2
allowed_day_to_expire=$3

echo cert_path= $cert_path
echo cert_pass= $cert_pass
echo allowed_day_to_expire= $allowed_day_to_expire

opensslPath="/usr/bin/openssl"

expire_str=$(${opensslPath} pkcs12 -in ${cert_path} -nokeys -passin pass:${cert_pass} | ${opensslPath} x509 -sha1 -noout -fingerprint -subject -dates -nameopt utf8,sep_semi_plus_space | grep notAfter)

if [ $? -ne 0 ]; then
  echo "Failed to load Enterprise Certificate, please check filename/password"
  exit 1
fi

echo "Checking expiration of Enterprise Certificate"
echo "Certificate expiring within ${allowed_day_to_expire} days are not allowed"
echo "expire_str = ${expire_str}"
# Input string
# expire_str="notAfter=Sep 3 02:56:04 2026 GMT"

# Extracting date using sed
expire_date=$(echo "$expire_str" | sed 's/^.*=\([A-Za-z]* \{1,\}[0-9]* \{1,\}[0-9]*:[0-9]*:[0-9]* [0-9]*\) GMT$/\1/')
# expire_date="Sep  3 02:56:04 2026"
echo "expire_date = ${expire_date}"

expire_date_iso=$(date -j -f "%b %e %T %Y" "$expire_date" +"%Y-%m-%dT%H:%M:%S")
current_date=$(date -u +"%Y-%m-%dT%H:%M:%S")

date_to_seconds() {
  local input_date="$1"
  local formatted_date=$(date -j -f "%Y-%m-%dT%H:%M:%S" "$input_date" "+%s")
  echo "$formatted_date"
}

# Get input dates and times
date_time1=$current_date
date_time2=$expire_date_iso

# Convert input dates and times to seconds
seconds1=$(date_to_seconds "$date_time1")
seconds2=$(date_to_seconds "$date_time2")

# Calculate the difference in seconds
difference=$((seconds2 - seconds1))

# Convert difference to days
days_to_expire=$(echo "$difference / 86400" | bc)

echo "Enterprise Certificate expires in ${days_to_expire} days"

if [ ${days_to_expire} -lt ${allowed_day_to_expire} ]; then
  if [ ${days_to_expire} -lt 0 ]; then
    echo "❌❌❌ Enterprise Certificate has expired. Build is not allowed. Please renew Enterprise Certificate"
  else
    echo "⏰⏰⏰ Enterprise Certificate is close to expiration. Build is not allowed. Please renew Enterprise Certificate"
  fi
  exit 1
else
  echo "✅✅✅ Enterprise Certificate expiration check passed"
fi

