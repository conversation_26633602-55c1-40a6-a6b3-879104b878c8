#!/bin/bash
export LANG="en_US.UTF-8"

profile_path=$1
allowed_day_to_expire=$2

echo "Checking expiration of Provisioning Profile"
echo "Provisioning Profile expiring within ${allowed_day_to_expire} days are not allowed"

expire_date=$(/usr/libexec/PlistBuddy -c 'Print :ExpirationDate' /dev/stdin <<<$(security cms -D -i "${profile_path}"))
if [ $? -ne 0 ]; then
  echo "Failed to load Provisioning Profile"
  exit 1
fi

echo "expire_date = ${expire_date}"

expire_date_iso=$(date -j -f "%a %b %d %T %Z %Y" "$expire_date" +"%Y-%m-%dT%H:%M:%S")
current_date=$(date -u +"%Y-%m-%dT%H:%M:%S")

date_to_seconds() {
  local input_date="$1"
  local formatted_date=$(date -j -f "%Y-%m-%dT%H:%M:%S" "$input_date" "+%s")
  echo "$formatted_date"
}

# Get input dates and times
date_time1=$current_date
date_time2=$expire_date_iso

# Convert input dates and times to seconds
seconds1=$(date_to_seconds "$date_time1")
seconds2=$(date_to_seconds "$date_time2")

# Calculate the difference in seconds
difference=$((seconds2 - seconds1))

# Convert difference to days
days_to_expire=$(echo "$difference / 86400" | bc)

echo "Provisioning Profile expires in ${days_to_expire} days"

if [ ${days_to_expire} -lt ${allowed_day_to_expire} ]; then
  if [ ${days_to_expire} -lt 0 ]; then
    echo "❌❌❌ Provisioning Profile has expired. Build is not allowed. Please renew Provisioning Profile"
  else
    echo "⏰⏰⏰ Provisioning Profile is close to expiration. Build is not allowed. Please renew Provisioning Profile"
  fi
  exit 1
else
  echo "✅✅✅ Provisioning Profile expiration check passed"
fi
