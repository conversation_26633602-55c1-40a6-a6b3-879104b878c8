/* eslint-disable @typescript-eslint/no-var-requires */
const build = process.argv[2] || 'phDev';

const fs = require('fs');
const path = require('path');
const exec = require('child_process').exec;
const easJson = JSON.parse(fs.readFileSync('eas.json', { encoding: 'utf8' }));

const isValidUrl = urlString => {
  try {
    return Boolean(new URL(urlString));
  } catch (e) {
    return false;
  }
};

try {
  const buildEnv = easJson.build[build].env;
  const envrcPath = path.join(__dirname, '../.envrc');

  let envrcStr = '#Environment variables:';

  for (let key in buildEnv) {
    const isNotArray = !Array.isArray(buildEnv[key]);

    if (typeof buildEnv[key] === 'string') {
      if (isValidUrl(buildEnv[key])) {
        envrcStr += `
  export ${key}='${buildEnv[key]}'`;
      } else {
        envrcStr += `
  export ${key}=${buildEnv[key]}`;
      }
    } else if (
      typeof buildEnv[key] === 'object' &&
      isNotArray &&
      buildEnv[key] !== null
    ) {
      for (let subKey in buildEnv[key]) {
        envrcStr += `
  export ${key}_${subKey}=${buildEnv[key][subKey]}`;
      }
    }
  }

  fs.writeFileSync(envrcPath, envrcStr);
  exec('direnv allow .')
} catch (e) {
  console.error(e);
}
