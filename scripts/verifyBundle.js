/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require('fs');
const path = require('path');
const util = require('node:util');
const exec = util.promisify(require('node:child_process').exec);

async function main() {
  const tempDir = path.join(__dirname, 'temp_' + new Date().getTime());
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir);
  }

  const rmDir = () => {
    fs.rmSync(tempDir, {
      force: true,
      recursive: true,
    });
  };

  try {
    const { stdout, stderr } = await exec(
      `npx react-native bundle --dev false --entry-file node_modules/expo/AppEntry.js --bundle-output ${tempDir}/index.bundle --assets-dest ${tempDir}`,
    );
    console.log(stdout);
    console.error(stderr);
    rmDir();
    console.error('✅ Bundle verification succeeded');
    process.exit(0);
  } catch (e) {
    console.error('❌ Bundle verification failed with error');
    console.error(e.message);
    rmDir();
    process.exit(1);
  }
}

main();
