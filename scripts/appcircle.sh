#!/bin/bash

# set -x
options='--fail --connect-timeout 6 --retry 5 -s -o /dev/null -w %{http_code}'
# source .env

Artifact=$1
APPCIRCLE_PAT=$2
DIST_ID=$3
BuildId=$4
Commit_ID=$5

APP_FILE="${Artifact}"

echo printing values:
echo APP_FILE= $Artifact
echo BuildId= $BuildId
echo Commit_ID= $Commit_ID

notes="$(cat <<EOF
Build Number: ${BuildId}
Commit SHA: ${Commit_ID}
EOF
)"

auth_json=$(curl --location 'https://auth.appcircle.io/auth/v1/token' \
      -H 'accept: application/json' \
      -H 'Content-Type: application/x-www-form-urlencoded' \
      --data-urlencode "pat=${APPCIRCLE_PAT}")

token=$(jq -r ".access_token" <<< "$auth_json")
echo "token=${token}"

res=$(curl $options -X POST "https://api.appcircle.io/distribution/v2/profiles/${DIST_ID}/app-versions" \
      -H "accept: */*" -H "Content-Type: multipart/form-data" \
      -H "Authorization: Bearer ${token}" \
      -F "Message=$notes" \
      -F "File=@$APP_FILE")
retVal=$?

if [[ $res -eq 200 ]] || [[ $res -eq 201 ]] || [[ $res -eq 202 ]]; then
  echo "OK ✅✅✅ retVal=${retVal}, response=${res}"
  exit 0
else
  echo "ERROR ❌❌❌ retVal=${retVal}, response=${res}"
  exit 1
fi