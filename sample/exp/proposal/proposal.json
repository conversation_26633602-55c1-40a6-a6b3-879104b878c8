{"apiTraceId": "eff819a2-aa2f-4efb-8160-ac1def6363b7", "success": true, "status": null, "responseData": {"caseCount": 689, "fnaCount": 6, "quickSICount": 115, "fullSICount": 42, "inAppCount": 526, "cases": [{"caseId": "6476f6f7543f4762f639afb0", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Standard", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd01", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Standard with Admin Requirements", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd02", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Standard with Exclusion only", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd03", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Standard with Exclusion and Admin Requirements", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd04", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON><PERSON>", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd05", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Incomplete Pre-UW checks", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd06", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Decline", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd07", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Postpone", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd07b", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Evidence Needed", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd08a", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "(TL)Sub-standard with Rating", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd08b", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "(VUL)Sub-standard with Rating", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd09", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Sub-Sta Rating and Admin Requirements", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd10", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Sub-Sta Rating and Exclusion", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f56e56071bd7c46e2cd11", "insured": [{"_id": "643f56e56071bd7c46e2cd10", "personalInfo": {"person": {"title": "T001", "firstName": "Sub-Sta Rating, Exclusion and Admin Requirement", "middleName": "", "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-19_1052", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f56ecb72d3e79b622210b", "applicationId": "643f5763948da93527db973c", "proposalId": "643f575e948da93527db973b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:56:25.848Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939304", "policyId": null}, {"caseId": "643f561d6071bd7c46e28f61", "insured": [{"_id": "643f561d6071bd7c46e28f62", "personalInfo": {"person": {"title": "T001", "firstName": "Test Wenjin 20", "middleName": "", "lastName": "20age demo1"}}}], "proposalName": "Term 5/5 2023-04-19_1048", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f5623b72d3e79b622210a", "applicationId": "643f5667948da93527db9738", "proposalId": "643f5664948da93527db9737", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:49:22.302Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939295", "policyId": null}, {"caseId": "643f55ee6071bd7c46e26a47", "insured": [{"_id": "643f55ee6071bd7c46e21389", "personalInfo": {"person": {"title": null, "firstName": "UnitLink Test Full", "middleName": null, "lastName": null}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643f55efb72d3e79b6222108", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-19T02:48:56.348Z", "agentId": "100007", "leadId": "643f55eda5fd6e26fb17d7f4", "sourceLeadId": "939293", "policyId": null}, {"caseId": "643f55ee6071bd7c46e26f35", "insured": [{"_id": "643f55ee6071bd7c46e26f36", "personalInfo": {"person": {"title": null, "firstName": "UnitLink Test Quick", "middleName": null, "lastName": null}}}], "proposalName": null, "productName": null, "status": "QUICK_SI", "fnaId": "643f55efb72d3e79b6222108", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-19T02:48:56.348Z", "agentId": "100007", "leadId": "643f55eda5fd6e26fb17d7f4", "sourceLeadId": "939293", "policyId": null}, {"caseId": "643f55006071bd7c46e2571b", "insured": [{"_id": "643f55006071bd7c46e2571c", "personalInfo": {"person": {"title": "T001", "firstName": "Test Wenjin 20", "middleName": "", "lastName": "20age test1"}}}], "proposalName": "Term 5/5 2023-04-19_1043", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643f5506b72d3e79b6222107", "applicationId": "643f554e948da93527db9736", "proposalId": "643f554a948da93527db9735", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:45:25.711Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "939277", "policyId": null}, {"caseId": "643f53db6071bd7c46e23f0a", "insured": [{"_id": "643f53db6071bd7c46e23f0b", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "Demo1"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-19_1038", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643f53e3b72d3e79b6222106", "applicationId": "643f5424948da93527db9734", "proposalId": "643f541f948da93527db9733", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:39:30.744Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "939257", "policyId": null}, {"caseId": "643f52f46071bd7c46e22456", "insured": [{"_id": "643f52f46071bd7c46e22457", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "Test1"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-19_1034", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643f52fdb72d3e79b6222105", "applicationId": "643f5349948da93527db9731", "proposalId": "643f5344948da93527db9730", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T02:36:29.506Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "939243", "policyId": null}, {"caseId": "643f49986071bd7c46e1c9af", "insured": [{"_id": "643f49996071bd7c46e1c9b0", "personalInfo": {"person": {"title": null, "firstName": "Test Wenjin 16", "middleName": null, "lastName": ""}}}], "proposalName": "FWD Whole Life 99/15 2023-04-19_0957", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643f499ab72d3e79b6222102", "applicationId": "643f4a9b948da93527db972b", "proposalId": "643f4a99948da93527db972a", "isQuickSI": false, "sumAssured": 1013651.0, "modalPremium": 143006.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T01:57:48.889Z", "agentId": "100007", "leadId": "642a767e5e39fa55463a4a50", "sourceLeadId": "939081", "policyId": null}, {"caseId": "643f47896071bd7c46e1a9b7", "insured": [{"_id": "643f47896071bd7c46e1a9b8", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "Y"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-19_0946", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643f4794b72d3e79b6222101", "applicationId": "643f480d948da93527db9728", "proposalId": "643f480a948da93527db9727", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T01:48:25.17Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "939049", "policyId": null}, {"caseId": "643f43c86071bd7c46e189cb", "insured": [{"_id": "643f43cd6071bd7c46e189cc", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "N"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-19_0930", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643f43d8b72d3e79b6222100", "applicationId": "643f442e948da93527db9725", "proposalId": "643f4424948da93527db9724", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T01:31:48.057Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "938987", "policyId": null}, {"caseId": "643df8066071bd7c46dc3324", "insured": [{"_id": "643df8066071bd7c46dc3325", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "406"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-18_0954", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643df811b72d3e79b62220df", "applicationId": "643df85a948da93527db96e1", "proposalId": "643df856948da93527db96e0", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-19T01:27:49.869Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "937581", "policyId": null}, {"caseId": "643eac2d6071bd7c46e16f3f", "insured": [{"_id": "643eac2d6071bd7c46e16f40", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_2243", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643eac32b72d3e79b62220ff", "applicationId": "643eac93948da93527db9722", "proposalId": "643eac8f948da93527db9721", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T14:45:18.188Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "938472", "policyId": null}, {"caseId": "643eab7e6071bd7c46e15a0b", "insured": [{"_id": "643eab7e6071bd7c46e15a0c", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_2239", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643eab83b72d3e79b62220fe", "applicationId": "643eabc1948da93527db9720", "proposalId": "643eabbd948da93527db971f", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T14:41:03.706Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "938471", "policyId": null}, {"caseId": "643ea77f6071bd7c46e144df", "insured": [{"_id": "643ea77f6071bd7c46e144e0", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_2223", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643ea784b72d3e79b62220fd", "applicationId": "643ea7d4948da93527db971e", "proposalId": "643ea7cb948da93527db971d", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T14:24:15.246Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "938442", "policyId": null}, {"caseId": "643e54876071bd7c46e12fbb", "insured": [{"_id": "643e54876071bd7c46e12fbc", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "406"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-18_1630", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643e548eb72d3e79b62220fc", "applicationId": "643e5538948da93527db971c", "proposalId": "643e5535948da93527db971b", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T08:33:23.527Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "938045", "policyId": null}, {"caseId": "643e4a946071bd7c46e0fb05", "insured": [{"_id": "643e4a946071bd7c46e0fb06", "personalInfo": {"person": {"title": null, "firstName": "Daniel0418", "middleName": null, "lastName": ""}}}], "proposalName": "FWD Life Saving 30/15 2023-04-18_1548", "productName": {"th": "เอฟดับบลิวดี ไลฟ์ เซฟวิ่ง 30/15", "en": "FWD Life Saving 30/15"}, "status": "IN_APP", "fnaId": "643e4a96b72d3e79b62220fb", "applicationId": "643e4b36948da93527db9719", "proposalId": "643e4b33948da93527db9718", "isQuickSI": false, "sumAssured": 1051190.0, "modalPremium": 137450.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T07:48:07.523Z", "agentId": "100007", "leadId": "643e4a94a5fd6e26fb17d7f3", "sourceLeadId": "938005", "policyId": null}, {"caseId": "643e4a1d6071bd7c46e0eb37", "insured": [{"_id": "643e4a1d6071bd7c46e0eb38", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_1546", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643e4a23b72d3e79b62220fa", "applicationId": "643e4abf948da93527db9716", "proposalId": "643e4ab8948da93527db9715", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T07:46:10.618Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "938001", "policyId": null}, {"caseId": "643e42086071bd7c46e01ff9", "insured": [{"_id": "643e42096071bd7c46e01ffa", "personalInfo": {"person": {"title": "", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_1541", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643e4211b72d3e79b62220f4", "applicationId": "643e49cf948da93527db9714", "proposalId": "643e49c7948da93527db9713", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T07:42:42.197Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "937977", "policyId": null}, {"caseId": "643e44676071bd7c46e066d6", "insured": [{"_id": "643e44686071bd7c46e066d7", "personalInfo": {"person": {"title": null, "firstName": "Bean19", "middleName": null, "lastName": null}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643e4524b72d3e79b62220f6", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-18T07:32:10.045Z", "agentId": "100007", "leadId": "643e4467d5258b0e9a9557d6", "sourceLeadId": "937982", "policyId": null}, {"caseId": "643e42d16071bd7c46e02fb5", "insured": [{"_id": "643e42d16071bd7c46e02fb6", "personalInfo": {"person": {"title": null, "firstName": "Bean60", "middleName": null, "lastName": "Bean60"}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643e4349b72d3e79b62220f5", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-18T07:15:23.247Z", "agentId": "100007", "leadId": "643e42d0d5258b0e9a9557d5", "sourceLeadId": "937979", "policyId": null}, {"caseId": "643e409d6071bd7c46dfd69b", "insured": [{"_id": "643e409d6071bd7c46dfd69c", "personalInfo": {"person": {"title": null, "firstName": "Bean27", "middleName": null, "lastName": "Bean27"}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643e419eb72d3e79b62220f3", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-18T07:08:25.076Z", "agentId": "100007", "leadId": "643e409c8080f772f4321943", "sourceLeadId": "937973", "policyId": null}, {"caseId": "643e41236071bd7c46dfde76", "insured": [{"_id": "643e41236071bd7c46dfde77", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_1506", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643e4128b72d3e79b62220f2", "applicationId": "643e4178948da93527db970b", "proposalId": "643e4175948da93527db970a", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T07:06:32.867Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "937974", "policyId": null}, {"caseId": "643e33bc6071bd7c46dfaa2e", "insured": [{"_id": "643e33bc6071bd7c46dfaa2f", "personalInfo": {"person": {"title": "", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_1409", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643e33c0b72d3e79b62220ef", "applicationId": "643e3406948da93527db9707", "proposalId": "643e3400948da93527db9706", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T06:09:48.92Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "937908", "policyId": null}, {"caseId": "643e24bc6071bd7c46df4ed8", "insured": [{"_id": "643e24bc6071bd7c46df4ed9", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_1305", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643e24c1b72d3e79b62220eb", "applicationId": "643e2518948da93527db9705", "proposalId": "643e2510948da93527db9704", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T05:05:35.661Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "937896", "policyId": null}, {"caseId": "643e21ca6071bd7c46df2f98", "insured": [{"_id": "643e21ca6071bd7c46df2f99", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-18_1253", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643e21d0b72d3e79b62220ea", "applicationId": "643e2232948da93527db9703", "proposalId": "643e222f948da93527db9702", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:59:27.798Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "937888", "policyId": null}, {"caseId": "643d1a076071bd7c46db3dc1", "insured": [{"_id": "643d1a076071bd7c46db3dc2", "personalInfo": {"person": {"title": null, "firstName": "Weras", "middleName": null, "lastName": "Asdf"}}}], "proposalName": "Term 5/5 2023-04-17_1813", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643d1a7bb72d3e79b62220d7", "applicationId": "643d1bec948da93527db96d1", "proposalId": "643d1a36948da93527db96d0", "isQuickSI": false, "sumAssured": 175277777.0, "modalPremium": 631000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:24:49.804Z", "agentId": "100007", "leadId": "643d1a06f2a2d331daac7a38", "sourceLeadId": "936641", "policyId": null}, {"caseId": "643d06e96071bd7c46da21a5", "insured": [{"_id": "643d06e96071bd7c46da21a6", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1645", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643d06efb72d3e79b62220cf", "applicationId": "643d073b948da93527db96bc", "proposalId": "643d0738948da93527db96bb", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:22:45.983Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936558", "policyId": null}, {"caseId": "643d13f76071bd7c46daf8fc", "insured": [{"_id": "643d13f76071bd7c46daf8fd", "personalInfo": {"person": {"title": null, "firstName": "Dbea", "middleName": null, "lastName": "Ad"}}}], "proposalName": "FWD Ultimate Saving 12/6 2023-04-17_1801", "productName": {"th": "เอฟดับบลิวดี อัลทิเมท เซฟวิ่ง  12/6", "en": "FWD Ultimate Saving 12/6"}, "status": "IN_APP", "fnaId": "643d1484b72d3e79b62220d6", "applicationId": "643d18f8948da93527db96ce", "proposalId": "643d1434948da93527db96cd", "isQuickSI": false, "sumAssured": 2000000.0, "modalPremium": 1958000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:21:38.48Z", "agentId": "100007", "leadId": "643d13f6f2a2d331daac7a37", "sourceLeadId": "936624", "policyId": null}, {"caseId": "6438d4f16071bd7c46d29528", "insured": [{"_id": "6438d4f16071bd7c46d29529", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1223", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438d4f6b72d3e79b6222094", "applicationId": "6438d54b948da93527db963f", "proposalId": "6438d548948da93527db963e", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:20:10.931Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934799", "policyId": null}, {"caseId": "643e11e36071bd7c46de83d1", "insured": [{"_id": "643e11e36071bd7c46de83d2", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "<PERSON>", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-18_1144", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643e11e8b72d3e79b62220e8", "applicationId": "643e1237948da93527db96f9", "proposalId": "643e1233948da93527db96f8", "isQuickSI": false, "sumAssured": 214899522.0, "modalPremium": 9010290.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T04:17:03.022Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "937858", "policyId": null}, {"caseId": "643e0b4a6071bd7c46de457d", "insured": [{"_id": "643e0b4a6071bd7c46de457e", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-18_1116", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643e0b50b72d3e79b62220e7", "applicationId": "643e0ba4948da93527db96f6", "proposalId": "643e0b9c948da93527db96f5", "isQuickSI": false, "sumAssured": 25017141.0, "modalPremium": 267680.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T03:42:52.862Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "937827", "policyId": null}, {"caseId": "643928856071bd7c46d5aaf2", "insured": [{"_id": "643928856071bd7c46d5aaf3", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1820", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6439288bb72d3e79b62220a9", "applicationId": "643928d8948da93527db9670", "proposalId": "643928d5948da93527db966f", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T03:13:41.885Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "935010", "policyId": null}, {"caseId": "643df55b6071bd7c46dba65a", "insured": [{"_id": "643df55b6071bd7c46dba65b", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-18_0943", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643df560b72d3e79b62220d9", "applicationId": "643df5c9948da93527db96d7", "proposalId": "643df5c6948da93527db96d6", "isQuickSI": false, "sumAssured": 145508162.0, "modalPremium": 6102792.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T03:12:22.168Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "937538", "policyId": null}, {"caseId": "643e090d6071bd7c46ddb19f", "insured": [{"_id": "643e090d6071bd7c46ddb1a0", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": null}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643e0911b72d3e79b62220e6", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-18T03:10:46.928Z", "agentId": "100007", "leadId": "643e090c7bd0e07f76c5ebab", "sourceLeadId": "937817", "policyId": null}, {"caseId": "643e05f76071bd7c46dd3cf7", "insured": [{"_id": "643e05f76071bd7c46dd3cf8", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-18_1056", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643e0600b72d3e79b62220e4", "applicationId": "643e06dc948da93527db96ee", "proposalId": "643e06d7948da93527db96ed", "isQuickSI": false, "sumAssured": 214899522.0, "modalPremium": 9010290.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T03:10:23.942Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "937803", "policyId": null}, {"caseId": "643e07c46071bd7c46dd5e9e", "insured": [{"_id": "643e07c46071bd7c46dd5e9f", "personalInfo": {"person": {"title": null, "firstName": "Bean1", "middleName": null, "lastName": "Adsf"}}}], "proposalName": null, "productName": null, "status": "FULL_SI", "fnaId": "643e0832b72d3e79b62220e5", "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-18T03:03:56.171Z", "agentId": "100007", "leadId": "643e07c37bd0e07f76c5ebaa", "sourceLeadId": "937815", "policyId": null}, {"caseId": "643dffa06071bd7c46dce515", "insured": [{"_id": "643dffa06071bd7c46dce516", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-18_1029", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643dffa5b72d3e79b62220e2", "applicationId": "643e0083948da93527db96eb", "proposalId": "643e007f948da93527db96ea", "isQuickSI": false, "sumAssured": 214899522.0, "modalPremium": 9064290.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T02:33:48.652Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "937710", "policyId": null}, {"caseId": "643df63d6071bd7c46dbd97e", "insured": [{"_id": "643df6466071bd7c46dbd97f", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Term 5/5 2023-04-18_0949", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643df650b72d3e79b62220db", "applicationId": "643df73f948da93527db96da", "proposalId": "643df73b948da93527db96d9", "isQuickSI": false, "sumAssured": 250263157.0, "modalPremium": 951000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T02:23:40.302Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "937555", "policyId": null}, {"caseId": "643df7f06071bd7c46dc23bc", "insured": [{"_id": "643df7f06071bd7c46dc23bd", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-18_0954", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643df7f5b72d3e79b62220de", "applicationId": "643df843948da93527db96df", "proposalId": "643df83f948da93527db96de", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T02:19:02.974Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "937580", "policyId": null}, {"caseId": "643dfa956071bd7c46dc7378", "insured": [{"_id": "643dfa956071bd7c46dc7379", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "Jiang"}}}], "proposalName": "FWD for Pension 85/7 2023-04-18_1013", "productName": {"th": "เอฟดับบลิวดี ฟอร์ เพนชัน 85/7 (บำนาญแบบลดหย่อนได้)", "en": "FWD for Pension 85/7"}, "status": "IN_APP", "fnaId": "643dfb4db72d3e79b62220e1", "applicationId": "643dfcd2948da93527db96e8", "proposalId": "643dfaec948da93527db96e6", "isQuickSI": false, "sumAssured": 2075723.0, "modalPremium": 675550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T02:13:39.721Z", "agentId": "100007", "leadId": "643dfa94edfed375500b3ebd", "sourceLeadId": "937619", "policyId": null}, {"caseId": "643df96e6071bd7c46dc59b4", "insured": [{"_id": "643df96e6071bd7c46dc59b5", "personalInfo": {"person": {"title": "", "firstName": "dsdsd", "middleName": "", "lastName": "sdfsdfs"}}}], "proposalName": "Whole Life 90/90 (Non Par) 2023-04-18_1000", "productName": {"th": "ตลอดชีพ 90/90", "en": "Whole Life 90/90 (Non Par)"}, "status": "IN_APP", "fnaId": "643df977b72d3e79b62220e0", "applicationId": "643df9cf948da93527db96e4", "proposalId": "643df9cc948da93527db96e3", "isQuickSI": false, "sumAssured": 9219496.0, "modalPremium": 116634.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T02:01:35.734Z", "agentId": "100007", "leadId": "642e3f8e6dd8bd05d18140d0", "sourceLeadId": "937603", "policyId": null}, {"caseId": "643df6bd6071bd7c46dbfac5", "insured": [{"_id": "643df6bd6071bd7c46dbfd55", "personalInfo": {"person": {"title": "T001", "firstName": "dsdsd", "middleName": "", "lastName": "sdfsdfs"}}}], "proposalName": "Whole Life 90/90 (Non Par) 2023-04-18_0950", "productName": {"th": "ตลอดชีพ 90/90", "en": "Whole Life 90/90 (Non Par)"}, "status": "IN_APP", "fnaId": "643df6d0b72d3e79b62220dd", "applicationId": "643df755948da93527db96dc", "proposalId": "643df752948da93527db96db", "isQuickSI": false, "sumAssured": 9219496.0, "modalPremium": 116634.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T01:53:22.025Z", "agentId": "100007", "leadId": "642e3f8e6dd8bd05d18140d0", "sourceLeadId": "937564", "policyId": null}, {"caseId": "643df3cb6071bd7c46db947c", "insured": [{"_id": "643df3cb6071bd7c46db947d", "personalInfo": {"person": {"title": null, "firstName": "dsdsd", "middleName": null, "lastName": "sdfsdfs"}}}], "proposalName": "Whole Life 90/90 (Non Par) 2023-04-18_0937", "productName": {"th": "ตลอดชีพ 90/90", "en": "Whole Life 90/90 (Non Par)"}, "status": "IN_APP", "fnaId": "643df3d7b72d3e79b62220d8", "applicationId": "643df446948da93527db96d4", "proposalId": "643df442948da93527db96d3", "isQuickSI": false, "sumAssured": 9219496.0, "modalPremium": 116634.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-18T01:37:11.993Z", "agentId": "100007", "leadId": "642e3f8e6dd8bd05d18140d0", "sourceLeadId": "937517", "policyId": null}, {"caseId": "643d0b646071bd7c46da8427", "insured": [{"_id": "643d0b646071bd7c46da8428", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "No dopa test 2"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1705", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643d0b6cb72d3e79b62220d2", "applicationId": "643d0bcb948da93527db96c6", "proposalId": "643d0bc6948da93527db96c5", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T09:37:44.23Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "936573", "policyId": null}, {"caseId": "643d10ca6071bd7c46dab6e1", "insured": [{"_id": "643d10ca6071bd7c46dab6e2", "personalInfo": {"person": {"title": "T001", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "No dopa test 2"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1727", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643d10d1b72d3e79b62220d4", "applicationId": "643d110d948da93527db96cb", "proposalId": "643d110a948da93527db96ca", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T09:36:49.746Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "936609", "policyId": null}, {"caseId": "643d0ff46071bd7c46daa51a", "insured": [{"_id": "643d0ff46071bd7c46daa51b", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": null}}}], "proposalName": null, "productName": null, "status": "QUICK_SI", "fnaId": null, "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-17T09:26:34.938Z", "agentId": "100007", "leadId": "643d0ff3f2a2d331daac7a35", "sourceLeadId": "936607", "policyId": null}, {"caseId": "643d08a56071bd7c46da5bb3", "insured": [{"_id": "643d08a66071bd7c46da5bb4", "personalInfo": {"person": {"title": "", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "No dopa test 2"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1653", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643d08acb72d3e79b62220d1", "applicationId": "643d091e948da93527db96c3", "proposalId": "643d0916948da93527db96c2", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:54:38.516Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "936564", "policyId": null}, {"caseId": "643d07a66071bd7c46da3fe9", "insured": [{"_id": "643d07a76071bd7c46da3fea", "personalInfo": {"person": {"title": "T001", "firstName": "FUWEndtoEnd", "middleName": "", "lastName": "Hucbsccvh"}}}], "proposalName": "FWD Precious Protection 2023-04-17_1649", "productName": {"th": "เอฟดับบลิวดี พรีเชียส โพรเทคชัน", "en": "FWD Precious Protection"}, "status": "IN_APP", "fnaId": "643d07adb72d3e79b62220d0", "applicationId": "643d0818948da93527db96bf", "proposalId": "643d0810948da93527db96be", "isQuickSI": false, "sumAssured": 60000.0, "modalPremium": 23252.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:53:06.855Z", "agentId": "100007", "leadId": "6419555a497ed908b2c9904f", "sourceLeadId": "936561", "policyId": null}, {"caseId": "643d011e6071bd7c46d97e6c", "insured": [{"_id": "643d011e6071bd7c46d97e6d", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1642", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643d0126b72d3e79b62220c9", "applicationId": "643d066a948da93527db96b9", "proposalId": "643d0666948da93527db96b8", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:44:06.839Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936511", "policyId": null}, {"caseId": "643d05606071bd7c46d9f457", "insured": [{"_id": "643d05616071bd7c46d9f458", "personalInfo": {"person": {"title": "", "firstName": "Wenjin Test DOPA", "middleName": "", "lastName": "No dopa"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1640", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643d0567b72d3e79b62220ce", "applicationId": "643d05fa948da93527db96b7", "proposalId": "643d05f7948da93527db96b6", "isQuickSI": false, "sumAssured": 11483272.0, "modalPremium": 1977764.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:41:37.121Z", "agentId": "100007", "leadId": "643d0324f2a2d331daac7a34", "sourceLeadId": "936553", "policyId": null}, {"caseId": "643cc8dd6071bd7c46d7539d", "insured": [{"_id": "643cc8dd6071bd7c46d7539e", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1221", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643cc8e2b72d3e79b62220b8", "applicationId": "643cc940948da93527db968c", "proposalId": "643cc93a948da93527db968b", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:19:10.395Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936293", "policyId": null}, {"caseId": "643cf4336071bd7c46d8b350", "insured": [{"_id": "643cf4336071bd7c46d8b351", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1526", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643cf437b72d3e79b62220c4", "applicationId": "643cf49c948da93527db96a8", "proposalId": "643cf499948da93527db96a7", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:16:15.53Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936428", "policyId": null}, {"caseId": "6438c8566071bd7c46d199e6", "insured": [{"_id": "6438c8566071bd7c46d199e7", "personalInfo": {"person": {"title": "T001", "firstName": "Haofeng test", "middleName": "", "lastName": "Sdf"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-14_1130", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "6438c85fb72d3e79b6222091", "applicationId": "6438c8f3948da93527db9639", "proposalId": "6438c8ee948da93527db9638", "isQuickSI": false, "sumAssured": 52429403.0, "modalPremium": 2209056.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T08:12:01.773Z", "agentId": "100007", "leadId": "6438b9791581cb7bb63cd4dd", "sourceLeadId": "934780", "policyId": null}, {"caseId": "643cf7b26071bd7c46d8ced7", "insured": [{"_id": "643cf7b26071bd7c46d8ced8", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "Asdf", "lastName": "Jiang"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-17_1547", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643cf86ab72d3e79b62220c5", "applicationId": "643cf97b948da93527db96ac", "proposalId": "643cf7fb948da93527db96aa", "isQuickSI": false, "sumAssured": 9616996.0, "modalPremium": 282218.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:58:16.125Z", "agentId": "100007", "leadId": "643cf7b272729a06ba4ad00e", "sourceLeadId": "936444", "policyId": null}, {"caseId": "643cf0996071bd7c46d874e0", "insured": [{"_id": "643cf0996071bd7c46d874e1", "personalInfo": {"person": {"title": "T001", "firstName": "Test Wenjin 20", "middleName": "", "lastName": "20age"}}}], "proposalName": "Term 5/5 2023-04-17_1511", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cf0a1b72d3e79b62220c1", "applicationId": "643cf143948da93527db96a2", "proposalId": "643cf13f948da93527db96a1", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:46:36.65Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "936409", "policyId": null}, {"caseId": "643cf1ef6071bd7c46d88b52", "insured": [{"_id": "643cf1ef6071bd7c46d88b53", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-17_1517", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643cf1f6b72d3e79b62220c2", "applicationId": "643cf27d948da93527db96a5", "proposalId": "643cf279948da93527db96a4", "isQuickSI": false, "sumAssured": 145508162.0, "modalPremium": 6102792.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:17:38.914Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "936414", "policyId": null}, {"caseId": "643cebef6071bd7c46d802bb", "insured": [{"_id": "643cebf06071bd7c46d802bc", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-17_1451", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643cebf1b72d3e79b62220bd", "applicationId": "643cec87948da93527db9699", "proposalId": "643cec84948da93527db9698", "isQuickSI": false, "sumAssured": 145508162.0, "modalPremium": 6102792.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:12:33.381Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "936369", "policyId": null}, {"caseId": "643cecb36071bd7c46d8481c", "insured": [{"_id": "643cecb36071bd7c46d8481d", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-17_1453", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643cecb9b72d3e79b62220bf", "applicationId": "643ced06948da93527db969e", "proposalId": "643ced03948da93527db969d", "isQuickSI": false, "sumAssured": 145508162.0, "modalPremium": 6102792.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:11:16.515Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "936373", "policyId": null}, {"caseId": "643cedc16071bd7c46d85e76", "insured": [{"_id": "643cedc16071bd7c46d85e77", "personalInfo": {"person": {"title": "T004", "firstName": "Test Wenjin 20", "middleName": "", "lastName": "20age"}}}], "proposalName": "Term 5/5 2023-04-17_1458", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cedccb72d3e79b62220c0", "applicationId": "643cee25948da93527db96a0", "proposalId": "643cee21948da93527db969f", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:02:18.043Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "936382", "policyId": null}, {"caseId": "643ce2ef6071bd7c46d7dda5", "insured": [{"_id": "643ce2ef6071bd7c46d7dda6", "personalInfo": {"person": {"title": "T001", "firstName": "Test Wenjin Big 16", "middleName": "", "lastName": "20age"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1413", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643ce2fab72d3e79b62220bb", "applicationId": "643ce39f948da93527db9693", "proposalId": "643ce39b948da93527db9692", "isQuickSI": false, "sumAssured": 143579.0, "modalPremium": 22094.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T07:00:33.035Z", "agentId": "100007", "leadId": "642a78205e39fa55463a4a51", "sourceLeadId": "936341", "policyId": null}, {"caseId": "643cec866071bd7c46d836bf", "insured": [{"_id": "643cec866071bd7c46d836c0", "personalInfo": {"person": {"title": "", "firstName": "Test Wenjin 20", "middleName": "", "lastName": "20age"}}}], "proposalName": "Term 5/5 2023-04-17_1453", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cec8db72d3e79b62220be", "applicationId": "643cecf0948da93527db969c", "proposalId": "643cecec948da93527db969b", "isQuickSI": false, "sumAssured": 7220588.0, "modalPremium": 24550.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T06:54:06.536Z", "agentId": "100007", "leadId": "642aa76b5e39fa55463a4a6c", "sourceLeadId": "936371", "policyId": null}, {"caseId": "643ce9bf6071bd7c46d7f169", "insured": [{"_id": "643ce9bf6071bd7c46d7f16a", "personalInfo": {"person": {"title": "", "firstName": "Test Wenjin Big 16", "middleName": "", "lastName": "20age"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-17_1443", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "643ce9cab72d3e79b62220bc", "applicationId": "643cea8b948da93527db9696", "proposalId": "643cea87948da93527db9695", "isQuickSI": false, "sumAssured": 143579.0, "modalPremium": 22094.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T06:49:47.73Z", "agentId": "100007", "leadId": "642a78205e39fa55463a4a51", "sourceLeadId": "936358", "policyId": null}, {"caseId": "643cd1e66071bd7c46d7ac56", "insured": [{"_id": "643cd1e66071bd7c46d7ac57", "personalInfo": {"person": {"title": null, "firstName": "keith 0417 one", "middleName": null, "lastName": ""}}}], "proposalName": "FWD One Link (Unit Linked) 2023-04-17_0111", "productName": {"th": "เอฟดับบลิวดีวันลิงค์ (ยูนิตลิงค์)", "en": "FWD One Link (Unit Linked)"}, "status": "IN_APP", "fnaId": "643cd1e8b72d3e79b62220ba", "applicationId": "643cd50c948da93527db9690", "proposalId": "643cd506948da93527db968f", "isQuickSI": false, "sumAssured": 150000.0, "modalPremium": 100000.0, "paymentMode": "ONE_TIME", "updatedAt": "2023-04-17T05:11:41.393Z", "agentId": "100007", "leadId": "643cd1e572729a06ba4ad00c", "sourceLeadId": "936313", "policyId": null}, {"caseId": "643ccaa06071bd7c46d764da", "insured": [{"_id": "643ccaa06071bd7c46d764db", "personalInfo": {"person": {"title": null, "firstName": "Sdfdsfs", "middleName": null, "lastName": "Sdfsfs"}}}], "proposalName": "FWD One Link (Unit Linked) 2023-04-17_1230", "productName": {"th": "เอฟดับบลิวดีวันลิงค์ (ยูนิตลิงค์)", "en": "FWD One Link (Unit Linked)"}, "status": "IN_APP", "fnaId": "643ccaa6b72d3e79b62220b9", "applicationId": "643ccb64948da93527db968e", "proposalId": "643ccb62948da93527db968d", "isQuickSI": false, "sumAssured": 150000.0, "modalPremium": 100000.0, "paymentMode": "ONE_TIME", "updatedAt": "2023-04-17T04:30:29.481Z", "agentId": "100007", "leadId": "6406d13580ef9d63bcbd7f2a", "sourceLeadId": "936298", "policyId": null}, {"caseId": "643cc3f96071bd7c46d715aa", "insured": [{"_id": "643cc3f96071bd7c46d715ab", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1200", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643cc3feb72d3e79b62220b4", "applicationId": "643cc456948da93527db9687", "proposalId": "643cc453948da93527db9686", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2632973.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T04:18:26.075Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936274", "policyId": null}, {"caseId": "643cc4246071bd7c46d72464", "insured": [{"_id": "643cc4246071bd7c46d72465", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-17_1200", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cc427b72d3e79b62220b5", "applicationId": "643cc46a948da93527db9689", "proposalId": "643cc466948da93527db9688", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T04:00:43.064Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "936276", "policyId": null}, {"caseId": "643cc2d26071bd7c46d70482", "insured": [{"_id": "643cc2d26071bd7c46d70483", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-17_1155", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cc2d7b72d3e79b62220b3", "applicationId": "643cc31b948da93527db9684", "proposalId": "643cc318948da93527db9683", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:55:08.099Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "936267", "policyId": null}, {"caseId": "643cc0806071bd7c46d6dfd0", "insured": [{"_id": "643cc0806071bd7c46d6dfd1", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "TesingTwo"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-17_1145", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "643cc087b72d3e79b62220b2", "applicationId": "643cc0dd948da93527db9682", "proposalId": "643cc0da948da93527db9681", "isQuickSI": false, "sumAssured": 250102386.0, "modalPremium": 10486697.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:48:20.158Z", "agentId": "100007", "leadId": "642bd0586dd8bd05d1814082", "sourceLeadId": "936262", "policyId": null}, {"caseId": "642809d78024c25fe0d04379", "insured": [{"_id": "642809d78024c25fe0d0437a", "personalInfo": {"person": {"title": "T004", "firstName": "keith 0401 five", "middleName": "", "lastName": "1234234312"}}}], "proposalName": "FWD Whole Life 99/15 2023-04-01_0643", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "642809d9f384243d2cfe7555", "applicationId": "64280ae2ee4447356f07c82c", "proposalId": "64280addee4447356f07c82b", "isQuickSI": false, "sumAssured": 1156058.0, "modalPremium": 200264.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:47:49.962Z", "agentId": "100007", "leadId": "642809d67ea2c6658fd154f0", "sourceLeadId": "921036", "policyId": null}, {"caseId": "643cc0476071bd7c46d6c764", "insured": [{"_id": "643cc0476071bd7c46d6c765", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-17_1144", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cc04cb72d3e79b62220b1", "applicationId": "643cc091948da93527db9680", "proposalId": "643cc08e948da93527db967e", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:45:24.479Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "936261", "policyId": null}, {"caseId": "6434db7b08ae8b23b7e07830", "insured": [{"_id": "6434db7b08ae8b23b7e07831", "personalInfo": {"person": {"title": "", "firstName": "Testing Ryan", "middleName": "", "lastName": "Testing"}}}], "proposalName": "Whole Life Extra 99/5 2023-04-11_1214", "productName": {"th": "ตลอดชีพพิเศษชำระเบี้ยประกันภัย 5 ปี (ชนิดมีเงินปันผล)", "en": "Whole Life Extra 99/5"}, "status": "IN_APP", "fnaId": "6434db86b92d29003a472c48", "applicationId": "6434deb89dd5ee6d2cdcdaaa", "proposalId": "6434deb59dd5ee6d2cdcdaa9", "isQuickSI": false, "sumAssured": 250050357.0, "modalPremium": 35014050.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:43:35.354Z", "agentId": "100007", "leadId": "642b88dc5e39fa55463a4a6d", "sourceLeadId": "930897", "policyId": null}, {"caseId": "643cbd926071bd7c46d6a539", "insured": [{"_id": "643cbd926071bd7c46d6a53a", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-17_1132", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cbd97b72d3e79b62220b0", "applicationId": "643cbddf948da93527db967d", "proposalId": "643cbddc948da93527db967c", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:32:48.358Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "936252", "policyId": null}, {"caseId": "643cbb166071bd7c46d64b1a", "insured": [{"_id": "643cbb166071bd7c46d64b1b", "personalInfo": {"person": {"title": "", "firstName": "dfsdfsdf", "middleName": "", "lastName": "fddfgdgf"}}}], "proposalName": "Term 5/5 2023-04-17_1124", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cbb17b72d3e79b62220ae", "applicationId": "643cbbe2948da93527db967b", "proposalId": "643cbbdf948da93527db967a", "isQuickSI": false, "sumAssured": 250263157.0, "modalPremium": 951000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:29:07.385Z", "agentId": "100007", "leadId": "643cbb1672729a06ba4ad00a", "sourceLeadId": "936246", "policyId": null}, {"caseId": "643cae6f6071bd7c46d615a2", "insured": [{"_id": "643cae6f6071bd7c46d615a3", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_1028", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643cae74b72d3e79b62220ac", "applicationId": "643caec5948da93527db9677", "proposalId": "643caec1948da93527db9676", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:28:47.402Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "936144", "policyId": null}, {"caseId": "643cb8f86071bd7c46d62dea", "insured": [{"_id": "643cb8f86071bd7c46d62deb", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Term 5/5 2023-04-17_1113", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "643cb8fcb72d3e79b62220ad", "applicationId": "643cb943948da93527db9679", "proposalId": "643cb940948da93527db9678", "isQuickSI": false, "sumAssured": 33555555.0, "modalPremium": 151000.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T03:13:09.47Z", "agentId": "100007", "leadId": "63da21c9212dd04c677c88e2", "sourceLeadId": "936228", "policyId": null}, {"caseId": "643ca43c6071bd7c46d5d1b9", "insured": [{"_id": "643ca43c6071bd7c46d5d1ba", "personalInfo": {"person": {"title": "T001", "firstName": "Yan", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-17_0947", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "643ca449b72d3e79b62220ab", "applicationId": "643ca528948da93527db9673", "proposalId": "643ca524948da93527db9672", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-17T02:05:06.825Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "935997", "policyId": null}, {"caseId": "643922636071bd7c46d57342", "insured": [{"_id": "643922636071bd7c46d57343", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1754", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "64392267b72d3e79b62220a8", "applicationId": "643922c1948da93527db966d", "proposalId": "643922bd948da93527db966c", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T10:14:47.69Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934998", "policyId": null}, {"caseId": "6438db6d6071bd7c46d32ff6", "insured": [{"_id": "6438db6d6071bd7c46d32ff7", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1251", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438db73b72d3e79b622209b", "applicationId": "6438dbc5948da93527db964d", "proposalId": "6438dbc2948da93527db964c", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T09:52:09.1Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934818", "policyId": null}, {"caseId": "6438fda46071bd7c46d4db28", "insured": [{"_id": "6438fda46071bd7c46d4db29", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1517", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438fda9b72d3e79b62220a6", "applicationId": "6438fe13948da93527db9667", "proposalId": "6438fe0f948da93527db9666", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T07:49:13.719Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934898", "policyId": null}, {"caseId": "6438f7c16071bd7c46d4adff", "insured": [{"_id": "6438f7c16071bd7c46d4ae00", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1452", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438f7c5b72d3e79b62220a4", "applicationId": "6438f813948da93527db9664", "proposalId": "6438f810948da93527db9663", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T07:03:27.876Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934891", "policyId": null}, {"caseId": "6438efc76071bd7c46d3df0a", "insured": [{"_id": "6438efc76071bd7c46d3df0b", "personalInfo": {"person": {"title": null, "firstName": "Child test", "middleName": null, "lastName": ""}}}], "proposalName": "FWD Whole Life 99/15 2023-04-14_1422", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "6438efcdb72d3e79b62220a0", "applicationId": "6438f13a948da93527db965d", "proposalId": "6438f137948da93527db965c", "isQuickSI": false, "sumAssured": 100000.0, "modalPremium": 14157.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T06:22:50.665Z", "agentId": "100007", "leadId": "64016b7680ef9d63bcbd7ee9", "sourceLeadId": "934866", "policyId": null}, {"caseId": "6438edc16071bd7c46d3a560", "insured": [{"_id": "6438edc26071bd7c46d3a563", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "Tran"}}}], "proposalName": null, "productName": null, "status": "QUICK_SI", "fnaId": null, "applicationId": null, "proposalId": null, "isQuickSI": null, "sumAssured": null, "modalPremium": null, "paymentMode": null, "updatedAt": "2023-04-14T06:08:05.848Z", "agentId": "100007", "leadId": "642e33d06dd8bd05d18140cd", "sourceLeadId": "934857", "policyId": null}, {"caseId": "6438dca36071bd7c46d3649a", "insured": [{"_id": "6438dca36071bd7c46d3649b", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1256", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438dca9b72d3e79b622209c", "applicationId": "6438dcf0948da93527db9650", "proposalId": "6438dcec948da93527db964f", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T05:11:55.923Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934821", "policyId": null}, {"caseId": "6438dac66071bd7c46d31cdc", "insured": [{"_id": "6438dac66071bd7c46d31cdd", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "<PERSON><PERSON>"}}}], "proposalName": "FWD One Link (Unit Linked) 2023-04-14_1250", "productName": {"th": "เอฟดับบลิวดีวันลิงค์ (ยูนิตลิงค์)", "en": "FWD One Link (Unit Linked)"}, "status": "IN_APP", "fnaId": "6438daccb72d3e79b622209a", "applicationId": "6438db84948da93527db964b", "proposalId": "6438db81948da93527db964a", "isQuickSI": false, "sumAssured": 150000.0, "modalPremium": 100000.0, "paymentMode": "ONE_TIME", "updatedAt": "2023-04-14T04:54:11.191Z", "agentId": "100007", "leadId": "6412d29acfe54a62686cdefe", "sourceLeadId": "934815", "policyId": null}, {"caseId": "6438d8d26071bd7c46d2f6ae", "insured": [{"_id": "6438d8d26071bd7c46d2f6af", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1239", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438d8d7b72d3e79b6222098", "applicationId": "6438d920948da93527db9648", "proposalId": "6438d91d948da93527db9647", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:48:41.302Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934809", "policyId": null}, {"caseId": "6438d7b06071bd7c46d2cbcc", "insured": [{"_id": "6438d7b16071bd7c46d2cbcd", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1235", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438d7b5b72d3e79b6222096", "applicationId": "6438d813948da93527db9645", "proposalId": "6438d810948da93527db9644", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:38:21.118Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934805", "policyId": null}, {"caseId": "6438d64e6071bd7c46d2af44", "insured": [{"_id": "6438d64e6071bd7c46d2af45", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-14_1229", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6438d652b72d3e79b6222095", "applicationId": "6438d69d948da93527db9642", "proposalId": "6438d69a948da93527db9641", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:33:35.319Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "934800", "policyId": null}, {"caseId": "6423f579b488af1251227699", "insured": [{"_id": "6423f579b488af125122769a", "personalInfo": {"person": {"title": null, "firstName": "<PERSON>", "middleName": null, "lastName": "Tao"}}}], "proposalName": "FWD Whole Life 99/15 2023-03-29_1625", "productName": {"th": "เอฟดับบลิวดี ตลอดชีพ มีเงินคืน 99/15", "en": "FWD Whole Life 99/15"}, "status": "IN_APP", "fnaId": "6423f583a52ee924bdec1f08", "applicationId": "6423f5e3583db87729e7fc6d", "proposalId": "6423f5df583db87729e7fc6c", "isQuickSI": false, "sumAssured": 773471.0, "modalPremium": 134762.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:20:51.176Z", "agentId": "100007", "leadId": "6423a713c2c2382f7b00f3d6", "sourceLeadId": "914983", "policyId": null}, {"caseId": "642ea7b13a05457c1dc2e667", "insured": [{"_id": "642ea7b13a05457c1dc2e668", "personalInfo": {"person": {"title": "T004", "firstName": "fai new test thai", "middleName": "", "lastName": "fgdfgd"}}}], "proposalName": "Term 5/5 2023-04-06_1908", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "642ea7b2b92d29003a472c46", "applicationId": "642ea8259dd5ee6d2cdcdaa6", "proposalId": "642ea8229dd5ee6d2cdcdaa5", "isQuickSI": false, "sumAssured": 1000000.0, "modalPremium": 3800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:19:03.192Z", "agentId": "100007", "leadId": "642e3ad26dd8bd05d18140ce", "sourceLeadId": "926511", "policyId": null}, {"caseId": "642a9e806bb8c130eaa85194", "insured": [{"_id": "642a9e806bb8c130eaa85195", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "Testing"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-03_1747", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "642a9e8211ff72259b86e6ea", "applicationId": "642e52219dd5ee6d2cdcda7f", "proposalId": "642aa0c2eb05a01c19d28a77", "isQuickSI": false, "sumAssured": 500000000.0, "modalPremium": 1890000.0, "paymentMode": "EVERY_MONTH", "updatedAt": "2023-04-14T04:08:24.743Z", "agentId": "100007", "leadId": "642a8aa05e39fa55463a4a5c", "sourceLeadId": "922174", "policyId": null}, {"caseId": "6437d2126071bd7c46cfcb1e", "insured": [{"_id": "6437d2126071bd7c46cfcb1f", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-13_1759", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "6437d216b72d3e79b6222087", "applicationId": "6437d275948da93527db961a", "proposalId": "6437d271948da93527db9619", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2631800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T04:05:32.23Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "933674", "policyId": null}, {"caseId": "6438c8686071bd7c46d1a816", "insured": [{"_id": "6438c8686071bd7c46d1a817", "personalInfo": {"person": {"title": "T001", "firstName": "EndToEnd", "middleName": "", "lastName": "MingsTest"}}}], "proposalName": "Whole Life Extra 99/5 2023-04-14_1133", "productName": {"th": "ตลอดชีพพิเศษชำระเบี้ยประกันภัย 5 ปี (ชนิดมีเงินปันผล)", "en": "Whole Life Extra 99/5"}, "status": "IN_APP", "fnaId": "6438c870b72d3e79b6222092", "applicationId": "6438c972948da93527db963b", "proposalId": "6438c96e948da93527db963a", "isQuickSI": false, "sumAssured": 2618228.0, "modalPremium": 371552.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T03:42:06.266Z", "agentId": "100007", "leadId": "6438bbb91581cb7bb63cd4de", "sourceLeadId": "934781", "policyId": null}, {"caseId": "6437d0be6071bd7c46cf9061", "insured": [{"_id": "6437d0be6071bd7c46cf9062", "personalInfo": {"person": {"title": "T001", "firstName": "Daniel0413", "middleName": "", "lastName": "Hi"}}}], "proposalName": "FWD Life Saving 30/15 2023-04-13_1753", "productName": {"th": "เอฟดับบลิวดี ไลฟ์ เซฟวิ่ง 30/15", "en": "FWD Life Saving 30/15"}, "status": "IN_APP", "fnaId": "6437d0c0b72d3e79b6222086", "applicationId": "6437d132948da93527db9617", "proposalId": "6437d12e948da93527db9616", "isQuickSI": false, "sumAssured": 1536585.0, "modalPremium": 326926.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T03:32:02.522Z", "agentId": "100007", "leadId": "6437d0bee2e8db34e5f51803", "sourceLeadId": "933671", "policyId": null}, {"caseId": "6438bbba6071bd7c46d0749a", "insured": [{"_id": "6438bbba6071bd7c46d0749b", "personalInfo": {"person": {"title": "T004", "firstName": "EndToEnd", "middleName": "", "lastName": "MingsTest"}}}], "proposalName": "Whole Life Extra 99/5 2023-04-14_1042", "productName": {"th": "ตลอดชีพพิเศษชำระเบี้ยประกันภัย 5 ปี (ชนิดมีเงินปันผล)", "en": "Whole Life Extra 99/5"}, "status": "IN_APP", "fnaId": "6438bbbbb72d3e79b622208c", "applicationId": "6438bda9948da93527db962d", "proposalId": "6438bda6948da93527db962c", "isQuickSI": false, "sumAssured": 2618228.0, "modalPremium": 371552.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T03:27:59.738Z", "agentId": "100007", "leadId": "6438bbb91581cb7bb63cd4de", "sourceLeadId": "934737", "policyId": null}, {"caseId": "6438b9796071bd7c46d01eb2", "insured": [{"_id": "6438b97a6071bd7c46d01eb3", "personalInfo": {"person": {"title": "T001", "firstName": "Haofeng test", "middleName": "Wd", "lastName": "Sdf"}}}], "proposalName": "Whole Life 90/10 (Non Par) 2023-04-14_1030", "productName": {"th": "ตลอดชีพ 90/10", "en": "Whole Life 90/10 (Non Par)"}, "status": "IN_APP", "fnaId": "6438b97bb72d3e79b622208a", "applicationId": "6438bae0948da93527db9627", "proposalId": "6438badb948da93527db9626", "isQuickSI": false, "sumAssured": 52429403.0, "modalPremium": 2214381.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-14T03:26:31.015Z", "agentId": "100007", "leadId": "6438b9791581cb7bb63cd4dd", "sourceLeadId": "934697", "policyId": null}, {"caseId": "64378a226071bd7c46ccbac5", "insured": [{"_id": "64378a226071bd7c46ccbac6", "personalInfo": {"person": {"title": null, "firstName": "Yan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-13_1623", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "64378a24b72d3e79b6222071", "applicationId": "6437bbe9948da93527db960f", "proposalId": "6437bbe4948da93527db960e", "isQuickSI": false, "sumAssured": 250171428.0, "modalPremium": 2676800.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-13T09:56:51.664Z", "agentId": "100007", "leadId": "64378a225db8a24c0f6708cc", "sourceLeadId": "933588", "policyId": null}, {"caseId": "6437d00b6071bd7c46cf7695", "insured": [{"_id": "6437d00b6071bd7c46cf7696", "personalInfo": {"person": {"title": null, "firstName": "Fa", "middleName": null, "lastName": "12"}}}], "proposalName": "FWD One Link (Unit Linked) 2023-04-13_1750", "productName": {"th": "เอฟดับบลิวดีวันลิงค์ (ยูนิตลิงค์)", "en": "FWD One Link (Unit Linked)"}, "status": "IN_APP", "fnaId": "6437d012b72d3e79b6222085", "applicationId": "6437d07f948da93527db9614", "proposalId": "6437d07c948da93527db9613", "isQuickSI": false, "sumAssured": 150000.0, "modalPremium": 100000.0, "paymentMode": "ONE_TIME", "updatedAt": "2023-04-13T09:50:56.447Z", "agentId": "100007", "leadId": "64224fd0c2c2382f7b00f2dd", "sourceLeadId": "933669", "policyId": null}, {"caseId": "642bc7323f4e3f27af2260c7", "insured": [{"_id": "642bc7333f4e3f27af2260c8", "personalInfo": {"person": {"title": "T001", "firstName": "<PERSON>", "middleName": "", "lastName": "Test"}}}], "proposalName": "Whole Life 99/99 (Non Par) 2023-04-04_1452", "productName": {"th": "ตลอดชีพ 99/99", "en": "Whole Life 99/99 (Non Par)"}, "status": "IN_APP", "fnaId": "642bc735c970a412fe45c653", "applicationId": "642e49d19dd5ee6d2cdcda7b", "proposalId": "642bc91cf896ba517bf40804", "isQuickSI": false, "sumAssured": 250167686.0, "modalPremium": 3249638.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-13T09:47:42.223Z", "agentId": "100007", "leadId": "642b932a5e39fa55463a4a6e", "sourceLeadId": "923541", "policyId": null}, {"caseId": "642ea3983a05457c1dc26342", "insured": [{"_id": "642ea3983a05457c1dc26343", "personalInfo": {"person": {"title": "T001", "firstName": "fai new test thai", "middleName": "", "lastName": "fgfg"}}}], "proposalName": "Term 5/5 2023-04-06_1850", "productName": {"th": "กำหนดระยะเวลา 5/5", "en": "Term 5/5"}, "status": "IN_APP", "fnaId": "642ea399b92d29003a472c44", "applicationId": "642ea40f9dd5ee6d2cdcdaa2", "proposalId": "642ea40c9dd5ee6d2cdcdaa1", "isQuickSI": false, "sumAssured": 35078947.0, "modalPremium": 133300.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-13T09:22:37.285Z", "agentId": "100007", "leadId": "642e3ad26dd8bd05d18140ce", "sourceLeadId": "926498", "policyId": null}, {"caseId": "6434e0ef08ae8b23b7e0919c", "insured": [{"_id": "6434e0ef08ae8b23b7e0919d", "personalInfo": {"person": {"title": null, "firstName": "Testing Ryan", "middleName": null, "lastName": ""}}}], "proposalName": "Whole Life Extra 99/5 2023-04-11_1225", "productName": {"th": "ตลอดชีพพิเศษชำระเบี้ยประกันภัย 5 ปี (ชนิดมีเงินปันผล)", "en": "Whole Life Extra 99/5"}, "status": "IN_APP", "fnaId": "6434e0f4b92d29003a472c49", "applicationId": "6434e1579dd5ee6d2cdcdaad", "proposalId": "6434e1539dd5ee6d2cdcdaac", "isQuickSI": false, "sumAssured": 250050357.0, "modalPremium": 35014050.0, "paymentMode": "EVERY_YEAR", "updatedAt": "2023-04-13T09:12:24.488Z", "agentId": "100007", "leadId": "642b88dc5e39fa55463a4a6d", "sourceLeadId": "930908", "policyId": null}, {"caseId": "6437c1ea6071bd7c46ce45e6", "insured": [{"_id": "6437c1eb6071bd7c46ce45e7", "personalInfo": {"person": {"title": "T001", "firstName": "Daniel0328", "middleName": "", "lastName": "<PERSON>"}}}], "proposalName": "FWD One Link (Unit Linked) 2023-04-13_1651", "productName": {"th": "เอฟดับบลิวดีวันลิงค์ (ยูนิตลิงค์)", "en": "FWD One Link (Unit Linked)"}, "status": "IN_APP", "fnaId": "6437c1f2b72d3e79b6222081", "applicationId": "6437c29d948da93527db9611", "proposalId": "6437c299948da93527db9610", "isQuickSI": false, "sumAssured": 150000.0, "modalPremium": 100000.0, "paymentMode": "ONE_TIME", "updatedAt": "2023-04-13T09:00:11.189Z", "agentId": "100007", "leadId": "64224fd0c2c2382f7b00f2dd", "sourceLeadId": "933631", "policyId": null}]}, "messageList": null}