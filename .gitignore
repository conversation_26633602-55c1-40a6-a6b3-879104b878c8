node_modules/
.expo/
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.keystore
*.mobileprovision
*.orig.*
web-build/
.vscode/launch.json
.envrc
.envrc.*
.idea

# macOS
.DS_Store

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
.npmrc
.yarnrc.yml
credentials.json

### Node ###
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# build artifacts
*.apk
*.ipa

# Ignore React Native component files
# RootNavigator.tsx
/ios/
/android/

```

.yalc
yalc.lock