import 'expo-dev-client';
import * as WebBrowser from 'expo-web-browser';
import 'intl-pluralrules';
import Lottie<PERSON>iew from 'lottie-react-native';
import { LogBox, useWindowDimensions, View } from 'react-native';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';
import 'utils/validation/customValidation';

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
});

LogBox.ignoreAllLogs();

import styled from '@emotion/native';
import { ThemeProvider } from '@emotion/react';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { LinkingOptions, NavigationContainer } from '@react-navigation/native';
import RootNavigator from '@regionSpecificNavigator';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from 'api/RootQueryClient';
import splashSource from 'assets/lottie/splash.json';
import AppLoadingIndicator from 'components/AppLoadingIndicator';
import CrossScreenWhiteBottom from 'components/CrossScreenWhiteBottom';
import CubePortalProvider from 'components/Portal/PortalProvider';
import PromptProvider from 'components/prompt/PromptContext';
import { lightTheme } from 'cube-ui-components';
import * as Linking from 'expo-linking';
import {
  isEnrolledAsync,
  supportedAuthenticationTypesAsync,
} from 'expo-local-authentication';
import * as Notifications from 'expo-notifications';
import { StatusBar } from 'expo-status-bar';
import TabletChatBotButton from 'features/aiBot/components/Tablet/TabletChatBotButton';
import useAgentInfo from 'features/aiBot/hooks/useAgentInfo';
import useInitializeAiBot from 'features/aiBot/hooks/useInitializeAiBot';
import useBoundStore from 'hooks/useBoundStore';
import useCheckIsDeviceRooted from 'hooks/useCheckIsDeviceRooted';
import useCheckLogin from 'hooks/useCheckLogin';
import { useForceLogout } from 'hooks/useForceLogout';
import useScreenTracking from 'hooks/useScreenTracking';
import useSplashScreen from 'hooks/useSplashScreen';
import React, { useEffect, useRef } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { EventProvider } from 'react-native-outside-press';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';

const prefix = Linking.createURL('');

function App() {
  const { showAiBotButton } = useInitializeAiBot();
  const { forceLogout } = useForceLogout();
  const isLoggedIn = useCheckLogin();
  const animation = useRef<LottieView>(null);
  const { width } = useWindowDimensions();
  const animationDimension = width * 0.8;
  const saveBiometricInfo = useBoundStore(
    state => state.authActions.saveBiometricInfo,
  );

  const { checkIsLeader } = useAgentInfo();

  useCheckIsDeviceRooted({
    actionWhenIsRooted: () => {
      console.log('Device is rooted');
      if (isLoggedIn) {
        console.log('forcing logout for rooted devices');
        forceLogout();
      }
    },
  });

  useEffect(() => {
    const getBiometricInfoAsync = async () => {
      try {
        const supported = await supportedAuthenticationTypesAsync();
        const isEnrolled = await isEnrolledAsync();
        saveBiometricInfo({
          supported,
          enrolled: isEnrolled,
        });
      } catch (error) {
        console.error(
          '🚀 ~ file: App.tsx:85 ~ getBiometricInfoAsync ~ error:',
          error,
        );
      }
    };

    // const appStateListener = AppState.addEventListener(
    //   'change',
    //   async nextAppState => {
    //     if (nextAppState === 'active') {
    //       // not in background
    //       setAppIdle();
    //     } else {
    //       // if in background, show loading to mask the screen
    //       setAppLoading();
    //     }
    //   },
    // );
    getBiometricInfoAsync();
  }, [saveBiometricInfo]);

  const { appIsReady, onLayoutRootView, animationFinished } = useSplashScreen({
    animation,
  });

  useEffect(() => {
    checkIsLeader();
  }, [checkIsLeader]);

  // TODO: transient solution to disable IB mobile access in production
  if (!appIsReady) return null;

  return (
    <View style={{ flex: 1 }} onLayout={onLayoutRootView}>
      {animationFinished ? (
        <>
          <StatusBar translucent />
          <RootNavigator />
          {showAiBotButton && <TabletChatBotButton />}
        </>
      ) : (
        <SplashAnimationContainer>
          <LottieView
            autoPlay
            loop={false}
            ref={animation}
            style={{ width: animationDimension, height: animationDimension }}
            source={splashSource}
            // onAnimationFinish not handled as the lottie crashed the app, handle in useSplashScreen instead
            // onAnimationFinish={onAnimationFinish}
          />
        </SplashAnimationContainer>
      )}
    </View>
  );
}

export default function Index() {
  const isShowWhiteBottom = useBoundStore(
    state => state.home.isShowWhiteBottom,
  );
  const { navigationRef, onNavigationStateChange, onNavigationReady } =
    useScreenTracking();
  //* Deep linking: prefix + route
  // * eg my.com.fwd.cube.dev/policy?type=<case || policy>&caseId=<CaseId>&policyId=<policyNum>
  const linkingConfig: LinkingOptions<
    ReactNavigation.RootParamList | RootStackParamList
  > = {
    prefixes: [prefix],
    config: {
      screens: {
        ExistingPolicyDetail: 'policy',
        AgentProfile: 'agent-profile',
      },
    },
    async getInitialURL() {
      // First, you may want to do the default deep link handling
      // Check if app was opened from a deep link
      const url = await Linking.getInitialURL();
      if (url != null) {
        console.log(
          '🚀 ~ getInitialURL ~ Linking.parse(url):',
          Linking.parse(url),
        );

        return url;
      }

      // Handle URL from expo push notifications
      const response = await Notifications.getLastNotificationResponseAsync();

      return response?.notification.request.content.data.url;
    },
    subscribe: listener => {
      const onReceiveURL = ({ url }: { url: string }) => listener(url);

      // Listen to incoming links from deep linking
      const eventListenerSubscription = Linking.addEventListener(
        'url',
        params => {
          // console.log(`App linked with URL: ${params.url}`);
          // const { path, queryParams } = Linking.parse(params.url);
          // console.log(`Linked to app with path: ${path}, params:`, queryParams);

          // dissmiss the web browser if it was opened
          WebBrowser.dismissBrowser();

          // let React Navigation handle the URL
          onReceiveURL(params);
        },
      );

      // Listen to expo push notifications
      const subscription =
        Notifications.addNotificationResponseReceivedListener(response => {
          const url = response.notification.request.content.data.url;

          // Any custom logic to see whether the URL needs to be handled
          //...

          // Let React Navigation handle the URL
          listener(url);
        });

      return () => {
        // Clean up the event listeners
        eventListenerSubscription.remove();
        subscription.remove();
      };
    },
  };

  return (
    <KeyboardProvider>
      <SafeAreaProvider>
        <NavigationContainer
          ref={navigationRef}
          onReady={onNavigationReady}
          onStateChange={onNavigationStateChange}
          linking={linkingConfig}>
          <QueryClientProvider client={queryClient}>
            <ThemeProvider theme={lightTheme}>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <EventProvider>
                  <RootSiblingParent>
                    <BottomSheetModalProvider>
                      <CubePortalProvider>
                        <PromptProvider>
                          <App />
                          <AppLoadingIndicator />
                        </PromptProvider>
                      </CubePortalProvider>
                    </BottomSheetModalProvider>
                  </RootSiblingParent>
                </EventProvider>
              </GestureHandlerRootView>
            </ThemeProvider>
          </QueryClientProvider>
        </NavigationContainer>
        {isShowWhiteBottom && (
          <CrossScreenWhiteBottom isShowWhiteBottom={isShowWhiteBottom} />
        )}
      </SafeAreaProvider>
    </KeyboardProvider>
  );
}

const SplashAnimationContainer = styled.View(() => ({
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
}));
