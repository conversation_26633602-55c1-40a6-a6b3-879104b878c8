let config = {
  dependencies: {},
};
if (process.env.IOS_SIMULATOR === 'true') {
  // remove react-native-vision-camera-face-detector when building for ios simulator
  // since mlkit is not supporting arm64 on apple silicon
  // https://github.com/googlesamples/mlkit/issues/810
  config.dependencies['react-native-vision-camera-face-detector'] = {
    platforms: {
      ios: null,
    },
  };
}

module.exports = config;
