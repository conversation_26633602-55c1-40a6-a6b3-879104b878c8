diff --git a/node_modules/react-native-vision-camera/src/frame-processors/runAsync.ts b/node_modules/react-native-vision-camera/src/frame-processors/runAsync.ts
index e98fab5..f88f27f 100644
--- a/node_modules/react-native-vision-camera/src/frame-processors/runAsync.ts
+++ b/node_modules/react-native-vision-camera/src/frame-processors/runAsync.ts
@@ -16,7 +16,9 @@ try {
   const Worklets = WorkletsProxy.Worklets
   isAsyncContextBusy = Worklets.createSharedValue(false)
 
-  const asyncContext = Worklets.createContext('VisionCamera.async')
+  // Custom fix runAsync work in dev but not working in release
+  // https://github.com/mrousavy/react-native-vision-camera/issues/2820
+  const asyncContext = Worklets.defaultContext
   runOnAsyncContext = asyncContext.createRunAsync((frame: Frame, func: () => void) => {
     'worklet'
     try {
diff --git a/node_modules/react-native-vision-camera/src/skia/useSkiaFrameProcessor.ts b/node_modules/react-native-vision-camera/src/skia/useSkiaFrameProcessor.ts
index 62e27d3..9423343 100644
--- a/node_modules/react-native-vision-camera/src/skia/useSkiaFrameProcessor.ts
+++ b/node_modules/react-native-vision-camera/src/skia/useSkiaFrameProcessor.ts
@@ -95,8 +95,14 @@ function withRotatedFrame(frame: Frame, canvas: SkCanvas, previewOrientation: Or
         break
       case 'landscape-left':
         // rotate two flips on (0,0) origin and move X + Y into view again
-        canvas.translate(frame.height, frame.width)
-        canvas.rotate(270, 0, 0)
+        // canvas.translate(frame.height, frame.width)
+        // canvas.rotate(270, 0, 0)
+
+        // Custom fix for Android blank screen
+        // https://github.com/mrousavy/react-native-vision-camera/issues/3362#issuecomment-2624299305
+        canvas.scale(1, -1);
+        canvas.translate(frame.height, -frame.width);
+        canvas.rotate(90, 0, 0);
         break
       case 'portrait-upside-down':
         // rotate three flips on (0,0) origin and move Y into view again
