diff --git a/node_modules/react-native-incall-manager/android/src/main/java/com/zxcpoiu/incallmanager/InCallManagerModule.java b/node_modules/react-native-incall-manager/android/src/main/java/com/zxcpoiu/incallmanager/InCallManagerModule.java
index 10cb1e5..a6349e1 100644
--- a/node_modules/react-native-incall-manager/android/src/main/java/com/zxcpoiu/incallmanager/InCallManagerModule.java
+++ b/node_modules/react-native-incall-manager/android/src/main/java/com/zxcpoiu/incallmanager/InCallManagerModule.java
@@ -877,21 +877,13 @@ public class InCallManagerModule extends ReactContextBaseJavaModule implements L
      */
     @ReactMethod
     public void setForceSpeakerphoneOn(final int flag) {
-        if (flag < -1 || flag > 1) {
-            return;
-        }
         Log.d(TAG, "setForceSpeakerphoneOn() flag: " + flag);
-        forceSpeakerOn = flag;
-
-        // --- will call updateAudioDeviceState()
-        // --- Note: in some devices, it may not contains specified route thus will not be effected.
-        if (flag == 1) {
-            selectAudioDevice(AudioDevice.SPEAKER_PHONE);
-        } else if (flag == -1) {
-            selectAudioDevice(AudioDevice.EARPIECE); // --- use the most common earpiece to force `speaker off`
-        } else {
-            selectAudioDevice(AudioDevice.NONE); // --- NONE will follow default route, the default route of `video` call is speaker.
-        }
+        AudioManager audioManager = (AudioManager) getReactApplicationContext().getSystemService(Context.AUDIO_SERVICE);
+        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
+        audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL, maxVolume, AudioManager.FLAG_SHOW_UI);
+        audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
+        audioManager.setSpeakerphoneOn(true);
+        selectAudioDevice(AudioDevice.SPEAKER_PHONE);
     }
 
     // --- TODO (zxcpoiu): Implement api to let user choose audio devices
