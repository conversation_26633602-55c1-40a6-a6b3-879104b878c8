diff --git a/node_modules/react-native-svg-charts/src/chart/chart-grouped.js b/node_modules/react-native-svg-charts/src/chart/chart-grouped.js
index 247a588..b82744a 100644
--- a/node_modules/react-native-svg-charts/src/chart/chart-grouped.js
+++ b/node_modules/react-native-svg-charts/src/chart/chart-grouped.js
@@ -108,7 +108,7 @@ class ChartGrouped extends PureComponent {
                             })}
                             {paths.path.map((path, index) => {
                                 const { svg: pathSvg } = data[index]
-                                const key = path + '-' + index
+                                const key = index
                                 return (
                                     <Path
                                         key={key}
