diff --git a/node_modules/react-native-pdf/ios/RNPDFPdf/RNPDFPdfView.mm b/node_modules/react-native-pdf/ios/RNPDFPdf/RNPDFPdfView.mm
index cddec76..15c9b05 100644
--- a/node_modules/react-native-pdf/ios/RNPDFPdf/RNPDFPdfView.mm
+++ b/node_modules/react-native-pdf/ios/RNPDFPdf/RNPDFPdfView.mm
@@ -292,7 +292,10 @@ using namespace facebook::react;
     // Disable built-in double tap, so as not to conflict with custom recognizers.
     for (UIGestureRecognizer *recognizer in _pdfView.gestureRecognizers) {
         if ([recognizer isKindOfClass:[UITapGestureRecognizer class]]) {
-            recognizer.enabled = NO;
+            UITapGestureRecognizer *tap = (UITapGestureRecognizer *)recognizer;
+            if (tap.numberOfTapsRequired == 2) {
+                recognizer.enabled = NO;
+            }
         }
     }
 
