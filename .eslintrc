{"root": true, "env": {"browser": true, "node": true}, "parser": "@typescript-eslint/parser", "plugins": ["react", "react-native", "@typescript-eslint", "prettier", "react-hooks", "@tanstack/query"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"react-native/no-color-literals": 1, "react-native/no-unused-styles": 1, "react-native/no-raw-text": [1, {"skip": ["Animated.Text", "SvgText", "Typography.ExtraLargeBody", "Typography.LargeBody", "Typography.Body", "Typography.SmallBody", "Typography.LargeLabel", "Typography.Label", "Typography.SmallLabel", "Typography.ExtraSmallLabel", "Typography.H1", "Typography.H2", "Typography.H3", "Typography.H4", "Typography.H5", "Typography.H6", "Typography.H7", "Typography.H8", "H5", "ResponsiveText", "RecruitmentProgressCard.Title", "RecruitmentProgressCard.Date", "RecruitmentProgressCard.Frame", "RecruitmentProgressCard.HeaderNote"]}], "react/jsx-props-no-spreading": "off", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": ["warn", {"additionalHooks": "(useAnimatedStyle|useDerivedValue|useAnimatedProps)"}], "@tanstack/query/exhaustive-deps": "warn", "@tanstack/query/prefer-query-object-syntax": "warn", "@typescript-eslint/no-non-null-assertion": "error"}}