const { getDefaultConfig } = require('expo/metro-config');
const jsoMetroPlugin = require('obfuscator-io-metro-plugin')({
  // for these option look javascript-obfuscator library options from  above url
  compact: true,
  sourceMap: false,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 1,
  numbersToExpressions: true,
  simplify: true,
  stringArrayShuffle: true,
  splitStrings: true,
  stringArrayThreshold: 1,
});
const config = getDefaultConfig(__dirname);

config.server.rewriteRequestUrl = url => {
  if (!url.endsWith('.bundle')) {
    return url;
  }
  return (
    url + '?platform=ios&dev=true&minify=false&modulesOnly=false&runModule=true'
  );
};

module.exports = { ...config, ...jsoMetroPlugin };
