import { useTheme } from '@emotion/react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import { View } from 'react-native';

// * Types
// import { RootStackParamList, RootStackParamListMap } from 'types/navigation';
import { RootStackParamListMap } from 'types/navigation';
import { TeamPerformanceViewType } from 'types/team';

//* Screens
import PaymentGateway from 'features/eApp/components/phone/payment/PaymentGateway';
import PaymentProcessing from 'features/eApp/components/phone/payment/PaymentProcessing';
import PaymentSuccessfulResult from 'features/eApp/components/phone/payment/PaymentSuccessfulResult';
import DataPrivacyReview from 'features/eApp/components/phone/review/dataPrivacy/DataPrivacyReview';
import ImageListView from 'features/eApp/components/phone/review/imageList/ImageListView';
import PolicyReplacementReview from 'features/eApp/components/phone/review/policyReplacement/PolicyReplacementReview';
import RiskProfileReview from 'features/eApp/components/phone/review/riskProfile/RiskProfileReview';
import AgentSignature from 'features/eApp/components/phone/signature/agent/AgentSignature';
import InsuredSignature from 'features/eApp/components/phone/signature/insured/InsuredSignature';
import PlaceOfSigning from 'features/eApp/components/phone/signature/placeOfSigning/PlaceOfSigning';
import PolicyOwnerSignature from 'features/eApp/components/phone/signature/policyOwner/PolicyOwnerSignature';
import RemoteAgentSignature from 'features/eApp/components/phone/signature/remoteAgent/RemoteAgentSignature';
import RemoteInsuredSignature from 'features/eApp/components/phone/signature/remoteInsured/RemoteInsuredSignature';
import RemotePolicyOwnerSignature from 'features/eApp/components/phone/signature/remotePolicyOwner/RemotePolicyOwnerSignature';
import PolicyDetailScreen from 'features/policy/components/PolicyDetails';
import FundIllustrationForm from 'features/proposal/components/FundIllustrationForm/FundIllustrationForm';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import ACRScreen from 'screens/ACRScreen/ACRScreen';
import AffiliateScreen from 'screens/AffiliateScreen';
import AgentPerformanceDetailsScreen from 'screens/AgentPerformanceDetailsScreen';
import AgentProfileScreen from 'screens/AgentProfileScreen/AgentProfileScreen';
import PersonalDetailsScreen from 'screens/AgentProfileScreen/PersonalDetailsScreen';
import SettingScreen from 'screens/AgentProfileScreen/SettingScreen';
import CoverageDetailsScreen from 'screens/CoverageDetailsScreen';
import DocumentsScreen from 'screens/DocumentsScreen/DocumentsScreen';
import EAppScreen from 'screens/EAppScreen';
import LoginScreen from 'screens/LoginScreen';
import PdfViewerScreen from 'screens/PdfViewerScreen';
import PoliciesScreen from 'screens/PoliciesScreen/PoliciesScreen';
import PoliciesSelectApproveScreen from 'screens/PoliciesScreen/PoliciesSelectApproveScreen';
import ReviewApplicationScreen from 'screens/PoliciesScreen/ReviewApplicationScreen';
import ProductRecommendationScreen from 'screens/ProductRecommendationScreen';
import ProductSelection from 'screens/ProductSelection';
import ProposalTableScreen from 'screens/ProposalTableScreen';
import ReportGenerationListScreen from 'screens/ReportGenerationScreen/ReportGenerationListScreen';
import RPQQuestionFormScreen from 'screens/RpqQuestionFormScreen';
import RpqResultScreen from 'screens/RpqResultScreen';
import SalesIllustrationForm from 'screens/SalesIllustrationForm';
import TeamOperationScreen from 'screens/TeamOperationScreen';
import TeamPerformanceDetailsScreen from 'screens/TeamPerformanceDetailsScreen';
import TeamPerformanceListScreen from 'screens/TeamPerformanceList';
import TeamTargetScreen from 'screens/TeamTargetScreen';

//* ScreenLots
import FnaScreen from 'screens/FnaScreen';
import SellerExpScreens from 'screens/SellerExperienceScreens';
import { SimulationTableScreen } from 'screens/SimulationTableScreen';
//* TBC it is phone/ph only screen */
import TeamMemberTargetsEditScreen from 'features/teamManagement/ph/phone/teamActivities/teamTarget/teamMemberTargetsEdit/TeamMemberTargetsEdit';

//* Navigator
import ProtectionGoal from 'features/fna/components/goals/protection/ProtectionGoal';
import SavingsGoal from 'features/fna/components/goals/savings/SavingsGoal';
import useCheckIsLoggedIn from 'hooks/useCheckIsLoggedIn';
import MainNavigator from 'navigation/MainNavigator/MainNavigator';
import BirthdayCardScreen from 'screens/BirthdayCardScreen';
import CustomerFactFindScreen from 'screens/CustomerFactFindScreen';
import NotificationScreen from 'screens/NotificationScreen';
import { BirthdayTasksScreen } from 'screens/TasksScreen';

// Ai Bot
import HealthQuestionsReview from 'features/eAppV2/common/components/review/healthQuestions/HealthQuestionsReview';
import PersonalInformationReview from 'features/eAppV2/common/components/review/personalInformationReview/PersonalInformationReview';
import ChargePremiumAuthorizationReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/ChargePremiumAuthorizationReview';
import DisclosureReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/DisclosureReview';
import EPolicyAndENoticesReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/EPolicyAndENoticesReview';
import FatcaReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/FatcaReview';
import InsuranceCoverageReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/InsuranceCoverageReview';
import PdpReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/PdpReview';
import RopReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/RopReview';
import TakeOverReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/TakeOverReview';
import Submission from 'features/eAppV2/ib/components/submission/Submission';
import AddCandidateScreen from 'features/eRecruit/ib/phone/components/AddCandidate/AddCandidateScreen';
import CandidatesSearchScreen from 'features/eRecruit/ib/phone/components/CandidateSearch/CandidatesSearchScreen';
import OverallFeedback from 'features/ecoach/screens/OverallFeedback';
import SelectDifficultyPage from 'features/ecoach/screens/SelectDifficulty';
import SessionHistory from 'features/ecoach/screens/SessionHistory';
import SplashPage from 'features/ecoach/screens/Splash';
import UserProfilePage from 'features/ecoach/screens/UserProfile';
import WatchVideoPage from 'features/ecoach/screens/WatchVideoPage';
import VideoCallPage from 'features/ecoach/screens/call/VideoCallPage';
import DetailSummaryPage from 'features/ecoach/screens/detailSummary/DetailSummary';
import DetailSummaryTabletPage from 'features/ecoach/screens/detailSummary/DetailSummary.tablet';
import ECoachHomePage from 'features/ecoach/screens/home/<USER>';
import SelectPolicyPage from 'features/ecoach/screens/selectPolicy/SelectPolicy';
import SummaryPage from 'features/ecoach/screens/summary/Summary';
import SummaryTabletPage from 'features/ecoach/screens/summary/Summary.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import AiBotHistory from 'screens/AiBotScreen/AiBotHistoryScreen';
import TeamIndividualScreen from 'screens/TeamIndividualScreen';
import { countryModuleSellerConfig } from 'utils/config/module';

import GuideLinesPage from 'features/ecoach/screens/guideLines/GuideLinesPage';
import AppointmentSummary from 'features/ecoach/screens/summary/AppointmentSummary';
import AppointmentSummaryTablet from 'features/ecoach/screens/summary/AppointmentSummary.tablet';
import ProductKnowledgeSummary from 'features/ecoach/screens/summary/ProductKnowledgeSummary';
import ProductKnowledgeSummaryTablet from 'features/ecoach/screens/summary/ProductKnowledgeSummary.tablet';
import useIgniteStackScreens from 'features/socialMarketing/hooks/useIgniteStackScreens';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import AiBotScreen from 'screens/AiBotScreen';
import AiBotFeedbackScreen from 'screens/AiBotScreen/AiBotFeedbackScreen';
import AiBotPromptLibraryScreen from 'screens/AiBotScreen/AiBotPromptLibraryScreen';
import AiBotTableScreen from 'screens/AiBotScreen/AiBotTableScreen';
import TeamIndividualPersistencyScreen from 'screens/TeamIndividualPersistencyScreen';
import InsurableInterestReview from 'features/eAppV2/ib/components/reviewSummary/sections/consentAndDeclarationSummary/InsurableInterestReview';

const Stack = createNativeStackNavigator<RootStackParamListMap['ib']>();
// const Stack = createNativeStackNavigator<RootStackParamList>();

export default function IbStackNavigator() {
  const { isLoggedIn } = useCheckIsLoggedIn();
  const { typography } = useTheme();
  const hasPermission = useHasPermission();

  const { isTabletMode } = useLayoutAdoptionCheck();
  const igniteStackScreens = useIgniteStackScreens<'ib'>(Stack);

  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  return (
    <View style={{ flex: 1 }}>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
          headerTitleAlign: 'center',
        }}>
        {isLoggedIn ? (
          // * Authenticated Navigator
          <>
            <Stack.Screen name="Main">{() => <MainNavigator />}</Stack.Screen>
            <Stack.Screen
              name="NotificationScreen"
              component={NotificationScreen}
              options={{
                headerShown: false,
              }}
            />
            {/* FWD News */}

            <Stack.Group>
              <Stack.Screen
                name="FWDNews"
                component={SellerExpScreens.FWDNewsScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
              <Stack.Screen
                name="FWDNewsDetails"
                component={SellerExpScreens.FWDNewsDetailsScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
              <Stack.Screen
                name="FWDNewsBookmarks"
                component={SellerExpScreens.FWDNewsBookmarkScreen}
                options={{
                  headerShown: false,
                  animation: 'fade',
                }}
              />
            </Stack.Group>

            {/* // Task Screens */}
            <Stack.Group>
              <Stack.Screen
                name="BirthdayTasksScreen"
                component={BirthdayTasksScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Birthday Card Screens */}
            <Stack.Group>
              <Stack.Screen
                name="BirthdayCardScreen"
                component={BirthdayCardScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Lead Screens */}
            <Stack.Group>
              <Stack.Screen
                name="LeadProfile"
                component={SellerExpScreens.LeadProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ProfileDetails"
                component={SellerExpScreens.LeadProfileDetailsScreen}
                options={{
                  animation: 'fade',
                }}
              />
              <Stack.Screen
                name="CustomerProfileDetails"
                component={SellerExpScreens.CustomerProfileDetailsScreen}
              />
              <Stack.Screen
                name="AddNewLeadOrEntity"
                component={SellerExpScreens.AddNewLeadOrEntityScreen}
                options={{
                  animation: 'fade',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ContactBook"
                component={SellerExpScreens.ContactBookScreen}
                options={{
                  animation: 'fade',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="LeadAndCustomerSearch"
                component={SellerExpScreens.LeadAndCustomerSearch}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="LogActivity"
                component={SellerExpScreens.LogActivityScreen}
                options={{
                  animation: 'fade',
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="ExistingPolicyDetail"
                component={PolicyDetailScreen}
              />
            </Stack.Group>

            {/* // Performance Screens */}
            <Stack.Group>
              <Stack.Screen
                name="RecognitionDetails"
                component={SellerExpScreens.RecognitionDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="PerformanceDetails"
                component={SellerExpScreens.PerformanceDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="PerformanceTarget"
                component={SellerExpScreens.PerformanceTargetScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="CampaignsDetails"
                component={SellerExpScreens.CampaignsDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="SummitClubsDetails"
                component={SellerExpScreens.SummitClubsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyDetails"
                component={SellerExpScreens.EliteAgencyDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyRequirements"
                component={SellerExpScreens.EliteAgencyRequirementsScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="EliteAgencyBenefits"
                component={SellerExpScreens.EliteAgencyBenefitsScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // FNA Screens */}

            {/* // SI Screens */}
            <Stack.Group>
              <Stack.Screen
                name="SalesIllustrationForm"
                component={SalesIllustrationForm}
              />
              <Stack.Screen
                name="RPQQuestionForm"
                component={RPQQuestionFormScreen}
              />
              <Stack.Screen name="RPQResult" component={RpqResultScreen} />
              <Stack.Screen
                name="ProductSelection"
                component={ProductSelection}
              />
              <Stack.Screen
                name="CoverageDetailsScreen"
                component={CoverageDetailsScreen}
                options={{
                  animation: 'fade',
                }}
              />
            </Stack.Group>

            {/* // Agent Profile Screens */}
            <Stack.Group>
              <Stack.Screen
                name="AgentProfile"
                component={AgentProfileScreen}
              />
              <Stack.Screen
                name="PersonalDetails"
                component={PersonalDetailsScreen}
              />
              <Stack.Screen name="Setting" component={SettingScreen} />
            </Stack.Group>

            {/* // Uncategoried */}
            <Stack.Group>
              <Stack.Screen
                name="BadgesCollection"
                component={SellerExpScreens.BadgesCollectionScreen}
              />
            </Stack.Group>

            {/* // Ai Bot Screens */}
            <Stack.Group>
              <Stack.Screen
                name="AiBotChat"
                component={AiBotScreen}
                options={{
                  headerShown: false,
                  animation: 'slide_from_bottom',
                }}
              />
              <Stack.Screen
                name="AiBotHistory"
                component={AiBotHistory}
                options={{
                  headerShown: false,
                  // animation: 'slide_from_bottom',
                }}
              />
              <Stack.Screen
                name="AiBotPromptLibrary"
                component={AiBotPromptLibraryScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="AiBotFeedback"
                component={AiBotFeedbackScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="AiBotTable"
                component={AiBotTableScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Ecoach Screens */}
            <Stack.Group>
              <Stack.Screen
                name="EcoachHome"
                component={ECoachHomePage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="Splash"
                component={SplashPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="UserProfile"
                component={UserProfilePage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="SelectPolicy"
                component={SelectPolicyPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="SelectDifficulty"
                component={SelectDifficultyPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="GuideLinesPage"
                component={GuideLinesPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="VideoCallPage"
                component={VideoCallPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="Summary"
                component={SummaryPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="SummaryTablet"
                component={SummaryTabletPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="AppointmentSummary"
                component={AppointmentSummary}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="AppointmentSummaryTablet"
                component={AppointmentSummaryTablet}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="ProductKnowledgeSummaryTablet"
                component={ProductKnowledgeSummaryTablet}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="ProductKnowledgeSummary"
                component={ProductKnowledgeSummary}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="DetailSummary"
                component={DetailSummaryPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="DetailSummaryTablet"
                component={DetailSummaryTabletPage}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="SessionHistory"
                component={SessionHistory}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="OverallFeedback"
                component={OverallFeedback}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                name="WatchVideoPage"
                component={WatchVideoPage}
                options={{
                  headerShown: true,
                  title: '',
                  headerBackTitle: 'Home',
                }}
              />
            </Stack.Group>

            {/* // Policies */}
            <Stack.Group>
              <Stack.Screen
                name="PoliciesNewBusiness"
                component={SellerExpScreens.PoliciesNewBusinessNavigator}
              />
            </Stack.Group>

            {/* // Pos */}
            <Stack.Group>
              <Stack.Screen
                name="PoliciesPOS"
                component={SellerExpScreens.POSNavigator}
              />
            </Stack.Group>

            {/* // Proposal Screens */}
            <Stack.Group>
              <Stack.Screen
                name="ProposalTable"
                component={ProposalTableScreen}
              />
            </Stack.Group>
            <Stack.Screen name="EApp" component={EAppScreen} />
            <Stack.Screen name="ACR" component={ACRScreen} />
            <Stack.Screen
              name="SimulationTable"
              component={SimulationTableScreen}
              options={{
                animation: 'fade',
                orientation: isTabletMode ? 'landscape' : 'landscape_left',
              }}
            />

            <Stack.Screen
              name="FundIllustrationForm"
              component={FundIllustrationForm}
              options={{
                animation: 'fade',
                orientation: isTabletMode ? 'landscape' : 'landscape_left',
              }}
            />

            {/* // App Review Screens */}
            <Stack.Group screenOptions={{ headerShown: false }}>
              <Stack.Screen
                name="PersonalInformationReview"
                component={PersonalInformationReview}
              />
              <Stack.Screen
                name="PolicyReplacementReview"
                component={PolicyReplacementReview}
              />
              <Stack.Screen
                name="DataPrivacyReview"
                component={DataPrivacyReview}
              />
              <Stack.Screen
                name="RiskProfileReview"
                component={RiskProfileReview}
              />
              <Stack.Screen
                name="HealthQuestionsReview"
                component={HealthQuestionsReview}
              />
              <Stack.Screen name="FatcaReview" component={FatcaReview} />
              <Stack.Screen name="RopReview" component={RopReview} />
              <Stack.Screen
                name="InsurableInterestReview"
                component={InsurableInterestReview}
              />
              <Stack.Screen name="PdpReview" component={PdpReview} />
              <Stack.Screen name="TakeOverReview" component={TakeOverReview} />
              <Stack.Screen
                name="DisclosureReview"
                component={DisclosureReview}
              />
              <Stack.Screen
                name="InsuranceCoverageReview"
                component={InsuranceCoverageReview}
              />
              <Stack.Screen
                name="ChargePremiumAuthorizationReview"
                component={ChargePremiumAuthorizationReview}
              />
              <Stack.Screen
                name="EPolicyAndENoticesReview"
                component={EPolicyAndENoticesReview}
              />
            </Stack.Group>

            {/* // Signature Screens */}
            <Stack.Group
              screenOptions={{
                headerShown: false,
                animation: 'slide_from_bottom',
                navigationBarHidden: true,
              }}>
              <Stack.Screen
                name="PolicyOwnerSignature"
                component={PolicyOwnerSignature}
                options={{
                  animation: 'slide_from_right',
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="PolicyOwnerSignatureBottom"
                component={PolicyOwnerSignature}
                options={{ gestureEnabled: false }}
              />
              <Stack.Screen
                name="PolicyOwnerSignatureFade"
                component={PolicyOwnerSignature}
                options={{ animation: 'fade', gestureEnabled: false }}
              />
              <Stack.Screen
                name="InsuredSignature"
                component={InsuredSignature}
              />
              <Stack.Screen name="AgentSignature" component={AgentSignature} />
              <Stack.Screen name="PlaceOfSigning" component={PlaceOfSigning} />
              <Stack.Screen
                name="RemotePolicyOwnerSignature"
                component={RemotePolicyOwnerSignature}
                options={{
                  animation: 'slide_from_right',
                  gestureEnabled: false,
                }}
              />
              <Stack.Screen
                name="RemotePolicyOwnerSignatureBottom"
                component={RemotePolicyOwnerSignature}
                options={{ gestureEnabled: false }}
              />
              <Stack.Screen
                name="RemotePolicyOwnerSignatureFade"
                component={RemotePolicyOwnerSignature}
                options={{ animation: 'fade', gestureEnabled: false }}
              />
              <Stack.Screen
                name="RemoteInsuredSignature"
                component={RemoteInsuredSignature}
              />
              <Stack.Screen
                name="RemoteAgentSignature"
                component={RemoteAgentSignature}
              />
            </Stack.Group>

            {/* // Pdf Viewer Screen */}
            <Stack.Group>
              <Stack.Screen
                name="PdfViewer"
                component={PdfViewerScreen}
                options={{ animation: 'slide_from_bottom' }}
              />
              <Stack.Screen name="ImageList" component={ImageListView} />
            </Stack.Group>

            {/* // List Performance Screens */}
            <Stack.Group
              screenOptions={{
                headerShown: true,
                headerLeft: () => <CustomHeaderBackButton />,
              }}>
              <Stack.Screen
                name="TeamListPerformance"
                component={TeamPerformanceListScreen}
                options={({ route }) => ({
                  headerTitle:
                    route.params?.viewType ===
                    TeamPerformanceViewType.Individual
                      ? 'View by individual'
                      : 'View by team',
                  headerTitleStyle: {
                    fontFamily: 'FWDCircularTT-Bold',
                    fontSize: typography.h7.size,
                  },
                })}
              />
              <Stack.Screen
                options={{ headerShown: false }}
                name="TeamPerformanceDetails"
                component={TeamPerformanceListScreen}
              />
            </Stack.Group>

            {/* // Team Operation Data Screens */}
            <Stack.Group>
              <Stack.Screen
                name="TeamOperation"
                component={TeamOperationScreen}
              />
              <Stack.Screen name="AgentPolicies" component={PoliciesScreen} />
              <Stack.Screen
                name="AgentPoliciesReview"
                component={ReviewApplicationScreen}
              />
              <Stack.Screen
                name="PoliciesSelectApprove"
                component={PoliciesSelectApproveScreen}
              />
            </Stack.Group>

            {/* // Team Leads Conversion Screens */}
            <Stack.Group>
              <Stack.Screen
                name="TeamLeadsConversion"
                component={SellerExpScreens.LeadsConversionScreen}
              />
            </Stack.Group>

            {/* // Team Target Screens */}
            <Stack.Group>
              <Stack.Screen name="TeamTarget" component={TeamTargetScreen} />
              <Stack.Screen
                name="TeamMemberTargetsEdit"
                component={TeamMemberTargetsEditScreen}
              />
            </Stack.Group>
            <Stack.Group>
              <Stack.Screen
                name="TeamIndividualProfile"
                component={TeamIndividualScreen}
              />
              <Stack.Screen
                name="TeamIndividualPersistency"
                component={TeamIndividualPersistencyScreen}
              />
            </Stack.Group>
            {/* // Group payment */}
            <Stack.Group>
              <Stack.Screen
                name="PaymentResult"
                component={PaymentSuccessfulResult}
              />
              <Stack.Screen
                name="PaymentProcessing"
                component={PaymentProcessing}
              />
              <Stack.Screen
                name="PaymentGateway"
                component={PaymentGateway}
                options={{
                  headerShown: false,
                  title: 'Payment',
                }}
              />
              <Stack.Screen name="Submission" component={Submission} />
            </Stack.Group>

            {/* // Team Performance Details Screens */}
            <Stack.Group>
              <Stack.Screen
                name="TeamView"
                component={TeamPerformanceDetailsScreen}
              />
            </Stack.Group>

            {/* // Agent Performance Screens */}
            <Stack.Group>
              <Stack.Screen
                name="AgentPerformance"
                component={AgentPerformanceDetailsScreen}
              />
            </Stack.Group>

            {/* // FNA */}
            <Stack.Group>
              <Stack.Screen
                name="Fna"
                component={FnaScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="SavingsGoal"
                component={SavingsGoal}
                options={{
                  headerShown: false,
                  animation: 'slide_from_right',
                }}
              />
              <Stack.Screen
                name="ProtectionGoal"
                component={ProtectionGoal}
                options={{
                  headerShown: false,
                  animation: 'slide_from_right',
                }}
              />
              <Stack.Screen
                name="ProductRecommendation"
                component={ProductRecommendationScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Report Screens */}
            <Stack.Group>
              <Stack.Screen
                name="ReportGenerationListScreen"
                component={ReportGenerationListScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="CustomerFactFind"
                component={CustomerFactFindScreen}
              />
            </Stack.Group>

            {/* // * ERecruit */}
            {countryModuleSellerConfig.ERecruit && (
              <>
                <Stack.Screen
                  name="ERecruitApplication"
                  component={SellerExpScreens.ERecruitAppScreen}
                />
                <Stack.Screen
                  name="ERecruitApplicationStatus"
                  component={SellerExpScreens.ERecruitAppStatusScreen}
                />
                <Stack.Screen
                  name="ERecruitCandidateProfile"
                  component={SellerExpScreens.ERecruitCandidateProfileScreen}
                />
                <Stack.Screen
                  name="ERecruitCheckApplication"
                  component={SellerExpScreens.ERecruitCheckAppScreen}
                />
                <Stack.Screen
                  name="CandidatesSearch"
                  component={CandidatesSearchScreen}
                />
                <Stack.Screen
                  name="AddCandidate"
                  component={AddCandidateScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ERecruitReviewAgentsSubmission"
                  component={
                    SellerExpScreens.ERecruitReviewAgentsSubmissionScreen
                  }
                />
                <Stack.Screen
                  name="ERecruitReviewAgentsApplication"
                  component={
                    SellerExpScreens.ERecruitReviewAgentsApplicationScreen
                  }
                />
                <Stack.Screen
                  name="ERecruitSignature"
                  component={SellerExpScreens.ERecruitSignatureScreen}
                />
                <Stack.Screen
                  name="ERecruitRemoteSignature"
                  component={SellerExpScreens.ERecruitRemoteSignatureScreen}
                />

                <Stack.Screen
                  name="ERecruitCheckApplicationRemoteSignature"
                  component={
                    SellerExpScreens.ERecruitCheckApplicationRemoteSignatureScreen
                  }
                />
                <Stack.Screen
                  name="SellerExpImageList"
                  component={SellerExpScreens.ImageListScreen}
                />
              </>
            )}
            {/* // Affiliate Screens */}
            <Stack.Group>
              <Stack.Screen
                name="AffiliateScreen"
                component={AffiliateScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="AffiliateProfile"
                component={SellerExpScreens.AffiliateProfileScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Screen
                name="AffiliatePostDetails"
                component={SellerExpScreens.AffiliatePostDetailsScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* // Documents */}
            <Stack.Group>
              <Stack.Screen
                name="DocumentsScreen"
                component={DocumentsScreen}
                options={{
                  headerShown: false,
                }}
              />
            </Stack.Group>

            {/* Ignite feature screen group */}
            {igniteStackScreens}

            {otherMenuTabsMobile
              .filter(menuItem => menuItem.navigationType === 'stack')
              .map(({ name, component }) => (
                <Stack.Screen
                  key={'otherMenuTabsMobile_' + name}
                  name={name}
                  component={component}
                />
              ))}
          </>
        ) : (
          // Unauthenticated Screens
          <Stack.Group>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{
                animationTypeForReplace: !isLoggedIn ? 'pop' : 'push',
              }}
            />
          </Stack.Group>
        )}
        {/* Common modal screens */}
      </Stack.Navigator>
    </View>
  );
}
