import { SvgIconProps } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function PhoneWithChatBubbleSVG(props: SvgIconProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  if (!isTabletMode) {
    return (
      <Svg width={44} height={44} fill="none" {...props}>
        <Path
          fill="#fff"
          d="M30.407 34.833c0 2.179-1.777 3.943-3.97 3.943h-13.07c-2.193 0-3.97-1.764-3.97-3.943V9.17c0-2.177 1.777-3.943 3.97-3.943h13.07c2.193 0 3.97 1.764 3.97 3.943v25.663Z"
        />
        <Path
          fill="#E87722"
          d="M26.543 32.337H13.259a1.83 1.83 0 0 1-1.83-1.829V10.786c0-1.01.82-1.829 1.83-1.829h13.284c1.01 0 1.83.82 1.83 1.83v19.721c0 1.01-.82 1.83-1.83 1.83Z"
        />
        <Path
          fill="#fff"
          d="M31.275 10.004H18.953a2.126 2.126 0 0 0-2.126 2.126v11.202c0 1.174.952 2.126 2.126 2.126h.935l-.757 2.603-.047.164c-.145.495.65.636 1.01.27l4.333-3.037h6.846a2.126 2.126 0 0 0 2.126-2.126V12.13a2.124 2.124 0 0 0-2.124-2.126Z"
        />
        <Path
          fill="#E87722"
          d="M21.595 35.723a1.02 1.02 0 0 1-1.017 1.017h-1.355a1.02 1.02 0 0 1-1.017-1.017v-.34a1.02 1.02 0 0 1 1.017-1.016h1.355a1.02 1.02 0 0 1 1.017 1.017v.339ZM19.377 22.76h.725a.287.287 0 0 0 0-.573h-.725a.287.287 0 0 0 0 .573ZM19.377 14.222h6.735a.287.287 0 0 0 0-.574h-6.735a.287.287 0 0 0 0 .574ZM19.377 12.104h6.735a.287.287 0 0 0 0-.573h-6.735a.287.287 0 0 0 0 .573ZM19.377 20.624h3.68a.287.287 0 0 0 0-.573h-3.68a.287.287 0 0 0 0 .573ZM19.377 16.354h6.684a.287.287 0 0 0 0-.573h-6.684a.287.287 0 0 0 0 .573ZM19.377 18.491h6.71a.287.287 0 0 0 0-.573h-6.71a.287.287 0 0 0 0 .573Z"
        />
      </Svg>
    );
  }
  return (
    <Svg width={22} height={28} viewBox="0 0 22 28" fill="none" {...props}>
      <Path
        d="M18.519 24.71c0 1.817-1.483 3.29-3.313 3.29H4.297c-1.83 0-3.313-1.473-3.313-3.29V3.29C.984 1.475 2.467 0 4.297 0h10.909c1.83 0 3.313 1.473 3.313 3.29v21.42z"
        fill="#E87722"
      />
      <Path
        d="M15.293 22.626H4.206a1.527 1.527 0 01-1.526-1.527V4.639c0-.842.683-1.526 1.526-1.526h11.087c.843 0 1.527.684 1.527 1.526V21.1c0 .843-.684 1.527-1.527 1.527z"
        fill="#fff"
      />
      <Path
        d="M19.241 3.988H8.958c-.98 0-1.774.794-1.774 1.774v9.349c0 .98.794 1.774 1.774 1.774h.78l-.631 2.173-.04.137c-.12.412.543.53.843.224l3.616-2.534h5.714c.98 0 1.775-.794 1.775-1.774V5.762a1.773 1.773 0 00-1.774-1.774z"
        fill="#E87722"
      />
      <Path
        d="M11.165 25.454a.851.851 0 01-.85.849h-1.13a.851.851 0 01-.85-.849v-.283c0-.466.383-.848.85-.848h1.13c.468 0 .85.382.85.848v.283zM9.313 14.634h.605a.24.24 0 000-.478h-.605a.24.24 0 000 .478zM9.313 7.507h5.621a.24.24 0 000-.478h-5.62a.24.24 0 000 .478zM9.313 5.741h5.621a.24.24 0 000-.478h-5.62a.24.24 0 000 .478zM9.313 12.852h3.07a.24.24 0 000-.478h-3.07a.24.24 0 000 .478zM9.313 9.289h5.579a.24.24 0 000-.478H9.313a.24.24 0 000 .478zM9.313 11.07h5.6a.24.24 0 000-.478h-5.6a.24.24 0 000 .478z"
        fill="#fff"
      />
    </Svg>
  );
}
