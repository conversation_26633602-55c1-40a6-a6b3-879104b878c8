import { SvgIconProps } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function SalesConnectSVG(props: SvgIconProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  if (!isTabletMode) {
    return (
      <Svg width={44} height={44} fill="none" {...props}>
        <Path
          fill="#fff"
          d="m25.326 23.13.26-.464c.923-1.06 2.273-.47 2.684.723.545-1.494 2.6-1.186 2.907.297.097.018.056-.015.078-.052.675-1.272 2.296-1.083 2.819.215.667 2.14 2.24 4.535 1.872 6.85-.103.667-.485 1.112-.723 1.72l.727 2.036c.067.522-.208.841-.653 1.045-1.716.783-3.682 1.306-5.425 2.058-1.602.32-1.661-1.535-2.473-2.403a33.928 33.928 0 0 1-5.93-4.038c-.953-.8-3.081-2.373-1.594-3.667.997-.872 2.22.026 3.137.567.419.249.827.52 1.235.786-.074-.582-.286-1.134-.482-1.687-.894-2.521-1.973-4.98-2.874-7.502-.42-1.724 1.913-2.562 2.703-1 .438.863.838 2.128 1.194 3.066.186.482.367.964.53 1.454h.008v-.004Z"
        />
        <Path
          fill="#fff"
          d="M13.907 16.1c.32-.041.638.037.783.348.222.494-.334.89.174 1.31 2.114.93 4.135 2.261 5.67 4 .26.293.516.55.349.98a19.709 19.709 0 0 0-1.498 2.391c-.442.835-.36 1.228-1.11 1.109-.314-.048-.696-.723-.912-.979-.96-1.138-2.143-2.114-3.482-2.781-.337-.17-.941-.482-1.309-.415-.597.11-.508 1.309-1.201 1.427-.352.06-.564-.118-.734-.397-.931-1.55-1.647-3.315-2.57-4.876-.17-.337 0-.76.345-.905l5.495-1.212Z"
        />
        <Path
          fill="#F3BB90"
          d="M28.8 15.58c.115-.023.226.03.334.059 1.962.508 3.934 1.309 5.896 1.847.234.122.337.426.182.656l-2.44 3.786c-.241.018-.423.018-.657.082-.233.063-.686.344-.767.348-.13 0-.234-.182-.334-.263-.694-.541-1.454-.694-2.266-.304-.107.052-.315.226-.404.23-.09.003-.423-.33-.553-.408-.523-.33-1.082-.415-1.68-.23-.226.07-.385.207-.582.308-.196.1-.03.063-.074 0l-.567-1.473c-.015-.051 0-.1.022-.148.033-.074.538-.46.645-.541.582-.456 1.254-.89 1.899-1.25.363-.204.797-.367 1.138-.567.672-.386-.46-1.406-.096-1.91.056-.074.215-.196.308-.215v-.007H28.8Z"
        />
        <Path
          fill="#F1AD7A"
          d="m19.896 17.846.018-3.727v-1.316c0-.49-.507-.81-.76-.909h-1.038a.304.304 0 0 1-.26-.167c-.06-.103 0-.228.037-.278L21.49 6.74c.1-.111.382-.334.724-.334.34 0 .611.21.704.315l3.634 4.728c.025.044.063.16.019.278-.045.12-.13.161-.167.167h-.909c-.697 0-.933.557-.964.835v5.228s-.13.204-.278.278c-.074-.204-.538-1.427-1.873-1.427s-1.89 1.13-1.965 1.724c-.13-.093-.52-.686-.52-.686Z"
        />
      </Svg>
    );
  }
  return (
    <Svg width={28} height={32} viewBox="0 0 28 32" fill="none" {...props}>
      <Path
        d="M17.327 17.13l.26-.464c.923-1.06 2.273-.47 2.684.723.545-1.494 2.6-1.186 2.907.297.097.018.056-.015.078-.052.675-1.272 2.296-1.083 2.819.215.667 2.14 2.24 4.535 1.872 6.849-.104.667-.485 1.112-.723 1.72l.727 2.036c.067.523-.208.842-.653 1.046-1.716.782-3.682 1.305-5.425 2.058-1.602.319-1.661-1.535-2.473-2.403a33.93 33.93 0 01-5.93-4.038c-.953-.801-3.081-2.373-1.594-3.668.997-.87 2.22.026 3.137.568.419.248.827.519 1.235.786-.075-.582-.286-1.135-.482-1.687-.894-2.522-1.973-4.98-2.874-7.502-.42-1.724 1.913-2.562 2.703-1.001.438.864.838 2.129 1.194 3.067.185.482.367.964.53 1.453h.008v-.003z"
        fill="#E87722"
      />
      <Path
        d="M5.907 10.099c.32-.04.638.037.783.348.222.494-.334.89.174 1.31 2.114.93 4.135 2.261 5.67 4 .26.293.516.55.349.98a19.716 19.716 0 00-1.498 2.391c-.442.835-.36 1.228-1.11 1.109-.314-.048-.696-.723-.911-.979-.96-1.138-2.144-2.114-3.483-2.781-.337-.17-.941-.482-1.309-.415-.597.11-.508 1.309-1.201 1.427-.352.06-.564-.118-.734-.396-.931-1.55-1.647-3.316-2.57-4.877-.17-.337 0-.76.345-.905L5.907 10.1z"
        fill="#FAE4D3"
      />
      <Path
        d="M20.801 9.58c.115-.023.226.03.334.059 1.961.508 3.934 1.309 5.896 1.847.234.122.337.426.182.656l-2.44 3.786c-.241.018-.423.018-.657.082-.233.063-.686.344-.767.348-.13 0-.234-.182-.334-.263-.694-.541-1.454-.694-2.266-.304-.107.052-.315.226-.404.23-.09.003-.423-.33-.553-.408-.523-.33-1.082-.415-1.68-.23-.226.07-.385.208-.582.308-.196.1-.03.063-.074 0l-.567-1.473c-.015-.051 0-.1.022-.148.033-.074.538-.46.645-.541.582-.456 1.254-.89 1.899-1.25.363-.204.797-.367 1.138-.567.672-.386-.46-1.406-.096-1.91.056-.074.215-.196.308-.215V9.58H20.8z"
        fill="#F3BB90"
      />
      <Path
        d="M11.897 11.845l.018-3.726V6.802c0-.49-.506-.81-.76-.908h-1.038a.304.304 0 01-.26-.167c-.06-.104 0-.229.037-.278l3.597-4.71c.1-.11.382-.333.723-.333.342 0 .612.21.705.315l3.634 4.728c.025.043.063.16.019.278-.045.119-.13.16-.167.167h-.909c-.697 0-.933.556-.964.834v5.229s-.13.204-.278.278c-.074-.204-.538-1.428-1.873-1.428s-1.89 1.131-1.965 1.724c-.13-.092-.52-.686-.52-.686z"
        fill="#F1AD7A"
      />
    </Svg>
  );
}
