import React from 'react';
import { colors } from 'cube-ui-components/dist/esm/theme/base';
import { Path, Svg } from 'react-native-svg';

const TeamIcon = (props: {
  size?: number;
  height?: number;
  width?: number;
  fill?: string;
}): JSX.Element => {
  return (
    <Svg
      width={props.width || props.size || 21}
      height={props.height || props.size || 16}
      viewBox="0 0 21 16"
      fill={props.fill || colors.fwdOrange[100]}>
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5 2.5C4.4485 2.5 4 2.9485 4 3.5005C4 4.052 4.4485 4.5005 5 4.5005C5.5515 4.5005 6 4.052 6 3.5005C6 2.9485 5.5515 2.5 5 2.5ZM9.8645 9.97188C9.21225 8.52247 7.66155 7.50044 5.85713 7.50044H4.52213C2.82813 7.50044 1.35763 8.40144 0.644127 9.71094C0.204627 10.5174 0.823127 11.5004 1.74163 11.5004H1.74563C1.98313 11.5004 2.17763 11.3309 2.23263 11.0999C2.45013 10.1884 3.39413 9.50044 4.52213 9.50044H5.85713C6.99636 9.50044 7.94822 10.2022 8.15291 11.1276C7.41433 11.8442 6.85561 12.756 6.55568 13.7775C6.30318 14.6365 6.94068 15.5 7.83618 15.5H7.83868C8.08968 15.5 8.30518 15.3135 8.33168 15.064C8.54118 13.088 10.1552 11.5 12.1087 11.5H14.5087C16.4607 11.5 18.0737 13.086 18.2852 15.0595C18.3122 15.311 18.5302 15.498 18.7827 15.4955C19.6802 15.4875 20.3147 14.6205 20.0612 13.76C19.3402 11.309 17.1237 9.5 14.5087 9.5H12.1087C11.3135 9.5 10.5551 9.66843 9.8645 9.97188ZM5 6.5005C3.3455 6.5005 2 5.155 2 3.5005C2 1.846 3.3455 0.5 5 0.5C6.6545 0.5 8 1.846 8 3.5005C8 5.155 6.6545 6.5005 5 6.5005ZM13 8.5C10.7945 8.5 9 6.7055 9 4.5C9 2.2945 10.7945 0.5 13 0.5C15.2055 0.5 17 2.2945 17 4.5C17 6.7055 15.2055 8.5 13 8.5ZM13 2.5C11.897 2.5 11 3.397 11 4.5C11 5.603 11.897 6.5 13 6.5C14.103 6.5 15 5.603 15 4.5C15 3.397 14.103 2.5 13 2.5Z"
        fill={props.fill || colors.fwdOrange[100]}
      />
    </Svg>
  );
};
export default TeamIcon;
