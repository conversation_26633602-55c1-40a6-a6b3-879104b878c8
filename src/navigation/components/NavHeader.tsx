import { memo, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>, HeaderBackButton } from '@react-navigation/elements';
import { Icon, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { BackHandler, StyleSheet, View } from 'react-native';
import styled from '@emotion/native';
import { useNavigation } from '@react-navigation/native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export const NavHeader = memo(function NavHeader({
  title,
  renderLeftIcon,
  onLeftPress,
  disabled,
}: {
  title: string;
  renderLeftIcon?: () => JSX.Element | null;
  onLeftPress?: () => void;
  disabled?: boolean;
}) {
  const { colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const renderBackIcon = useCallback(() => {
    if (renderLeftIcon) {
      return renderLeftIcon();
    }
    return <Icon.ArrowLeft size={24} fill={colors.onBackground} />;
  }, [renderLeftIcon]);

  const { goBack } = useNavigation();

  const onLeftPressFn = useCallback(() => {
    if (disabled) return;
    if (onLeftPress) {
      onLeftPress();
    } else {
      goBack();
    }
  }, [onLeftPress, disabled]);

  useEffect(() => {
    const backAction = () => {
      return disabled;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [disabled]);

  const Title = isTabletMode ? Typography.H6 : Typography.H7;

  return (
    <View>
      <Header
        headerTitle={({ children }) => (
          <Title
            numberOfLines={1}
            fontWeight="bold"
            color={colors.onBackground}>
            {children}
          </Title>
        )}
        headerTitleAlign={isTabletMode ? 'left' : 'center'}
        title={title}
        headerLeft={props => (
          <SHeaderBackButton
            {...props}
            disabled={false}
            backImage={renderBackIcon}
            onPress={onLeftPressFn}
            labelVisible={false}
            isTablet={isTabletMode}
          />
        )}
      />
    </View>
  );
});
export default NavHeader;

const SHeaderBackButton = styled(HeaderBackButton)<{ isTablet: boolean }>(
  ({ theme, isTablet }) => ({
    width: theme.sizes[11],
    height: theme.sizes[11],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: isTablet ? 6 : undefined,
  }),
);
