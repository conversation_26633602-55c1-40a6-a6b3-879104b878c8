import { useTheme } from '@emotion/react';
import { Box, Icon, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import DrawerTabStyled from '../MainNavigator/my/DrawerTabStyled';
import { LayoutChangeEvent, View } from 'react-native';

export interface MoreMenuTabButtonProps {
  onPress: () => void;
  focused: boolean;
  selectMenuName: string;
  isShowPop: boolean;
  onLayout?: (event: LayoutChangeEvent) => void;
}

export default function MoreMenuTabButton({
  onPress,
  focused,
  selectMenuName,
  isShowPop,
  onLayout,
}: MoreMenuTabButtonProps) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('navigation');

  return (
    <View onLayout={onLayout}>
      <DrawerTabStyled.TouchableButton
        onPress={onPress}
        focused={focused}
        lineNumber={0}>
        <Box alignItems="center">
          <MoreBtnIcon
            focused={focused}
            selectMenuName={selectMenuName}
            isShowPop={isShowPop}
          />
          <Typography.SmallLabel
            color={colors.background}
            style={{
              alignSelf: 'center',
              textAlign: 'center',
              maxWidth: '94%',
            }}>
            {t('tabScreen.Others')}
          </Typography.SmallLabel>
        </Box>
      </DrawerTabStyled.TouchableButton>
    </View>
  );
}

function MoreBtnIcon({
  focused,
  selectMenuName,
  isShowPop,
}: {
  focused: boolean;
  selectMenuName: string;
  isShowPop: boolean;
}) {
  const { colors, space } = useTheme();

  const iconProps = {
    fill: colors.background,
    height: space[7],
    width: space[7],
  };

  if (focused && (selectMenuName === '' || isShowPop)) {
    return <Icon.Close {...iconProps} />;
  }
  return <Icon.Menu {...iconProps} />;
}
