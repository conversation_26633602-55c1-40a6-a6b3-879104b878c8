import { memo, useCallback, useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import { useNotificationHandler } from 'hooks/useNotificationHandler';

let processed = false;
export const LastNotificationHandler = memo(function LastNotificationHandler() {
  const handler = useNotificationHandler();
  const start = useCallback(async () => {
    const response = await Notifications.getLastNotificationResponseAsync();
    if (response && response.notification.request.identifier) {
      handler(response);
    }
  }, [handler]);

  useEffect(() => {
    //Only process on first mounting
    if (!processed) {
      processed = true;
      start();
    }
  }, []);

  return null;
});
export default LastNotificationHandler;
