import { useSafeAreaInsets } from 'react-native-safe-area-context';
import React, { FC, useEffect, useRef, useState } from 'react';
import {
  TouchableOpacity,
  View,
  Text,
  Dimensions,
  ViewStyle,
  Modal,
} from 'react-native';
import { useTheme } from '@emotion/react';
import Animated, { EasingNode } from 'react-native-reanimated';
import useBoundStore from 'hooks/useBoundStore';
import { Icon } from 'cube-ui-components';

export const AnimatedModal: FC<{
  visible: boolean;
  onClose: () => void;
  children: JSX.Element;
  showFrom?: 'bottom' | 'top';
  style?: ViewStyle;
  searched?: boolean;
}> = ({ visible, onClose, children, showFrom = 'bottom', style, searched }) => {
  const theme = useTheme();
  const { colors, sizes } = theme;

  const { bottomBarHeightInZus } = useBoundStore();
  const { top, bottom } = useSafeAreaInsets();
  const { width, height } = Dimensions.get('window');

  const animation = useRef(new Animated.Value(0)).current;

  const isShownFromBottom = showFrom === 'bottom';
  const isShownFromTop = showFrom === 'top';

  const bottomOffset = bottomBarHeightInZus + 1;

  const [shouldBeShown, setShouldBeShown] = useState(false);

  useEffect(() => {
    if (visible && !shouldBeShown) {
      setShouldBeShown(true);
    }
    const timer = setTimeout(() => {
      Animated.timing(animation, {
        toValue: visible ? 1 : 0,
        duration: 360,
        easing: EasingNode.linear,
      }).start(() => {
        if (!visible) {
          setShouldBeShown(false);
        }
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [visible]);

  const translateY = animation.interpolate({
    inputRange: [0, 1],
    // TODO better handling needed
    outputRange: [isShownFromTop ? -height : height / 2.5, 0],
  });

  const translateX = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [width, 0],
  });

  const scale = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const opacity = animation.interpolate({
    inputRange: [0, 0.2, 1],
    outputRange: [0, 0.2, 0.25],
  });

  const borderRadius = animation.interpolate({
    inputRange: [0.5, 1],
    outputRange: [187, isShownFromTop ? 0 : 64],
  });

  return (
    <Modal statusBarTranslucent transparent visible={shouldBeShown}>
      {/* //* V background */}
      <Animated.View
        style={{
          zIndex: 1000 - 1,
          bottom: bottomOffset,
          height: shouldBeShown ? '100%' : '0%',
          width: shouldBeShown ? '100%' : '0%',
          backgroundColor: colors.palette.black,
          position: 'absolute',
          opacity,
        }}
      />
      {/* // TODO improve this style */}
      {isShownFromTop && (
        <Animated.View
          style={{
            zIndex: 1000 - 1,
            bottom: 0,
            height: bottomOffset,
            width: shouldBeShown ? '100%' : '0%',
            backgroundColor: colors.palette.black,
            position: 'absolute',
            opacity,
          }}
        />
      )}
      {/* //? V content of the menu */}
      <Animated.View
        style={[
          {
            width: shouldBeShown ? (width > 600 ? '50%' : '100%') : '0%',
            position: 'absolute',
            zIndex: 1000,
            bottom: isShownFromBottom ? bottomOffset : 0,
            right: 0,
            top: isShownFromBottom ? top : 0,
            backgroundColor: colors.primary,
            paddingHorizontal: sizes[4],
            transform: isShownFromBottom
              ? [{ translateY }, { translateX }, { scale }]
              : [{ translateY }, { translateX }],
            paddingTop: isShownFromTop ? top : undefined,
            opacity: 0.95,
          },
          isShownFromTop
            ? { borderBottomLeftRadius: borderRadius }
            : { borderTopLeftRadius: borderRadius },
          style,
        ]}>
        <View style={[isShownFromTop && { paddingTop: 0 }]}>
          <View
            style={[
              {
                width: '100%',
                alignItems: 'flex-end',
                paddingHorizontal: searched ? sizes[4] : 0,
              },
            ]}>
            {isShownFromTop && (
              <TouchableOpacity onPress={onClose}>
                <Icon.CloseCircleFill
                  fill={colors.background}
                  height={30}
                  width={30}
                />
              </TouchableOpacity>
            )}
          </View>
          {children}
        </View>
      </Animated.View>
    </Modal>
  );
};
