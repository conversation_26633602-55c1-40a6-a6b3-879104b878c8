import { useSafeAreaInsets } from 'react-native-safe-area-context';
import React, { FC, useEffect, useRef, useState } from 'react';
import {
  TouchableOpacity,
  View,
  Dimensions,
  ViewStyle,
  Modal,
  Platform,
  Animated,
  Easing,
} from 'react-native';
import { useTheme } from '@emotion/react';
import useBoundStore from 'hooks/useBoundStore';
import { Icon } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export const AnimatedModal: FC<{
  visible: boolean;
  onClose: () => void;
  children: JSX.Element | Array<JSX.Element>;
  otherChildren?: JSX.Element | Array<JSX.Element>;
  showFrom?: 'bottom' | 'top';
  style?: ViewStyle;
  searched?: boolean;
  isNoPadding?: boolean;
  isForSearching?: boolean;
  isSolidBackground?: boolean;
  customBottomOffset?: number;
}> = ({
  visible,
  onClose,
  children,
  showFrom = 'bottom',
  style,
  searched,
  otherChildren,
  isNoPadding = false,
  isForSearching = false,
  isSolidBackground = false,
  customBottomOffset,
}) => {
  const theme = useTheme();
  const { colors, sizes } = theme;

  const { bottomBarHeightInZus } = useBoundStore();
  const { top, bottom } = useSafeAreaInsets();
  const { isWideScreen, shouldAdaptWideLayout } = useWindowAdaptationHelpers();
  const { width, height } = Dimensions.get('window');

  const animation = useRef(new Animated.Value(0)).current;

  const isShownFromBottom = showFrom === 'bottom';
  const isShownFromTop = showFrom === 'top';

  const bottomOffset = customBottomOffset ?? bottomBarHeightInZus;

  const [shouldBeShown, setShouldBeShown] = useState(false);

  const isAndroid = Platform.OS === 'android';

  useEffect(() => {
    if (visible && !shouldBeShown) {
      setShouldBeShown(true);
    }
    const timer = setTimeout(() => {
      Animated.timing(animation, {
        toValue: visible ? 1 : 0,
        duration: 360,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        if (!visible) {
          setShouldBeShown(false);
        }
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [visible]);

  const startOfTranslateY = isShownFromTop ? -height : height / 2.5;

  const translateY = animation.interpolate({
    inputRange: [0, 0.5, 1],
    // TODO better handling needed
    outputRange: [startOfTranslateY, startOfTranslateY, 0],
  });

  const translateX = animation.interpolate({
    inputRange: [0, 0.8, 1],
    outputRange: [width, 0, 0],
  });

  const scale = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const opacity = animation.interpolate({
    inputRange: [0, 0.2, 1],
    outputRange: [0, 0.2, 0.25],
  });

  const borderRadius = animation.interpolate({
    inputRange: [0.5, 1],
    outputRange: [187, isShownFromTop ? 0 : 64],
  });

  return (
    <Modal statusBarTranslucent transparent visible={shouldBeShown}>
      {/* //* V background */}
      <Animated.View
        style={{
          flex: 1,
          bottom: bottomOffset,
          height: shouldBeShown ? '100%' : '0%',
          width: shouldBeShown ? '100%' : '0%',
          backgroundColor: colors.palette.black,
          position: 'absolute',
          opacity,
        }}
      />
      {/* // TODO improve this style */}
      {isShownFromTop && (
        <Animated.View
          style={{
            bottom: 0,
            height: bottomOffset,
            width: shouldBeShown ? '100%' : '0%',
            backgroundColor: colors.palette.black,
            position: 'absolute',
            opacity,
          }}
        />
      )}
      {/* //? V content of the menu */}
      <Animated.View
        style={[
          {
            position: 'absolute',
            right: 0,
            backgroundColor: colors.primary,
            top: isShownFromBottom ? top : 0,
            bottom: isShownFromBottom ? bottomOffset : 0,
            paddingHorizontal: isNoPadding ? 0 : sizes[4],
            paddingTop: isShownFromTop ? top : undefined,
            opacity: isSolidBackground ? 1 : 0.95,
            width: shouldBeShown
              ? shouldAdaptWideLayout && !isForSearching
                ? '50%'
                : '100%'
              : '0%',
            transform: isShownFromBottom
              ? [{ translateY }, { translateX }, { scale }]
              : [{ translateY }, { translateX }],
          },
          isShownFromTop
            ? { borderBottomLeftRadius: borderRadius }
            : { borderTopLeftRadius: borderRadius },
          style,
        ]}>
        <View style={[isShownFromTop && { paddingTop: 0 }]}>
          <View
            style={[
              {
                width: '100%',
                alignItems: 'flex-end',
                paddingHorizontal: searched ? sizes[4] : 0,
              },
            ]}>
            {isShownFromTop && (
              <TouchableOpacity
                onPress={onClose}
                style={{
                  paddingHorizontal: !isNoPadding ? 0 : sizes[4],
                  marginTop: sizes[isAndroid ? 3 : 1],
                }}>
                <Icon.CloseCircleFill
                  fill={colors.background}
                  height={30}
                  width={30}
                />
              </TouchableOpacity>
            )}
          </View>
          {children}
        </View>
      </Animated.View>
      {otherChildren ?? <></>}
    </Modal>
  );
};
