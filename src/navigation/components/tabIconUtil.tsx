import React from 'react';
import { MainTabParamList } from 'types/navigation';
import { View } from 'react-native';
import { Icon } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';

export const tabIconUtil = ({
  tab,
  focused,
  isClose,
}: {
  tab: keyof MainTabParamList;
  focused: boolean;
  isClose?: boolean;
}) => {
  switch (tab) {
    case 'Home':
      return (
        <View style={{ marginBottom: 2 }}>
          <Icon.Home
            size={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        </View>
      );
    case 'Lead':
      return (
        <View style={{ marginBottom: 2 }}>
          <Icon.Team
            size={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        </View>
      );
    case 'Proposals':
      // ? Parent Document Icon not added
      return (
        <View style={{ marginBottom: 2 }}>
          <Icon.Document
            size={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        </View>
      );
    case 'Policies':
      return (
        <View style={{ marginBottom: 2 }}>
          <Icon.DocumentCertified
            size={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        </View>
      );
    case 'Others': {
      if (isClose) {
        return (
          <Icon.Close
            height={24}
            width={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        );
      }
      return (
        <Icon.Menu
          size={24}
          fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
        />
      );
    }
    case 'ERecruit':
      return (
        <View style={{ marginBottom: 2 }}>
          <Icon.Career
            size={24}
            fill={focused ? colors.fwdOrange[100] : colors.fwdGreyDarker}
          />
        </View>
      );

    default:
      return <></>;
  }
};
