import { useTheme } from '@emotion/react';
import { Box, Icon, Typography } from 'cube-ui-components';
import * as Linking from 'expo-linking';
import React from 'react';
import { useTranslation } from 'react-i18next';
import DrawerTabStyled from 'navigation/MainNavigator/my/DrawerTabStyled';
import { View } from 'react-native';

export default function MysLMSButton() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('navigation');

  return (
    <View>
      <DrawerTabStyled.TouchableButton
        onPress={() => {
          Linking.openURL('https://fwdmylms.disprz.com/');
        }}
        focused={false}
        lineNumber={0}>
        <Box alignItems="center">
          <Icon.Book
            fill={colors.background}
            height={space[7]}
            width={space[7]}
          />
          <Typography.SmallLabel
            color={colors.background}
            style={{
              alignSelf: 'center',
              textAlign: 'center',
              maxWidth: '94%',
            }}>
            {t('tabScreen.myLMS')}
          </Typography.SmallLabel>
        </Box>
      </DrawerTabStyled.TouchableButton>
    </View>
  );
}
