import { useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/core';
import { HeaderBackButton } from '@react-navigation/elements';
import { Icon } from 'cube-ui-components/';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React from 'react';
export default function CustomHeaderBackButton({
  fill,
  size,
  onPressBack,
}: {
  fill?: string;
  size?: number;
  onPressBack?: () => void;
}) {
  const navigation = useNavigation();
  const { isNarrowScreen, shouldAdaptWideLayout } =
    useWindowAdaptationHelpers();
  const { space } = useTheme();

  const handlePress = () => {
    onPressBack ? onPressBack() : navigation.canGoBack() && navigation.goBack();
  };
  const { colors } = useTheme();
  return (
    <HeaderBackButton
      disabled={false}
      backImage={() => (
        <Icon.ArrowLeft size={size || 24} fill={fill || colors.onBackground} />
      )}
      onPress={handlePress}
      labelVisible={false}
      style={{
        ...(isNarrowScreen && { left: -space[3] }),
        ...(shouldAdaptWideLayout && { left: -space[3] }),
      }}
    />
  );
}
