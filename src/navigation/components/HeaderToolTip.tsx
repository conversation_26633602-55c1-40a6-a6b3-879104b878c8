import { useTheme } from '@emotion/react';
import { Icon, Row } from 'cube-ui-components';
import React, { FC, useState } from 'react';
import {
  Platform,
  StatusBar,
  StyleProp,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import Tooltip from 'react-native-walkthrough-tooltip';

const HIT_SLOP = { top: 8, left: 8, right: 8, bottom: 8 };

export default function HeaderToolTip({
  children,
  isDefaultCloseButtonShown = true,
  innerContentStyle,
}: {
  children: React.ReactNode;
  isDefaultCloseButtonShown?: boolean;
  innerContentStyle?: StyleProp<ViewStyle>;
}) {
  const { colors, sizes, elevation, borderRadius, space } = useTheme();

  const [isVisible, setIsVisible] = useState(false);

  const isTooltipOnHandler = () => setIsVisible(!isVisible);

  return (
    <>
      <Tooltip
        disableShadow
        arrowSize={{ width: sizes[6], height: sizes[3] }}
        arrowStyle={{ marginLeft: 2, zIndex: 999 }}
        displayInsets={{
          // Jessica: padding of bubbles
          right: 12,
          top: 0,
          bottom: 0,
          left: 12,
        }}
        useInteractionManager={true}
        backgroundColor="rgba(0,0,0,0)"
        topAdjustment={
          Platform.OS === 'android' ? -(StatusBar.currentHeight ?? 0) : 0
        }
        placement="bottom"
        isVisible={isVisible}
        tooltipStyle={[
          { paddingTop: space[2] },
          Platform.OS === 'ios' && elevation[5], // Jessica: IOS elevation
        ]}
        contentStyle={[
          {
            borderRadius: borderRadius['large'],
            padding: 0,
            height: 'auto',
          },
          Platform.OS === 'android' && { elevation: 5 }, // Jessica: Android elevation
        ]}
        onClose={() => {
          isTooltipOnHandler();
        }}
        content={
          <TooltipWindow
            isVisibleHandler={isTooltipOnHandler}
            children={children}
            innerContentStyle={innerContentStyle}
            isDefaultCloseButtonShown={isDefaultCloseButtonShown}
          />
        }>
        <TouchableOpacity onPress={isTooltipOnHandler}>
          <Icon.InfoCircle
            fill={colors.secondary}
            height={sizes[5]}
            width={sizes[5]}
          />
        </TouchableOpacity>
      </Tooltip>
    </>
  );
}

const TooltipWindow: FC<{
  children: React.ReactNode;
  isVisibleHandler: () => void;
  innerContentStyle?: StyleProp<ViewStyle>;
  isDefaultCloseButtonShown?: boolean;
}> = ({
  children,
  isVisibleHandler,
  innerContentStyle,
  isDefaultCloseButtonShown,
}) => {
  const { colors, sizes, borderRadius } = useTheme();

  return (
    <Animated.View
      style={[
        {
          borderRadius: borderRadius['large'],
          padding: sizes[4],
          backgroundColor: colors.background,
        },
        innerContentStyle,
      ]}
      entering={FadeIn}
      exiting={FadeOut}>
      <Row>
        {children}
        {isDefaultCloseButtonShown && (
          <TouchableOpacity
            style={{
              width: sizes[6],
              height: sizes[6],
            }}
            onPress={isVisibleHandler}
            hitSlop={HIT_SLOP}>
            <Icon.Close
              fill={colors.primary}
              height={sizes[6]}
              width={sizes[6]}
            />
          </TouchableOpacity>
        )}
      </Row>
    </Animated.View>
  );
};
