import { View, Text, TouchableOpacity } from 'react-native';
import React from 'react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useRootStackNavigation } from 'hooks/useRootStack';

export default function HomeButton() {
  const { colors, space, borderRadius } = useTheme();
  const { navigate } = useRootStackNavigation();
  return (
    <TouchableOpacity
      style={{
        alignSelf: 'flex-start',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        gap: space[1],
        paddingHorizontal: space[2],
      }}
      onPress={() =>
        navigate('Main', {
          screen: 'Home',
        })
      }>
      <Icon.Home fill={colors.secondary} />
      <Typography.LargeLabel fontWeight="bold">Home</Typography.LargeLabel>
    </TouchableOpacity>
  );
}
