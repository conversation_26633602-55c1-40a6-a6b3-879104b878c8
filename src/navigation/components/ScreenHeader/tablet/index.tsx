import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import {
  AllRouteTitleKeys,
  headerDefaultTitleMap,
} from 'navigation/components/ScreenHeader/constant';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';

type ScreenHeaderProps = {
  route: AllRouteTitleKeys;
  customTitle?: string;
  leftChildren?: JSX.Element;
  rightChildren?: JSX.Element;
  showBottomSeparator?: boolean;
  // customBackgroundColor?: string;
  isLeftCrossBackShown?: boolean;
  isLeftArrowBackShown?: boolean;
  onPressDefaultLeftButton?: () => void;
};

export default function ScreenHeader({
  leftChildren,
  rightChildren,
  route,
  showBottomSeparator,
  customTitle,
  // customBackgroundColor,
  isLeftCrossBackShown,
  isLeftArrowBackShown,
  onPressDefaultLeftButton,
}: ScreenHeaderProps) {
  const theme = useTheme();
  const { sizes, colors } = theme;

  const titleLabelHandler = (): string => {
    if (customTitle) {
      return customTitle;
    }
    if (
      route in headerDefaultTitleMap &&
      headerDefaultTitleMap[route].length > 0
    ) {
      return headerDefaultTitleMap[route];
    }
    return '';
  };

  const { top } = useSafeAreaInsets();
  return (
    <Box backgroundColor={colors.background} paddingTop={top}>
      <HeaderStyledView showBottomSeparator={showBottomSeparator}>
        <Row gap={sizes[5]} width={'60%'}>
          {leftChildren ? (
            leftChildren
          ) : (
            <DefaultLeftButton
              onPressDefaultLeftButton={onPressDefaultLeftButton}
              isLeftCrossBackShown={isLeftCrossBackShown}
              isLeftArrowBackShown={isLeftArrowBackShown}
            />
          )}
          <Typography.H6
            fontWeight="bold"
            numberOfLines={1}
            ellipsizeMode="tail">
            {titleLabelHandler()}
          </Typography.H6>
        </Row>
        <Box>{rightChildren}</Box>
      </HeaderStyledView>
    </Box>
  );
}

type HeaderStyledViewProps = {
  showBottomSeparator?: boolean;
  theme?: Theme;
};

const HeaderStyledView = styled.View(
  ({ showBottomSeparator = true, theme }: HeaderStyledViewProps) => ({
    minHeight: theme?.sizes[11],
    width: '100%',
    borderBottomColor: theme?.colors.surface,
    borderBottomWidth: showBottomSeparator ? 1 : 0,
    padding: theme?.sizes[4],
    flexDirection: 'row',
    justifyContent: 'space-between',
  }),
);

const DefaultLeftButton = ({
  isLeftCrossBackShown,
  isLeftArrowBackShown,
  onPressDefaultLeftButton,
}: {
  isLeftCrossBackShown: boolean | undefined;
  isLeftArrowBackShown: boolean | undefined;
  onPressDefaultLeftButton?: () => void;
}) => {
  const { sizes, colors } = useTheme();

  if (isLeftCrossBackShown) {
    return (
      <GoBackButton onPress={onPressDefaultLeftButton}>
        <Icon.Close size={sizes[6]} fill={colors.secondary} />
      </GoBackButton>
    );
  }
  if (isLeftArrowBackShown) {
    return (
      <GoBackButton onPress={onPressDefaultLeftButton}>
        <Icon.ArrowLeft size={sizes[6]} fill={colors.secondary} />
      </GoBackButton>
    );
  }
  return <></>;
};

const GoBackButton = ({
  children: iconChild,
  onPress,
}: {
  children: React.ReactNode;
  onPress?: () => void;
}) => {
  const { colors, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <TouchableOpacity
      hitSlop={HIT_SLOP_SPACE(2)}
      style={{}}
      onPress={() => {
        onPress
          ? onPress()
          : navigation.canGoBack()
          ? navigation.goBack()
          : console.log('~~~~~cannot go back');
      }}>
      {/* <Icon.Close size={sizes[5]} fill={colors.secondary} /> */}
      {iconChild}
    </TouchableOpacity>
  );
};
