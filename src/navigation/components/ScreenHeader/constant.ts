import {
  IbRootStackParamList,
  MainTabParamList,
  MyRootStackParamList,
  PhRootStackParamList,
} from 'types/navigation';

export type AllRouteTitleKeys =
  | keyof MainTabParamList
  | keyof PhRootStackParamList
  | keyof IbRootStackParamList
  | keyof MyRootStackParamList;

export const headerDefaultTitleMap: Record<AllRouteTitleKeys, string> = {
  AiBot: 'Ask me anything',
  AiBotHistory: 'History',
  AiBotFeedback: 'Feedback',
  AiBotTable: 'Table view',
  LeadProfile: 'Lead profile',
  Household: '',
  GapAnalysis: '',
  ProductDetails: '',
  SalesIllustrationForm: '',
  PoliciesNewBusiness: '',
  ContactBook: 'Contacts',
  FWDNews: 'FWD News',
  EliteAgencyDetails: '',
  EliteAgencyRequirements: '',
  EliteAgencyBenefits: '',
  CampaignsDetails: '',
  AddNewLead: 'Add new lead / entity',
  ProposalTable: '',
  Home: '',
  Lead: 'Lead Management',
  Proposals: '',
  Policies: 'Policy tracking',
  Others: '',
  Customers: '',
  Performance: 'Performance',
  ReportGeneration: '',
  Document: '',
  TeamManagement: 'Team management',
  Main: '',
  Login: '',
  AgentProfile: 'My profile',
  Setting: 'Setting',
  PersonalDetails: 'Personal details',
  LeadAndCustomerSearch: '',
  PdfViewer: '',
  EApp: '',
  ProductSelection: '',
  CoverageDetailsScreen: '',
  ProfileDetails: 'Profile details',
  TeamListPerformance: '',
  TeamOperation: 'View operation data',
  TeamLeadsConversion: 'Leads conversion',
  TeamTarget: 'Team target',
  RPQQuestionForm: 'RPQ',
  ImageList: '',
  HealthQuestionsReview: '',
  PersonalInfoReview: '',
  EntityInfoReview: '',
  PolicyReplacementReview: '',
  DataPrivacyReview: '',
  RiskProfileReview: '',
  FatcaReview: '',
  RocReview: '',
  PdpReview: '',
  FWDNewsDetails: '',
  FWDNewsBookmarks: 'Bookmark',
  BadgesCollection: 'Badges collection',
  AgentPerformance: 'Top agent’s performance',
  TeamView: '',
  PoliciesPOS: '',
  TeamPerformanceDetails: '',
  ACR: '',
  RPQResult: '',
  PaymentResult: '',
  PaymentProcessing: '',
  PaymentGateway: '',
  PolicyOwnerSignature: '',
  InsuredSignature: '',
  AgentSignature: '',
  PlaceOfSigning: '',
  RemotePolicyOwnerSignature: '',
  RemoteInsuredSignature: '',
  RemoteAgentSignature: '',
  PRQ: '',
  RecognitionDetails: 'Recognition',
  PolicyOwnerSignatureBottom: '',
  PolicyOwnerSignatureFade: '',
  RemotePolicyOwnerSignatureBottom: '',
  RemotePolicyOwnerSignatureFade: '',
  PerformanceDetails: '',
  TeamMemberTargetsEdit: 'Edit team member’s target',
  Fna: '',
  LogActivity: 'Log activity',
  AgentPolicies: 'Policy Tracking',
  FundIllustrationForm: '',
  ProductRecommendation: '',
  tm: '',
  fwd: '',
  afwd: '',
  fwm: '',
  afwm: '',
  fwo: '',
  afwo: '',
  fwp: '',
  fwa: '',
  Opportunities: '',
  FNA: '',
  RPQ: '',
  Activities: '',
  CustomerProfileDetails: 'Customer Profile',
  BirthdayTasksScreen: '',
  BirthdayCardScreen: '',
  SimulationTable: '',
  SavingsGoal: '',
  ProtectionGoal: '',
  ExistingPolicyDetail: 'Certificate details',
  fwbm: '',
  afwbm: '',
  ERecruit: '',
  NotificationScreen: '',
  ReportGenerationListScreen: '',
  CustomerFactFind: '',
  ERecruitApplication: 'ERecruitApplication',
  ERecruitApplicationStatus: 'ERecruitApplicationStatus',
  CandidateProfile: 'Candidate profile',
  ReviewCandidateApplication: 'Review application',
  ERecruitCheckApplication: 'ERecruitCheckApplication',
  ERecruitReviewAgentsSubmission: 'ERecruitReviewAgentsSubmission',
  ERecruitReviewAgentsApplication: 'ERecruitReviewAgentsApplication',
  Affiliate: '',
  TrainerAiBot: '',
  Ecoach: '',
  PersonalInformationReview: '',
  RopReview: '',
  TakeOverReview: '',
  DisclosureReview: '',
  ChargePremiumAuthorizationReview: '',
  InsuranceCoverageReview: '',
  EPolicyAndENoticesReview: '',
  Submission: '',
  SubmissionFailed: '',
  SolicitingOfficerSignature: '',
  TeamTargetsEdit: '',
  DocumentsScreen: '',
  CreateInsured: '',
  InsuredDetails: '',
  SearchExistingLead: '',
  AddNewLeadOrEntity: '',
  SummitClubsDetails: '',
  AddNewEntity: '',
  GYBAttendees: '',
  MaterialDetails: '',
  CandidatesSearch: '',
  AiBotChat: '',
  EcoachHome: '',
  Splash: '',
  UserProfile: '',
  SelectPolicy: '',
  SelectDifficulty: '',
  GuideLinesPage: '',
  VideoCallPage: '',
  WellDone: '',
  Summary: '',
  SummaryTablet: '',
  DetailSummary: '',
  DetailSummaryTablet: '',
  SessionHistory: '',
  OverallFeedback: '',
  WatchVideoPage: '',
  AffiliateScreen: '',
  AffiliateProfile: '',
  AffiliatePostDetails: '',
  PerformanceTarget: '',
  ERecruitCandidateProfile: '',
  LapsedPoliciesReportScreen: '',
  PolicyAnniversaryListScreen: '',
  PremiumReceivedReportScreen: '',
  UnsuccessfulAdaAcaScreen: '',
  AddCandidate: 'Add new candidate',
  myLMS: '',
  Merchandise: '',
  WatchVideoPage: '',
  AgentAssist: 'Agent assist',
  StartClaim: 'Start Claim',
  agentAssistDetail: '',
};
