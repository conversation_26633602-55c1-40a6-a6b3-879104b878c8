import styled from '@emotion/native';
import { useTheme } from '@emotion/react';

const Container = styled.View(() => {
  const { colors, space } = useTheme();

  return {
    backgroundColor: colors.palette.whiteTransparent,
    paddingLeft: space[2],
  };
});

const TouchableButton = styled.TouchableOpacity(
  ({ lineNumber, focused }: { lineNumber: number; focused: boolean }) => {
    const { colors, space } = useTheme();

    return {
      width: '100%',
      borderTopLeftRadius: space[2],
      borderBottomLeftRadius: space[2],
      alignItems: 'center',
      justifyContent: 'center',
      gap: space[1],
      paddingVertical: space[2],
      // paddingTop: space[2],
      // height: lineNumber >= 2 ? 76 : 60,
      paddingRight: space[2],
      backgroundColor: focused
        ? colors.background
        : colors.palette.whiteTransparent,
    };
  },
);

const ContainerV2 = styled.View(() => {
  const { colors, space } = useTheme();

  return {
    // backgroundColor: colors.palette.whiteTransparent,
    // paddingLeft: space[2],
  };
});

const TouchableButtonV2 = styled.TouchableOpacity(
  ({ lineNumber, focused }: { lineNumber: number; focused: boolean }) => {
    const { colors, space } = useTheme();

    return {
      backgroundColor: focused
        ? 'rgba(255, 255, 255, 0.20)'
        : colors.palette.whiteTransparent,
      paddingVertical: space[3],
      marginHorizontal: space[2],
      borderRadius: space[4],
      // width: '100%',
      // borderTopLeftRadius: space[2],
      // borderBottomLeftRadius: space[2],
      // alignItems: 'center',
      // justifyContent: 'center',
      // gap: space[1],
      // paddingVertical: space[2],
      // // paddingTop: space[2],
      // // height: lineNumber >= 2 ? 76 : 60,
      // paddingRight: space[2],
      // backgroundColor: focused
      //   ? colors.background
      //   : colors.palette.whiteTransparent,
    };
  },
);

const DrawerTabStyled = {
  Container,
  TouchableButton,
  TouchableButtonV2,
  ContainerV2,
};

export default DrawerTabStyled;
