// import { StyleSheet, Text, View, TextInput, Keyboard } from 'react-native';
// import React, { Fragment, useRef, useEffect } from 'react';
// import { AnimatedModal } from 'navigation/components/AnimatedModal';
// import { H4 } from 'cube-ui-components/dist/cjs/components/Typography';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';
// import { useTheme } from '@emotion/react';
// import useBoundStore from 'hooks/useBoundStore';
// import { NavigationProp, useNavigation } from '@react-navigation/native';
// import { MainTabParamList } from 'types';

// export default function SearchFullPageModal() {
//   const {
//     appActions: { setIsSearchingModalOn },
//     isSearchingModalOn,
//     searchTriggeredFrom,
//   } = useBoundStore();

//   const searchFieldRef = useRef<TextInput>(null);
//   const { top, bottom } = useSafeAreaInsets();
//   const theme = useTheme();

//   const {
//     colors: { palette },
//     sizes,
//   } = theme;

//   const onCloseHandler = () => {
//     setIsSearchingModalOn({
//       bool: false,
//       triggeredFrom: null,
//     });
//   };

//   useEffect(() => {
//     if (isSearchingModalOn) {
//       searchFieldRef.current && searchFieldRef.current.focus();
//     } else {
//       Keyboard.dismiss();
//     }
//   }, [isSearchingModalOn]);

//   return (
//     <AnimatedModal
//       visible={isSearchingModalOn}
//       onClose={onCloseHandler}
//       showFrom={'top'}>
//       <Fragment>
//         <Text>searchTriggeredFrom: {`${searchTriggeredFrom}`}</Text>
//         <View style={{ paddingHorizontal: sizes[3] }}>
//           {/* <Text>Testing</Text> */}
//           <H4 style={{ color: palette.white }}>
//             <Text>I'm looking for...</Text>
//           </H4>
//         </View>
//         <TextInput ref={searchFieldRef} style={styles.input} />
//       </Fragment>
//     </AnimatedModal>
//   );
// }

// const styles = StyleSheet.create({
//   input: {
//     height: 40,
//     margin: 12,
//     borderBottomWidth: 1,
//     borderBottomColor: 'white',
//     padding: 10,
//   },
// });
