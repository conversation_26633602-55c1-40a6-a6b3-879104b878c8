import React from 'react';
import {
  // LeadConversionTabParamList,
  PoliciesDetailTabParamList,
  PoliciesTabParamList,
  TopFiveTeamTabParamList,
} from 'types/navigation';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { ViewStyle } from 'react-native';
import { useTheme } from '@emotion/react';
import { useTabbarStyle } from 'hooks/useTabbarStyle';

const Tab = createMaterialTopTabNavigator();
export type TopTabProps = {
  tabList: {
    component: React.FC<any>;
    name:
      | keyof PoliciesTabParamList
      | keyof PoliciesDetailTabParamList
      | keyof TopFiveTeamTabParamList;
    // | keyof LeadConversionTabParamList;
  }[];
  options?: {
    sceneContainerStyle?: ViewStyle;
    style?: ViewStyle;
    tabBarScrollEnabled?: boolean;
  };
  screenOptions?: {
    tabBarScrollEnabled?: boolean;
  };
};

export default function TopTabBar({
  tabList,
  options,
  screenOptions,
}: TopTabProps) {
  const tabarStyle = useTabbarStyle();
  return (
    <Tab.Navigator
      screenOptions={{
        ...tabarStyle,
        ...screenOptions,
      }}
      {...options}>
      {tabList.map(({ name, component }, i) => {
        return <Tab.Screen key={i} name={String(name)} component={component} />;
      })}
    </Tab.Navigator>
  );
}
