import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
} from '@react-navigation/drawer';
import {
  Column,
  Icon,
  Row,
  SvgIconProps,
  Typography,
} from 'cube-ui-components';
import FwdCubeSquaredLogoOnOrangeSVG from 'features/home/<USER>/FwdCubeSquaredLogoOnOrangeSVG';
import TeamManagementScreen from 'screens/TeamManagementScreen';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import DrawerTabStyled from 'navigation/components/DrawerTabStyled';
import { MainTab } from 'types';

import {
  CubeUIComponentIconProps,
  OtherMenuTab,
} from 'navigation/components/TabsConfig';
import TeamIcon from 'navigation/icons/TeamIcon';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  GestureResponderEvent,
  LayoutChangeEvent,
  Linking,
  Platform,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AffiliateScreen from 'screens/AffiliateScreen';
import DocumentsScreen from 'screens/DocumentsScreen';
import ERecruitScreen from 'screens/ERecruitScreen';
import HomeScreen from 'screens/HomeScreen';
import LeadScreen from 'screens/LeadScreen';
import PerformanceScreen from 'screens/PerformanceScreen';
import PoliciesScreen from 'screens/PoliciesScreen';
import ProposalScreen from 'screens/ProposalScreen';
import ReportGenerationScreen from 'screens/ReportGenerationScreen';
import { CHANNELS } from 'types/channel';
import {
  country,
  salesConnectUrlLogin,
  fwdPintrAppleStoreUrl,
  fwdPintrGooglePlayStoreUrl,
} from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import MysLMSButton from './MysLMSButton';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { NavigationState } from '@react-navigation/native';
import { useHasPermission } from 'hooks/useCheckClientScope';
import MoreMenuTabButton from './MoreMenuTabButton';
import OthersTabPopUpCard from './OthersTabPopUpCard';
import { usePortal } from '@gorhom/portal';
import SalesConnectSVG from 'navigation/icons/SalesConnectSVG';
import PhoneWithChatBubbleSVG from 'navigation/icons/PhoneWithChatBubbleSVG';

type DrawerTabsConfigItem = Omit<MainTab | OtherMenuTab, 'icon'> & {
  icon: (props: SvgIconProps) => JSX.Element;
};

const getActiveRouteState = function (
  routes: NavigationState['routes'],
  index: number,
  name: string,
) {
  return routes[index].name.toLowerCase().indexOf(name.toLowerCase()) >= 0;
};

export default function RootDrawerTabBar(props: DrawerContentComponentProps) {
  const { colors, space } = useTheme();
  //? activeRouteName type casting TBC
  const activeRouteName = props.state.routes[props.state.index]
    ?.name as DrawerTabsConfigItem['name'];
  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { data: agentProfile, isLoading } = useGetAgentProfile();
  const hasPermission = useHasPermission();
  const { bottom: bottomSafeArea } = useSafeAreaInsets();
  const pop = usePortal();

  const [isOtherMenuOpen, setIsOtherMenuOpen] = useState(false);
  const [isShowPop, setIsShowPop] = useState(false);
  const [selectMoreMenuName, setSelectMoreMenuName] = useState('');

  const [contentHeight, setContentHeight] = useState(0);
  const [otherButtonY, setOtherButtonY] = useState(0);
  const logoHeight = 64;
  const minLayoutHeight = 67;
  const maxLayoutHeight = 82;

  const maxMenuNum =
    Math.floor((contentHeight - logoHeight) / maxLayoutHeight) || 0;

  const bottomPadding = bottomSafeArea > 20 ? bottomSafeArea : space[5];
  const popupBottom =
    contentHeight - otherButtonY - minLayoutHeight - logoHeight - bottomPadding;

  const filteredDrawerTabs = drawerTabs.filter(item => {
    // Permission check
    if (item.feature && !hasPermission(item.feature)) {
      return false;
    }
    // Module config check
    if (!countryModuleSellerConfig[item.name]) {
      return false;
    }
    // Temp Hide for PH (TeamManagement)
    if (item.name === 'TeamManagement' && isBanca && country === 'ph') {
      return false;
    }
    // Temp Hide ERecruit for ph
    if (item.name === 'ERecruit' && country === 'ph') {
      return false;
    }
    // Hide TeamManagement for FWP designation
    if (
      item.name === 'TeamManagement' &&
      agentProfile?.designation.toLowerCase() === 'fwp'
    ) {
      return false;
    }
    // Hide ERecruit for Banca
    if (item.name === 'ERecruit' && isBanca) {
      return false;
    }
    // Passed all checks
    return true;
  });

  // // TBC
  // const isShownMainTabsOverFlow = filteredDrawerTabs?.length > maxMenuNum;

  // const withRangeFilteredDrawerTabs = filteredDrawerTabs?.slice(
  //   0,
  //   maxMenuNum - 1,
  // );
  // const overFlowFilteredDrawerTabs = filteredDrawerTabs?.slice(
  //   maxMenuNum - 1,
  //   filteredDrawerTabs?.length,
  // );

  const PortalContent = (
    <OthersTabPopUpCard
      props={props}
      bottom={popupBottom}
      drawerTabsData={[
        {
          name: 'salesConnect',
          icon: SalesConnectSVG,
          showHeader: false,
          onPress: async () => {
            console.log('salesConnect on press');
            const canOpen = await Linking.canOpenURL(salesConnectUrlLogin);
            if (!canOpen) {
              console.log(
                'Cannot open URL. Please check: ',
                salesConnectUrlLogin,
              );
            }

            console.log(
              'salesConnect on press after canOpen: ',
              canOpen,
              salesConnectUrlLogin,
            );

            Linking.openURL(salesConnectUrlLogin);
          },
        },
        {
          name: 'pintr',
          icon: PhoneWithChatBubbleSVG,
          showHeader: false,
          onPress: async () => {
            const platformSpecficUrl =
              Platform.OS == 'ios'
                ? fwdPintrAppleStoreUrl
                : fwdPintrGooglePlayStoreUrl;

            console.log('pintr on press : ', platformSpecficUrl);

            const canOpen = await Linking.canOpenURL(platformSpecficUrl);
            if (!canOpen) {
              console.log(
                'Cannot open URL. Please check: ',
                platformSpecficUrl,
              );
            }
            Linking.openURL(platformSpecficUrl);
          },
        },
      ]}
      setIsShowPop={setIsShowPop}
      setSelectMoreMenuName={setSelectMoreMenuName}
      setIsOtherMenuOpen={setIsOtherMenuOpen}
      pop={pop}
      selectMoreMenuName={selectMoreMenuName}
    />
  );

  return (
    <>
      <TopBackgroundPaddingForStatusBar />
      <Row flex={1}>
        <Column
          flex={1}
          onLayout={e => {
            setContentHeight(e.nativeEvent.layout.height);
          }}>
          <DrawerContentScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{
              gap: space[2],
              backgroundColor: colors.primary,
              paddingTop: space[2],
            }}>
            <View style={{ alignItems: 'center' }}>
              <FwdCubeSquaredLogoOnOrangeSVG />
            </View>
            {!isLoading && agentProfile && (
              <Column flex={1} gap={space[1]}>
                {filteredDrawerTabs
                  .sort((a, b) =>
                    country === 'ib'
                      ? ibDrawerTabSequence[a.name] -
                        ibDrawerTabSequence[b.name]
                      : 0,
                  )
                  .map(
                    ({ name, icon: Icon, tabletIcon: TabletIcon, onPress }) => {
                      const focused =
                        !isOtherMenuOpen && selectMoreMenuName === ''
                          ? getActiveRouteState(
                              props.state.routes,
                              props.state.index,
                              name,
                            )
                          : false;

                      // * show ERecruit ONLY for dev/sit (already filtered above for banca)
                      if (name === 'ERecruit') {
                        return (
                          <DrawerTabButtonV2
                            key={name}
                            name={name}
                            Icon={Icon as CubeUIComponentIconProps}
                            focused={focused}
                            navigationHandler={e => {
                              if (isOtherMenuOpen) {
                                pop.removePortal('OtherMenu');
                                setIsOtherMenuOpen(false);
                                setIsShowPop(false);
                                setSelectMoreMenuName('');
                              }
                              onPress
                                ? onPress(e)
                                : props.navigation.navigate(name);
                            }}
                          />
                        );
                      }

                      const TabIcon =
                        isTabletMode && TabletIcon
                          ? TabletIcon
                          : (Icon as CubeUIComponentIconProps);

                      return (
                        <DrawerTabButtonV2
                          key={name}
                          name={name}
                          Icon={TabIcon}
                          focused={focused}
                          navigationHandler={e => {
                            if (isOtherMenuOpen) {
                              pop.removePortal('OtherMenu');
                              setIsOtherMenuOpen(false);
                              setIsShowPop(false);
                              setSelectMoreMenuName('');
                            }
                            onPress
                              ? onPress(e)
                              : props.navigation.navigate(name);
                          }}
                        />
                      );
                    },
                  )}
                {countryModuleSellerConfig.myLMS && isBanca == false && (
                  <MysLMSButton />
                )}
                {country == 'id' && (
                  <MoreMenuTabButton
                    focused={isOtherMenuOpen}
                    selectMenuName={selectMoreMenuName}
                    isShowPop={isShowPop}
                    onLayout={(nativeEvent: LayoutChangeEvent) => {
                      setOtherButtonY(nativeEvent.nativeEvent.layout.y);
                    }}
                    onPress={() => {
                      // pop.addPortal('OtherMenu', PortalContent);
                      if (selectMoreMenuName != '') {
                        setIsOtherMenuOpen(true);
                        setIsShowPop(!isShowPop);
                        !isShowPop
                          ? pop.addPortal('OtherMenu', PortalContent)
                          : pop.removePortal('OtherMenu');
                      } else {
                        setIsShowPop(!isShowPop);
                        setIsOtherMenuOpen(!isOtherMenuOpen);
                        !isShowPop
                          ? pop.addPortal('OtherMenu', PortalContent)
                          : pop.removePortal('OtherMenu');
                      }
                    }}
                  />
                )}
              </Column>
            )}
          </DrawerContentScrollView>
        </Column>
      </Row>
    </>
  );
}

function DrawerTabButtonV2({
  name,
  Icon,
  focused,
  navigationHandler,
}: {
  name: (MainTab | OtherMenuTab)['name'];
  Icon: CubeUIComponentIconProps;
  focused: boolean;
  navigationHandler: (event: GestureResponderEvent) => void;
}) {
  const [lineNumber, setLineNumber] = useState(0);
  const { colors, space } = useTheme();
  const { t } = useTranslation('navigation');

  const onPressHandler = (event: GestureResponderEvent) => {
    // if (isSectionBlocked && isAgentBlocked) {
    //   setIsMandatoryCourseModalVisible(true);
    // } else {
    //   navigationHandler();
    // }
    if (!countryModuleSellerConfig[name]) {
      console.log(
        '🔴🔴🔴 \nfile: RootDrawerTabBar.tsx:102 DrawerTabButton onPressHandler 📺Screen',
        name,
        `is not❌ implemented. Please check ~${name}~ is set to true module config at`,
        `src/utils/config/module/${country}/sellerExp/index.ts \n🔴🔴🔴 `,
      );
    }
    navigationHandler(event);
  };
  return (
    <DrawerTabStyled.ContainerV2>
      <DrawerTabStyled.TouchableButtonV2
        onPress={onPressHandler}
        lineNumber={lineNumber}
        focused={focused}>
        <View
          style={{
            alignItems: 'center',
          }}>
          <Icon fill={colors.background} height={space[7]} width={space[7]} />
          <Typography.SmallLabel
            color={colors.background}
            style={{
              alignSelf: 'center',
              textAlign: 'center',
              maxWidth: '94%',
            }}>
            {t(`tabScreen.${name}`)}
          </Typography.SmallLabel>
        </View>
      </DrawerTabStyled.TouchableButtonV2>
    </DrawerTabStyled.ContainerV2>
  );
}

const TopBackgroundPaddingForStatusBar = styled.View(() => {
  const { top } = useSafeAreaInsets();
  const { colors } = useTheme();
  return {
    paddingTop: top,
    backgroundColor: colors.background,
  };
});

// const RoundedSidePadding = styled.View(
//   ({ currentRoute }: { currentRoute: DrawerTabsConfigItem['name'] }) => {
//     const { colors, getElevation } = useTheme();
//     const screenToPaddingColor: Partial<
//       Record<DrawerTabsConfigItem['name'], ColorValue>
//     > = {
//       Home: colors.palette.fwdGrey[50],
//       Lead:
//         country === 'ph' ? colors.palette.white : colors.palette.fwdGrey[50],
//       Proposals: colors.palette.fwdGrey[50],
//       Policies: colors.palette.fwdGrey[50],
//       Performance: colors.palette.fwdGrey[50],
//       ERecruit:
//         country === 'ib' ? colors.palette.fwdGrey[50] : colors.palette.white,
//       Document: colors.palette.white,
//       ReportGeneration: colors.palette.fwdGrey[50],
//       Affiliate: colors.palette.fwdGrey[50],
//       TeamManagement: colors.palette.fwdGrey[50],
//     };
//     const bgColor = screenToPaddingColor[currentRoute];

//     return {
//       width: 32,
//       backgroundColor: bgColor ?? colors.palette.white,
//       borderTopLeftRadius: 300,
//       borderBottomLeftRadius: 300,
//       borderWidth: 1,
//       borderColor: colors.palette.whiteTransparent,
//     };
//   },
// );

const ibDrawerTabSequence: Record<(typeof drawerTabs)[number]['name'], number> =
  {
    Home: 0,
    Lead: 1,
    Proposals: 2,
    Policies: 3,
    Performance: 4,
    ERecruit: 5,
    Affiliate: 6,
    Document: 0,
    Others: 0,
    Customers: 0,
    AiBot: 0,
    ReportGeneration: 0,
    TeamManagement: 7,
    myLMS: 0,
    Merchandise: 0,
    TrainerAiBot: 0,
    AgentAssist: 0,
    AgentPolicies: 0,
    SocialMarketing: 0,
    SocialMarketingTemplates: 0,
    SocialMarketingMyPosts: 0,
    SocialMarketingCreateNew: 0,
  };

export const drawerTabs: Array<
  Omit<MainTab | OtherMenuTab, 'icon'> & {
    icon: (props: SvgIconProps) => JSX.Element;
    tabletIcon?: (props: SvgIconProps) => JSX.Element;
  }
> = [
  {
    name: 'Home',
    component: HomeScreen,
    // focusedIcon: tabIconUtil({ tab: 'Home', focused: true }),
    // icon: tabIconUtil({ tab: 'Home', focused: false }),
    icon: Icon.Home,
    showHeader: false,
  },
  {
    name: 'Lead',
    component: LeadScreen,
    // focusedIcon: tabIconUtil({ tab: 'Lead', focused: true }),
    icon: Icon.Team,
    showHeader: false,
    feature: 'lead',
  },
  {
    name: 'Proposals',
    //! temp fix
    component: ProposalScreen,
    // focusedIcon: tabIconUtil({ tab: 'Proposals', focused: true }),
    icon: Icon.Document,
    tabletIcon: Icon.Document,
    showHeader: false,
    feature: 'si',
  },
  {
    name: 'Policies',
    component: PoliciesScreen,
    // focusedIcon: tabIconUtil({ tab: 'Policies', focused: true }),
    icon: Icon.DocumentCertified,
    showHeader: true,
    feature: 'policy',
  },
  {
    name: 'Performance',
    component: PerformanceScreen,
    // focusedIcon: othersTabIconMapping.Performance.focused,
    icon: Icon.InvestorInformation,
    showHeader: false,
    feature: 'performance',
  },
  {
    name: 'TeamManagement',
    component: TeamManagementScreen,
    icon: TeamIcon,
    showHeader: false,
    feature: 'team',
  },
  {
    name: 'ERecruit',
    component: ERecruitScreen,
    icon: Icon.Career,
    showHeader: false,
    feature: 'eRecruit',
  },
  {
    name: 'ReportGeneration',
    component: ReportGenerationScreen,
    icon: Icon.SearchForDocument,
    showHeader: false,
  },
  {
    name: 'Document',
    component: DocumentsScreen,
    icon: Icon.DocumentCopy,
    showHeader: false,
  },
  {
    name: 'Affiliate',
    component: AffiliateScreen,
    icon: Icon.Teams,
    showHeader: false,
    feature: 'affiliate',
  },
];
