import React from 'react';
import { HeaderBackButton } from '@react-navigation/elements';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components/';
export default function HeaderCloseButton({
  fill,
  size,
  handlePress,
}: {
  fill?: string;
  size?: number;
  handlePress?: () => void
}) {

  const { colors } = useTheme();
  return (
    <HeaderBackButton
      disabled={false}
      backImage={() => (
        <Icon.Close size={size || 24} fill={fill || colors.onBackground} />
      )}
      onPress={handlePress}
      labelVisible={false}
    />
  );
}
