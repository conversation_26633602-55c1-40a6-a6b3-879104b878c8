import React, { useContext, useEffect, useMemo, useState } from 'react';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { MainTabParamList, RootStackParamList } from 'types/navigation';
import { TouchableOpacity, useWindowDimensions } from 'react-native';
import RootBottomTabBarContext from './RootBottomTabBarContext';
import { Box, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { build, country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import Svg, { Path, Rect } from 'react-native-svg';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { tabIconUtil } from './tabIconUtil';
import styled from '@emotion/native';
import DropShadow from 'react-native-drop-shadow';
import { useCheckIsAiBotEnabled } from 'features/aiBot/utils/useCheckIsAiBotEnabled';
import useChatBotAi from 'features/aiBot/hooks/useChatBotAi';
import MobileTooltip from '../../features/aiBot/components/Tooltip/BaseTooltip/MobileTooltip';
import {
  TooltipEventType,
  TooltipType,
} from 'features/aiBot/constants/Tooltip';
import { useAiBotTooltipStore } from 'features/aiBot/store/tooltipStore';
import OtherTabsModal from './OtherTabsModal';
import TabsInModal from './TabsInModal';
import { MainTab } from 'types';
import { getEnvVariable } from 'features/aiBot/utils/misc/misc';
import { EnvVariables } from 'features/aiBot/constants/EnvVariables';

export default function RootBottomTabBar(
  props: BottomTabBarProps & {
    tablist: TabProps['TabList'];
    noShownTabList?: TabProps['TabList'];
  },
) {
  switch (country) {
    case 'ib':
    case 'ph':
    default:
      return <TabBarWithTransparentCutOff {...props} />;
    // return <DefaultTabBar {...props} />;
  }
}

/**
 *
 * animated
 *
 */
// function AnimatedTabBar(
//   props: BottomTabBarProps & { tablist: TabProps['TabList'] },
// ) {

function TabBarWithTransparentCutOff(
  props: BottomTabBarProps & {
    tablist: TabProps['TabList'];
    noShownTabList?: TabProps['TabList'];
  },
) {
  const { colors, space, borderRadius, getElevation } = useTheme();
  const { width } = useWindowDimensions();
  const { bottom } = useSafeAreaInsets();
  const { t } = useTranslation('navigation');
  const curRouteName = props?.state?.routes[props?.state?.index]?.name;
  const [offset, setOffset] = useState(0);
  const isIB = country === 'ib';
  const isBuildEnabled = build === 'dev' || build === 'sit' || build === 'uat';

  const shouldShowNudgeForNewUser = useMemo(() =>
    getEnvVariable<boolean>(EnvVariables.AIBOT_SHOW_NUDGE_FOR_NEW_USER), []);
  const { shouldShowTooltip, setLearnMoreClicked } = useChatBotAi();
  const isAiBotEnabled = useCheckIsAiBotEnabled();

  const safePadding = bottom;
  const barHeight = 100;
  const circleRadius = 76;

  // return <></>;
  const [isOthersMenuOn, setIsOthersMenuOn] = useState(false);
  const modalHandler = () => setIsOthersMenuOn(prev => !prev);
  const [isOthersTabPressed, setIsOthersTabPressed] = useState(false);
  const { toggleShouldHighlightOthers, shouldHighlightOthers } = useContext(
    RootBottomTabBarContext,
  );

  const setShowTooltip = useAiBotTooltipStore(state => state.setShow);
  const setTooltipEvent = useAiBotTooltipStore(state => state.setEvent);
  const setTooltipType = useAiBotTooltipStore(state => state.setType);

  useEffect(() => {
    if (shouldShowTooltip && shouldShowNudgeForNewUser) {
      setShowTooltip(true);
      setTooltipType(TooltipType.AiIntroduceTooltip);
      setTooltipEvent({ type: TooltipEventType.OPEN_WIDGET });
    }
  }, [shouldShowTooltip, setShowTooltip, setTooltipType, setTooltipEvent]);

  const otherTabOnPress = () => {
    // defaultProps?.onPress ? defaultProps?.onPress(e) : null;
    toggleShouldHighlightOthers();
    setIsOthersTabPressed(!isOthersTabPressed);
    modalHandler();
  };

  const onClickAiBotTooltip = () => {
    if (!isAiBotEnabled) return;
    props?.navigation?.navigate('AiBot');
    setShowTooltip(false);
    setLearnMoreClicked(true);
  };

  return (
    <DropShadowContainer>
      {isAiBotEnabled && shouldShowTooltip && (
        <MobileTooltip
          onPressButton={() => onClickAiBotTooltip()}
          shouldTrackCloseButton={true}
        />
      )}
      <Box h={barHeight + safePadding}>
        <Box position="absolute" zIndex={-1}>
          {props?.tablist?.find(tab => tab.name === 'AiBot') == null ? (
            <BarBgWithNoCutoff
              barWidth={width}
              barHeight={barHeight + safePadding}
            />
          ) : (
            <BarBgWithSemiCircleCutoff
              barWidth={width}
              barHeight={barHeight + safePadding}
            />
          )}
        </Box>
        <Row px={space[2]} py={space[3]} h={barHeight}>
          {props?.tablist?.map((tab, index) => {
            const isActiveTab = curRouteName === tab?.name;
            const shouldCurrentTabHightlight =
              isActiveTab && !shouldHighlightOthers;
            const isInOthersTab =
              Boolean(
                props?.noShownTabList?.find(
                  otherTabs => otherTabs?.name === curRouteName,
                ),
              ) ?? false;

            if (tab?.name === 'AiBot') {
              const Icon = tab?.icon;
              return (
                <TabButtonContainer
                  style={{
                    paddingHorizontal: space[3],
                  }}
                  key={tab.name}
                  onLayout={e => {
                    const offset = e.nativeEvent.layout.width - circleRadius;
                    setOffset(offset);
                  }}>
                  <TouchableOpacity
                    onPress={() => onClickAiBotTooltip()}
                    style={{
                      position: 'absolute',
                      height: circleRadius,
                      width: circleRadius,
                      left: Math.floor(offset / 2),
                      top: -space[12],
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Box mb={20}>{Icon}</Box>
                  </TouchableOpacity>
                  <Box flex={1} pb={space[1]} justifyContent="flex-end">
                    <Typography.SmallLabel
                      color={
                        shouldCurrentTabHightlight
                          ? colors.primary
                          : colors.palette.fwdGreyDarker
                      }
                      style={{
                        textAlign: 'center',
                      }}>
                      {t(`tabScreen.${tab.name}`)}
                    </Typography.SmallLabel>
                  </Box>
                </TabButtonContainer>
              );
            }

            if (tab?.name === 'Others') {
              return (
                <TabButtonContainer key={tab.name}>
                  <TouchableOpacity
                    key={tab.name}
                    onPress={otherTabOnPress}
                    // onPress={otherTabOnPress}
                    style={{
                      flex: 1,
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: space[17],
                      width: 64,
                      paddingVertical: space[1],
                      backgroundColor:
                        shouldHighlightOthers || isInOthersTab
                          ? colors.palette.fwdOrange[20]
                          : colors.palette.whiteTransparent,
                      borderRadius: borderRadius.large,
                    }}>
                    <Box flex={1} justifyContent="center">
                      {tabIconUtil({
                        tab: tab?.name,
                        focused: shouldHighlightOthers || isInOthersTab,
                        isClose: shouldHighlightOthers,
                      })}
                    </Box>
                    <Box flex={1} justifyContent="center">
                      <Typography.SmallLabel
                        color={
                          shouldHighlightOthers || isInOthersTab
                            ? colors.primary
                            : colors.palette.fwdGreyDarker
                        }
                        style={{
                          textAlign: 'center',
                        }}>
                        {t(`tabScreen.${tab.name}`)}
                      </Typography.SmallLabel>
                    </Box>
                  </TouchableOpacity>
                  <OtherTabsModal
                    customBottomOffset={0}
                    isModalOn={isOthersMenuOn}
                    otherTabOnPress={otherTabOnPress}
                    OtherChildren={
                      <Box pos="absolute" bottom={0} w="100%" h={97}>
                        <RootBottomTabBar {...props} />
                      </Box>
                    }
                    children={<TabsInModal closeModal={modalHandler} />}
                  />
                </TabButtonContainer>
              );
            }

            return (
              <TabButtonContainer key={tab.name}>
                <TouchableOpacity
                  key={tab.name}
                  onPress={() => {
                    if (shouldHighlightOthers) {
                      toggleShouldHighlightOthers();
                    }
                    props?.navigation?.navigate(tab.name);
                  }}
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: space[17],
                    width: 64,
                    paddingVertical: space[1],
                    backgroundColor: shouldCurrentTabHightlight
                      ? colors.palette.fwdOrange[20]
                      : colors.palette.whiteTransparent,
                    borderRadius: borderRadius.large,
                  }}>
                  <Box flex={1} justifyContent="center">
                    {tabIconUtil({
                      tab: tab?.name,
                      focused: shouldCurrentTabHightlight,
                      isClose: false,
                    })}
                  </Box>
                  <Box flex={1} justifyContent="center">
                    <Typography.SmallLabel
                      color={
                        shouldCurrentTabHightlight
                          ? colors.primary
                          : colors.palette.fwdGreyDarker
                      }
                      style={{
                        textAlign: 'center',
                      }}>
                      {t(`tabScreen.${tab.name}`)}
                    </Typography.SmallLabel>
                  </Box>
                </TouchableOpacity>
              </TabButtonContainer>
            );
          })}
        </Row>
      </Box>
    </DropShadowContainer>
  );
}

function BarBgWithSemiCircleCutoff({
  barHeight,
  barWidth,
}: {
  barHeight: number;
  barWidth: number;
}) {
  const WIDTH = barWidth;
  const HEIGHT = barHeight ?? 90;
  const CORNER_RADIUS = 0;
  const CUTOUT_RADIUS = 44;
  const CUTOUT_LEFT_X = WIDTH / 2 - CUTOUT_RADIUS;
  const CUTOUT_RIGHT_X = WIDTH / 2 + CUTOUT_RADIUS;
  const CUTOUT_CORNER_RADIUS = 16;

  const d = `
M0,${HEIGHT - 2}
L0,${CORNER_RADIUS} Q0,0 ${CORNER_RADIUS},0
L${CUTOUT_LEFT_X - CUTOUT_CORNER_RADIUS},0

  Q ${CUTOUT_LEFT_X},0 ${CUTOUT_LEFT_X},${CUTOUT_CORNER_RADIUS}
  A${CUTOUT_RADIUS},${CUTOUT_RADIUS} 0 0 0 ${CUTOUT_RIGHT_X},0

L${CUTOUT_RIGHT_X + 1.4},${CUTOUT_CORNER_RADIUS} Q ${CUTOUT_RIGHT_X},0 ${CUTOUT_RIGHT_X + CUTOUT_CORNER_RADIUS
    },0

L${WIDTH - CORNER_RADIUS},0 Q${WIDTH},0 ${WIDTH},${CORNER_RADIUS}
L${WIDTH},${HEIGHT - 2}
Z
`;
  return (
    <Svg width={WIDTH} height={HEIGHT} fill={'#fff'}>
      <Path d={d} />
    </Svg>
  );
}

function BarBgWithNoCutoff({
  barHeight,
  barWidth,
}: {
  barHeight: number;
  barWidth: number;
}) {
  const WIDTH = barWidth;
  const HEIGHT = barHeight ?? 90;

  return (
    <Svg width={WIDTH} height={HEIGHT} fill={'#fff'}>
      <Rect x={0} y={0} width={WIDTH} height={HEIGHT} fill={'#fff'} />
    </Svg>
  );
}

const DropShadowContainer = styled(DropShadow)(({ theme }) => {
  return {
    ...theme.getElevation(8),
    position: 'relative',
  };
});

const TabButtonContainer = styled.View(({ theme }) => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  height: theme.space[17],
}));

type MainTabNavigationProp = NavigationProp<MainTabParamList>;

export type TabProps = {
  navigation?: MainTabNavigationProp;
  route?: RouteProp<RootStackParamList, 'Main'>;
  itemId?: number;
  /**
   * screen name, screen component , screen icon show in the tab bar (focused/not focused , dark/light )
   */
  TabList: MainTab[];
  modalHandler: () => void;
  isModalOn: boolean;
};

// export c
