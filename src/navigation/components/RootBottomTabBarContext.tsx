import React from 'react';
import { SharedValue } from 'react-native-reanimated';

const initialState = {
  isShowBottomBar: null,
  hideBottomBar: () => {
    return;
  },
  showBottomBar: () => {
    return;
  },
  safeBottomPadding: 0,
  shouldHighlightOthers: false,
  toggleShouldHighlightOthers: () => {
    return;
  },
};

export const RootBottomBarContext =
  React.createContext<RootBottomBarContextState>(initialState);

interface RootBottomBarContextState {
  isShowBottomBar: SharedValue<boolean> | null;
  hideBottomBar: () => void | null;
  showBottomBar: () => void | null;
  safeBottomPadding: number;
  shouldHighlightOthers: boolean;
  toggleShouldHighlightOthers: () => void;
}

export default RootBottomBarContext;
