import styled from '@emotion/native';

import {
  MainTabParamList,
  OtherMenuTabParamList,
  RootStackParamList,
} from 'types/navigation';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import React, { FC, useContext, useState } from 'react';
import { MainTab } from 'types';

import { tabIconUtil } from './tabIconUtil';
import {
  TouchableOpacity,
  View,
  Pressable,
  GestureResponderEvent,
} from 'react-native';

import { useTheme } from '@emotion/react';

import RootBottomTabBarContext from '../components/RootBottomTabBarContext';
import {
  NavigationProp,
  useNavigation,
  useNavigationState,
} from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { Box, H1, Row, SmallLabel } from 'cube-ui-components';
import { AnimatedModal } from './AnimatedModal';
import TabsInModal from './TabsInModal';
import OtherTabsModal from './OtherTabsModal';

const tabKeyListInOtherMenu: Array<keyof OtherMenuTabParamList> = [
  'Customers',
  'Document',
  'ReportGeneration',
  'Performance',
  'TeamManagement',
  'ERecruit',
];

const checkOtherMenu = [...tabKeyListInOtherMenu] as string[];

const TabIconContainer: FC<{
  icon: React.ReactNode | JSX.Element;
  tabName: string;
  isFocused: boolean;
}> = ({ icon, tabName, isFocused }) => {
  const {
    colors: { primary, placeholder },
    sizes,
  } = useTheme();

  const IconText = styled.Text({
    marginTop: 2,
    fontSize: sizes[3],
    color: isFocused ? primary : placeholder,
  });

  return (
    <>
      {icon}
      <IconText>{tabName}</IconText>
    </>
  );
};

type TabButtonComponentProp = {
  name: MainTab['name'];
  icon: MainTab['icon'];
  focusedIcon: MainTab['focusedIcon'];
  defaultProps?: BottomTabBarButtonProps & {
    onPress: (
      e: GestureResponderEvent,
      navigate: NavigationProp<RootStackParamList>,
    ) => void;
  };
  isModalOn: boolean;
  modalHandler: () => void;
  hideTitle?: boolean;
};

export const MainTabButton: FC<TabButtonComponentProp> = ({
  name,
  isModalOn,
  modalHandler,
  defaultProps,
  icon,
  focusedIcon,
  hideTitle = false,
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const { shouldHighlightOthers, toggleShouldHighlightOthers } = useContext(
    RootBottomTabBarContext,
  );

  const theme = useTheme();
  const {
    colors: {
      palette: { fwdGreyDarker, fwdOrange },
    },
    sizes,
  } = theme;

  const routeNames = useNavigationState(state => state.routeNames) as Array<
    keyof MainTabParamList
  >;
  const routeIndex = useNavigationState(state => state.index);

  const currentActive = name === routeNames[routeIndex];
  const { t } = useTranslation('navigation');

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <TouchableOpacity
      {...defaultProps}
      activeOpacity={1}
      onPress={e => {
        defaultProps?.onPress ? defaultProps?.onPress(e, navigation) : null;

        shouldHighlightOthers ? toggleShouldHighlightOthers() : null;
        const tabPress = () => {
          console.log('is Modal On____');
          modalHandler();
          toggleShouldHighlightOthers();
        };

        isModalOn
          ? tabPress()
          : // ?tabOnPress()
            null;
      }}>
      {(isFocus || currentActive) && !isModalOn ? focusedIcon : icon}

      {!hideTitle && (
        <SmallLabel
          style={{
            textAlign: 'center',
            // marginTop: sizes[1],
            color:
              (isFocus || currentActive) && !isModalOn
                ? fwdOrange[100]
                : fwdGreyDarker,
          }}>
          {t(`tabScreen.${name}`)}
        </SmallLabel>
      )}
    </TouchableOpacity>
  );
};

export const OtherMenuTabButton: FC<TabButtonComponentProp> = ({
  name,
  isModalOn,
  modalHandler,
  defaultProps,
  icon,
  focusedIcon,
}) => {
  const [isFocus, setIsFocus] = useState(false);
  tabIconUtil({ tab: 'Others', focused: isFocus, isClose: isModalOn });
  // console.log(children);
  const theme = useTheme();
  const {
    colors: { palette },
    sizes,
  } = theme;

  const { toggleShouldHighlightOthers } = useContext(RootBottomTabBarContext);

  const routeNames = useNavigationState(state => state.routeNames);
  const routeIndex = useNavigationState(state => state.index);

  const isInOtherMenu = checkOtherMenu.includes(routeNames[routeIndex]);

  const activeOtherMenuIcon = tabIconUtil({
    tab: 'Others',
    focused: isInOtherMenu,
    isClose: isModalOn,
  });

  const OtherTabIcon = () => {
    if (isInOtherMenu) {
      return (
        <TabIconContainer isFocused icon={activeOtherMenuIcon} tabName={name} />
      );
    }

    if (isModalOn) {
      return <TabIconContainer isFocused icon={focusedIcon} tabName={name} />;
    }

    // if (!isInOtherMenu) {
    return <TabIconContainer isFocused={false} icon={icon} tabName={name} />;
    // }
  };

  const otherTabOnPress = () => {
    // defaultProps?.onPress ? defaultProps?.onPress(e) : null;
    // console.log('OtherMenuTabButton: ');
    toggleShouldHighlightOthers();
    setIsFocus(!isFocus);
    modalHandler();
  };

  return (
    <TouchableOpacity
      style={defaultProps?.style}
      activeOpacity={1}
      onPress={otherTabOnPress}>
      {/* {renderIconHandler()} */}
      <OtherTabIcon />
      {/* {renderIconHandler()} */}

      <OtherTabsModal
        isModalOn={isModalOn}
        otherTabOnPress={otherTabOnPress}
        children={<TabsInModal closeModal={modalHandler} />}
      />
    </TouchableOpacity>
  );
};
