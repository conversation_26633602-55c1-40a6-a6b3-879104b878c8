import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { Box, Row, SvgIconProps, Typography } from 'cube-ui-components';
import { OtherMenuTab } from 'navigation/components/TabsConfig';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { usePortal } from '@gorhom/portal';

import { MainTab } from 'types';

export default function OthersTabPopUpCard({
  props,
  bottom,
  drawerTabsData,
  selectMoreMenuName,
  setIsShowPop,
  setSelectMoreMenuName,
  setIsOtherMenuOpen,
  pop,
}: {
  props: DrawerContentComponentProps;
  bottom: number;
  selectMoreMenuName: string;
  drawerTabsData: (Omit<MainTab | OtherMenuTab, 'icon'> & {
    icon: (props: SvgIconProps) => JSX.Element;
    tabletIcon?: ((props: SvgIconProps) => JSX.Element) | undefined;
  })[];
  setIsShowPop: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectMoreMenuName: React.Dispatch<React.SetStateAction<string>>;
  setIsOtherMenuOpen: React.Dispatch<React.SetStateAction<boolean>>;
  pop: ReturnType<typeof usePortal>;
}) {
  const { t } = useTranslation('navigation');
  const { colors, sizes } = useTheme();

  return (
    <MenuContainer>
      <TabMenuContainer bottom={bottom}>
        <Row
          gap={sizes[4]}
          px={sizes[4]}
          pt={sizes[5]}
          pb={sizes[4]}
          ml={sizes[2]}>
          <Typography.H8
            color={colors.palette.fwdDarkGreen[100]}
            fontWeight={'bold'}>
            {t(`tabScreen.More`)}
          </Typography.H8>
        </Row>

        {drawerTabsData.map((tab, i) => {
          const { name, icon: Icon, onPress, component } = tab;
          return (
            <Button
              bgColor={
                selectMoreMenuName === name
                  ? colors.palette.fwdOrange[20]
                  : colors.background
              }
              key={'OtherMenuPopup__' + name}
              underlayColor={colors.palette.fwdOrange[20]}
              onPress={e => {
                setIsShowPop(false);
                if (component) {
                  setSelectMoreMenuName(name);
                  setIsOtherMenuOpen(true);
                } else if (!selectMoreMenuName) {
                  setIsOtherMenuOpen(false);
                }
                pop.removePortal('OtherMenu');
                onPress ? onPress(e) : props.navigation.navigate(name);
              }}>
              <Row alignItems="flex-start">
                <Box
                  gap={sizes[2]}
                  width={sizes[11]}
                  height={sizes[11]}
                  borderRadius={sizes[2]}
                  alignItems="center"
                  justifyContent="center"
                  backgroundColor={colors.palette.fwdOrange[5]}>
                  <Icon
                    fill={colors.palette.fwdOrange[100]}
                    height={sizes[7]}
                    width={sizes[7]}
                  />
                </Box>

                <Typography.LargeBody
                  color={colors.palette.fwdDarkGreen[100]}
                  style={{
                    alignSelf: 'center',
                    textAlign: 'center',
                    maxWidth: '94%',
                    paddingHorizontal: sizes[4],
                  }}>
                  {t(`tabScreen.${name}` as any)}
                </Typography.LargeBody>
              </Row>
            </Button>
          );
        })}
      </TabMenuContainer>
    </MenuContainer>
  );
}

const TabMenuContainer = styled(Box)(
  ({ theme, bottom }: { theme?: Theme; bottom: number }) => ({
    marginLeft: theme?.sizes[6],
    width: '30%',
    position: 'absolute',
    backgroundColor: theme?.colors.background,
    borderRadius: theme?.sizes[4],
    paddingBottom: theme?.sizes[4],
    overflow: 'visible',
    shadowColor: theme?.colors.palette.black,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 5,
    bottom,
  }),
);

const Button = styled.TouchableHighlight<{ bgColor: string }>(
  ({ theme, bgColor }) => ({
    paddingHorizontal: theme.space[4],
    paddingVertical: theme.space[3],
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: bgColor,
    flex: 1,
  }),
);

const MenuContainer = styled(Box)(({ theme: { sizes, colors } }) => ({
  marginLeft: sizes[6],
  width: '100%',
  height: '100%',
  position: 'absolute',
  left: '4%',
  backgroundColor: colors.palette.whiteTransparent,
}));
