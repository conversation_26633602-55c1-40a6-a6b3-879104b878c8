import { MainTabParamList } from 'types/navigation';
import React from 'react';
import { View, Pressable } from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Row } from 'cube-ui-components';
import { AnimatedModal } from './AnimatedModal';
import useBoundStore from 'hooks/useBoundStore';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useGetTabsMobile } from './TabsConfig';

export default function OtherTabsModal({
  isModalOn,
  otherTabOnPress,
  children,
  OtherChildren,
  customBottomOffset,
}: {
  isModalOn: boolean;
  otherTabOnPress: () => void;
  OtherChildren?: JSX.Element;
  customBottomOffset?: number;
  children?: JSX.Element;
}) {
  return (
    <View style={{ position: 'absolute', bottom: 100 }}>
      <AnimatedModal
        isNoPadding
        customBottomOffset={customBottomOffset}
        showFrom={'bottom'}
        isForSearching={false}
        visible={isModalOn}
        onClose={otherTabOnPress}
        otherChildren={
          <>
            {OtherChildren ? OtherChildren : null}
            <TransparentBottomBar
              otherTabOnPress={otherTabOnPress}
              disabled={!isModalOn}
            />
          </>
        }>
        {children ?? <></>}
      </AnimatedModal>
    </View>
  );
}

const TransparentBottomBar = ({
  otherTabOnPress,
  disabled,
}: {
  otherTabOnPress: () => void;
  disabled?: boolean;
}) => {
  const { bottomBarHeightInZus } = useBoundStore();
  const navigation = useNavigation<NavigationProp<MainTabParamList>>();

  const { top, bottom } = useSafeAreaInsets();

  const mainTabsMobile = useGetTabsMobile().mainTabs;

  return (
    <Row style={{ position: 'absolute', bottom: bottom / 2 }}>
      {mainTabsMobile.map(({ name }) => {
        return (
          <Pressable
            disabled={disabled}
            key={'animatedModal_' + name}
            onPress={() => {
              if (name === 'Others') {
                otherTabOnPress();
                return;
              }
              navigation.navigate(name);
              otherTabOnPress();
            }}
            style={{
              flex: 1,
              height: bottomBarHeightInZus - bottom / 2,
            }}
          />
        );
      })}
    </Row>
  );
};
