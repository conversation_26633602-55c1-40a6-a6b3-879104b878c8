import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { DrawerContentComponentProps } from '@react-navigation/drawer';
import {
  Column,
  Icon,
  Row,
  SvgIconProps,
  Typography,
} from 'cube-ui-components';
import FwdCubeSquaredLogoOnOrangeSVG from 'features/home/<USER>/FwdCubeSquaredLogoOnOrangeSVG';
import TeamManagementScreen from 'screens/TeamManagementScreen';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import DrawerTabStyled from './DrawerTabStyled';
import {
  CubeUIComponentIconProps,
  OtherMenuTab,
} from 'navigation/components/TabsConfig';
import TeamIcon from 'navigation/icons/TeamIcon';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  GestureResponderEvent,
  Linking,
  View,
  LayoutChangeEvent,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AffiliateScreen from 'screens/AffiliateScreen';
import DocumentsScreen from 'screens/DocumentsScreen';
import ERecruitScreen from 'screens/ERecruitScreen';
import HomeScreen from 'screens/HomeScreen';
import LeadScreen from 'screens/LeadScreen';
import PerformanceScreen from 'screens/PerformanceScreen';
import PoliciesScreen from 'screens/PoliciesScreen';
import ProposalScreen from 'screens/ProposalScreen';
import ReportGenerationScreen from 'screens/ReportGenerationScreen';
import { CHANNELS } from 'types/channel';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { NavigationState } from '@react-navigation/native';
import MoreMenuTabButton from '../../components/MoreMenuTabButton';
import { usePortal } from '@gorhom/portal';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { MainTab } from 'types';
import OthersTabPopUpCard from 'navigation/components/OthersTabPopUpCard';

const getActiveRouteState = function (
  routes: NavigationState['routes'],
  index: number,
  name: string,
) {
  return routes[index].name.toLowerCase().indexOf(name.toLowerCase()) >= 0;
};

export default function MYRootDrawerTabBar(props: DrawerContentComponentProps) {
  const { colors, space } = useTheme();
  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { data: agentProfile } = useGetAgentProfile();
  const pop = usePortal();
  const hasPermission = useHasPermission();
  const [otherButtonY, setOtherButtonY] = useState(0);
  const [isOtherMenuOpen, setIsOtherMenuOpen] = useState(false);
  const [isShowPop, setIsShowPop] = useState(false);
  const [selectMoreMenuName, setSelectMoreMenuName] = useState('');
  const [contentHeight, setContentHeight] = useState(0);
  const logoHeight = 64;
  const maxLayoutHeight = 82;
  const minLayoutHeight = 67;
  const bottomPadding = space[4] + space[1];
  const maxMenuNum =
    Math.floor((contentHeight - logoHeight) / maxLayoutHeight) || 0;

  const drawerTabsData = drawerTabsMY?.filter(function (item) {
    if (!countryModuleSellerConfig[item.name]) {
      return null;
    }
    if (item.feature && !hasPermission(item.feature)) {
      console.log('check the feature', item.feature, 'is not enabled');
      return null;
    }

    return item;
  });

  const drawerTabsNormalData =
    drawerTabsData?.length > maxMenuNum
      ? drawerTabsData?.slice(0, maxMenuNum - 1)
      : drawerTabsData;

  const isShowMoreButton = drawerTabsData?.length > maxMenuNum;

  const popupBottom =
    contentHeight - otherButtonY - minLayoutHeight - logoHeight - bottomPadding;

  const PortalContent = (
    <OthersTabPopUpCard
      props={props}
      bottom={popupBottom}
      selectMoreMenuName={selectMoreMenuName}
      drawerTabsData={drawerTabsData?.slice(
        maxMenuNum - 1,
        drawerTabsData?.length,
      )}
      setIsShowPop={setIsShowPop}
      setSelectMoreMenuName={setSelectMoreMenuName}
      setIsOtherMenuOpen={setIsOtherMenuOpen}
      pop={pop}
    />
  );
  return (
    <>
      <TopBackgroundPaddingForStatusBar />
      <Row flex={1}>
        <Column flex={1}>
          <View
            onLayout={e => {
              setContentHeight(e.nativeEvent.layout.height);
            }}
            style={{
              flex: 1,
              gap: space[2],
              backgroundColor: colors.primary,
              paddingTop: space[2],
            }}>
            <View style={{ alignItems: 'center' }}>
              <FwdCubeSquaredLogoOnOrangeSVG />
            </View>
            <Column flex={1} gap={space[1]}>
              {drawerTabsNormalData.map(
                ({ name, icon: Icon, tabletIcon: TabletIcon, onPress }) => {
                  let focused = false;
                  if (!isOtherMenuOpen && selectMoreMenuName === '') {
                    focused = getActiveRouteState(
                      props.state.routes,
                      props.state.index,
                      name,
                    );
                  }

                  const TabIcon =
                    isTabletMode && TabletIcon
                      ? TabletIcon
                      : (Icon as CubeUIComponentIconProps);

                  return (
                    <DrawerTabButton
                      key={name}
                      name={name}
                      Icon={TabIcon}
                      focused={focused}
                      navigationHandler={e => {
                        if (isOtherMenuOpen) {
                          pop.removePortal('OtherMenu');
                          setIsOtherMenuOpen(false);
                          setIsShowPop(false);
                          setSelectMoreMenuName('');
                        }
                        onPress ? onPress(e) : props.navigation.navigate(name);
                      }}
                    />
                  );
                },
              )}
              {isShowMoreButton && (
                <MoreMenuTabButton
                  focused={isOtherMenuOpen}
                  selectMenuName={selectMoreMenuName}
                  isShowPop={isShowPop}
                  onLayout={(nativeEvent: LayoutChangeEvent) => {
                    setOtherButtonY(nativeEvent.nativeEvent.layout.y);
                  }}
                  onPress={() => {
                    if (selectMoreMenuName != '') {
                      setIsOtherMenuOpen(true);
                      setIsShowPop(!isShowPop);
                      !isShowPop
                        ? pop.addPortal('OtherMenu', PortalContent)
                        : pop.removePortal('OtherMenu');
                    } else {
                      setIsShowPop(!isShowPop);
                      setIsOtherMenuOpen(!isOtherMenuOpen);
                      !isShowPop
                        ? pop.addPortal('OtherMenu', PortalContent)
                        : pop.removePortal('OtherMenu');
                    }
                  }}
                />
              )}
            </Column>
          </View>
        </Column>
      </Row>
    </>
  );
}

function DrawerTabButton({
  name,
  Icon,
  focused,
  navigationHandler,
}: {
  name: (MainTab | OtherMenuTab)['name'];
  Icon: CubeUIComponentIconProps;
  focused: boolean;
  navigationHandler: (e: GestureResponderEvent) => void;
}) {
  const [lineNumber, setLineNumber] = useState(0);
  const { colors, space } = useTheme();
  const { t } = useTranslation('navigation');

  const onPressHandler = (e: GestureResponderEvent) => {
    if (!countryModuleSellerConfig[name]) {
      console.log(
        '🔴🔴🔴 \nfile: RootDrawerTabBar.tsx:102 DrawerTabButton onPressHandler 📺Screen',
        name,
        `is not❌ implemented. Please check ~${name}~ is set to true module config at`,
        `src/utils/config/module/${country}/sellerExp/index.ts \n🔴🔴🔴 `,
      );
    }
    navigationHandler(e);
  };
  return (
    <View>
      <DrawerTabStyled.TouchableButton
        onPress={onPressHandler}
        lineNumber={lineNumber}
        focused={focused}>
        <View style={{ alignItems: 'center' }}>
          <Icon fill={colors.background} height={space[7]} width={space[7]} />
          <Typography.SmallLabel
            color={colors.background}
            style={{
              alignSelf: 'center',
              textAlign: 'center',
              maxWidth: '94%',
            }}>
            {t(`tabScreen.${name}` as any)}
          </Typography.SmallLabel>
        </View>
      </DrawerTabStyled.TouchableButton>
    </View>
  );
}

const TopBackgroundPaddingForStatusBar = styled.View(() => {
  const { top } = useSafeAreaInsets();
  const { colors } = useTheme();
  return {
    paddingTop: top,
    backgroundColor: colors.background,
  };
});

export const drawerTabsMY: Array<
  Omit<MainTab | OtherMenuTab, 'icon'> & {
    icon: (props: SvgIconProps) => JSX.Element;
    tabletIcon?: (props: SvgIconProps) => JSX.Element;
  }
> = [
  {
    name: 'Home',
    component: HomeScreen,
    icon: Icon.Home,
    showHeader: false,
  },
  {
    name: 'Lead',
    component: LeadScreen,
    icon: TeamIcon,
    showHeader: false,
    feature: 'lead',
  },
  {
    name: 'Proposals',
    component: ProposalScreen,
    icon: Icon.Document,
    showHeader: false,
    feature: 'savedProposal',
  },
  {
    name: 'Policies',
    component: PoliciesScreen,
    icon: Icon.DocumentCertified,
    showHeader: true,
    feature: 'policy',
  },
  {
    name: 'Performance',
    component: PerformanceScreen,
    icon: Icon.InvestorInformation,
    showHeader: false,
    feature: 'performance',
  },
  {
    name: 'TeamManagement',
    component: TeamManagementScreen,
    icon: TeamIcon,
    showHeader: false,
    feature: 'team',
  },
  {
    name: 'ERecruit',
    component: ERecruitScreen,
    icon: Icon.Career,
    showHeader: false,
    feature: 'eRecruit',
  },
  {
    name: 'ReportGeneration',
    component: ReportGenerationScreen,
    icon: Icon.SearchForDocument,
    showHeader: false,
  },
  {
    name: 'Document',
    component: DocumentsScreen,
    icon: Icon.DocumentCopy,
    showHeader: false,
  },
  {
    name: 'Affiliate',
    component: AffiliateScreen,
    icon: Icon.Teams,
    showHeader: false,
  },
  {
    name: 'myLMS',
    icon: Icon.Book,
    showHeader: false,
    onPress: () => {
      Linking.openURL('https://fwdmylms.disprz.com/');
    },
    feature: 'myLMS',
  },
  {
    name: 'Merchandise',
    icon: Icon.Shopping,
    showHeader: false,
    onPress: () => {
      Linking.openURL('https://fwdtmerch.com/');
    },
  },
];
