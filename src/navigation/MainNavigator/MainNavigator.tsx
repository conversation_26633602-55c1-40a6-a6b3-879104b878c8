import React, { useEffect } from 'react';
import useBoundStore from 'hooks/useBoundStore';
import MainNavigatorTablet from './MainNavigator.tablet';
import MainNavigatorPhone from './MainNavigator.phone';
import GATracking from 'utils/helper/gaTracking/gaTracking';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import LastNotificationHandler from 'navigation/components/LastNotificationHandler';

export default function MainNavigator() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { agentCode, channel } = useBoundStore(state => state.auth);
  useEffect(() => {
    if (agentCode) {
      GATracking.trackAgentLogin(agentCode, channel);
    }
    return () => {
      if (agentCode) {
        GATracking.trackAgentLogout();
      }
    };
  }, [agentCode]);

  return (
    <>
      {isTabletMode ? <MainNavigatorTablet /> : <MainNavigatorPhone />}
      <LastNotificationHandler />
    </>
  );
}
