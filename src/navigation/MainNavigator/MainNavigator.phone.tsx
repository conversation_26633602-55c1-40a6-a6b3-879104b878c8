// * For the Bottom Tab

import {
  MainTabParamList,
  OtherMenuTabParamList,
  ShownMainTabParamList,
} from 'types/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import TabBar from 'navigation/components/RootBottomTabBar';
import { Text, Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTheme } from '@emotion/react';
import Animated, {
  FadeInUp,
  FadeOutDown,
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import RootBottomTabBarContext from 'navigation/components/RootBottomTabBarContext';
import useBoundStore from 'hooks/useBoundStore';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import {
  MainTabButton,
  OtherMenuTabButton,
} from 'navigation/components/MenuTabButton';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import {
  getFocusedRouteNameFromRoute,
  useFocusEffect,
  useRoute,
} from '@react-navigation/native';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useHasPermission } from 'hooks/useCheckClientScope';

const Tab = createBottomTabNavigator<MainTabParamList>();

// type MainNavigatorProps = {
//   isModalOn: boolean;
//   modalHandler: () => void;
// };

// * For the Bottom Tab
export default function PhoneMainBottomTabNavigator() {
  const { colors, space } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();

  const hasPermission = useHasPermission();
  // For bottom bar animation
  const isShowBottomBar = useSharedValue(true);

  const [bottomBarHeight, setBottomBarHeight] = useState(97);
  const [shouldHighlightOthers, setShouldHighlightOthers] = useState(false);

  const [isOthersMenuOn, setIsOthersMenuOn] = useState(false);
  const modalHandler = () => setIsOthersMenuOn(prev => !prev);

  const { setBottomBarHeightInZus } = useBoundStore().appActions;

  const route = useRoute();
  const routeName = getFocusedRouteNameFromRoute(route);

  useEffect(() => {
    isShowBottomBar.value = !['AiBot'].includes(routeName ?? '');
  }, [routeName]);

  const hideBottomBar = useCallback(() => {
    'worklet';
    if (!isShowBottomBar.value) return;
    isShowBottomBar.value = false;
  }, []);

  const showBottomBar = useCallback(() => {
    'worklet';
    if (isShowBottomBar.value) return;
    isShowBottomBar.value = true;
  }, []);

  // const animatedBarHeight = useDerivedValue(() => {
  //   return isShowBottomBar.value ? bottomBarHeight : 0;
  // }, []);

  const barStyle = useAnimatedStyle(
    () => ({
      ...(bottomBarHeight && {
        bottom: isShowBottomBar.value ? 0 : -bottomBarHeight,
        height: isShowBottomBar.value ? bottomBarHeight : 0,
        position: 'absolute',
        width: '100%',
      }),
    }),
    [],
  );

  const toggleShouldHighlightOthers = () => {
    setShouldHighlightOthers(!shouldHighlightOthers);
  };
  const isIos = Platform.OS === 'ios';
  const heightOfAnimatedBar = 4;

  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );
  const resetFnaStoreState = useFnaStore(state => state.resetFnaStoreState);
  useFocusEffect(
    useCallback(() => {
      resetFnaStoreState();
      clearActiveCase();
    }, []),
  );

  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  const mainTabsMobile = useGetTabsMobile().mainTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  return (
    <RootBottomTabBarContext.Provider
      value={{
        shouldHighlightOthers,
        toggleShouldHighlightOthers,
        isShowBottomBar,
        hideBottomBar,
        showBottomBar,
        safeBottomPadding: bottomBarHeight,
      }}>
      <Tab.Navigator
        backBehavior="history"
        tabBar={props => (
          <Animated.View
            layout={LinearTransition}
            exiting={FadeOutDown}
            entering={FadeInUp}
            onLayout={e => {
              e.nativeEvent.layout.height > bottomBarHeight &&
                setBottomBarHeight(e.nativeEvent.layout.height);
              setBottomBarHeightInZus(e.nativeEvent.layout.height);
            }}
            style={[barStyle]}>
            <TabBar
              noShownTabList={otherMenuTabsMobile}
              tablist={mainTabsMobile}
              {...props}
            />
          </Animated.View>
        )}
        screenOptions={{
          tabBarLabelPosition: 'below-icon',
          headerShown: false,
          tabBarActiveTintColor: colors.primary,
          tabBarStyle: {
            paddingTop: space[1],
            borderTopWidth: 0,
            elevation: 0,
            height: isIos
              ? space[24] - heightOfAnimatedBar
              : space[isWideScreen ? 16 : 20] - heightOfAnimatedBar,
          },
          tabBarItemStyle: {
            justifyContent: 'flex-start',
            paddingTop: space[1],
          },
        }}>
        {mainTabsMobile.map(
          (
            {
              name,
              component,
              icon,
              focusedIcon,
              onPress,
              darkIcon,
              focusedDarkIcon,
              showHeader,
              headerTitle,
              hideTitle,
            },
            i,
          ) => (
            <Tab.Screen
              key={i}
              name={name}
              component={component}
              options={{
                headerShown: showHeader,
                headerTitle: headerTitle,
                headerStatusBarHeight: 0,

                header: ({ navigation, route, options }) => {
                  return (
                    <ScreenHeader
                      showBottomSeparator={false}
                      route={route.name as keyof ShownMainTabParamList}
                    />
                  );
                },

                tabBarButton: props => {
                  if (name === 'Others') {
                    return (
                      <OtherMenuTabButton
                        name={name}
                        isModalOn={isOthersMenuOn}
                        icon={icon}
                        modalHandler={modalHandler}
                        focusedIcon={focusedIcon}
                        defaultProps={props}
                      />
                    );
                  }

                  return (
                    <MainTabButton
                      name={name}
                      defaultProps={onPress ? { ...props, onPress } : props}
                      isModalOn={isOthersMenuOn}
                      modalHandler={modalHandler}
                      icon={icon}
                      focusedIcon={focusedIcon}
                      hideTitle={hideTitle}
                    />
                  );
                },
              }}
            />
          ),
        )}
        {otherMenuTabsMobile
          .filter(menuItem => menuItem.navigationType === 'tab')
          .map(({ name, component, showHeader }) => (
            <Tab.Screen
              key={'otherMenuTabsMobile_' + name}
              name={name}
              component={component}
              options={{
                headerShown: showHeader,
                header: ({ route }) => (
                  <ScreenHeader
                    route={route.name as keyof OtherMenuTabParamList}
                  />
                ),

                headerRight: () => <Text>right</Text>,
                tabBarItemStyle: {
                  display: 'none',
                },
              }}
            />
          ))}
      </Tab.Navigator>
    </RootBottomTabBarContext.Provider>
  );
}
