import styled from '@emotion/native';
import { useTheme } from '@emotion/react';

const TouchableButton = styled.TouchableOpacity(
  ({ lineNumber, focused }: { lineNumber: number; focused: boolean }) => {
    const { colors, space } = useTheme();

    return {
      backgroundColor: focused
        ? 'rgba(255, 255, 255, 0.20)'
        : colors.palette.whiteTransparent,
      paddingVertical: space[3],
      marginHorizontal: space[2],
      borderRadius: space[4],
      maxHeight:space[21],
      justifyContent:'center'
    };
  },
);

const DrawerTabStyled = {
  TouchableButton,
};

export default DrawerTabStyled;
