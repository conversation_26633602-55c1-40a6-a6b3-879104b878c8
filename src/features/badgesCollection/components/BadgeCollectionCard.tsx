import React, { useState } from 'react';
import { Badge, ExtraLargeBody, LargeBody, XView } from 'cube-ui-components';
import styled from '@emotion/native';
import { Theme } from '@emotion/react';
import BadgeSVG from 'features/home/<USER>/BadgeSVG';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import i18n from 'utils/translation';

export const CARD_WIDTH = 312;
const ACTIVE_CARD_HEIGHT = 521;
const INACTIVE_CARD_HEIGHT = 473;
const CARD_PADDING = 16;
const BORDER_WIDTH = 4;
const BADGE_SIZE = 36;

interface ActiveCardOuterStyleWrapperProps {
  showOuter: boolean;
  children?: React.ReactNode;
}

interface BadgeCollectionCardProps {
  isActive: boolean;
  isCurrentMonth: boolean;
}

export default function BadgeCollectionCard({
  isActive,
  isCurrentMonth,
}: BadgeCollectionCardProps) {
  return (
    <ActiveCardOuterStyleWrapper showOuter={isActive && isCurrentMonth}>
      <BaseBadgeCollectionCard
        isActive={isActive}
        isCurrentMonth={isCurrentMonth}
      />
    </ActiveCardOuterStyleWrapper>
  );
}

// Outer
function ActiveCardOuterStyleWrapper({
  showOuter,
  children,
}: ActiveCardOuterStyleWrapperProps) {
  return <Outer showOuter={showOuter}>{children}</Outer>;
}

// Base card
function BaseBadgeCollectionCard({
  isActive,
  isCurrentMonth,
}: BadgeCollectionCardProps) {
  const [badgeGapDimension, setBadgeGapDimension] = useState(0);

  return (
    <Card isActive={isActive} isCurrentMonth={isCurrentMonth}>
      {isCurrentMonth ? (
        <CurrentMonthDate fontWeight="bold">Active Month 2023</CurrentMonthDate>
      ) : (
        <Date fontWeight="bold">Inactive Month 2023</Date>
      )}

      <BadgesContainer
        style={{ gap: badgeGapDimension }}
        onLayout={e => {
          const containerWidth = e.nativeEvent.layout.width || 0;
          const gapWidth = Math.round((containerWidth - BADGE_SIZE * 5) / 4);
          containerWidth && setBadgeGapDimension(gapWidth);
        }}>
        {Array(31)
          .fill('badge')
          .map((badge, index) => (
            <BadgeSVG
              key={'badge_' + index}
              width={BADGE_SIZE}
              height={BADGE_SIZE}
              // date={`${index + 1} Dec`}
            />
          ))}
      </BadgesContainer>
    </Card>
  );
}

const Card = styled.View<{
  isActive: boolean;
  isCurrentMonth: boolean;
  theme?: Theme;
}>(({ isActive, isCurrentMonth, theme }) => ({
  width: CARD_WIDTH,
  height: isActive ? ACTIVE_CARD_HEIGHT : INACTIVE_CARD_HEIGHT,
  backgroundColor: theme.colors.background,
  padding: CARD_PADDING,
  borderRadius: theme.borderRadius.large,
  borderWidth: isCurrentMonth ? BORDER_WIDTH : undefined,
  borderColor: theme.colors.primary,
}));

const Outer = styled.View<{
  showOuter: boolean;
  theme?: Theme;
}>(({ showOuter, theme }) => ({
  width: showOuter ? CARD_WIDTH + BORDER_WIDTH * 2 : undefined,
  height: showOuter ? ACTIVE_CARD_HEIGHT + BORDER_WIDTH * 2 : undefined,
  backgroundColor: showOuter ? theme.colors.primaryVariant : undefined,
  borderRadius: showOuter ? theme.borderRadius.large : undefined,
  alignItems: showOuter ? 'center' : undefined,
  justifyContent: showOuter ? 'center' : undefined,
}));

const CurrentMonthDate = styled(ExtraLargeBody)(({ theme }) => ({
  color: theme.colors.primary,
}));

const Date = styled(LargeBody)(({ theme }) => ({
  color: theme.colors.secondaryVariant,
}));

const BadgesContainer = styled(XView)(({ theme }) => ({
  width: '100%',
  flexWrap: 'wrap',
  marginTop: theme.space[4],
}));
