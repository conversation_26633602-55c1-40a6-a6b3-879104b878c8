import React, { Fragment } from 'react';
import styled from '@emotion/native';
import { ExtraLargeBody, H5, H6, H8, XView } from 'cube-ui-components';
import BackgroundBadgeSVG from '../assets/BackgroundBadgeSVG';
import { useTheme } from '@emotion/react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import i18n from 'utils/translation';
import { useTranslation } from 'react-i18next';

type SummaryConfigType = 'monthly' | 'total' | 'bestRecordMonth';

const SUMMARY_CONFIG = [
  {
    type: 'monthly',
    label: 'Badges in\nthis month',
    tabletLabel: 'Badges in this month',
    value: 10,
  },
  {
    type: 'total',
    label: 'Total\nbadges',
    tabletLabel: 'Total badges',
    value: 23,
  },
  {
    type: 'bestRecordMonth',
    label: 'Best record month',
    tabletLabel: 'Best record month',
    value: 'November 2022',
  },
] as const;

export default function CollectionSummaryCard() {
  const { space } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();

  return (
    <Card>
      <BackgroundBadgeContainer isWideScreen={isWideScreen}>
        <BackgroundBadgeSVG />
      </BackgroundBadgeContainer>

      <Title fontWeight="bold">Collection summary</Title>

      <XView style={{ marginBottom: space[3] }}>
        {SUMMARY_CONFIG.map(({ type, label, tabletLabel, value }, index) => (
          <Fragment key={'SUMMARY_CONFIG_' + type}>
            <SummaryItem
              type={type}
              label={isWideScreen ? tabletLabel : label}
              value={value}
            />

            {SUMMARY_CONFIG.length - 1 > index && <ColSeparator />}
          </Fragment>
        ))}
      </XView>
    </Card>
  );
}

interface SummaryItemProps {
  type: SummaryConfigType;
  label: string;
  value: number | string;
}

function SummaryItem({ type, label, value }: SummaryItemProps) {
  return (
    <ItemContainer type={type}>
      <H8>{label}</H8>
      {type !== 'bestRecordMonth' ? (
        <ItemValue fontWeight="bold">{value}</ItemValue>
      ) : (
        <SmallItemValue fontWeight="bold">{value}</SmallItemValue>
      )}
    </ItemContainer>
  );
}

const Card = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.primaryVariant2,
  borderRadius: theme.borderRadius.small,
  paddingTop: theme.space[3],
  paddingLeft: theme.space[3],
  overflow: 'hidden',
}));

const BackgroundBadgeContainer = styled.View<{ isWideScreen: boolean }>(
  ({ isWideScreen }) => ({
    position: 'absolute',
    bottom: isWideScreen ? -24 : -4,
    right: 0,
  }),
);

const Title = styled(ExtraLargeBody)(({ theme }) => ({
  marginBottom: theme.space[2],
}));

const ItemContainer = styled.View<{ type: SummaryConfigType }>(({ type }) => ({
  width: type !== 'bestRecordMonth' ? 'auto' : '40%',
}));

const ItemValue = styled(H5)(({ theme }) => ({
  color: theme.colors.primary,
}));

const SmallItemValue = styled(H6)(({ theme }) => ({
  color: theme.colors.primary,
}));

const ColSeparator = styled.View(({ theme }) => ({
  width: 1,
  height: '100%',
  marginLeft: theme.space[6],
  marginRight: theme.space[3],
  backgroundColor: theme.colors.background,
}));
