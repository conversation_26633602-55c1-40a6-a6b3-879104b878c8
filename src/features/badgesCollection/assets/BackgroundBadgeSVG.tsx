import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, Defs, ClipPath } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function BackgroundBadgeSVG(props: SvgIconProps) {
  return (
    <Svg width={127} height={126} viewBox="0 0 127 126" fill="none" {...props}>
      <G clipPath="url(#clip0_8754_96683)" opacity={0.1}>
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.26 43.531c1.944-3.57 1.944-7.466 2.592-12.01.324-3.247 2.591-6.817 4.86-9.09 2.267-2.272 5.83-4.544 9.07-4.869 4.212-.649 8.1-.324 11.988-2.597 3.563-1.947 5.507-5.843 8.099-9.089C53.813 3.28 57.7 1.332 60.616.682c3.24-.973 7.452-.973 10.367.325 3.888 1.623 7.452 3.895 11.663 3.895 4.212 0 7.452-2.272 11.664-3.895 2.915-1.298 7.127-.974 10.367-.325 3.239.974 6.803 2.922 8.747 5.194 2.592 3.57 4.536 7.142 8.099 9.09 3.564 1.947 7.452 1.947 11.987 2.596 3.24.325 6.804 2.597 9.072 4.87 2.268 2.272 4.535 5.842 4.859 9.088.648 4.22.648 8.116 2.592 12.011 1.944 3.57 5.508 5.519 9.071 8.115 2.592 1.948 4.536 5.843 5.184 8.765.972 3.246.972 7.466-.324 10.388-1.62 3.895-3.888 7.466-3.888 11.685 0 4.22 2.268 7.467 3.888 11.687 1.296 2.921 1.296 7.141.324 10.387-.972 3.246-2.916 6.817-5.508 8.765-3.563 2.597-7.127 4.544-9.071 8.115-1.944 3.571-1.944 7.466-2.592 12.011-.324 3.246-2.591 6.816-4.859 9.089-2.268 2.272-6.156 4.544-9.072 4.869-4.211.649-8.423.324-11.987 2.597-3.563 1.947-5.507 5.843-8.099 9.089-1.944 2.597-5.831 4.544-8.747 5.194-3.24.973-7.452.973-10.367-.325-3.888-1.623-7.452-3.895-11.663-3.895-4.212 0-7.452 2.272-11.663 3.895-2.916 1.298-7.128 1.298-10.368.325-3.24-.974-6.803-2.922-8.747-5.194-2.592-3.571-4.536-7.142-8.1-9.089-3.563-1.948-7.45-1.948-11.986-2.597-3.24-.325-6.804-2.597-9.072-4.869-2.268-2.273-4.535-6.168-4.86-9.089-.647-4.22-.323-8.44-2.591-12.011-1.944-3.571-5.832-5.518-9.071-8.115-2.592-1.948-4.536-5.843-5.184-8.765-.972-3.246-.972-7.466.324-10.388 1.62-3.895 3.888-7.466 3.888-11.686S2.625 75.02 1.005 70.8C-.291 67.877.033 63.657.681 60.41c.972-3.246 2.916-6.817 5.184-8.765 3.887-2.596 7.451-4.544 9.395-8.115z"
          fill="#E87722"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M82.647 23.406c32.721 0 59.287 26.618 59.287 59.404s-26.566 59.404-59.287 59.404c-32.722 0-59.288-26.618-59.288-59.404.324-32.786 26.89-59.404 59.288-59.404z"
          fill="#FAE4D3"
        />
        <Path
          d="M115.692 76.64l-14.903 12.984 4.535 19.477c.324 1.299-.324 2.922-1.295 3.571-.972.974-2.592.974-3.888 0l-16.847-10.063-16.846 10.063c-1.296.649-2.592.649-3.888 0-1.296-.974-1.62-2.272-1.296-3.571L65.8 89.624 50.897 76.64c-.972-.974-1.62-2.272-.972-3.895.324-1.299 1.62-2.273 2.916-2.273l19.762-1.947 7.775-18.179c.648-1.298 1.944-2.272 3.24-2.272 1.296 0 2.592.974 3.24 2.273l7.775 18.178 19.763 1.947c1.296 0 2.592.974 2.915 2.273 0 1.298-.647 2.921-1.619 3.895z"
          fill="#E87722"
        />
        <Path
          d="M82.645 134.422c-28.51 0-51.512-23.048-51.512-51.614 0-28.565 23.002-51.613 51.512-51.613 28.509 0 51.512 23.048 51.512 51.613 0 28.242-23.003 51.614-51.512 51.614zm0-99.331c-26.242 0-47.624 21.424-47.624 47.717 0 26.294 21.382 47.718 47.624 47.718s47.624-21.424 47.624-47.718c0-26.293-21.382-47.717-47.624-47.717z"
          fill="#E87722"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_8754_96683">
          <Path fill="#fff" d="M0 0H165V165H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
