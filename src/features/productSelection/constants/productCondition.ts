import { ConcernId } from 'features/fna/types/concern';
import { LifeStage } from 'features/fna/types/lifeJourney';

export const productRecommendationConditionPH: Record<
  ConcernId,
  Record<LifeStage, Array<Array<{ pid: string; riders: Array<string> }>>>
> = {
  HEALTH_PROTECTION: {
    SINGLE: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
      [
        { pid: 'MCCI', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'VBT', riders: [] },
      ],
    ],
  },
  LOAN_PROTECTION: {
    SINGLE: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
      [
        { pid: 'STC', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'FWDM', riders: [] },
      ],
    ],
  },
  SAVINGS: {
    SINGLE: [[{ pid: '', riders: [] }]],
    SINGLE_WITH_DEPENDENT: [[{ pid: '', riders: [] }]],
    COUPLE: [[{ pid: '', riders: [] }]],
    COUPLE_WITH_KIDS: [[{ pid: '', riders: [] }]],
    EMPTY_NESTER: [[{ pid: '', riders: [] }]],
    RETIRED: [[{ pid: '', riders: [] }]],
  },
  INVESTMENT: {
    SINGLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
  },
  RETIREMENT: {
    SINGLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'USP', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
  },
  LEGACY_PLANNING: {
    SINGLE: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US02', riders: [] },
        { pid: 'US09', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
      [
        { pid: 'EP', riders: [] },
        { pid: 'FWDM', riders: [] },
        { pid: 'US11', riders: [] },
        { pid: 'US13', riders: [] },
        { pid: 'U2P', riders: [] },
        { pid: 'UPW', riders: [] },
        { pid: 'UUW', riders: [] },
      ],
    ],
  },
  EDUCATION: {
    SINGLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
    COUPLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
    RETIRED: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
      ],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
      [{ pid: 'FWDM', riders: [] }],
    ],
  },
  INCOME_PROTECTION: {
    SINGLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
    SINGLE_WITH_DEPENDENT: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
    COUPLE: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
    COUPLE_WITH_KIDS: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
    EMPTY_NESTER: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
    RETIRED: [
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'SFL', riders: [] },
        { pid: 'RPVLR', riders: [] },
        { pid: 'RPVLSO', riders: [] },
        { pid: 'LPVL', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
      [
        { pid: 'FWDM', riders: [] },
        { pid: 'ICP', riders: [] },
        { pid: 'EP', riders: [] },
        { pid: 'U2P', riders: [] },
      ],
    ],
  },
  ACCIDENT_EXPENSES: {
    SINGLE: [[{ pid: '', riders: [] }]],
    SINGLE_WITH_DEPENDENT: [[{ pid: '', riders: [] }]],
    COUPLE: [[{ pid: '', riders: [] }]],
    COUPLE_WITH_KIDS: [[{ pid: '', riders: [] }]],
    EMPTY_NESTER: [[{ pid: '', riders: [] }]],
    RETIRED: [[{ pid: '', riders: [] }]],
  },
  CRITICAL_ILLNESS: {
    SINGLE: [[{ pid: '', riders: [] }]],
    SINGLE_WITH_DEPENDENT: [[{ pid: '', riders: [] }]],
    COUPLE: [[{ pid: '', riders: [] }]],
    COUPLE_WITH_KIDS: [[{ pid: '', riders: [] }]],
    EMPTY_NESTER: [[{ pid: '', riders: [] }]],
    RETIRED: [[{ pid: '', riders: [] }]],
  },
};

export const productRecommendationAgencyIB: Record<ConcernId, string[]> = {
  INCOME_PROTECTION: ['GL1', 'GL2', 'FA1'],
  EDUCATION: ['HA1', 'GS2', 'GS1'],
  HEALTH_PROTECTION: ['GL1', 'GL2', 'GS2'],
  INVESTMENT: ['GL1', 'GL2'],
  LEGACY_PLANNING: [],
  LOAN_PROTECTION: [],
  RETIREMENT: ['FA3', 'FA2', 'HA1'],
  SAVINGS: ['HA1', 'GS2', 'GS1'],
  ACCIDENT_EXPENSES: [],
  CRITICAL_ILLNESS: [],
};

//IJ7 = QasehLink Pintar Plus
//PA1 = Qaseh Saving (Qaseh Impian)
export const productRecommendationBancaIB: Record<ConcernId, string[]> = {
  INCOME_PROTECTION: ['IJ7', 'PA1'],
  EDUCATION: ['PA1', 'IJ7'],
  HEALTH_PROTECTION: ['IJ7'],
  INVESTMENT: ['IJ7'],
  LEGACY_PLANNING: [],
  LOAN_PROTECTION: [],
  RETIREMENT: ['PA1', 'IJ7'],
  SAVINGS: ['PA1', 'IJ7'],
  ACCIDENT_EXPENSES: [],
  CRITICAL_ILLNESS: [],
};

export const productRecommendationAgencyMY: Record<ConcernId, string[]> = {
  INCOME_PROTECTION: ['TM1', 'NGC', 'HYT'],
  EDUCATION: ['ILB'],
  HEALTH_PROTECTION: ['NGC', 'FAMILYCI', 'ILM'],
  INVESTMENT: ['ILBP', 'ILM', 'EN9'],
  LEGACY_PLANNING: [],
  LOAN_PROTECTION: [],
  RETIREMENT: ['EN9', 'OFB', 'ILBP'],
  SAVINGS: ['OFB', 'ILBP', 'EN9'],
  ACCIDENT_EXPENSES: [],
  CRITICAL_ILLNESS: [],
};

export const productRecommendationBancaMY: Record<ConcernId, string[]> = {
  INCOME_PROTECTION: ['EN6', 'LVS', 'IP1', 'EN4', 'EN5'],
  EDUCATION: ['IL4', 'TSI'],
  HEALTH_PROTECTION: [],
  INVESTMENT: ['TSI', 'IP2', 'IP1'],
  LEGACY_PLANNING: [],
  LOAN_PROTECTION: [],
  RETIREMENT: ['EN8', 'IP1', 'IP2', 'TSI'],
  SAVINGS: ['EN8', 'IP1', 'IP2', 'TSI'],
  ACCIDENT_EXPENSES: [],
  CRITICAL_ILLNESS: [],
};

// FCA = Critical Armor
// FLP = FWD Legacy Protection
// FTP = Tomorrow Protection
// FWDLPJ = FWD Whole Life Protection - Jumbo
// FWDLPR = FWD Whole Life Protection - Regular
export const productRecommendationAgencyId: Record<ConcernId, string[]> = {
  LEGACY_PLANNING: ['FCA', 'FLP'],
  CRITICAL_ILLNESS: ['FCA'],
  HEALTH_PROTECTION: ['FCA'],
  ACCIDENT_EXPENSES: [],
  EDUCATION: [],
  RETIREMENT: ['FLP'],
  SAVINGS: [],
  INCOME_PROTECTION: ['FCA'],
  LOAN_PROTECTION: [],
  INVESTMENT: [],
};
