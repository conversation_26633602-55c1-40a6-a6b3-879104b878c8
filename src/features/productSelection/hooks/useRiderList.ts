import { useMutation } from '@tanstack/react-query';
import { getRiderList, RiderListRequest } from 'api/proposalApi';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { AgentProfile } from 'types';
import { convertParamsToRequestBody } from './useProducts';

export type RiderListParams = Omit<RiderListRequest, 'agent'> & {
  agent?: AgentProfile;
  pid?: string;
};

export function useRiderList() {
  const channel = useGetCubeChannel();

  const { data: agentInfo } = useGetAgentProfile();
  return useMutation({
    mutationFn: async (params: Omit<RiderListParams, 'agent' | 'channel'>) => {
      const pl = await getRiderList(
        convertParamsToRequestBody({
          agent: agentInfo,
          ...params,
          channel,
        }),
      );
      return pl;
    },
  });
}
