import { useTheme } from '@emotion/react';
import { useCallback } from 'react';
import {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export const usePressInOutCardStyle = (
  inactiveColor: string,
  activeColor: string,
) => {
  const progress = useSharedValue(0);
  const { components } = useTheme();
  const { animationConfig } = components.button;
  const onPressIn = useCallback(() => {
    progress.value = withTiming(1, animationConfig);
  }, [[progress, animationConfig]]);
  const onPressOut = useCallback(() => {
    progress.value = withTiming(0, animationConfig);
  }, [[progress, animationConfig]]);

  const cardStyle = useAnimatedStyle(
    () => ({
      backgroundColor: interpolateColor(
        progress.value,
        [0, 1],
        [inactiveColor, activeColor],
      ),
    }),
    [inactiveColor, activeColor, progress.value],
  );
  return {
    progress,
    onPressIn,
    onPressOut,
    cardStyle,
  };
};
