import { useTranslation } from 'react-i18next';
import {
  LayoutChangeEvent,
  LayoutRectangle,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  useWindowDimensions,
} from 'react-native';
import { useTheme } from '@emotion/react';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useBoundStore from 'hooks/useBoundStore';

import { shallow } from 'zustand/shallow';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useLatest from 'hooks/useLatest';
import useToggle from 'hooks/useToggle';
import { Case, CaseStatus, FnaRejectRecommendedReason } from 'types/case';
import { ConcernId } from 'features/fna/types/concern';
import { GroupCode, Product } from 'types/products';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useCreateFna } from 'hooks/useCreateFna';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { hasLicense } from 'utils/helper/agentUtils';
import {
  useFormatProductListParties,
  useProductListQuery,
} from 'features/productSelection/hooks/useProducts';
import { useGenerateFnaPdf } from 'hooks/useGenerateFnaPdf';
import { country } from 'utils/context';
import {
  CommonActions,
  NavigationProp,
  useNavigation,
} from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useProductRecommendation } from './useProductRecommendation';
import { useSaveProductRecommendation } from 'hooks/useSaveProductRecommendation';
import { mapProductsForRecommendation } from 'utils/helper/mapProducts';
import { countryModuleFnaConfig, moduleConfigs } from 'utils/config/module';
import { SaleIllustrationTargetType } from 'features/home/<USER>/CreateSaleIllustrationModal/types';
import PdfViewer, {
  PdfGenerator,
} from 'features/pdfViewer/components/PdfViewer';
import { getFnaMailConfig } from 'features/productSelection/utils/helpers/getFnaMailConfig';
import { PartyRole } from 'types/party';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ClientWaiverModal from 'features/productSelection/components/ClientWaiverModal/ClientWaiverModal';
import DialogPhone from 'components/Dialog.phone';
import { Box, Icon, Typography, Row, Button } from 'cube-ui-components';
import ClientReasonModal from 'features/productSelection/components/ClientReasonModal/ClientReasonModal';
import FamilySharePlan from 'features/productSelection/components/FamilySharePlan';
import { navigationRef } from 'hooks/useScreenTracking';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import { Insured } from 'types/quotation';
import { useQuotation } from 'hooks/useQuotation';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';

export type ProductWithConcern = {
  need: ConcernId | '';
  product: Product;
} | null;

export const useProductRecommendationLogic = (
  externalCase?: Case,
  useNavigationRef = false,
) => {
  const { t } = useTranslation(['product', 'fna', 'common']);
  const { width } = useWindowDimensions();
  const { colors, space } = useTheme();
  const {
    setFamilySharePlanType,
    setFamilySharePlanCustomerId,
    setFamilySharePlanPolicyNumber,
    updateIdNumber,
  } = useFnaStore(
    state => ({
      setFamilySharePlanType: state.setFamilySharePlanType,
      setFamilySharePlanPolicyNumber: state.setFamilySharePlanPolicyNumber,
      setFamilySharePlanCustomerId: state.setFamilySharePlanCustomerId,
      updateIdNumber: state.updateIdNumber,
    }),
    shallow,
  );
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const [sectionLayouts, setSectionLayouts] = useState<Array<LayoutRectangle>>(
    [],
  );
  const [recommendedListWidth, setRecommendedListWidth] = useState(0);
  const latestSectionLayouts = useLatest(sectionLayouts);
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollingRef = useRef<boolean>(false);
  const scrollOffsetRef = useRef(0);

  const [waiverVisible, showWaiver, hideWaiver] = useToggle();
  const [reasonVisible, showReason, hideReason] = useToggle();
  const [reason, setReason] = useState<FnaRejectRecommendedReason>(
    FnaRejectRecommendedReason.NOT_REJECT,
  );
  const [customReason, setCustomReason] = useState('');
  const [isViewedDoc, setIsViewedDoc] = useState(false);
  const [selectedProduct, setSelectedProduct] =
    useState<ProductWithConcern>(null);
  const [invalidProductName, setInvalidProductName] = useState<string>();
  const [isGeneratingFnaDoc, setGeneratingFnaDoc] = useState(false);
  const [viewingPdf, showPdf, hidePdf] = useToggle();
  const [familyPlanVisible, showFamilyPlan, hideFamilyPlan] = useToggle();

  const { data: agentInfo } = useGetAgentProfile();
  const channel = useGetCubeChannel();
  const { caseObj: activeCase } = useGetActiveCase();
  const { mutateAsync: createFna, isLoading: isCreatingFna } = useCreateFna();
  const { isTabletMode, isMobileMode } = useLayoutAdoptionCheck();

  const caseObj = externalCase ?? activeCase;
  const saveAcknowledgement = useCallback(
    (acknowledge: boolean) => {
      if (caseObj && caseObj.id && caseObj.fna) {
        return createFna({
          caseId: caseObj.id,
          fna: {
            ...caseObj.fna,
            acknowledge,
          },
        });
      }
    },
    [caseObj, createFna],
  );

  const validateLicense = useCallback(
    (product: Product) => {
      if (!agentInfo) return false;
      const { licenses, isAgentLicenseActive } = agentInfo;
      if (!isAgentLicenseActive) return false;

      // Check for TL or VL licenses
      if (hasLicense(licenses, 'TL') || hasLicense(licenses, 'VL')) {
        return true;
      }

      // Check for TL license and non-VUL product
      if (hasLicense(licenses, 'TL') && !product.isVUL) {
        return true;
      }

      // Check for VL license and VUL product
      if (hasLicense(licenses, 'VL') && product.isVUL) {
        return true;
      }

      return false;
    },
    [agentInfo],
  );

  const { lifeJourney, goalCompletion, updateSelectedProductCode } =
    useFnaStore(
      state => ({
        lifeJourney: state.lifeJourney,
        goalCompletion: state.goalCompletion,
        updateSelectedProductCode: state.updateSelectedProductCode,
      }),
      shallow,
    );

  const {
    mutate: getProducts,
    data: products,
    isSuccess: isGetProductsSuccess,
  } = useProductListQuery();
  const { mutateAsync: generateFnaPdf } = useGenerateFnaPdf<typeof country>();

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const quickQuotePid = useMemo(
    () =>
      caseObj?.quotations
        ?.sort(
          (qa, qb) =>
            new Date(qb.expiryDate).getTime() -
            new Date(qa.expiryDate).getTime(),
        )
        .find(q => q.isPdfGenerated)?.pid,
    [caseObj?.quotations],
  );
  const { recommendationProducts, otherProducts } = useProductRecommendation(
    products,
    lifeJourney,
    channel,
    quickQuotePid,
  );

  const {
    mutateAsync: saveProductRecommendation,
    isLoading: isSavingProductRecommendation,
  } = useSaveProductRecommendation();

  const otherProductSections = useMemo(() => {
    switch (country) {
      case 'ph':
        return {
          vertical: false,
          sections: mapProductsForRecommendation(otherProducts),
        };
      case 'my':
      case 'ib':
      case 'id':
        return {
          vertical: isMobileMode,
          sections: [
            {
              title: t('product:otherProducts') as GroupCode,
              data: otherProducts,
            },
          ],
        };
      default:
        return { vertical: false, sections: [] };
    }
  }, [isMobileMode, otherProducts, t]);

  const onSectionLayout = (index: number) => (e: LayoutChangeEvent) => {
    e.persist();
    setSectionLayouts(layouts => {
      layouts[index] = e.nativeEvent.layout;
      return layouts;
    });
  };

  const { proposers, insureds } = useFormatProductListParties();

  useEffect(() => {
    const coverageDetailsCompleted = caseObj?.status?.includes(
      CaseStatus.COVERAGE,
    );

    const productListPayload = coverageDetailsCompleted
      ? {
          insureds: getMainInsuredList(insureds, caseObj),
          proposers,
        }
      : {
          // FNA flow (Insured not available)
          proposers,
        };

    getProducts(productListPayload);
  }, [getProducts]);

  const [tabIndex, setTabIndex] = useState(0);
  const latestTabIndex = useLatest(tabIndex);

  const handleOnScroll = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { y } = e.nativeEvent.contentOffset;
      const direction = y > scrollOffsetRef.current ? 'up' : 'down';
      scrollOffsetRef.current = y;
      if (scrollingRef.current) {
        return;
      }
      let index = -1;

      if (direction === 'up') {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.4 : 0;
          if (y >= latestSectionLayouts.current[i].y - space) {
            index = i;
          }
        }
      } else {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.3 : 0;
          if (
            y >= latestSectionLayouts.current[i].y - space &&
            y <=
              latestSectionLayouts.current[i].y +
                latestSectionLayouts.current[i].height * 0.4
          ) {
            index = i;
          }
        }
      }
      if (index !== latestTabIndex.current && index >= 0) {
        setTabIndex(index);
      }
    },
    [latestSectionLayouts, latestTabIndex],
  );

  const save = useCallback(
    async ({
      reason,
      selectedProduct,
    }: {
      reason: FnaRejectRecommendedReason;
      selectedProduct: ProductWithConcern;
    }) => {
      if (!caseObj || !selectedProduct) return;

      await saveProductRecommendation({
        caseId: caseObj?.id || '',
        data: {
          needs: Object.entries(
            recommendationProducts.reduce<
              Partial<{
                [need in ConcernId]: Product[];
              }>
            >((ret, item) => {
              ret[item.need] ||= [];
              ret[item.need]?.push(item.product);
              return ret;
            }, {}),
          ).map(([need, products]) => ({
            need,
            recommendedProducts: products.map(p => ({
              pid: p.pid,
              productName: p.productName,
            })),
          })),
          selectedNeed: selectedProduct.need,
          rejectRecommendedReason: reason,
          rejectRecommendedOtherReason: customReason,
          selectedProduct: {
            pid: selectedProduct.product.pid,
            productName: selectedProduct.product.productName,
          },
        },
      });
    },
    [caseObj, customReason, recommendationProducts, saveProductRecommendation],
  );

  const navigateToNextScreen = (args: {
    lockMainInsuredToProposer?: boolean;
  }) => {
    const nav = useNavigationRef ? navigationRef : navigation;
    const state = nav.getState();
    let routes = state.routes;
    if (!moduleConfigs[country].fnaConfig.isProductReselectionEnabled) {
      // pop out recommendation screen if product reselection is not enabled
      let numberOfScreensToPopOut = 1; // only product recommendation
      if (state.routes[state.routes.length - 2].name === 'Fna') {
        numberOfScreensToPopOut = 2; // fna + product recommendation
      }
      routes = state.routes.slice(
        0,
        state.routes.length - numberOfScreensToPopOut,
      );
    }
    if (caseObj?.status?.some(s => s === CaseStatus.COVERAGE)) {
      routes.push({
        name: 'SalesIllustrationForm',
        params: {
          lockMainInsuredToProposer: args.lockMainInsuredToProposer,
          goBack: moduleConfigs[country].fnaConfig.isProductReselectionEnabled,
          from: 'product_recommendation',
        },
      } as (typeof state.routes)[0]);
    } else if (caseObj?.status?.some(s => s === CaseStatus.FNA)) {
      routes.push({
        name: 'CoverageDetailsScreen',
        params: {
          targetType: SaleIllustrationTargetType.INDIVIDUAL,
        },
      } as (typeof state.routes)[0]);
    } else {
      routes.push({
        name: 'CoverageDetailsScreen',
      } as (typeof state.routes)[0]);
    }
    if (useNavigationRef) {
      navigationRef.resetRoot({
        ...state,
        routes,
        index: routes.length - 1,
      });
    } else {
      navigation.dispatch(
        CommonActions.reset({
          ...state,
          routes,
          index: routes.length - 1,
        }),
      );
    }
  };

  const onPressOtherProduct = useCallback(
    async (p: Product) => {
      if (country === 'ph') {
        const licenseValidated = validateLicense(p);
        if (licenseValidated) {
          setSelectedProduct({ need: '', product: p });
          setReason(FnaRejectRecommendedReason.NOT_REJECT);
          showReason();
        } else {
          setInvalidProductName(p.productName.en);
        }
      } else {
        setSelectedProduct({ need: '', product: p });
        setReason(FnaRejectRecommendedReason.NOT_REJECT);
        country === 'id' ? showWaiver() : showReason();
      }
    },
    [showReason, validateLicense, showWaiver],
  );

  const onRecommendedSelect = useCallback(
    async (data: { need?: ConcernId; product: Product }) => {
      if (country === 'ph') {
        setReason(FnaRejectRecommendedReason.NOT_REJECT);
        const licenseValidated = validateLicense(data.product);
        if (licenseValidated) {
          setSelectedProduct({ need: data.need || '', product: data.product });
          if (isViewedDoc) setIsViewedDoc(false);
          showWaiver();
        } else {
          setInvalidProductName(data.product.productName.en);
        }
      } else {
        setSelectedProduct({ need: data.need || '', product: data.product });
        if (isViewedDoc) setIsViewedDoc(false);
        showWaiver();
      }
    },
    [showWaiver, validateLicense, save, reason, isViewedDoc, setIsViewedDoc],
  );

  const onConfirmReason = useCallback(
    (reason: FnaRejectRecommendedReason) => {
      setReason(reason);
      if (isViewedDoc) setIsViewedDoc(false);
      setTimeout(() => {
        showWaiver();
      }, 500);
    },
    [showWaiver, isViewedDoc, setIsViewedDoc],
  );

  const pdfGeneratorRef = useRef<PdfGenerator>(async () => ({
    base64: '',
    fileName: '',
  }));

  const mailConfig = useMemo(() => {
    return getFnaMailConfig({
      clientFirstName: caseObj?.fna?.firstName || '',
      clientEmail:
        caseObj?.parties && caseObj?.parties.length > 0
          ? caseObj?.parties[0].contacts.email
          : '',
      agentNumber: agentInfo?.contact?.mobilePhone || '',
      agentName: agentInfo?.person?.fullName || '',
      agentEmail: agentInfo?.contact?.email || '',
    });
  }, [caseObj, agentInfo]);

  const onGetFnaDoc = useCallback(
    async (acknowledge: boolean) => {
      if (caseObj && selectedProduct) {
        setGeneratingFnaDoc(true);
        try {
          await saveAcknowledgement(acknowledge);
          await save({ reason, selectedProduct });
          pdfGeneratorRef.current = async () => {
            let pdf = '';
            const res = await generateFnaPdf(caseObj.id);
            pdf = typeof res === 'string' ? res : res.report;
            if (!pdf) {
              throw new Error('empty pdf');
            }
            return {
              fileName: t('product:previewFNA'),
              base64: pdf,
            };
          };
          hideWaiver();
          showPdf();
        } catch (e) {
          console.log(e);
        } finally {
          setGeneratingFnaDoc(false);
        }
      }
    },
    [
      caseObj,
      selectedProduct,
      saveAcknowledgement,
      save,
      reason,
      hideWaiver,
      showPdf,
      generateFnaPdf,
      t,
    ],
  );

  const acknowledge = useRef(false);

  const onConfirmWaiver = useCallback(async () => {
    if (caseObj && selectedProduct) {
      await saveAcknowledgement(acknowledge.current);
      await save({ reason, selectedProduct });
      const isMultipleInsureds =
        selectedProduct?.product?.templates.isMultipleInsureds;

      const isOwnerInsured =
        caseObj.status?.includes(CaseStatus.COVERAGE) &&
        caseObj?.parties?.some(
          p =>
            p.roles.includes(PartyRole.INSURED) &&
            p.roles.includes(PartyRole.PROPOSER),
        );

      const lockMainInsuredToProposer = isMultipleInsureds || isOwnerInsured;

      updateSelectedProductCode(
        selectedProduct?.product?.pid || '',
        isMultipleInsureds,
      );
      navigateToNextScreen({
        lockMainInsuredToProposer,
      });
    }
  }, [
    caseObj,
    selectedProduct,
    save,
    reason,
    saveAcknowledgement,
    updateSelectedProductCode,
    navigation,
  ]);

  const title = `${t('product:recommended')} (${
    recommendationProducts?.length || 0
  })`;

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const insets = useSafeAreaInsets();

  const shouldShowFamilySharePlan =
    isTabletMode && selectedProduct?.product.pid === 'IJ7';

  const shouldDisplaySummaryNeeds =
    countryModuleFnaConfig?.shouldDisplaySummaryNeeds &&
    lifeJourney.concerns.length > 0 &&
    lifeJourney.concerns.some(concern => goalCompletion[concern]);

  const renderClientReasonModal = useCallback(() => {
    if (!reasonVisible) return null;
    return (
      <ClientReasonModal
        reason={reason}
        setCustomReason={setCustomReason}
        handleClose={hideReason}
        onConfirm={onConfirmReason}
        visible={reasonVisible}
      />
    );
  }, [hideReason, onConfirmReason, reason, reasonVisible, setCustomReason]);

  const renderWaiverModal = useCallback(() => {
    if (!waiverVisible) return null;
    return (
      <ClientWaiverModal
        handleClose={hideWaiver}
        reason={reason}
        onGetFnaDoc={onGetFnaDoc}
        onConfirm={async (ack: boolean) => {
          acknowledge.current = ack;
          if (shouldShowFamilySharePlan) {
            showFamilyPlan();
          } else {
            await onConfirmWaiver();
          }
        }}
        isGettingFnaDoc={isGeneratingFnaDoc}
        isSaving={isSavingProductRecommendation || isCreatingFna}
        isViewedDoc={isViewedDoc}
      />
    );
  }, [
    hideWaiver,
    isCreatingFna,
    isGeneratingFnaDoc,
    isSavingProductRecommendation,
    isViewedDoc,
    onConfirmWaiver,
    onGetFnaDoc,
    reason,
    shouldShowFamilySharePlan,
    showFamilyPlan,
    waiverVisible,
  ]);

  const renderLicenseInvalidModal = useCallback(() => {
    return (
      <LicenseInvalidModal
        productName={invalidProductName}
        handleClose={() => setInvalidProductName(undefined)}
      />
    );
  }, [invalidProductName, setInvalidProductName]);

  const renderPdfViewer = useCallback(() => {
    return (
      <PdfViewer
        visible={viewingPdf}
        title={t('product:previewFNA')}
        onClose={hidePdf}
        pdfGenerator={pdfGeneratorRef.current}
        downloadable
        sharable={moduleConfigs[country].fnaConfig.previewFNASharable}
        mailConfig={mailConfig}
        shareType={'email'}
        actionOption={
          moduleConfigs[country].fnaConfig.previewFNAMandatory
            ? {
                text: t('fna:done'),
                onPress: () => {
                  setIsViewedDoc(true);
                  hidePdf();
                  setTimeout(() => {
                    showWaiver();
                  }, 500);
                },
                actionMode: 'default',
                activeMode: !isViewedDoc ? 'end-of-file' : 'default',
              }
            : undefined
        }
      />
    );
  }, [hidePdf, isViewedDoc, mailConfig, showWaiver, t, viewingPdf]);

  const renderFamilySharePlan = useCallback(() => {
    if (!familyPlanVisible) return null;
    return (
      <FamilySharePlan
        visible={familyPlanVisible}
        onDismiss={hideFamilyPlan}
        onDone={async (
          familySharePlanType,
          policyNumber,
          customerIdNumber,
          customerId,
        ) => {
          setFamilySharePlanType(familySharePlanType);
          setFamilySharePlanPolicyNumber(policyNumber);
          setFamilySharePlanCustomerId(customerId || '');
          updateIdNumber(customerIdNumber);
          setAppLoading();
          try {
            await onConfirmWaiver();
          } finally {
            setAppIdle();
          }
        }}
        pid={selectedProduct?.product.pid || ''}
      />
    );
  }, [
    familyPlanVisible,
    hideFamilyPlan,
    onConfirmWaiver,
    selectedProduct?.product.pid,
    setAppIdle,
    setAppLoading,
    setFamilySharePlanCustomerId,
    setFamilySharePlanPolicyNumber,
    setFamilySharePlanType,
    updateIdNumber,
  ]);

  return {
    t,
    width,
    colors,
    space,
    setFamilySharePlanType,
    setFamilySharePlanCustomerId,
    setFamilySharePlanPolicyNumber,
    updateIdNumber,
    setAppLoading,
    setAppIdle,
    sectionLayouts,
    setSectionLayouts,
    recommendedListWidth,
    setRecommendedListWidth,
    latestSectionLayouts,
    scrollViewRef,
    scrollingRef,
    scrollOffsetRef,
    waiverVisible,
    showWaiver,
    hideWaiver,
    reasonVisible,
    showReason,
    hideReason,
    reason,
    customReason,
    setCustomReason,
    isViewedDoc,
    setIsViewedDoc,
    selectedProduct,
    setSelectedProduct,
    invalidProductName,
    setInvalidProductName,
    isGeneratingFnaDoc,
    setGeneratingFnaDoc,
    viewingPdf,
    showPdf,
    hidePdf,
    familyPlanVisible,
    showFamilyPlan,
    hideFamilyPlan,
    agentInfo,
    channel,
    caseObj,
    createFna,
    isCreatingFna,
    isTabletMode,
    isMobileMode,
    saveAcknowledgement,
    validateLicense,
    lifeJourney,
    updateSelectedProductCode,
    getProducts,
    products,
    isGetProductsSuccess,
    generateFnaPdf,
    navigation,
    quickQuotePid,
    recommendationProducts,
    otherProducts,
    saveProductRecommendation,
    isSavingProductRecommendation,
    otherProductSections,
    onSectionLayout,
    insureds,
    proposers,
    tabIndex,
    setTabIndex,
    latestTabIndex,
    handleOnScroll,
    save,
    navigateToNextScreen,
    onPressOtherProduct,
    onRecommendedSelect,
    onConfirmReason,
    pdfGeneratorRef,
    mailConfig,
    onGetFnaDoc,
    acknowledge,
    onConfirmWaiver,
    title,
    isNarrowScreen,
    insets,
    shouldShowFamilySharePlan,
    shouldDisplaySummaryNeeds,
    renderWaiverModal,
    renderLicenseInvalidModal,
    renderClientReasonModal,
    renderPdfViewer,
    renderFamilySharePlan,
  };
};

const LicenseInvalidModal = ({
  productName,
  handleClose,
}: {
  productName?: string;
  handleClose: () => void;
}) => {
  const { t } = useTranslation(['product']);
  const { colors, space, sizes } = useTheme();

  return (
    <DialogPhone visible={productName !== undefined}>
      <Box alignSelf="center">
        <Icon.Warning fill={colors.palette.alertRed} size={sizes[16]} />
      </Box>
      <Box mt={space[4]}>
        <Typography.H7 fontWeight="medium" style={{ textAlign: 'center' }}>
          {t('product:invalidLicense', {
            productName,
          })}
        </Typography.H7>
      </Box>
      <Row mt={space[6]}>
        <Button
          text={t('product:gotIt')}
          // variant="secondary"
          onPress={handleClose}
          style={{ flex: 1 }}
        />
      </Row>
    </DialogPhone>
  );
};

function getMainInsuredList(insureds: Insured[], caseObj: Case | undefined) {
  const quickSiMainInsuredId = caseObj?.quotations
    ?.find(q => q.isPdfGenerated)
    ?.insuredSetups?.find(i => i.isMainInsured)?.insuredId;

  if (quickSiMainInsuredId)
    return insureds.filter(i => i.id == quickSiMainInsuredId);

  const ownerInsuredId = caseObj?.status?.includes(CaseStatus.COVERAGE)
    ? caseObj?.parties?.find(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          p.roles.includes(PartyRole.PROPOSER),
      )?.id
    : undefined;

  if (ownerInsuredId) return insureds.filter(i => i.id == ownerInsuredId);

  // assume the first insured is the mainInsured if ownerInsuredId and quickSiMainInsuredId are both undefined
  return insureds.slice(0, 1);
}
