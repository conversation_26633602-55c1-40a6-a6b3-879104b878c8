import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useCallback, useMemo, useState } from 'react';
import { RootStackParamList } from 'types';
import { CaseStatus } from 'types/case';
import { PartyRole } from 'types/party';
import { Product } from 'types/products';
import { countryModuleSiConfig } from 'utils/config/module';

/* max number of quotation allowed in a case object */
export const MAX_QUOTATION_COUNT = 10;

const useProductSelection = () => {
  const caseId = useBoundStore(state => state.case.caseId);
  const resetQuotationStore = useBoundStore(
    state => state.quotationActions.reset,
  );
  const { data: caseData } = useGetCase(caseId);
  const [maxModalVisible, setMaxModalVisible] = useState(false);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const quotationCount = useMemo(
    () => caseData?.quotations?.length ?? 0,
    [caseData?.quotations?.length],
  );

  const isProductReselectionEnabled =
    countryModuleSiConfig.isProductReselectionEnabled;

  const onPressItemSelect = useCallback(
    (p: Product) => {
      if (quotationCount < MAX_QUOTATION_COUNT) {
        const isOwnerInsured =
          caseData?.status?.includes(CaseStatus.COVERAGE) &&
          caseData?.parties?.some(
            p =>
              p.roles.includes(PartyRole.PROPOSER) &&
              p.roles.includes(PartyRole.INSURED),
          );

        const lockMainInsuredToProposer =
          p.templates.isMultipleInsureds || isOwnerInsured;

        const routeParams = {
          pid: p.pid,
          lockMainInsuredToProposer,
          from: 'quick_quote_form' as const,
        };
        if (isProductReselectionEnabled) {
          navigation.navigate('SalesIllustrationForm', routeParams);
        } else {
          navigation.dispatch(
            StackActions.replace('SalesIllustrationForm', routeParams),
          );
        }
      } else setMaxModalVisible(true);

      resetQuotationStore();
    },
    [
      isProductReselectionEnabled,
      navigation,
      quotationCount,
      resetQuotationStore,
    ],
  );

  return {
    onPressItemSelect,
    maxModalVisible,
    quotationCount,
    setMaxModalVisible,
  };
};

export default useProductSelection;
