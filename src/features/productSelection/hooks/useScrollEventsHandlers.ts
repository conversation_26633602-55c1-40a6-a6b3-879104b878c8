import {
  ScrollEventHandlerCallbackType,
  ScrollEventsHandlersHookType,
  useScrollEventsHandlersDefault,
} from '@gorhom/bottom-sheet';
import { ScrollEventContextType } from '@gorhom/bottom-sheet/lib/typescript/hooks/useScrollEventsHandlersDefault';
import { useCallback } from 'react';
import { NativeScrollEvent } from 'react-native';
import { runOnJS } from 'react-native-reanimated';

export const scrollEventsHandlerBuilder: (
  onScroll: (e: NativeScrollEvent) => void,
) => ScrollEventsHandlersHookType =
  onScroll => (scrollableRef, scrollableContentOffsetY) => {
    const {
      handleOnScroll: handleOnScrollDefault,
      handleOnBeginDrag,
      handleOnEndDrag,
      handleOnMomentumEnd,
    } = useScrollEventsHandlersDefault(scrollableRef, scrollableContentOffsetY);

    const handleOnScroll: ScrollEventHandlerCallbackType<ScrollEventContextType> =
      useCallback(
        (event, context) => {
          'worklet';
          if (handleOnScrollDefault) {
            handleOnScrollDefault(event, context);
          }
          if (onScroll) {
            runOnJS(onScroll)(event);
          }
        },
        [scrollableRef, scrollableContentOffsetY, handleOnScrollDefault],
      );

    return {
      handleOnScroll,
      handleOnBeginDrag,
      handleOnEndDrag,
      handleOnMomentumEnd,
    };
  };
