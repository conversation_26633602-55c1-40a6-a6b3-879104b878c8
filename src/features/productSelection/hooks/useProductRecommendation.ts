import { useMemo } from 'react';
import { Product } from 'types/products';

import { ConcernId } from 'features/fna/types/concern';
import { FnaState } from 'features/fna/utils/store/fnaStore';
import {
  productRecommendationAgencyIB,
  productRecommendationAgencyId,
  productRecommendationAgencyMY,
  productRecommendationBancaIB,
  productRecommendationBancaMY,
  productRecommendationConditionPH,
} from 'features/productSelection/constants/productCondition';
import { CHANNELS } from 'types/channel';
import { country } from 'utils/context';
import { cloneDeep } from 'utils/helper/objectUtil';
const FIRST_N_GOALS = 3;

export const useProductRecommendation = (
  products: (Product | undefined)[] = [],
  lifeJourney: FnaState['lifeJourney'] | undefined,
  channel: string,
  quickQuotePid?: string,
) => {
  const recommendationProducts = useMemo<
    {
      need: ConcernId;
      product: Product;
      fromQuickQuote?: boolean;
    }[]
  >(() => {
    if (!lifeJourney) {
      return [];
    }

    const segment = getSegmentFromIncomeRange(lifeJourney.monthlyIncome);
    const recommendationProducts: { need: ConcernId; product: Product }[] = [];
    const recommendIdList: Partial<{ [need in ConcernId]: string[] }> = {};
    switch (country) {
      case 'ph':
        for (const concern of lifeJourney.concerns) {
          if (!Array.isArray(recommendIdList[concern])) {
            recommendIdList[concern] = [];
          }
          if (lifeJourney.lifeStage) {
            const pidList =
              productRecommendationConditionPH[concern]?.[
                lifeJourney.lifeStage
              ]?.[segment];
            if (pidList) {
              recommendIdList[concern]?.push(
                ...productRecommendationConditionPH[concern][
                  lifeJourney.lifeStage
                ][segment].map(i => i.pid),
              );
            }
          }
        }
        if (products) {
          for (const need in recommendIdList) {
            if (recommendIdList[need as ConcernId]) {
              for (const id of recommendIdList[need as ConcernId] as string[]) {
                const matchedProduct = products.find(p => p?.pid === id);
                if (matchedProduct) {
                  recommendationProducts.push({
                    need: need as ConcernId,
                    product: cloneDeep(matchedProduct),
                  });
                }
              }
            }
          }
        }
        return recommendationProducts;
      case 'ib': {
        const productRecommendation =
          channel === CHANNELS.BANCA
            ? productRecommendationBancaIB
            : productRecommendationAgencyIB;

        for (let i = 0; i < FIRST_N_GOALS; i++) {
          recommendIdList[lifeJourney.concerns[i]] = [
            productRecommendation[lifeJourney.concerns[i]]?.[i],
          ].filter(Boolean);
        }

        if (products) {
          for (const need in recommendIdList) {
            if (recommendIdList[need as ConcernId]) {
              for (const p of products) {
                if (p && recommendIdList[need as ConcernId]?.includes(p.pid)) {
                  recommendationProducts.push({
                    need: need as ConcernId,
                    product: p,
                  });
                }
              }
            }
          }
        }

        return recommendationProducts;
      }
      case 'my': {
        const productRecommendation =
          channel === CHANNELS.BANCA
            ? productRecommendationBancaMY
            : productRecommendationAgencyMY;

        const topConcerns = lifeJourney.concerns.slice(0, FIRST_N_GOALS);

        lifeJourney.concerns.forEach(concern => {
          if (!recommendIdList[concern]) {
            recommendIdList[concern] = [];
          }
        });

        const existingRecommendedProducts = new Set<string>();
        topConcerns.forEach(concern => {
          productRecommendation[concern].forEach(pid => {
            if (!existingRecommendedProducts.has(pid)) {
              recommendIdList[concern]?.push(pid);
              existingRecommendedProducts.add(pid);
            }
          });
        });

        if (products) {
          Object.keys(recommendIdList).forEach(need => {
            (recommendIdList[need as ConcernId] ?? []).forEach(pid => {
              const product = products.find(p => p?.pid === pid);
              if (product) {
                recommendationProducts.push({
                  need: need as ConcernId,
                  product,
                });
              }
            });
          });
        }

        const quickQuotePidIdx = recommendationProducts.findIndex(
          item => item.product.pid === quickQuotePid,
        );
        if (quickQuotePidIdx > -1) {
          const quickQuoteProduct = recommendationProducts.splice(
            quickQuotePidIdx,
            1,
          );
          console.log(recommendationProducts.map(p => p.product.productName));
          return [
            {
              ...quickQuoteProduct[0],
              fromQuickQuote: true,
            },
            ...recommendationProducts,
          ];
        }

        return recommendationProducts;
      }

      case 'id': {
        if (lifeJourney?.productWithPremiumDevelopmentPotential) {
          return [];
        }

        const productRecommendation = productRecommendationAgencyId;

        lifeJourney.concerns.map(concern => {
          recommendIdList[concern] = productRecommendation[concern] || [];
        });

        if (products) {
          Object.keys(recommendIdList).forEach(need => {
            (recommendIdList[need as ConcernId] ?? []).forEach(pid => {
              const product = products.find(p => p?.pid === pid);
              if (
                product &&
                !recommendationProducts.some(p => p.product.pid === pid) //avoid duplication
              ) {
                recommendationProducts.push({
                  need: need as ConcernId,
                  product,
                });
              }
            });
          });
        }

        return recommendationProducts;
      }
      default:
        return [];
    }
  }, [products, channel, lifeJourney, quickQuotePid]);

  const otherProducts = useMemo(() => {
    const recommendationPidList = recommendationProducts.map(
      i => i.product.pid,
    );

    const otherProducts: Product[] = [];
    if (products) {
      for (const p of products) {
        if (p && !recommendationPidList.includes(p.pid)) {
          otherProducts.push(p);
        }
      }
    }

    if (country === 'my') {
      const quickQuotePidIdx = otherProducts.findIndex(
        item => item.pid === quickQuotePid,
      );
      if (quickQuotePidIdx > -1) {
        const quickQuoteProduct = otherProducts.splice(quickQuotePidIdx, 1);
        return [
          {
            ...quickQuoteProduct[0],
            fromQuickQuote: true,
          },
          ...otherProducts,
        ];
      }
    }

    return otherProducts;
  }, [products, quickQuotePid, recommendationProducts]);

  return { recommendationProducts, otherProducts };
};

const getSegmentFromIncomeRange = (range: {
  from: number | null;
  to: number | null;
}) => {
  if (range.from) {
    if (range.from >= 25e4) {
      return 5;
    }
    if (range.from >= 20e4) {
      return 4;
    }
    if (range.from >= 15e4) {
      return 3;
    }
    if (range.from >= 10e4) {
      return 2;
    }
    if (range.from >= 5e4) {
      return 1;
    }
  }

  return 0;
};
