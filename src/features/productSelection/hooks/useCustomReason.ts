import { useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FnaRejectRecommendedReason } from 'types/case';

export const MIN_CUSTOM_REASON_LENGTH = 10;

export const useCustomReason = (
  defaultReason: FnaRejectRecommendedReason | undefined,
  setCustomReasonProp?: (value: string) => void,
) => {
  const { t } = useTranslation(['product']);

  const [reason, setReason] = useState<FnaRejectRecommendedReason | undefined>(
    defaultReason,
  );
  const [customReason, setCustomReason] = useReducer(
    (oldValue: string, newValue: string) => {
      setCustomReasonProp?.(newValue);
      return newValue;
    },
    '',
  );

  const error =
    customReason.length === 0 || customReason.length >= MIN_CUSTOM_REASON_LENGTH
      ? ''
      : t('product:validation.error.minLength10');

  const disabled =
    reason === undefined ||
    reason === FnaRejectRecommendedReason.NOT_REJECT ||
    (reason === FnaRejectRecommendedReason.OTHER_REASONS &&
      customReason.length < MIN_CUSTOM_REASON_LENGTH);

  return { customReason, setCustomReason, error, reason, setReason, disabled };
};
