import { FNA_EMAIL_TEMPLATE } from 'constants/emailTemplates/common/fna';
import { formatPhoneNumberForEmail } from 'utils';

interface MailInfo {
  clientFirstName: string;
  clientEmail?: string;
  agentName: string;
  agentNumber: string;
  agentEmail: string;
}

export const getFnaMailConfig = ({
  clientFirstName,
  clientEmail,
  agentNumber,
  agentName,
  agentEmail,
}: MailInfo) => {
  // TODO: mapping product to template
  const template = FNA_EMAIL_TEMPLATE;
  const replacePlaceholder = (input: string) => {
    return input
      .replaceAll('{{clientFirstName}}', clientFirstName)
      .replaceAll('{{agentRawNumber}}', agentNumber)
      .replaceAll('{{agentNumber}}', formatPhoneNumberForEmail(agentNumber))
      .replaceAll('{{agentName}}', agentName);
  };
  return {
    mailTo: clientEmail,
    mailCc: agentEmail,
    subject: replacePlaceholder(template.subject),
    content: replacePlaceholder(template.content),
    html: replacePlaceholder(template.html),
  };
};
