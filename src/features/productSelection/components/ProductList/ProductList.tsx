import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ProductListPhone, { IProductListPhoneProps } from './ProductList.phone';
import ProductListTablet, {
  IProductListTabletProps,
} from './ProductList.tablet';

export default function ProductList(
  props: IProductListPhoneProps | IProductListTabletProps,
) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <ProductListTablet {...props} />
  ) : (
    <ProductListPhone {...(props as IProductListPhoneProps)} />
  );
}
