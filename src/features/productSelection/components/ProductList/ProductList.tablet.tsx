import React from 'react';
import { country } from 'utils/context';
import MYProductListTablet, { ProductListTabletProps } from './my/ProductList';
import PhProductListTablet, { ProductListProps } from './ph/ProductList';

export type IProductListTabletProps = ProductListTabletProps | ProductListProps;

export default function ProductListTablet(
  props: ProductListTabletProps | ProductListProps,
) {
  switch (country) {
    case 'ph':
      return <PhProductListTablet {...(props as ProductListProps)} />;
    case 'my':
    case 'ib':
    default:
      return <MYProductListTablet {...(props as ProductListTabletProps)} />;
  }
}
