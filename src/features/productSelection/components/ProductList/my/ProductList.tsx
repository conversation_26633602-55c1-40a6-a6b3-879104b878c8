import React from 'react';
import { FlatList } from 'react-native';

import styled from '@emotion/native';
import { Box } from 'cube-ui-components';
import { Product } from 'types/products';
import { country } from 'utils/context';
import ProductCard from '../../ProductCard';

export type ProductListTabletProps = {
  data: Product[];
  onPressItemSelect: (p: Product) => void;
};

const ProductList = ({ data, onPressItemSelect }: ProductListTabletProps) => {
  return (
    <Box flex={1}>
      <FlatList
        data={data}
        keyExtractor={item => item.pid}
        numColumns={4}
        renderItem={({ item }) => (
          <StyledProductCard {...item} onPress={onPressItemSelect} />
        )}
        showsVerticalScrollIndicator={false}
      />
    </Box>
  );
};

const StyledProductCard = styled(ProductCard)(
  ({ theme: { space, sizes } }) => ({
    marginRight: space[3],
    marginBottom: space[5],
    height: country === 'my' ? sizes[59] : sizes[78],
  }),
);

export default ProductList;
