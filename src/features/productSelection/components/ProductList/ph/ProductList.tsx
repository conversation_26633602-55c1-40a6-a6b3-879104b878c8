import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Typography } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import {
  FlatList,
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  StyleProp,
  View,
  ViewStyle,
} from 'react-native';
import { ListProduct, Product } from 'types/products';
import ProductCard from '../../ProductCard';

export type ProductListProps = {
  sections: ListProduct[];
  onSectionLayout: (index: number) => (event: LayoutChangeEvent) => void;
  onScroll: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollViewRef: React.RefObject<ScrollView>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  onPressItemSelect: (p: Product) => void;
  isNarrowScreen?: boolean;
};

const Container = styled.View(({ theme: { space, colors } }) => ({
  flex: 1,
  paddingBottom: space[4],
  backgroundColor: colors.palette.fwdGrey[50],
}));

const ProductList = ({
  sections,
  onSectionLayout,
  onScroll,
  scrollViewRef,
  contentContainerStyle,
  onPressItemSelect,
  isNarrowScreen,
}: ProductListProps) => {
  const { space } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();
  const cardStyle = isTabletMode ? {} : { marginRight: space[6] };
  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={onScroll}
        ref={scrollViewRef}
        contentContainerStyle={contentContainerStyle}>
        {sections.map((section, index) => (
          <View
            key={section.title}
            style={{ paddingTop: space[8] }}
            onLayout={onSectionLayout(index)}>
            <Typography.H6
              fontWeight="bold"
              style={{ paddingHorizontal: space[4] }}>
              {section.title} ({section.data.length})
            </Typography.H6>
            <FlatList
              showsHorizontalScrollIndicator={false}
              style={{
                paddingTop: space[3],
                marginHorizontal: space[isNarrowScreen ? 3 : 4],
              }}
              data={section.data}
              keyExtractor={item => item.productName.en}
              renderItem={({ item }) => (
                <Box mr={isTabletMode ? space[3] : 0}>
                  <ProductCard
                    {...item}
                    onPress={onPressItemSelect}
                    style={cardStyle}
                  />
                </Box>
              )}
              horizontal
            />
          </View>
        ))}
      </ScrollView>
    </Container>
  );
};

export default ProductList;
