import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { RiderListResponse } from 'api/proposalApi';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import {
  Box,
  Checkbox,
  Column,
  CubePictogramIcon,
  H6,
  Row,
  Typography,
} from 'cube-ui-components';
import { format } from 'date-fns';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import FnaConfirmFooter from 'features/fna/components/common/FnaConfirmFooter';
import SummaryContent from 'features/fna/components/summary/SummaryContent';
import SummaryOverview from 'features/fna/components/summary/SummaryOverview';
import { RiderGroup } from 'features/fna/constants/riderListConstants';
import { useFnaSnapPoints } from 'features/fna/hooks/useFnaSnapPoint';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useFormatProductListParties } from 'features/productSelection/hooks/useProducts';
import { useRiderList } from 'features/productSelection/hooks/useRiderList';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SharedValue, useAnimatedStyle } from 'react-native-reanimated';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { ClientWaiverModalProps } from '../ClientWaiverModal';

export default memo(function IDClientWaiverModalPhone({
  handleClose,
  onGetFnaDoc: onGetFnaDocAction,
  onConfirm: onConfirmAction,
  isGettingFnaDoc,
}: Pick<
  ClientWaiverModalProps,
  'handleClose' | 'onGetFnaDoc' | 'onConfirm' | 'isGettingFnaDoc'
>) {
  const { t } = useTranslation(['product', 'fna']);
  const bottomSheetProps = useBottomSheet();

  const today = format(new Date(), 'dd/MM/yyyy');
  const { space, colors } = useTheme();
  const [checked, setChecked] = useState(false);
  const fnaState = useFnaStore();
  const {
    totalInsuranceCompensation,
    recommendedProductType,
    appropriateTypeofAdditionalBenefit,
  } = fnaState.getFnaSummary();

  const { insureds, proposers } = useFormatProductListParties();
  const [riders, setRiders] = useState<RiderListResponse[]>();
  const { mutateAsync: getRiderList } = useRiderList();
  const [expanded, setExpanded] = useState(false);
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { height } = useSafeAreaFrame();
  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const fullHeightSnapPoints = useFnaSnapPoints();
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  useEffect(() => {
    getRiderList({
      insureds,
      proposers,
    }).then(data => {
      const filteredRiders = data.filter(rider =>
        appropriateTypeofAdditionalBenefit.includes(
          rider.riderGroup as RiderGroup,
        ),
      );
      setRiders(filteredRiders);
    });
  }, [getRiderList]);

  // remove duplicate rider groups
  const riderGroups = useMemo(() => {
    return riders?.reduce((acc, rider) => {
      if (!acc.includes(rider.riderGroup as RiderGroup)) {
        acc.push(rider.riderGroup as RiderGroup);
      }
      return acc;
    }, [] as RiderGroup[]);
  }, [riders]);

  const items = [
    {
      key: 'compensation',
      icon: <CubePictogramIcon.Cash size={36} />,
      title: t('fna:totalInsuranceCompensation'),
      content: (
        <Row>
          <Typography.Body
            color={colors.palette.black}
            style={{
              paddingRight: space[1],
              lineHeight: 20,
            }}>
            {t('fna:totalInsuranceCompensation.IDR')}
          </Typography.Body>
          <Typography.H6 fontWeight="bold" color={colors.palette.black}>
            {t(`fna:${totalInsuranceCompensation}` as keyof typeof t)}
          </Typography.H6>
        </Row>
      ),
    },
    {
      key: 'product',
      icon: <CubePictogramIcon.ShieldWithStars size={36} />,
      title: t('fna:recommendedProductType'),
      content: (
        <Row>
          <Typography.LargeBody color={colors.palette.black}>
            {t(`fna:${recommendedProductType}` as keyof typeof t)}
          </Typography.LargeBody>
        </Row>
      ),
    },
    {
      key: 'benefits',
      icon: <CubePictogramIcon.ManWithShield size={36} />,
      title: t('fna:appropriateTypeofAdditionalBenefit'),
      content: (
        <Row>
          <Column>
            {!riderGroups || riderGroups.length === 0 ? (
              <Row>
                <Typography.LargeBody color={colors.palette.black}>
                  {t('fna:appropriateTypeofAdditionalBenefit.no')}
                </Typography.LargeBody>
              </Row>
            ) : (
              riderGroups?.map((group, index) => (
                <Row key={index}>
                  <Typography.LargeBody color={colors.palette.black}>
                    {`${index + 1}. ${group}`}
                  </Typography.LargeBody>
                </Row>
              ))
            )}
          </Column>
        </Row>
      ),
    },
  ];

  const onConfirm = useCallback(async () => {
    await onConfirmAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef, onConfirmAction]);

  const onGetFnaDoc = useCallback(async () => {
    await onGetFnaDocAction();
  }, [onGetFnaDocAction]);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:confirm')}
          disabled={!checked}
          onPress={onConfirm}
          secondaryButtonTitle={t('fna:reviewPdf')}
          onSecondaryPress={onGetFnaDoc}
          secondaryLoading={isGettingFnaDoc}
        />
      );
    },
    [t, onConfirm, onGetFnaDoc, isGettingFnaDoc, checked],
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  return (
    <BottomSheetModal
      {...bottomSheetProps}
      onDismiss={handleClose}
      style={{ padding: 0 }}
      backdropComponent={renderBackdrop}
      snapPoints={
        expanded
          ? fullHeightSnapPoints
          : (animatedSnapPoints as SharedValue<(string | number)[]>)
      }
      handleHeight={animatedHandleHeight}
      contentHeight={animatedContentHeight}
      handleStyle={{ display: 'flex' }}
      footerComponent={renderFooter}>
      <Row
        paddingX={space[isNarrowScreen ? 3 : 4]}
        paddingTop={space[4]}
        justifyContent="space-between">
        <Row alignItems="center" gap={space[3]} flex={1}>
          <H6 fontWeight="bold">{t('fna:fnaSummaryResult')}</H6>
        </Row>
      </Row>
      <BottomSheetScrollView
        onLayout={e => {
          if (!expanded) {
            e.nativeEvent.layout.height += 57;
            handleContentLayout(e);
          }
        }}
        keyboardDismissMode="on-drag"
        style={useAnimatedStyle(() => ({
          paddingHorizontal: space[isNarrowScreen ? 3 : 4],
          marginBottom: space[4],
          maxHeight: height - space[22] - 57 - animatedHandleHeight.value,
        }))}>
        <SummaryOverview items={items} />
        <Header>
          <H6 fontWeight="bold">{t('fna:clientAcknowledgement.heading')}</H6>
        </Header>
        <SummaryContent
          content={t('fna:fnaSummaryContent')}
          expanded={expanded}
          setExpanded={setExpanded}
        />
        <CheckboxContainer>
          <Checkbox
            label={t('fna:fnaSummaryCheckbox')}
            value={checked}
            onChange={setChecked}
            style={{
              alignItems: 'flex-start',
            }}
            labelStyle={{
              marginTop: -space[1],
            }}
            checked={checked}
          />

          <CheckboxSubLabel>
            {t('fna:fnaSummaryCheckboxAcceptedOn', {
              date: today,
            })}
          </CheckboxSubLabel>
        </CheckboxContainer>
        <BottomSheetFooterSpace />
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
});

const Header = styled(Row)(({ theme }) => ({
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.space[4],
}));

const CheckboxSubLabel = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.secondaryVariant,
  marginTop: theme.space[2],
}));

const CheckboxContainer = styled(Box)(({ theme }) => ({
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.medium,
  padding: theme.space[4],
  marginVertical: theme.space[4],
}));
