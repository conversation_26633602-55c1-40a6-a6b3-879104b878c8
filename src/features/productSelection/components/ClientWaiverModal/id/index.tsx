import DeviceBasedRendering from 'components/DeviceBasedRendering';
import React from 'react';
import IDClientWaiverModalTablet from './IDClientWaiverModal.tablet';

import { ClientWaiverModalProps } from '../ClientWaiverModal';
import IDClientWaiverModalPhone from './IDClientWaiverModal.phone';

export default function IDClientWaiverModal({
  handleClose,
  onGetFnaDoc,
  onConfirm,
  isGettingFnaDoc,
}: ClientWaiverModalProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <IDClientWaiverModalTablet
          handleClose={handleClose}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
        />
      }
      phone={
        <IDClientWaiverModalPhone
          handleClose={handleClose}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
        />
      }
    />
  );
}
