import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  Checkbox,
  Column,
  H5,
  H6,
  LargeBody,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import React, { Fragment, memo, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { ScrollView } from 'react-native-gesture-handler';
import { FnaRejectRecommendedReason } from 'types/case';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import styled from '@emotion/native';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { ClientWaiverModalProps } from '../ClientWaiverModal';

export default memo(function PHClientWaiverModal({
  handleClose,
  reason,
  waiverChecked,
  onToggleWaiverChecked,
  onGetFnaDoc: onGetFnaDocAction,
  onConfirm: onConfirmAction,
  isGettingFnaDoc,
  isSaving,
}: ClientWaiverModalProps) {
  const theme = useTheme();
  const { height: windowHeight } = useSafeAreaFrame();

  const { t } = useTranslation(['product']);

  const bottomSheetProps = useBottomSheet();

  const onConfirm = useCallback(async () => {
    await onConfirmAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef, onConfirmAction]);
  const onGetFnaDoc = useCallback(async () => {
    await onGetFnaDocAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef, onGetFnaDocAction]);
  const renderFooter = useCallback(() => {
    return (
      <Footer
        onGetFnaDoc={onGetFnaDoc}
        onConfirm={onConfirm}
        checked={waiverChecked}
        onCheckPress={onToggleWaiverChecked}
        disabled={!waiverChecked}
        isGettingFnaDoc={isGettingFnaDoc}
        isSaving={isSaving}
      />
    );
  }, [
    onGetFnaDoc,
    onConfirm,
    waiverChecked,
    onToggleWaiverChecked,
    isGettingFnaDoc,
    isSaving,
  ]);

  const fullContent = useMemo(() => {
    if (reason && reason === FnaRejectRecommendedReason.USER_SELECTION) {
      return t('product:disclaimer.2');
    }
    return t('product:disclaimer.1');
  }, [t, reason]);

  const shortContent = useMemo(() => {
    if (reason && reason === FnaRejectRecommendedReason.USER_SELECTION) {
      return t('product:disclaimer.2.short');
    }
    return t('product:disclaimer.1.short');
  }, [t, reason]);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [seeMore, setSeeMore] = useState(isTabletMode);

  const Title = isTabletMode ? H5 : H6;
  const Container = isTabletMode ? TabletContainer : Fragment;
  const ContentContainer = isTabletMode ? TabletContentContainer : Fragment;

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          index={0}
          backdropComponent={renderBackdrop}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          handleStyle={{ display: isTabletMode ? 'none' : 'flex' }}
          contentHeight={animatedContentHeight}
          backgroundComponent={isTabletMode ? null : undefined}>
          <BottomSheetView onLayout={handleContentLayout}>
            <Container>
              <ContentContainer>
                <Box
                  mt={isTabletMode ? theme.space[12] : theme.space[4]}
                  mb={isTabletMode ? theme.space[8] : theme.space[4]}
                  mx={
                    isTabletMode
                      ? theme.space[14]
                      : theme.space[isNarrowScreen ? 3 : 4]
                  }>
                  <Title fontWeight="bold">
                    {t('product:disclaimer.title')}
                  </Title>
                </Box>
                <ScrollView
                  style={{
                    maxHeight: isTabletMode
                      ? undefined
                      : windowHeight - theme.space[90],
                  }}>
                  <Column
                    px={
                      isTabletMode
                        ? theme.space[14]
                        : theme.space[isNarrowScreen ? 3 : 4]
                    }>
                    <Row>
                      <LargeBody style={{ flex: 1 }}>
                        {seeMore ? fullContent : shortContent}
                        {!isTabletMode && (
                          <LargeBody
                            suppressHighlighting
                            onPress={() => {
                              if (seeMore) {
                                setSeeMore(false);
                              } else {
                                setSeeMore(true);
                              }
                            }}
                            fontWeight="bold"
                            color={
                              theme.colors.palette.fwdAlternativeOrange[100]
                            }>
                            {seeMore
                              ? ` ${t('product:disclaimer.close')}`
                              : t('product:disclaimer.more')}
                          </LargeBody>
                        )}
                      </LargeBody>
                    </Row>
                  </Column>
                </ScrollView>
                {renderFooter()}
              </ContentContainer>
            </Container>
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const Footer = memo(
  ({
    onGetFnaDoc,
    onConfirm,
    disabled,
    checked,
    onCheckPress,
    isGettingFnaDoc,
    isSaving,
  }: {
    onGetFnaDoc: () => void;
    onConfirm: () => void;
    disabled: boolean;
    checked: boolean;
    onCheckPress: (value: boolean) => void;
    isGettingFnaDoc: boolean;
    isSaving: boolean;
  }) => {
    const { t } = useTranslation(['product']);
    const theme = useTheme();
    const insets = useSafeAreaInsets();
    const today = useMemo(() => {
      return dateFormatUtil(new Date());
    }, []);
    const isPrimaryLoading = isSaving && !isGettingFnaDoc;
    const isSecondaryLoading = isSaving || isGettingFnaDoc;

    const { isNarrowScreen } = useWindowAdaptationHelpers();
    const { isTabletMode } = useLayoutAdoptionCheck();

    return (
      <Box>
        <Box
          backgroundColor={theme.colors.background}
          px={
            isTabletMode ? theme.space[14] : theme.space[isNarrowScreen ? 3 : 4]
          }
          pt={isTabletMode ? theme.space[6] : theme.space[4]}
          pb={isTabletMode ? theme.space[2] : theme.space[4]}>
          <Box
            p={isTabletMode ? theme.space[4] : 0}
            borderWidth={isTabletMode ? (checked ? 2 : 1) : 0}
            borderColor={
              checked ? theme.colors.primary : theme.colors.palette.fwdGrey[100]
            }
            borderRadius={theme.borderRadius.small}
            backgroundColor={
              isTabletMode && checked
                ? theme.colors.primaryVariant3
                : theme.colors.background
            }>
            <Checkbox
              label={t('product:disclaimer.accept')}
              value={checked}
              onChange={onCheckPress}
              style={{
                alignItems: 'flex-start',
                paddingTop: 2,
              }}
              labelStyle={{
                marginTop: -2,
                flex: 1,
              }}
            />
            <Typography.Label
              style={{
                marginTop: theme.space[1],
                marginLeft: theme.sizes[5] + theme.space[3],
                color: theme.colors.palette.fwdGreyDarker,
              }}>{`${t(
              'product:disclaimer.acceptedOn',
            )} ${today}`}</Typography.Label>
          </Box>
        </Box>
        <DeviceBasedRendering
          phone={
            <FormAction
              primaryLoading={isPrimaryLoading}
              primaryDisabled={
                disabled || (!isPrimaryLoading && isSecondaryLoading)
              }
              onPrimaryPress={onConfirm}
              primaryLabel={t('product:done')}
              secondaryLoading={!isPrimaryLoading && isSecondaryLoading}
              secondaryDisabled={isPrimaryLoading}
              secondaryLabel={t('product:shareFNADoc')}
              onSecondaryPress={onGetFnaDoc}
              style={{ gap: theme.space[4] }}
            />
          }
          tablet={
            <Row
              pb={insets.bottom + theme.space[4]}
              p={theme.space[4]}
              gap={theme.space[4]}
              justifyContent="flex-end">
              <Button
                variant="secondary"
                text={t('product:shareFNADoc')}
                style={{ width: 200 }}
                onPress={onGetFnaDoc}
                loading={!isPrimaryLoading && isSecondaryLoading}
                disabled={isPrimaryLoading}
              />
              <Button
                disabled={disabled || (!isPrimaryLoading && isSecondaryLoading)}
                loading={isPrimaryLoading}
                onPress={onConfirm}
                text={t('product:done')}
                style={{ width: 200 }}
              />
            </Row>
          }
        />
      </Box>
    );
  },
);

const TabletContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.primary,
  paddingTop: 6,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const TabletContentContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));
