import React, { memo, useCallback, useMemo } from 'react';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  Checkbox,
  CheckboxProps,
  Column,
  H6,
  Icon,
  LargeBody,
  Row,
} from 'cube-ui-components';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native-gesture-handler';
import styled from '@emotion/native';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { ClientWaiverModalProps } from '../ClientWaiverModal';
import { TouchableOpacity } from 'react-native';

export default memo(function IBClientWaiverModalTablet({
  handleClose,
  waiverChecked,
  onToggleWaiverChecked,
  providedServiceGuideChecked,
  onToggleServiceGuideChecked,
  onGetFnaDoc: onGetFnaDocAction,
  onConfirm: onConfirmAction,
  isGettingFnaDoc,
  isSaving,
  isViewedDoc,
}: ClientWaiverModalProps) {
  const theme = useTheme();
  const { t } = useTranslation(['product']);
  const bottomSheetProps = useBottomSheet();

  const onConfirm = useCallback(async () => {
    await onConfirmAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef, onConfirmAction]);
  const onGetFnaDoc = useCallback(async () => {
    await onGetFnaDocAction();
  }, [onGetFnaDocAction]);

  const handleCloseBottomSheet = useCallback(async () => {
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef]);

  const renderFooter = useCallback(() => {
    return (
      <Footer
        onGetFnaDoc={onGetFnaDoc}
        handleClose={handleCloseBottomSheet}
        onConfirm={onConfirm}
        waiverChecked={waiverChecked}
        onToggleWaiverChecked={onToggleWaiverChecked}
        providedServiceGuideChecked={providedServiceGuideChecked}
        onToggleServiceGuideChecked={onToggleServiceGuideChecked}
        disabled={!waiverChecked || !providedServiceGuideChecked}
        isGettingFnaDoc={isGettingFnaDoc}
        isSaving={isSaving}
        isViewedDoc={isViewedDoc}
      />
    );
  }, [
    onConfirm,
    waiverChecked,
    onToggleWaiverChecked,
    providedServiceGuideChecked,
    onToggleServiceGuideChecked,
    isGettingFnaDoc,
    isSaving,
    onGetFnaDoc,
    handleCloseBottomSheet,
    isViewedDoc,
  ]);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          index={0}
          backdropComponent={renderBackdrop}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          handleStyle={{ display: 'none' }}
          contentHeight={animatedContentHeight}
          backgroundComponent={null}>
          <BottomSheetView onLayout={handleContentLayout}>
            <TabletContainer>
              <TabletContentContainer>
                <Row
                  mt={theme.space[6]}
                  mb={theme.space[8]}
                  mx={theme.space[10]}
                  gap={theme.space[3]}>
                  <TouchableOpacity onPress={handleCloseBottomSheet}>
                    <Icon.Close fill={theme.colors.secondary} />
                  </TouchableOpacity>
                  <H6 fontWeight="bold">{t('product:disclaimer.title')}</H6>
                </Row>
                <ScrollView>
                  <Column px={theme.space[10]}>
                    <Row>
                      <LargeBody style={{ flex: 1 }}>
                        {t('product:disclaimer.1')}
                      </LargeBody>
                    </Row>
                  </Column>
                </ScrollView>
                {renderFooter()}
              </TabletContentContainer>
            </TabletContainer>
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const Footer = memo(
  ({
    onGetFnaDoc,
    handleClose,
    onConfirm,
    disabled,
    waiverChecked,
    onToggleWaiverChecked,
    providedServiceGuideChecked,
    onToggleServiceGuideChecked,
    isGettingFnaDoc,
    isSaving,
    isViewedDoc,
  }: {
    onGetFnaDoc: () => void;
    handleClose: () => void;
    onConfirm: () => void;
    disabled: boolean;
    waiverChecked: boolean;
    onToggleWaiverChecked: (value: boolean) => void;
    providedServiceGuideChecked: boolean;
    onToggleServiceGuideChecked: (value: boolean) => void;
    isGettingFnaDoc: boolean;
    isSaving: boolean;
    isViewedDoc: boolean;
  }) => {
    const { t } = useTranslation(['product', 'common']);
    const theme = useTheme();
    const insets = useSafeAreaInsets();
    const isPrimaryLoading = isSaving && !isGettingFnaDoc;
    const isSecondaryLoading = isGettingFnaDoc;

    return (
      <Box>
        <Box px={theme.space[14]} pt={theme.space[6]} pb={theme.space[4]}>
          {isViewedDoc && (
            <Box gap={theme.space[4]}>
              <WaiverCheckBox
                label={t('product:disclaimer.accept')}
                value={waiverChecked}
                onChange={onToggleWaiverChecked}
                labelStyle={{
                  flex: 1,
                }}
                checked={waiverChecked}
              />
              <WaiverCheckBox
                label={t('product:disclaimer.accept2')}
                value={providedServiceGuideChecked}
                onChange={onToggleServiceGuideChecked}
                labelStyle={{
                  flex: 1,
                }}
                checked={providedServiceGuideChecked}
              />
            </Box>
          )}
        </Box>

        <DeviceBasedRendering
          phone={null}
          tablet={
            <Row
              pb={insets.bottom + theme.space[4]}
              p={theme.space[4]}
              gap={theme.space[4]}
              justifyContent="flex-end"
              borderTopWidth={1}
              borderColor={theme.colors.palette.fwdGrey[100]}>
              {isViewedDoc ? (
                <>
                  <Box backgroundColor={theme.colors.background}>
                    <Button
                      variant="secondary"
                      text={t('product:viewCFF')}
                      onPress={onGetFnaDoc}
                      loading={isSecondaryLoading}
                      disabled={isSecondaryLoading}
                    />
                  </Box>
                  <Button
                    disabled={disabled || isPrimaryLoading}
                    loading={isPrimaryLoading}
                    onPress={onConfirm}
                    text={t('product:done')}
                    style={{ width: 200 }}
                  />
                </>
              ) : (
                <Button
                  text={t('product:previewCFF')}
                  onPress={onGetFnaDoc}
                  loading={isSecondaryLoading}
                  disabled={isSecondaryLoading}
                  style={{ width: 200 }}
                />
              )}
            </Row>
          }
        />
      </Box>
    );
  },
);

const TabletContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.primary,
  paddingTop: 6,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const TabletContentContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const WaiverCheckBox = styled(Checkbox)<CheckboxProps & { checked: boolean }>(
  ({ theme, checked }) => ({
    borderWidth: 1,
    borderRadius: theme.space[2],
    padding: theme.space[4],
    backgroundColor: checked
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    borderColor: checked
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
  }),
);
