import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import IBClientWaiverModalTablet from './IBClientWaiverModal.tablet';
import IBClientWaiverModalPhone from './IBClientWaiverModal.phone';
import { ClientWaiverModalProps } from '../ClientWaiverModal';

export default function IBClientWaiverModal({
  handleClose,
  waiverChecked,
  onToggleWaiverChecked,
  providedServiceGuideChecked,
  onToggleServiceGuideChecked,
  onGetFnaDoc,
  onConfirm,
  isGettingFnaDoc,
  isSaving,
  isViewedDoc,
}: ClientWaiverModalProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <IBClientWaiverModalTablet
          handleClose={handleClose}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={onToggleWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={onToggleServiceGuideChecked}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      }
      phone={
        <IBClientWaiverModalPhone
          handleClose={handleClose}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={onToggleWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={onToggleServiceGuideChecked}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      }
    />
  );
}
