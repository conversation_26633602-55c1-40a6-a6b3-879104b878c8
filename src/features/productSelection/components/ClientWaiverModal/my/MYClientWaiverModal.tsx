import React from 'react';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import MYClientWaiverModalTablet from './MYClientWaiverModal.tablet';
import MYClientWaiverModalPhone from './MYClientWaiverModal.phone';
import { ClientWaiverModalProps } from '../ClientWaiverModal';

export default function MYClientWaiverModal({
  handleClose,
  waiverChecked,
  onToggleWaiverChecked,
  providedServiceGuideChecked,
  onToggleServiceGuideChecked,
  onGetFnaDoc,
  onConfirm,
  isGettingFnaDoc,
  isSaving,
  isViewedDoc,
}: ClientWaiverModalProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <MYClientWaiverModalTablet
          handleClose={handleClose}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={onToggleWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={onToggleServiceGuideChecked}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      }
      phone={
        <MYClientWaiverModalPhone
          handleClose={handleClose}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={onToggleWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={onToggleServiceGuideChecked}
          onGetFnaDoc={onGetFnaDoc}
          onConfirm={onConfirm}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      }
    />
  );
}
