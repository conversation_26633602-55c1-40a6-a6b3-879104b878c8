import { LinearGradient } from 'expo-linear-gradient';
import { ColorValue } from 'react-native';
import { Svg, Circle, Path } from 'react-native-svg';

export default function GuruButton({ color }: { color: ColorValue }) {
  return (
    <LinearGradient
      colors={['#E87722', '#E87722', '#FED141', '#6ECEB2', '#E87722']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={{
        width: 50,
        height: 50,
        borderRadius: 25,
        borderColor: color,
        padding: 2,
      }}>
      <Svg width="46" height="46" viewBox="0 0 46 46" fill="white">
        <Circle cx="23" cy="23" r="23" fill={color} fill-opacity="0.8" />
        <Path
          d="M21.7369 32.2839C21.1294 32.0091 21.1294 31.1464 21.7369 30.8716L22.4006 30.5714C22.6043 30.4793 22.7583 30.3038 22.823 30.0899L22.9213 29.7653C23.1435 29.0313 24.1826 29.0313 24.4048 29.7653L24.5031 30.0899C24.5678 30.3038 24.7218 30.4793 24.9255 30.5714L25.5892 30.8716C26.1967 31.1464 26.1967 32.0091 25.5892 32.2839L24.9255 32.5841C24.7218 32.6762 24.5678 32.8517 24.5031 33.0656L24.4048 33.3902C24.1826 34.1242 23.1435 34.1242 22.9213 33.3902L22.823 33.0656C22.7583 32.8517 22.6043 32.6762 22.4006 32.5841L21.7369 32.2839Z"
          fill="white"
        />
        <Path
          d="M28.368 22.9325C28.6267 22.28 29.5502 22.28 29.8089 22.9325L30.5272 24.7443C30.6104 24.9543 30.7812 25.1175 30.9947 25.1912L32.7799 25.8078C33.4759 26.0481 33.4759 27.0325 32.7799 27.2729L30.9947 27.8894C30.7812 27.9631 30.6104 28.1263 30.5272 28.3363L29.8089 30.1481C29.5502 30.8006 28.6267 30.8006 28.368 30.1481L27.6497 28.3363C27.5664 28.1263 27.3957 27.9631 27.1822 27.8894L25.397 27.2729C24.701 27.0325 24.701 26.0481 25.397 25.8078L27.1822 25.1912C27.3957 25.1175 27.5664 24.9543 27.6497 24.7443L28.368 22.9325Z"
          fill="white"
        />
        <Path
          d="M13.0462 20.6555C12.0341 20.281 12.0341 18.8495 13.0462 18.475L16.3867 17.2389C16.7049 17.1212 16.9557 16.8703 17.0735 16.5521L18.3096 13.2116C18.6841 12.1995 20.1156 12.1995 20.4901 13.2116L21.7262 16.5521C21.8439 16.8703 22.0948 17.1212 22.413 17.2389L25.7535 18.475C26.7655 18.8495 26.7655 20.281 25.7535 20.6555L22.413 21.8916C22.0948 22.0094 21.8439 22.2602 21.7262 22.5784L20.4901 25.9189C20.1156 26.931 18.6841 26.931 18.3096 25.9189L17.0735 22.5784C16.9557 22.2602 16.7049 22.0094 16.3867 21.8916L13.0462 20.6555Z"
          fill="white"
        />
      </Svg>
    </LinearGradient>
  );
}
