import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  H6,
  RadioButton,
  Row,
  TextField,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView } from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { reasons } from './MYClientReasonModal';
import {
  useCustomReason
} from 'features/productSelection/hooks/useCustomReason';

export default function MYClientReasonModalTablet({
  reason: defaultReason,
  setCustomReason: setCustomReasonProp,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  const { t } = useTranslation(['product']);
  const { space, colors, borderRadius, typography } = useTheme();

  const { height } = useSafeAreaFrame();

  const { setCustomReason, error, reason, setReason, disabled } =
    useCustomReason(defaultReason, setCustomReasonProp);

  return (
    <CFFModal visible={visible}>
      <KeyboardAvoidingViewContainer behavior="padding">
        <Box
          w={762}
          maxH={height - space[40]}
          backgroundColor={colors.background}
          p={space[12]}
          borderRadius={borderRadius.large}>
          <Box>
            <Row mb={space[5]} justifyContent="space-between">
              <H6 fontWeight="bold">{t('product:reason.title')}</H6>
            </Row>
            <Box gap={space[6]}>
              {reasons.map(r => {
                const checked = reason === r;
                const isOtherReasons =
                  r === FnaRejectRecommendedReason.OTHER_REASONS;
                return (
                  <Box key={r}>
                    <RadioButton
                      key={r}
                      value={`${r}`}
                      selected={checked}
                      label={t(`product:reason.${r}`)}
                      onSelect={() => {
                        if (!isOtherReasons) setCustomReason('');
                        setReason(r);
                      }}
                      labelStyle={{
                        marginLeft: space[2],
                        fontSize: typography['largeBody'].size,
                        lineHeight: typography['largeBody'].lineHeight,
                      }}
                    />
                    {isOtherReasons && checked && (
                      <OtherReasonInput
                        placeholder={t('product:reason.G.placeholder')}
                        onChangeText={setCustomReason}
                        error={error}
                      />
                    )}
                  </Box>
                );
              })}
            </Box>
            <Row justifyContent="center" mt={space[6]} gap={space[4]}>
              <ActionButton
                size="medium"
                variant="secondary"
                text={t('product:back')}
                onPress={handleClose}
              />
              <ActionButton
                size="medium"
                text={t('product:continue')}
                onPress={() => {
                  if (reason) {
                    onConfirm(reason);
                    handleClose();
                  }
                }}
                disabled={disabled}
              />
            </Row>
          </Box>
        </Box>
      </KeyboardAvoidingViewContainer>
    </CFFModal>
  );
}

const ActionButton = styled(Button)({
  width: 200,
});

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));

const OtherReasonInput = styled(TextField)(({ theme }) => ({
  marginTop: 18,
  marginLeft: theme.space[8],
}));
