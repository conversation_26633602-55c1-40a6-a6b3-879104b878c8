import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import MYClientReasonModalPhone from './MYClientReasonModal.phone';
import MYClientReasonModalTablet from './MYClientReasonModal.tablet';

export const reasons: FnaRejectRecommendedReason[] = [
  FnaRejectRecommendedReason.PREFER_RETURNS,
  FnaRejectRecommendedReason.PREFER_INVESTMENT,
  FnaRejectRecommendedReason.PREFER_CHEAPER,
  FnaRejectRecommendedReason.PREFER_SHORTER,
  FnaRejectRecommendedReason.PREFER_LOWER_PAYMENT,
  FnaRejectRecommendedReason.OTHER_PLAN_FEATURES,
  FnaRejectRecommendedReason.OTHER_REASONS,
];

export default function MYClientReasonModal({
  reason: defaultReason,
  setCustomReason,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <MYClientReasonModalTablet
          reason={defaultReason}
          setCustomReason={setCustomReason}
          handleClose={handleClose}
          onConfirm={onConfirm}
          visible={visible}
        />
      }
      phone={
        <MYClientReasonModalPhone
          reason={defaultReason}
          setCustomReason={setCustomReason}
          handleClose={handleClose}
          onConfirm={onConfirm}
          visible={visible}
        />
      }
    />
  );
}
