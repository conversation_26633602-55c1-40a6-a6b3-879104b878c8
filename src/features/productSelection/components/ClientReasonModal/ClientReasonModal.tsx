import { Box } from 'cube-ui-components';
import React, { memo } from 'react';
import { FnaRejectRecommendedReason } from 'types/case';
import { country } from 'utils/context';
import IBClientReasonModal from './ib/IBClientReasonModal';
import MYClientReasonModal from './my/MYClientReasonModal';
import PHClientReasonModal from './ph/PHClientReasonModal';

export interface ClientReasonModalProps {
  reason?: FnaRejectRecommendedReason;
  setCustomReason?: (value: string) => void;
  handleClose: () => void;
  onConfirm: (reason: FnaRejectRecommendedReason) => void;
  visible: boolean;
}

export default memo(function ClientReasonModal(props: ClientReasonModalProps) {
  return <Box>{ClientReason && <ClientReason {...props} />}</Box>;
});
const ClientReason = {
  ib: IBClientReasonModal,
  ph: PHClientReasonModal,
  id: null,
  my: MYClientReasonModal,
}[country];
