import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { Box, Button, H5, H6, RadioButton, Row } from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { Fragment, memo, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';

const reasons: FnaRejectRecommendedReason[] = [
  FnaRejectRecommendedReason.EXISTING_FWD_POLICY,
  FnaRejectRecommendedReason.EXISTING_OTHER_POLICY,
  FnaRejectRecommendedReason.USER_SELECTION,
];

export default memo(function PHClientReasonModal({
  reason: defaultReason,
  handleClose,
  onConfirm,
}: ClientReasonModalProps) {
  const theme = useTheme();

  const { t } = useTranslation(['product']);
  const insets = useSafeAreaInsets();

  const bottomSheetProps = useBottomSheet();

  const [reason, setReason] = useState<FnaRejectRecommendedReason | undefined>(defaultReason);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H5 : H6;
  const Container = isTabletMode ? TabletContainer : Fragment;
  const ContentContainer = isTabletMode ? TabletContentContainer : Fragment;

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          index={0}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          handleStyle={{ display: isTabletMode ? 'none' : 'flex' }}
          contentHeight={animatedContentHeight}
          backdropComponent={renderBackdrop}
          backgroundComponent={isTabletMode ? null : undefined}>
          <BottomSheetView onLayout={handleContentLayout}>
            <Container>
              <ContentContainer>
                <Box
                  mt={isTabletMode ? theme.space[12] : theme.space[4]}
                  mb={isTabletMode ? theme.space[8] : theme.space[4]}
                  mx={
                    isTabletMode
                      ? theme.space[14]
                      : theme.space[isNarrowScreen ? 3 : 4]
                  }>
                  <Title fontWeight="bold">{t('product:reason.title2')}</Title>
                </Box>
                <Box
                  backgroundColor={theme.colors.background}
                  px={
                    isTabletMode
                      ? theme.space[14]
                      : theme.space[isNarrowScreen ? 3 : 4]
                  }
                  mb={isTabletMode ? theme.space[6] : 0}>
                  {reasons.map((r, index) => (
                    <RadioButton
                      key={r}
                      value={`${r}`}
                      selected={reason === r}
                      label={t(`product:reason.${r}`)}
                      onSelect={() => setReason(r)}
                      style={{
                        marginTop: index > 0 ? 10 : 0,
                        alignItems: 'flex-start',
                        paddingTop: 1,
                      }}
                      labelStyle={{
                        flex: 1,
                        marginLeft: isTabletMode ? 12 : 9,
                        fontSize:
                          theme.typography[isTabletMode ? 'largeBody' : 'label']
                            .size,
                        lineHeight:
                          theme.typography[isTabletMode ? 'largeBody' : 'label']
                            .lineHeight,
                      }}
                    />
                  ))}
                </Box>
                <Box height={theme.space[isTabletMode ? 2 : 3]} />
                <DeviceBasedRendering
                  phone={
                    <FormAction
                      primaryDisabled={
                        reason === undefined ||
                        reason === FnaRejectRecommendedReason.NOT_REJECT
                      }
                      onPrimaryPress={() => {
                        if (reason) {
                          onConfirm(reason);
                        }
                        bottomSheetProps.bottomSheetRef.current?.close();
                      }}
                      primaryLabel={t('product:continue')}
                      secondaryLabel={t('product:back')}
                      onSecondaryPress={() =>
                        bottomSheetProps.bottomSheetRef.current?.close()
                      }
                      style={{ gap: theme.space[4] }}
                    />
                  }
                  tablet={
                    <Row
                      pb={insets.bottom + theme.space[4]}
                      p={theme.space[4]}
                      gap={theme.space[4]}
                      justifyContent="flex-end">
                      <Button
                        variant="secondary"
                        text={t('product:back')}
                        style={{ width: 100 }}
                        onPress={() =>
                          bottomSheetProps.bottomSheetRef.current?.close()
                        }
                      />
                      <Button
                        disabled={
                          reason === undefined ||
                          reason === FnaRejectRecommendedReason.NOT_REJECT
                        }
                        onPress={() => {
                          if (reason) {
                            onConfirm(reason);
                          }
                          bottomSheetProps.bottomSheetRef.current?.close();
                        }}
                        text={t('product:continue')}
                        style={{ width: 200 }}
                      />
                    </Row>
                  }
                />
              </ContentContainer>
            </Container>
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
});

const TabletContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.primary,
  paddingTop: 6,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const TabletContentContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));
