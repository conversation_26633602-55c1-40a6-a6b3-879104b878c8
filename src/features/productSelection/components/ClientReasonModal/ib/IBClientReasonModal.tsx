import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import IBClientReasonModalPhone from './IBClientReasonModal.phone';
import IBClientReasonModalTablet from './IBClientReasonModal.tablet';

export const reasons: FnaRejectRecommendedReason[] = [
  FnaRejectRecommendedReason.NOT_WITHIN_MY_PREFERENCE,
  FnaRejectRecommendedReason.NOT_MEET_MY_NEEDS,
  FnaRejectRecommendedReason.EXISTING_POLICY_SAME_FEATURES,
];

export default function IBClientReasonModal({
  reason: defaultReason,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  return (
    <DeviceBasedRendering
      tablet={
        <IBClientReasonModalTablet
          reason={defaultReason}
          handleClose={handleClose}
          onConfirm={onConfirm}
          visible={visible}
        />
      }
      phone={
        <IBClientReasonModalPhone
          reason={defaultReason}
          handleClose={handleClose}
          onConfirm={onConfirm}
          visible={visible}
        />
      }
    />
  );
}
