import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, H6, RadioButton, Row } from 'cube-ui-components';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView } from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { reasons } from './IBClientReasonModal';

export default function IBClientReasonModalTablet({
  reason: defaultReason,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  const { t } = useTranslation(['product']);
  const { space, colors, borderRadius, typography } = useTheme();

  const [reason, setReason] = useState<FnaRejectRecommendedReason | undefined>(
    defaultReason,
  );
  const { height } = useSafeAreaFrame();

  return (
    <CFFModal visible={visible}>
      <KeyboardAvoidingViewContainer behavior="padding">
        <Box
          w={762}
          maxH={height - space[40]}
          backgroundColor={colors.background}
          p={space[12]}
          borderRadius={borderRadius.large}>
          <Box>
            <Row mb={space[5]} justifyContent="space-between">
              <H6 fontWeight="bold">{t('product:reason.title')}</H6>
            </Row>
            <Box>
              {reasons.map(r => {
                const checked = reason === r;
                return (
                  <Box
                    key={r}
                    mb={space[3]}
                    px={space[3]}
                    py={space[4]}
                    borderWidth={1}
                    borderColor={
                      checked ? colors.primary : colors.palette.fwdGrey[100]
                    }
                    borderRadius={borderRadius.small}
                    backgroundColor={
                      checked ? colors.primaryVariant3 : colors.background
                    }>
                    <RadioButton
                      key={r}
                      value={`${r}`}
                      selected={reason === r}
                      label={t(`product:reason.${r}`)}
                      onSelect={() => setReason(r)}
                      labelStyle={{
                        marginLeft: space[2],
                        fontSize: typography['largeBody'].size,
                        lineHeight: typography['largeBody'].lineHeight,
                      }}
                    />
                  </Box>
                );
              })}
            </Box>
            <Row justifyContent="center" mt={space[3]} gap={space[4]}>
              <ActionButton
                size="medium"
                variant="secondary"
                text={t('product:back')}
                onPress={handleClose}
              />
              <ActionButton
                size="medium"
                text={t('product:continue')}
                onPress={() => {
                  if (reason) {
                    onConfirm(reason);
                    handleClose();
                  }
                }}
                disabled={
                  reason === undefined ||
                  reason === FnaRejectRecommendedReason.NOT_REJECT
                }
              />
            </Row>
          </Box>
        </Box>
      </KeyboardAvoidingViewContainer>
    </CFFModal>
  );
}
const ActionButton = styled(Button)({
  width: 200,
});

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));
