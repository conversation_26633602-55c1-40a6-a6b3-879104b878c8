import { useTheme } from '@emotion/react';
import {
  Box,
  H6,
  RadioButton,
  Row,
} from 'cube-ui-components';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import { reasons } from './IBClientReasonModal';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { SharedValue } from 'react-native-reanimated';
import FnaConfirmFooter from 'features/fna/components/common/FnaConfirmFooter';

export default function IBClientReasonModalPhone({
  reason: defaultReason,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  const bottomSheetProps = useBottomSheet();
  const { t } = useTranslation(['product']);
  const { space, colors, borderRadius, typography } = useTheme();

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const [reason, setReason] = useState<FnaRejectRecommendedReason | undefined>(
    defaultReason,
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );


  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => (
      <FnaConfirmFooter
        {...props}
        buttonTitle={t('product:continue')}
        onPress={() => {
          if (reason) {
            onConfirm(reason);
            handleClose();
          }
        }}
        disabled={
          reason === undefined ||
          reason === FnaRejectRecommendedReason.NOT_REJECT
        }
        secondaryButtonTitle={t('product:back')}
        onSecondaryPress={handleClose}
      />
    ),
    [t, reason, handleClose, onConfirm],
  );

  if (!visible) return null;

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          backdropComponent={renderBackdrop}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
          handleStyle={{ display: 'flex' }}
          backgroundComponent={undefined}
          footerComponent={renderFooter}>
          <BottomSheetView onLayout={handleContentLayout}>
            <Box paddingX={space[isNarrowScreen ? 3 : 4]} paddingY={space[4]}>
              <H6 fontWeight="bold">{t('product:reason.title')}</H6>
            </Box>
            <Box p={space[4]} pt={0}>
              {reasons.map(r => {
                const checked = reason === r;
                return (
                  <Box
                    key={r}
                    mb={space[3]}
                    px={space[3]}
                    py={space[4]}
                    borderWidth={1}
                    borderColor={
                      checked ? colors.primary : colors.palette.fwdGrey[100]
                    }
                    borderRadius={borderRadius.small}
                    backgroundColor={
                      checked ? colors.primaryVariant3 : colors.background
                    }>
                    <RadioButton
                      key={r}
                      value={`${r}`}
                      selected={reason === r}
                      label={t(`product:reason.${r}`)}
                      onSelect={() => setReason(r)}
                      labelStyle={{
                        marginLeft: space[2],
                        fontSize: typography['largeBody'].size,
                        lineHeight: typography['largeBody'].lineHeight,
                        flex: 1
                      }}
                    />
                  </Box>
                );
              })}
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}
