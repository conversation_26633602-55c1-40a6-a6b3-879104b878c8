import {
  View,
  ScrollView,
  NativeSyntheticEvent,
  NativeScrollEvent,
  LayoutChangeEvent,
  StyleProp,
  ViewStyle,
  useWindowDimensions,
} from 'react-native';
import React, { useState } from 'react';
import { Box, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { ListProduct, Product } from 'types/products';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ProductCard from 'features/productSelection/components/ProductCard';

export type OtherRecommendationProductListProps = {
  sections: ListProduct[];
  onSectionLayout: (index: number) => (event: LayoutChangeEvent) => void;
  onScroll: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
  scrollViewRef: React.RefObject<ScrollView>;
  contentContainerStyle?: StyleProp<ViewStyle>;
  onPressItemSelect: (p: Product) => void;
  isNarrowScreen?: boolean;
  vertical?: boolean;
};

const Container = styled.View(({ theme: { space, colors } }) => ({
  flex: 1,
  paddingBottom: space[4],
  backgroundColor: colors.palette.fwdGrey[50],
}));

const OtherRecommendationProductList = ({
  sections,
  onSectionLayout,
  onScroll,
  scrollViewRef,
  contentContainerStyle,
  onPressItemSelect,
  isNarrowScreen,
  vertical,
}: OtherRecommendationProductListProps) => {
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { width } = useWindowDimensions();
  const [productListWidth, setProductListWidth] = useState<number[]>([]);

  return (
    <Container>
      <ScrollView
        nestedScrollEnabled={vertical}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={onScroll}
        ref={scrollViewRef}
        style={{
          minHeight: isTabletMode ? 323 : 304,
        }}
        contentContainerStyle={contentContainerStyle}>
        {sections.map((section, sectionIndex) => (
          <View
            key={section.title}
            style={{
              paddingTop: space[isTabletMode ? 5 : 6],
              minHeight: isTabletMode ? 323 : 304,
            }}
            onLayout={onSectionLayout(sectionIndex)}>
            <Typography.H6
              fontWeight="bold"
              style={{ paddingHorizontal: space[isTabletMode ? 6 : 4] }}>
              {section.title} ({section.data.length})
            </Typography.H6>
            <ScrollView
              showsHorizontalScrollIndicator={false}
              style={{
                paddingTop: space[isTabletMode ? 6 : 4],
              }}
              onContentSizeChange={w => {
                setProductListWidth(prev => [...prev, w]);
              }}
              contentContainerStyle={{
                paddingLeft: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 3 : 4],
                paddingRight: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 3 : 4],
                gap: space[3],
              }}
              horizontal={!vertical}>
              {section.data
                .reduce<Product[][]>((acc, item) => {
                  // if vertical, add 2 items into 1 nest array [[item1, item2], [item3, item4], ...]
                  // if horizontal, add 1 item into 1 nest array [[item1], [item2], ...]
                  if (acc.length === 0) {
                    acc.push([item]);
                  } else if (vertical && acc[acc.length - 1].length < 2) {
                    acc[acc.length - 1].push(item);
                  } else if (acc[acc.length - 1].length === 0) {
                    acc[acc.length - 1].push(item);
                  } else {
                    acc.push([item]);
                  }
                  return acc;
                }, [])
                .map((items, sectionDataIndex) => {
                  const isLast = sectionDataIndex === section.data.length - 1;
                  const item1 = items[0];
                  const item2 = items[1];
                  return (
                    <Row
                      gap={space[3]}
                      key={`${item1?.pid}_${item2?.pid}`}
                      zIndex={999 - sectionDataIndex}
                      overflow="visible">
                      <ProductCard
                        {...item1}
                        onPress={onPressItemSelect}
                        isInRecommendation
                        vertical={vertical}
                        popUpDirection={
                          productListWidth[sectionIndex] > width && isLast
                            ? 'left'
                            : 'right'
                        }
                      />
                      {item2 ? (
                        <ProductCard
                          {...item2}
                          onPress={onPressItemSelect}
                          isInRecommendation
                          vertical={vertical}
                          popUpDirection={
                            productListWidth[sectionIndex] > width && isLast
                              ? 'left'
                              : 'right'
                          }
                        />
                      ) : (
                        <Box
                          display={vertical ? 'flex' : 'none'}
                          flex={1}
                          padding={space[isTabletMode ? 4 : 2]}
                        />
                      )}
                    </Row>
                  );
                })}
            </ScrollView>
          </View>
        ))}
      </ScrollView>
    </Container>
  );
};

export default OtherRecommendationProductList;
