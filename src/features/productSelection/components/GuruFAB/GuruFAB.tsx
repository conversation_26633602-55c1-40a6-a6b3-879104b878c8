import styled from '@emotion/native';
import { XView } from 'cube-ui-components';
import Animated from 'react-native-reanimated';
import { useCard } from '../CardContext';
import { PropsWithChildren, useMemo } from 'react';
import { randomUUID } from 'expo-crypto';
import {
  GuruSuggestion,
  GuruSuggestionActive,
} from 'features/productSelection/assets';

const GURU_SUGGESTION_BUTTON_WIDTH = 46;
const GURU_SUGGESTION_BUTTON_HEIGHT = 46;

const Guru = styled.Image(() => ({
  width: GURU_SUGGESTION_BUTTON_WIDTH,
  height: GURU_SUGGESTION_BUTTON_HEIGHT,
}));

const GuruSuggestionButton = styled.TouchableOpacity(() => ({}));

const Gap = styled.View(({ theme: { space } }) => ({
  width: space[2],
  height: space[2],
}));

export type GuruFABProps = PropsWithChildren<{
  popUpDirection?: 'left' | 'right';
}>;

export default function GuruFAB({
  popUpDirection = 'right',
  children,
}: GuruFABProps) {
  const cardKey = useMemo(randomUUID, []);
  const {
    isActive,
    onPress: onCardPress,
    animatedStyle,
  } = useCard(cardKey, popUpDirection);

  return (
    <XView
      style={{
        position: 'absolute',
        top: '28%',
        left: '72%',
      }}>
      <GuruSuggestionButton onPress={onCardPress}>
        <Guru source={isActive ? GuruSuggestionActive : GuruSuggestion} />
      </GuruSuggestionButton>
      <Gap />
      <Animated.View style={animatedStyle}>{children}</Animated.View>
    </XView>
  );
}
