import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import IdNumberField from 'components/IdNumberField';
import LoadingIndicator from 'components/LoadingIndicator';
import { NEW_NRIC } from 'constants/optionList';
import {
  Body,
  Box,
  Button,
  H6,
  Icon,
  LargeBody,
  LargeLabel,
  RadioButton,
  Row,
  Text,
} from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { FamilySharePlanType } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  Keyboard,
  KeyboardAvoidingView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { isValidNewNRIC } from 'utils/helper/idNumberUtils';
import type { InternalFamilySharePlanProps } from './FamilySharePlan';
import LinkedDocumentIcon from './LinkedDocumentIcon';
import NotesIcon from './NotesIcon';
import { UnionPolicy } from 'hooks/useGetUnionPolicyList';

const keyExtractor = (item: UnionPolicy) => item.policyNumber;

export default function FamilySharePlanTablet({
  visible,
  onDismiss,
  onDone,
  loading,
  onEnter,
  option,
  onChangeOption,
  result,
  selectedPlan,
  setSelectedPlan,
  reset,
}: InternalFamilySharePlanProps) {
  const { t } = useTranslation(['product', 'common']);
  const { space, colors, borderRadius } = useTheme();
  const [nric, setNric] = useState('');

  const error = useMemo(() => {
    if (!isValidNewNRIC(nric)) return t('product:familySharePlan.invalidNric');

    if (result.isSuccess) {
      if (!result.isValid) return t('product:familySharePlan.hasDeathClaim');
      if (option === 'existing' && result.plans && result.plans.length === 0)
        return t('product:familySharePlan.noPlan');
    }
    return '';
  }, [nric, option, result.isSuccess, result.isValid, result.plans, t]);

  const disabled = useMemo(() => {
    if (result.isSuccess) {
      if (option === 'new') {
        return !result.isValid;
      } else if (option === 'existing') {
        return (
          !result.isValid ||
          !result.plans ||
          result.plans.length === 0 ||
          !selectedPlan
        );
      }
    }
    return true;
  }, [option, result.isSuccess, result.isValid, result.plans, selectedPlan]);

  const { height } = useSafeAreaFrame();

  return (
    <CFFModal visible={visible} dismissable onDismiss={onDismiss}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingViewContainer behavior="padding">
          <Box
            w={794}
            maxH={height - 58}
            backgroundColor={colors.background}
            p={space[12]}
            borderRadius={borderRadius.large}>
            <H6 fontWeight="bold">{t('product:familySharePlan.title')}</H6>
            <Row mt={space[6]} mb={space[6]} gap={space[4]}>
              <Option
                type="new"
                selected={option === 'new'}
                onPress={() => {
                  onChangeOption('new');
                  setNric('');
                }}
              />
              <Option
                type="existing"
                selected={option === 'existing'}
                onPress={() => {
                  onChangeOption('existing');
                  setNric('');
                }}
              />
            </Row>
            <IdNumberField
              idType={NEW_NRIC}
              label={t('product:familySharePlan.nricLabel')}
              hint={t('product:familySharePlan.nricHint')}
              right={
                <>
                  {loading && <LoadingIndicator size={24} />}
                  {!loading && result.isValid && (
                    <Icon.TickCircle
                      size={24}
                      fill={colors.palette.alertGreen}
                    />
                  )}
                </>
              }
              error={error}
              enterKeyHint="done"
              value={nric}
              onChange={value => {
                setNric(value);
                if (value && isValidNewNRIC(value)) {
                  onEnter(value);
                } else {
                  reset();
                }
              }}
            />
            {option === 'existing' &&
              !loading &&
              result.plans &&
              result.plans.length > 0 && (
                <Box mt={space[4]} flexShrink={1}>
                  <LargeLabel fontWeight="bold">
                    {t('product:familySharePlan.choosePlan')}
                  </LargeLabel>
                  <PlanList
                    ListHeaderComponent={ListHeader}
                    data={result.plans}
                    keyExtractor={keyExtractor}
                    ItemSeparatorComponent={Separator}
                    renderItem={({ item }) => (
                      <Plan
                        item={item}
                        selected={item === selectedPlan}
                        onPress={() => setSelectedPlan(item)}
                      />
                    )}
                  />
                </Box>
              )}
            <Row gap={space[6]} mt={space[6]} justifyContent="center">
              <ActionButton
                size="medium"
                variant="secondary"
                onPress={onDismiss}
                text={t('common:cancel')}
              />
              <ActionButton
                size="medium"
                disabled={disabled}
                onPress={() => {
                  onDismiss();
                  if (option === 'new')
                    onDone(FamilySharePlanType.PRIMARY, '', '');
                  if (option === 'existing' && selectedPlan)
                    onDone(
                      FamilySharePlanType.SUBSIDIARY,
                      selectedPlan.policyNumber,
                      nric,
                      selectedPlan.clientCode,
                    );
                }}
                text={t('common:confirm')}
              />
            </Row>
          </Box>
        </KeyboardAvoidingViewContainer>
      </TouchableWithoutFeedback>
    </CFFModal>
  );
}

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Option = ({
  type,
  onPress,
  selected,
}: {
  type: 'new' | 'existing';
  selected: boolean;
  onPress: () => void;
}) => {
  const { t } = useTranslation(['product']);
  const { colors, borderRadius, space } = useTheme();
  return (
    <TouchableOpacity onPress={onPress} style={{ flex: 1 }}>
      <Row
        py={space[4]}
        justifyContent="center"
        alignItems="center"
        backgroundColor={selected ? colors.primaryVariant3 : colors.background}
        borderWidth={selected ? 2 : 1}
        borderColor={selected ? colors.primary : colors.palette.fwdGrey[100]}
        borderRadius={borderRadius['x-small']}
        gap={space[2]}>
        {type === 'new' && <NotesIcon />}
        {type === 'existing' && <LinkedDocumentIcon />}
        <Text
          style={{ fontSize: 18, lineHeight: 27 }}
          fontWeight={selected ? 'bold' : 'normal'}>
          {t(`product:familySharePlan.plan.${type}`)}
        </Text>
      </Row>
    </TouchableOpacity>
  );
};

const ActionButton = styled(Button)({
  width: 200,
});

const PlanList = styled(FlatList)(({ theme }) => ({
  marginTop: theme.space[4],
  flexShrink: 1,
})) as unknown as typeof FlatList;

const ListHeader = () => {
  const { t } = useTranslation(['product']);
  const { space, colors } = useTheme();
  return (
    <Row>
      <Box width={22} mr={space[5]} />
      <Body style={{ flexBasis: '25%' }} color={colors.palette.fwdGreyDarker}>
        {t('product:familySharePlan.primaryPolicyNumber')}
      </Body>
      <Body style={{ flex: 1 }} color={colors.palette.fwdGreyDarker}>
        {t('product:familySharePlan.insured')}
      </Body>
      <Body style={{ flexBasis: '15%' }} color={colors.palette.fwdGreyDarker}>
        {t('product:familySharePlan.availableSlots')}
      </Body>
    </Row>
  );
};

const Plan = ({
  item,
  selected,
  onPress,
}: {
  item: UnionPolicy;
  selected?: boolean;
  onPress?: () => void;
}) => {
  const isFull = (item.quota?.total || 0) === (item.quota?.totalMax || 0);
  const availableSlots = `${
    (item.quota?.totalMax || 0) - (item.quota?.total || 0)
  } / ${item.quota?.totalMax}`;
  const insuredNames = useMemo(
    () =>
      [item.insuredName]
        .concat(item.subPolicies.map(p => p.insuredName?.trim()))
        .join(', '),
    [item.insuredName, item.subPolicies],
  );

  return (
    <PlanPressable onPress={onPress} disabled={isFull}>
      <ItemRadio
        selected={selected}
        value={item.policyNumber}
        label=""
        onSelect={() => !isFull && onPress?.()}
      />
      <LargeBody style={{ flexBasis: '25%' }}>{item.policyNumber}</LargeBody>
      <LargeBody style={{ flex: 1 }} numberOfLines={2}>
        {insuredNames}
      </LargeBody>
      <LargeBody style={{ flexBasis: '15%' }}>{availableSlots}</LargeBody>
    </PlanPressable>
  );
};

const PlanPressable = styled.TouchableOpacity(({ theme, disabled }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingVertical: theme.space[4],
  opacity: disabled ? 0.5 : 1,
}));

const ItemRadio = styled(RadioButton)(() => ({
  marginRight: 11,
}));

const Separator = styled.View(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));
