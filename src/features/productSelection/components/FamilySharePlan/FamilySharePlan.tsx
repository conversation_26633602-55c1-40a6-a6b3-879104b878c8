import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { format } from 'date-fns';
import {
  FamilySharePlanType,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import {
  GetUnionPolicyListParams,
  UnionPolicy,
  useGetUnionPolicyList,
} from 'hooks/useGetUnionPolicyList';
import { useCallback, useEffect, useState } from 'react';
import FamilySharePlanPhone from './FamilySharePlan.phone';
import FamilySharePlanTablet from './FamilySharePlan.tablet';
import { AxiosError } from 'axios';
import { CubeResponse } from 'types/response';

const ACTIVE_STATUSES = [
  'IF',
  'UW',
  'NP',
  'PS',
  'UA',
  'NC',
  'MP',
  'AP',
  'UP',
  'NOT_CHECKED',
];

const DEATH_CLAIM_STATUS = 'DH';

interface FamilySharePlanProps {
  visible: boolean;
  onDismiss: () => void;
  onDone: (
    familySharePlanType: FamilySharePlanType,
    policyNumber: string,
    customerIdNumber: string,
    customerId?: string,
  ) => void;
  pid: string;
}

export interface InternalFamilySharePlanProps extends FamilySharePlanProps {
  option: 'new' | 'existing';
  onChangeOption: (option: 'new' | 'existing') => void;
  onEnter: (nric: string) => void;
  reset: () => void;
  loading: boolean;
  result: {
    isSuccess: boolean;
    isValid?: boolean;
    plans?: UnionPolicy[];
  };
  selectedPlan?: UnionPolicy;
  setSelectedPlan: (plan: UnionPolicy) => void;
}

export default function FamilySharePlan(props: FamilySharePlanProps) {
  const lifeJourney = useFnaStore(state => state.lifeJourney);
  const [option, setOption] =
    useState<InternalFamilySharePlanProps['option']>('new');
  const [result, setResult] = useState<InternalFamilySharePlanProps['result']>({
    isSuccess: false,
  });
  const [selectedPlan, setSelectedPlan] = useState<UnionPolicy>();
  const [familySharePlanParams, setFamilySharePlanParams] =
    useState<GetUnionPolicyListParams>();
  const {
    data,
    isFetching: isLoadingFamilySharePlanList,
    isError,
    error,
  } = useGetUnionPolicyList(familySharePlanParams);

  useEffect(() => {
    if (!data) return;
    const policies = data.filter(
      p =>
        p.clientCode &&
        p.planCode === props.pid &&
        ACTIVE_STATUSES.includes(p.status),
    );
    const hasDeathClaim = data.some(p => p.status === DEATH_CLAIM_STATUS);
    setResult({
      isSuccess: true,
      plans: policies,
      isValid: !hasDeathClaim,
    });
  }, [data, props.pid]);

  useEffect(() => {
    if (isError) {
      if (error instanceof AxiosError && error.response?.status === 404) {
        // new proposer
        setResult({
          isSuccess: true,
          plans: [],
          isValid: true,
        });
      }
    }
  }, [error, isError]);

  const reset = useCallback(() => {
    setFamilySharePlanParams(undefined);
    setResult(result => {
      if (result.isSuccess) {
        return { isSuccess: false };
      }
      return result;
    });
    setSelectedPlan(undefined);
  }, []);

  const onChangeOption = useCallback(
    (option: 'new' | 'existing') => {
      reset();
      setOption(option);
    },
    [reset],
  );

  const onEnter = useCallback(
    async (nric: string) => {
      setResult({ isSuccess: false });
      setSelectedPlan(undefined);
      const { firstName, gender, dob } = lifeJourney;
      if (!(firstName && gender && dob))
        throw new Error('missing owner personal info');

      setFamilySharePlanParams({
        firstName: firstName,
        gender: gender,
        dob: format(dob, 'yyyy-MM-dd'),
        idNumber: nric,
      });
    },
    [lifeJourney],
  );

  return (
    <DeviceBasedRendering
      tablet={
        <FamilySharePlanTablet
          {...props}
          loading={isLoadingFamilySharePlanList}
          onEnter={onEnter}
          option={option}
          onChangeOption={onChangeOption}
          result={result}
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedPlan}
          reset={reset}
        />
      }
      phone={
        <FamilySharePlanPhone
          {...props}
          loading={isLoadingFamilySharePlanList}
          onEnter={onEnter}
          option={option}
          onChangeOption={onChangeOption}
          result={result}
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedPlan}
          reset={reset}
        />
      }
    />
  );
}
