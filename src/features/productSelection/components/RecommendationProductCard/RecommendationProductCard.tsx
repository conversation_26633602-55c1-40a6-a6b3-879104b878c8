import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Icon, Row, Typography } from 'cube-ui-components';
import { ConcernId } from 'features/fna/types/concern';
import { useGuruFAB } from 'features/productSelection/hooks/useGuruFAB';
import { usePressInOutCardStyle } from 'features/productSelection/hooks/usePressInOutCardStyle';
import { useProductImageQuery } from 'features/productSelection/hooks/useProducts';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { AppEvent } from 'types/event';
import { Product } from 'types/products';
import { EventRegister } from 'utils/helper/eventRegister';
import { renderLabelByLanguage } from 'utils/helper/translation';
import GuruFAB, { GuruFABProps } from '../GuruFAB';
import { GuruSuggestionGroup, GuruSuggestionItem } from '../GuruSuggestion';
import ProductPdfOptions from '../ProductPdfOptions';
import { TFuncKey } from 'i18next';

type Props = {
  need?: ConcernId;
  product: Product;
  onPress: (data: { need?: ConcernId; product: Product }) => void;
  fromQuickQuote?: boolean;
} & GuruFABProps;

const SelectButton = styled(Button)(({ theme: { space, borderRadius } }) => {
  return {
    height: space[7],
    paddingHorizontal: 0,
    paddingVertical: 0,
    borderRadius: borderRadius.large,
  };
});

const CardContainer = styled(Animated.View)<{ desc?: Product['productDesc'] }>(
  ({ theme: { space, borderRadius }, desc }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    let height = isTabletMode ? 356 : 374;
    if (!desc) {
      height -= 60;
    }

    return {
      width: isTabletMode ? 257 : 225,
      height,
      marginRight: space[isTabletMode ? 3 : 2],
      borderRadius: borderRadius.medium,
      overflow: 'hidden',
    };
  },
);

const ProductImage = styled.Image(({ theme: { colors } }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    width: '100%',
    height: isTabletMode ? 84 : 74,
    backgroundColor: colors.palette.fwdGrey[50],
  };
});

const ContentContainer = styled(ScrollView)(({ theme: { sizes } }) => ({
  marginVertical: sizes[4],
  flexShrink: 1,
}));

const ImageContainer = styled.View(() => {
  return {
    width: '100%',
    marginTop: 38,
  };
});

const ForYouContainer = styled.View(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.palette.fwdYellow[100],
    borderTopLeftRadius: borderRadius.medium,
    borderTopRightRadius: borderRadius.medium,
    paddingHorizontal: space[3],
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 38,
    justifyContent: 'center',
  }),
);

const RecommendationProductCard = ({
  onPress,
  need,
  product,
  popUpDirection,
  fromQuickQuote,
}: Props) => {
  const { productName, productDesc, productThumbnailUrl } = product;
  const { space, colors } = useTheme();

  const { t } = useTranslation(['common', 'fna', 'aiBot']);
  const { t: productT } = useTranslation(['product']);
  const { data } = useProductImageQuery(productThumbnailUrl);
  const [contentHeight, setContentHeight] = useState(0);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const shouldUseGuruFAB = useGuruFAB();

  const handleOnSelectPress = useCallback(() => {
    onPress({ need, product });
  }, [onPress, product, need]);

  const { onPressIn, onPressOut, cardStyle } = usePressInOutCardStyle(
    colors.background,
    colors.primaryVariant3,
  );

  return (
    <>
      <CardContainer style={[cardStyle]} desc={productDesc}>
        <ImageContainer>
          <ProductImage resizeMode="cover" source={{ uri: data }} />
        </ImageContainer>
        {Boolean(need) && (
          <ForYouContainer>
            <Typography.SmallLabel
              numberOfLines={2}
              fontWeight="medium"
              color={colors.secondary}>
              {fromQuickQuote
                ? productT('product:agentRecommendation')
                : `${productT('product:recommendedFor')} ${t(
                    `fna:lifeStage.concern.recommendation.${
                      need as ConcernId
                    }` as TFuncKey<['fna']>,
                  ).toLocaleLowerCase()}`}
            </Typography.SmallLabel>
          </ForYouContainer>
        )}
        <Box flex={1} px={space[3]} pb={space[isTabletMode ? 4 : 3]}>
          {shouldUseGuruFAB && (
            <View style={{ flex: 1, justifyContent: 'center' }}>
              <Typography.H6 color={colors.palette.black} fontWeight="bold">
                {productName.en}
              </Typography.H6>
            </View>
          )}
          {!shouldUseGuruFAB && (
            <>
              <ContentContainer
                scrollEnabled={
                  isTabletMode
                    ? contentHeight > space[40]
                    : contentHeight > space[48]
                }
                showsVerticalScrollIndicator={false}>
                <Box
                  gap={space[2]}
                  onLayout={e => {
                    setContentHeight(e.nativeEvent.layout.height);
                  }}>
                  {!!product?.productGroup && (
                    <Typography.Label fontWeight="medium">
                      {Array.isArray(product?.productGroup)
                        ? renderLabelByLanguage(product?.productGroup?.[0])
                        : renderLabelByLanguage(product.productGroup)}
                    </Typography.Label>
                  )}

                  <Typography.H6 color={colors.primary} fontWeight="bold">
                    {renderLabelByLanguage(productName)}
                  </Typography.H6>
                  <Box overflow="hidden" flexShrink={1}>
                    {!!productDesc && (
                      <Typography.Label>
                        {renderLabelByLanguage(productDesc)}
                      </Typography.Label>
                    )}
                  </Box>
                </Box>
              </ContentContainer>
              <Box flex={1} />
            </>
          )}

          <Row alignItems="center" justifyContent="space-between">
            <ProductPdfOptions
              brochurePdf={product.brochurePdf}
              productName={product.productName}
              riplayPdf={product?.riplayPdf}>
              <Typography.H8 fontWeight="bold" color={colors.primary}>
                {productT('product:details')}
              </Typography.H8>
            </ProductPdfOptions>
            <SelectButton
              variant={'secondary'}
              onPress={handleOnSelectPress}
              icon={<Icon.ArrowRight size={18} />}
              text={t('common:select')}
              rounded
              contentStyle={styles.content}
              textStyle={styles.text}
              onPressIn={onPressIn}
              onPressOut={onPressOut}
              style={{
                marginHorizontal: 0,
              }}
            />
          </Row>
        </Box>
      </CardContainer>
      {shouldUseGuruFAB && (
        <GuruFAB popUpDirection={popUpDirection}>
          <GuruSuggestionGroup>
            <GuruSuggestionItem
              label={t('aiBot:howToPitch')}
              onPress={() => {
                EventRegister.emit<AppEvent.ProductSelectionPitch>(
                  AppEvent.ProductSelectionPitch,
                  {
                    pid: product.pid,
                  },
                );
              }}
            />
            <GuruSuggestionItem
              label={t('aiBot:productSummary')}
              onPress={() => {
                EventRegister.emit<AppEvent.ProductSelectionSummary>(
                  AppEvent.ProductSelectionSummary,
                  {
                    pid: product.pid,
                  },
                );
              }}
            />
          </GuruSuggestionGroup>
        </GuruFAB>
      )}
    </>
  );
};

export default React.memo(RecommendationProductCard);

const styles = StyleSheet.create({
  content: {
    height: '100%',
    paddingHorizontal: 8,
    margin: 0,
    paddingVertical: 0,
    borderWidth: 2,
  },
  text: {
    fontSize: 14,
    lineHeight: 17.5,
    marginLeft: -4,
  },
});
