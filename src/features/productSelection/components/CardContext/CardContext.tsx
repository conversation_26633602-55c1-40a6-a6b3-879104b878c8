import { useTheme } from '@emotion/react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import {
  Extrapolation,
  interpolate,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { AppEvent } from 'types/event';
import { EventRegister } from 'utils/helper/eventRegister';

type CardProviderProps = React.PropsWithChildren;

type CardContextProps = {
  activeCard: string | null;
  setActiveCard: (card: string | null) => void;
};

const defaultContext = {
  activeCard: null,
  setActiveCard: () => {},
};

export const CardContext = createContext<CardContextProps>(defaultContext);

export function CardProvider({ children }: CardProviderProps) {
  const [activeCardKey, setActiveCardKey] = useState<string | null>(null);
  return (
    <CardContext.Provider
      value={{
        ...defaultContext,
        activeCard: activeCardKey,
        setActiveCard: setActiveCardKey,
      }}>
      {children}
    </CardContext.Provider>
  );
}

export function useCard(cardKey: string, popUpDirection: 'left' | 'right') {
  const { animation } = useTheme();
  const { activeCard, setActiveCard } = useContext(CardContext);
  const activeCardSharedValue = useSharedValue(0);
  // to avoid capturing event when card is not active
  const widthSharedValue = useDerivedValue(() => {
    // set to 1, to avoid exception
    return activeCardSharedValue.value === 0 ? 1 : 'auto';
  });

  const animatedStyle = useAnimatedStyle(() => ({
    zIndex: -1,
    left: interpolate(
      activeCardSharedValue.value,
      [0, 1],
      [-50, popUpDirection === 'right' ? 0 : -230],
      Extrapolation.CLAMP,
    ),
    opacity: interpolate(
      activeCardSharedValue.value,
      [0, 1],
      [0, 1],
      Extrapolation.CLAMP,
    ),
    width: widthSharedValue.value,
  }));

  const show = useCallback(() => {
    activeCardSharedValue.value = withTiming(1, {
      duration: animation.duration,
    });
    setActiveCard(cardKey);
  }, [activeCardSharedValue, animation.duration, setActiveCard, cardKey]);

  const guruBtnPressedHandler = useCallback(
    (target: string | null) => {
      if (cardKey === target) {
        return;
      }
      activeCardSharedValue.value = withTiming(0, {
        duration: animation.duration,
      });
    },
    [activeCardSharedValue, animation.duration, cardKey],
  );

  const dismiss = useCallback(() => {
    activeCardSharedValue.value = withTiming(0, {
      duration: animation.duration,
    });
    setActiveCard(null);
  }, [activeCardSharedValue, animation.duration, setActiveCard]);

  const onPress = () => {
    if (activeCard === cardKey) {
      dismiss();
      return;
    }
    show();
  };

  useEffect(() => {
    guruBtnPressedHandler(activeCard);
  }, [activeCard, guruBtnPressedHandler]);

  useEffect(() => {
    const id1 = EventRegister.addEventListener(
      AppEvent.ProductSelectionPitch,
      dismiss,
    );
    const id2 = EventRegister.addEventListener(
      AppEvent.ProductSelectionSummary,
      dismiss,
    );
    return () => {
      EventRegister.removeEventListener(id1);
      EventRegister.removeEventListener(id2);
    };
  }, [dismiss]);

  return {
    isActive: cardKey == activeCard,
    activeCard,
    animatedStyle,
    setActiveCard,
    onPress,
  };
}
