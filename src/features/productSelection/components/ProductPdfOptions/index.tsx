import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useRootStackNavigation } from 'hooks/useRootStack';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { ProductBrochurePdf, ProductName } from 'types/products';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking';
import { renderLabelByLanguage } from 'utils/helper/translation';
import ProductPdfOptionsPhone from './ProductPdfOptions.phone';
import ProductPdfOptionsTablet from './ProductPdfOptions.tablet';

export type ProductPdfOptionsProps = {
  brochurePdf?: ProductBrochurePdf[];
  riplayPdf?: ProductBrochurePdf[];
  productName?: ProductName;
  children: React.ReactNode;
};

export default function ProductPdfOptions(props: ProductPdfOptionsProps) {
  const { brochurePdf, riplayPdf, productName } = props;

  const { t } = useTranslation(['common', 'product', 'proposal']);
  const { navigate } = useRootStackNavigation();

  const hasRiplayPdf = Array.isArray(riplayPdf) && riplayPdf?.length >= 1;

  const hasMultipleBrochurePdfs =
    Array.isArray(brochurePdf) && brochurePdf?.length > 1;

  const formattedBrochurePdfs = (brochurePdf || []).map(item => ({
    ...item,
    label: item?.label ?? { en: t('product:productBrochure') },
    title: t('proposal:header.productBrochure'),
  }));

  const formattedRiplayPdfs = (riplayPdf || []).map(item => ({
    ...item,
    label: item?.label ?? { en: 'RIPLAY' },
    title: 'RIPLAY',
  }));

  const dropdownPdfs = hasRiplayPdf
    ? [...formattedBrochurePdfs, ...formattedRiplayPdfs]
    : formattedBrochurePdfs;

  const viewPdf = (url: string, title?: string) => {
    const brochureUrl = encodeURI(url);
    navigate('PdfViewer', {
      title: title ?? t('proposal:header.productBrochure'),
      fileName: renderLabelByLanguage(productName) || '',
      downloadable: false,
      sharable: false,
      url: brochureUrl,
    });
  };

  const onChildPress = (showDropdownSelection: () => void) => {
    const productLink = `https://www.fwd.com.${country}/products/`;
    if (
      !brochurePdf ||
      !Array.isArray(brochurePdf) ||
      brochurePdf?.length === 0
    ) {
      return Linking.openURL(productLink);
    }

    GATracking.logCustomEvent(GATracking.EVENTS.SELECT_PRODUCT, {
      product_name: renderLabelByLanguage(productName) ?? '',
      action_type: GATracking.ACTION_TYPES.PRODUCT_REC_VIEW_PDF,
    });

    // if there are more than 1 pdf, show the dropdown selection
    if (hasRiplayPdf || hasMultipleBrochurePdfs) {
      showDropdownSelection();
    } else {
      const brochureUrl = encodeURI(brochurePdf?.[0].url);
      navigate('PdfViewer', {
        title: t('proposal:header.productBrochure'),
        fileName: renderLabelByLanguage(productName) || '',
        downloadable: false,
        sharable: false,
        url: brochureUrl,
      });
    }
  };

  return (
    <DeviceBasedRendering
      phone={
        <ProductPdfOptionsPhone
          {...props}
          dropdownPdfs={dropdownPdfs}
          onPdfFilePress={viewPdf}
          productName={productName}
          hasRiplayPdf={hasRiplayPdf}
          onChildPress={onChildPress}
        />
      }
      tablet={
        <ProductPdfOptionsTablet
          {...props}
          dropdownPdfs={dropdownPdfs}
          hasRiplayPdf={hasRiplayPdf}
          onPdfFilePress={viewPdf}
          onChildPress={onChildPress}
        />
      }
    />
  );
}
