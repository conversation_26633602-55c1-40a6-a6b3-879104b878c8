import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Card, Icon, Label, LargeBody, Row } from 'cube-ui-components';
import useToggle from 'hooks/useToggle';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  GestureResponderEvent,
  Pressable,
  useWindowDimensions,
} from 'react-native';
import Modal from 'react-native-modal';
import { TranslationField } from 'types';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { ProductPdfOptionsProps } from '.';

type Props = ProductPdfOptionsProps & {
  dropdownPdfs: {
    label: TranslationField;
    title: string;
    url: string;
    value?: string;
  }[];
  onPdfFilePress: (url: string, title?: string) => void;
  hasRiplayPdf: boolean;
  onChildPress: (showDropdownSelection: () => void) => void;
};

export default function ProductPdfOptions({
  brochurePdf,
  dropdownPdfs,
  children,
  onPdfFilePress,
  onChildPress,
  hasRiplayPdf,
}: Props) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['common', 'product', 'proposal']);

  const [
    dropdownSelectionVisible,
    showDropdownSelection,
    hideDropdownSelection,
  ] = useToggle();
  const [dropdownPosition, setDropdownPosition] = useState<{
    top?: number;
    left?: number;
  }>({});

  const { sizes } = useTheme();
  const { height: deviceHeight } = useWindowDimensions();

  const onShowDropdown = (e: GestureResponderEvent) => {
    showDropdownSelection();

    const dropdownHeight = sizes[11] * ((dropdownPdfs?.length ?? 0) + 1);
    const topPosition =
      e.nativeEvent.pageY > deviceHeight - dropdownHeight
        ? dropdownHeight
        : sizes[11];
    setDropdownPosition({
      top: e.nativeEvent.pageY - topPosition,
      left: e.nativeEvent.pageX - sizes[25],
    });
  };

  const onViewPdf = (url: string, title?: string) => {
    onPdfFilePress(url, title);
    hideDropdownSelection();
  };

  return (
    <>
      <Pressable
        hitSlop={ICON_HIT_SLOP}
        onPress={e => onChildPress(() => onShowDropdown(e))}>
        {children}
      </Pressable>
      {brochurePdf && (
        <Modal
          isVisible={dropdownSelectionVisible}
          animationIn="fadeIn"
          animationOut="fadeOut"
          onBackdropPress={hideDropdownSelection}
          backdropColor="transparent"
          statusBarTranslucent>
          <DropdownSelectionContainer
            top={dropdownPosition.top}
            left={dropdownPosition.left}>
            {!hasRiplayPdf && (
              <Label
                fontWeight="medium"
                color={colors.palette.fwdDarkGreen[20]}>
                {t('product:selectPackage')}
              </Label>
            )}

            {dropdownPdfs.map(item => (
              <Pressable
                key={item.url}
                onPress={() => onViewPdf(item?.url, item?.title)}>
                <Row
                  alignItems="center"
                  justifyContent="space-between"
                  style={{ paddingVertical: 10 }}>
                  <LargeBody>{renderLabelByLanguage(item?.label)}</LargeBody>
                  <Icon.ChevronRight size={space[4]} fill={colors.primary} />
                </Row>
              </Pressable>
            ))}
          </DropdownSelectionContainer>
        </Modal>
      )}
    </>
  );
}

const DropdownSelectionContainer = styled(Card)<{
  top?: number;
  left?: number;
}>(({ theme: { space, colors, borderRadius, sizes }, top, left }) => ({
  position: 'absolute',
  top: top ?? 220,
  left: left ?? 0,
  width: sizes[60],
  paddingHorizontal: space[4],
  paddingVertical: space[2],
  shadowColor: colors.palette.black,
  shadowOffset: {
    width: 0,
    height: 3,
  },
  shadowOpacity: 0.29,
  shadowRadius: 4.65,
  elevation: 7,
  borderBottomLeftRadius: borderRadius.small,
  borderBottomRightRadius: borderRadius.small,
}));
