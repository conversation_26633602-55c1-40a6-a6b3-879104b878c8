import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { ActionPanel, Label } from 'cube-ui-components';
import PdfFileRow from 'features/proposal/components/PdfFilePopup/PdfFileRow';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import { TranslationField } from 'types';
import { ProductName } from 'types/products';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { ProductPdfOptionsProps } from '.';

type Props = ProductPdfOptionsProps & {
  dropdownPdfs: {
    label: TranslationField;
    title: string;
    url: string;
    value?: string;
  }[];
  onPdfFilePress: (url: string, title?: string) => void;
  hasRiplayPdf: boolean;
  productName: ProductName | undefined;
  onChildPress: (showDropdownSelection: () => void) => void;
};

export default function ProductPdfOptions({
  brochurePdf,
  dropdownPdfs,
  productName,
  children,
  hasRiplayPdf,
  onChildPress,
  onPdfFilePress,
}: Props) {
  const [isShowModalPdf, showModalPdf] = useState(false);
  const { colors } = useTheme();
  const { t } = useTranslation(['common', 'product', 'proposal']);

  return (
    <>
      <Pressable
        hitSlop={ICON_HIT_SLOP}
        onPress={() => onChildPress(() => showModalPdf(true))}>
        {children}
      </Pressable>
      {brochurePdf && (
        <ActionPanel
          visible={isShowModalPdf}
          handleClose={() => showModalPdf(false)}
          title={renderLabelByLanguage(productName)}>
          {!hasRiplayPdf && (
            <Label fontWeight="medium" color={colors.palette.fwdDarkGreen[20]}>
              {t('product:selectPackage')}
            </Label>
          )}

          {dropdownPdfs.map((item, index) => (
            <>
              <PdfFileRow
                key={`${item.title}.${index}`}
                title={renderLabelByLanguage(item?.label) + ''}
                onPress={() => {
                  onPdfFilePress(item?.url, item?.title);
                  showModalPdf(false);
                }}
              />
              {dropdownPdfs.length !== index + 1 && <Divider />}
            </>
          ))}
        </ActionPanel>
      )}
    </>
  );
}

const Divider = styled.View(({ theme: { colors, space } }) => ({
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  marginVertical: space[3],
}));
