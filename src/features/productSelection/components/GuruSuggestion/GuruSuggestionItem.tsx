import styled from '@emotion/native';
import { Image, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import { LinearGradient } from 'expo-linear-gradient';
import { Stars } from 'features/productSelection/assets';

const STARS_SIZE = 24;

const Container = styled.TouchableOpacity(({ theme: { colors, space } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: colors.palette.white,
  paddingVertical: space[2],
  paddingLeft: space[2],
  paddingRight: space[3],
  margin: 2,
  borderRadius: space[10],
  gap: space[1],
}));

const Text = styled(Typography.Label)(({ theme: { colors } }) => ({
  color: colors.primary,
  fontWeight: 'bold',
}));

type GuruSuggestionItemProps = {
  label: string;
  onPress: () => void;
};

export function GuruSuggestionItem({
  label,
  onPress,
}: GuruSuggestionItemProps) {
  const { space, colors } = useTheme();
  return (
    <View
      style={{
        backgroundColor: colors.palette.white,
        borderRadius: space[10],
      }}>
      <LinearGradient
        colors={['#E87722', '#E87722', '#FED141', '#6ECEB2', '#E87722']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ borderRadius: space[10] }}>
        <Container onPress={onPress}>
          <Image source={Stars} width={STARS_SIZE} height={STARS_SIZE} />
          <Text>{label}</Text>
        </Container>
      </LinearGradient>
    </View>
  );
}
