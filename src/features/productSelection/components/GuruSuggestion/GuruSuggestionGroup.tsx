import styled from '@emotion/native';
import React from 'react';
import { PropsWithChildren } from 'react';

type GuruSuggestionGroupProps = PropsWithChildren;

const Container = styled.View(({ theme: { space } }) => ({
  gap: space[2],
}));

export function GuruSuggestionGroup({ children }: GuruSuggestionGroupProps) {
  return (
    <Container>
      {React.Children.map(children, (child, index) => {
        return <React.Fragment key={index}>{child}</React.Fragment>;
      })}
    </Container>
  );
}
