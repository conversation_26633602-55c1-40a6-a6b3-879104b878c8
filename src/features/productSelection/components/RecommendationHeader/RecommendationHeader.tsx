import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Box, H6, Icon, LargeLabel, Row } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useRootStackNavigation } from 'hooks/useRootStack';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function RecommendationHeader() {
  const theme = useTheme();
  const { top: topInset } = useSafeAreaInsets();
  const { t } = useTranslation(['fna', 'product']);
  const navigation = useRootStackNavigation();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Row
      zIndex={10}
      h={theme.sizes[14] + topInset}
      pt={topInset}
      gap={theme.space[5]}
      px={theme.space[isTabletMode || !isNarrowScreen ? 4 : 3]}
      backgroundColor={theme.colors.background}
      alignItems="center">
      <BackButton
        hitSlop={ICON_HIT_SLOP}
        onPress={() => navigation.canGoBack() && navigation.goBack()}>
        <Icon.ArrowLeft
          fill={theme.colors.onBackground}
          size={theme.sizes[6]}
        />
      </BackButton>
      {!isTabletMode && <Box flex={1} />}
      {isTabletMode ? (
        <H6 color={theme.colors.onBackground} fontWeight="bold">
          {t('product:selectOneProduct')}
        </H6>
      ) : (
        <LargeLabel color={theme.colors.onBackground} fontWeight="bold">
          {t('product:selectOneProduct')}
        </LargeLabel>
      )}
      <Box flex={1} />
      {isTabletMode ? (
        <IconButton
          onPress={() => navigation.navigate('Main', { screen: 'Home' })}>
          <Icon.Home fill={theme.colors.onBackground} size={theme.sizes[6]} />
          <LargeLabel color={theme.colors.onBackground} fontWeight="bold">
            {t('fna:home')}
          </LargeLabel>
        </IconButton>
      ) : (
        <Box width={theme.sizes[6]} />
      )}
    </Row>
  );
}

const BackButton = styled(TouchableOpacity)({
  justifyContent: 'center',
  alignItems: 'center',
});

const IconButton = styled(TouchableOpacity)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: theme.space[2],
  paddingVertical: 10,
  gap: theme.space[1],
}));
