import React, { useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import {
  Button,
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  H7,
  H8,
  Icon,
  Label,
  Body,
  SmallLabel,
} from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { reportBackground } from 'features/ecoach/assets';
import {
  Image,
  ImageBackground,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { format, parseISO } from 'date-fns';
import { EcoachParamList } from 'types/navigation';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { ConversationDataV2, FeedbackCard, PraiseCard } from 'features/ecoach/api/conversationApi';
import SkillChart from 'features/ecoach/components/v2/SkillChart';
import { extractScore } from 'features/ecoach/utils/extractScore';
import { BlurView } from 'expo-blur';
import { Speedometer } from 'features/ecoach/components/Speedometer';
import { Divider } from 'features/lead/components/Divider';

// Styled Components
const PageImageBackground = styled(ImageBackground)(() => ({
  flex: 1,
  width: '100%',
  height: '100%',
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  position: 'absolute',
  top: Platform.OS === 'ios' ? 0 : sizes[5],
  right: 0,
  zIndex: 1,
}));

const HeaderContainer = styled(View)(() => ({
  marginHorizontal: sizes[4],
  gap: 2
}));

const ScoreContainer = styled(View)(() => ({
  alignItems: 'center',
  paddingVertical: sizes[6],
  // backgroundColor: colors.fwdDarkGreen[100],
}));

const ScoreText = styled(H1)(() => ({
  color: colors.fwdYellow[100],
}));

const LastScoreContainer = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: sizes[2],
  marginBottom: sizes[6],
}));

const LastScoreText = styled(SmallLabel)(() => ({
  color: colors.fwdDarkGreen[50],
  marginRight: sizes[1],
}));

const LastScoreValue = styled(H8)(() => ({
  color: colors.fwdDarkGreen[20]
}));

const BadgeContainer = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  gap: sizes[2],
  marginTop: sizes[4],
}));

const Badge = styled(View)<{ backgroundColor: string }>(({ backgroundColor }) => ({
  backgroundColor,
  paddingHorizontal: sizes[3],
  paddingVertical: sizes[1],
  borderRadius: 4,
}));

const BadgeText = styled(SmallLabel)(() => ({
  color: colors.white,
  fontWeight: 'bold',
}));

const IndicatorView = styled(View)(() => ({
  backgroundColor: colors.fwdGrey[100],
  width: 51,
  height: sizes[1],
  borderRadius: 2,
  alignSelf: 'center',
}));

const SummarySection = styled(View)(() => ({
  backgroundColor: colors.fwdOrange[5],
  borderTopLeftRadius: 24,
  borderTopRightRadius: 24,
  marginTop: sizes[6],
  padding: sizes[4],
  gap: sizes[4]
}));

const SectionTitle = styled(H6)(() => ({
  marginBottom: 0,
}));

const SummaryText = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  lineHeight: 24,
}));

const ImprovementAndStrengthSection = styled(View)(() => ({
  backgroundColor: colors.white,
  padding: sizes[5],
}));
const ImprovementSection = styled(View)(() => ({
  backgroundColor: colors.fwdOrange[20],
  padding: sizes[4],
  borderRadius: 12,
}));

const ImprovementHeader = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: sizes[3],
}));

const ImprovementIcon = styled(View)(() => ({
  width: 24,
  height: 20,
  marginRight: sizes[2],
  // Add improvement icon styling
}));

const ImprovementTitle = styled(H6)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: '500',
}));

const ImprovementSummary = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  marginBottom: sizes[4],
}));

const SeeMoreButton = styled(TouchableOpacity)(() => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingVertical: sizes[3],
}));

const SeeMoreText = styled(H7)(() => ({
  color: colors.fwdDarkGreen[50],
  fontWeight: '500',
}));

const ButtonContainer = styled(View)(() => ({
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[6],
}));

interface SummaryV1Props {
  conversationData: ConversationDataV2;
  session?: any;
  reTake?: () => void;
}

const SummaryV1: React.FC<SummaryV1Props> = ({ conversationData, session, reTake }) => {
  const { t } = useTranslation('ecoach');
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  
  const { report : { overall_score, last_score, overall_summary, skill_set_details } } = conversationData;

  const goBackBtn = () => {
    if (session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  const tryAgain = () => {
    navigation.goBack();
    reTake && reTake();
  };

  const goHistoryScreen = () => {
    navigation.push('SessionHistory', {});
  };

  const convertSecondsToMins = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = (duration % 60).toFixed(2);
    return `${minutes} ${t('min')} ${seconds} ${t('sec')}`;
  };

  const showDetailedFeedback = () => {
    // Navigate to detailed feedback screen
    // navigation.navigate('DetailedFeedbackV1', { conversationData });
  };

  const getBadgeInfo = (score: string) => {
    const numScore = extractScore(score);
    if (numScore >= 80) return { text: 'Perfect', color: colors.fwdLightGreen[100] };
    if (numScore >= 60) return { text: 'Pass', color: colors.fwdOrange[100] };
    return { text: 'Needs Work', color: colors.alertRed };
  };

  const overallScoreNum = extractScore(overall_score);
  const lastScoreNum = extractScore(last_score);
  const badgeInfo = getBadgeInfo(overall_score);

  return (
    <PageImageBackground source={reportBackground} resizeMode="cover">
      <BlurView intensity={50} tint="dark" style={StyleSheet.absoluteFill} />
      <StatusBar hidden />
      <SafeAreaView style={{ flex: 1, backgroundColor:
          'linear-gradient(90deg, rgba(1, 1, 1, 0.65) 33.19%, rgba(1, 1, 1, 0.65) 93.58%)', }}>
        <HeaderContainer>
          <BackBtn onPress={goBackBtn}>
            {session ? (
              <Icon.ArrowLeft fill={colors.fwdGrey[20]} />
            ) : (
              <Icon.Close fill={colors.fwdGrey[20]} />
            )}
          </BackBtn>
          <H5 color={colors.white} fontWeight="bold" style={{ textAlign: 'center' }}>
            {t('yourReport')}
          </H5>
          <H8 color={colors.white} style={{ textAlign: 'center' }}>
            {t('ultimateRoleplay')}
          </H8>
        </HeaderContainer>

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Score Section */}
          <ScoreContainer>
            <Speedometer percentage={80} size={250} />
            <LastScoreContainer>
              <LastScoreText>{t('lastScore')}</LastScoreText>
              <LastScoreValue fontWeight={'bold'}>{lastScoreNum}</LastScoreValue>
            </LastScoreContainer>
            <Button
              icon = {<Icon.Refresh />}
              variant="primary"
              text="Try again"
              onPress={tryAgain}
            />
          </ScoreContainer>

          {/* Summary Section */}
          <SummarySection>
            <IndicatorView />
            <H6 fontWeight={'bold'} color={colors.fwdDarkGreen[100]}>{t('summary')}</H6>
            <Body color={colors.fwdDarkGreen[100]}>{overall_summary}</Body>
            <Divider />
          </SummarySection>

          {/* Skill Chart */}
          <SkillChart skills={skill_set_details} />

          <ImprovementAndStrengthSection>
            {/* Improvement Section */}
            <ImprovementSection>
              <ImprovementHeader>
                <ImprovementIcon />
                <ImprovementTitle>{t('improvements')}</ImprovementTitle>
              </ImprovementHeader>

              <ImprovementSummary>
                {skill_set_details[0]?.improvement?.summary || ''}
              </ImprovementSummary>

              <SeeMoreButton onPress={showDetailedFeedback}>
                <SeeMoreText>
                  See {skill_set_details.reduce((total, skill) =>
                  total + skill.improvement.cards.length, 0
                )} improvements
                </SeeMoreText>
                <Icon.ChevronUp fill={colors.fwdOrange[100]} />
              </SeeMoreButton>
            </ImprovementSection>

            {/*Strengths Section*/}


          </ImprovementAndStrengthSection>


        </ScrollView>
      </SafeAreaView>
    </PageImageBackground>
  );
};

export default SummaryV1;
