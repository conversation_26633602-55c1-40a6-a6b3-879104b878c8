import React from 'react';
import styled from '@emotion/native';
import { View } from 'react-native';
import { H5, H6, H7, Small<PERSON>abe<PERSON> } from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { SkillSetDetailV2 } from 'features/ecoach/api/conversationApi';
import { extractScore } from 'features/ecoach/utils/extractScore';
import { useTranslation } from 'react-i18next';

const ChartContainer = styled(View)(() => ({
  backgroundColor: colors.fwdOrange[5],
  paddingHorizontal: sizes[4],
}));

const SkillChartRow = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'flex-end',
  // gap: 22,
}));

const SkillBarContainer = styled(View)(() => ({
  flex: 1,
  // justifyContent: 'space-between',
  alignItems: 'center',
  // maxWidth: 83,
}));

const SkillNameView = styled(View)(() => ({
  // flex: 1,
  borderTopLeftRadius: 8,
  borderTopRightRadius: 8,
  borderTopWidth: 3,
  borderColor: colors.fwdOrange[100],
  backgroundColor: colors.white,
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: sizes[4],
  height: sizes[15],
  width: '100%',
}));

const SkillBarBackground = styled(View)(() => ({
  height: 83,
  backgroundColor: colors.fwdDarkGreen[20],
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.white,
  overflow: 'hidden',
  position: 'relative',
}));

const SkillBarFill = styled(View)<{ height: number; backgroundColor: string }>(
  ({ height, backgroundColor }) => ({
    height,
    backgroundColor,
    borderRadius: sizes[2],
    width: 41,
  })
);

const SkillScore = styled(SmallLabel)(() => ({
  textAlign: 'center',
  marginBottom: sizes[2],
}));

interface SkillChartProps {
  skills: SkillSetDetailV2[];
}

const SkillChart: React.FC<SkillChartProps> = ({ skills }) => {
  const { t } = useTranslation('ecoach');
  const getSkillBarColor = (score: string) => {
    const numScore = extractScore(score);
    if (numScore >= 80) return colors.fwdLightGreen[100]; // Green for excellent
    if (numScore >= 60) return colors.fwdYellow[100]; // Yellow for good
    if (numScore >= 30) return colors.alertRed; // Red for needs improvement
    return colors.fwdDarkGreen[20]; // Gray for poor
  };

  const formatSkillName = (name: string) => {
    // Split long skill names into multiple lines for better display
    return name.replace(/\s+/g, '\n');
  };

  return (
    <ChartContainer>
      <H6 color={colors.fwdDarkGreen[100]} fontWeight="bold" >
        {t('detailedFeedback')}
      </H6>
      <SkillChartRow>
        {skills.map((skill, index) => (
          <SkillBarContainer key={index}>
            <SkillScore color={colors.fwdDarkGreen[100]} fontWeight={'bold'}>{extractScore(skill.score)}</SkillScore>
            <SkillBarFill
              height={Math.round(extractScore(skill.score)*2)}
              backgroundColor={getSkillBarColor(skill.score)}
            />
            <SkillNameView>
              <SmallLabel color={colors.fwdOrange[100]} fontWeight={'bold'}>{formatSkillName(skill.name)}</SmallLabel>
            </SkillNameView>
          </SkillBarContainer>
        ))}
      </SkillChartRow>
    </ChartContainer>
  );
};

export default SkillChart;
