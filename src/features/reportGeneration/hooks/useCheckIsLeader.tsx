import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { CHANNELS } from 'types/channel';

export default function useCheckIsLeader() {
  const { data: agentProfile } = useGetAgentProfile();

  const channel = agentProfile?.channel;
  const designation = agentProfile?.designation?.toLowerCase();

  // Agency FWP is not leader
  const isAgencyLeader = channel === CHANNELS.AGENCY && designation !== 'fwp';

  // Banca FSC and SFSC are not leader
  const isBancaLeader =
    channel === CHANNELS.BANCA &&
    designation !== 'fsc' &&
    designation !== 'sfsc';

  const isLeader = isAgencyLeader || isBancaLeader;

  return { isLeader };
}
