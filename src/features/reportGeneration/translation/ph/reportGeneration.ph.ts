export default {
  reportGeneration: 'Report generation',
  lapsedPolicies: 'Lapsed policies',
  lapsedReport: 'Lapsed report',
  premiumReceived: 'Premium received',
  creditCardExpiration: 'Credit card expiration',
  unsuccessfulAdaAca: 'Unsuccessful ADA/ACA',
  policy: 'Policy anniversary',
  commission: 'Commission statement',
  tax: 'Tax statement',
  persistency: 'Persistency',
  reportSetting: 'Report setting',
  totalResult: 'Total results',
  information: 'Information',
  lapsed: 'Lapsed',
  premiumHoliday: 'Premium holiday',
  anticipatedLapse: 'Anticipated lapse',
  fullTable: 'Full table',

  // Report type
  'type.businessAndClienteleReport': 'Business & Clientele report',
  'type.myPersonalReport': 'My personal report',

  // Toolbar
  'toolbar.selectedAgent': 'Selected agent',
  'toolbar.team': 'Team',
  'toolbar.status': 'Status',
  'toolbar.policyHolder': 'Policy holder',
  'toolbar.policyInfo': 'Policy holder/number',
  'toolbar.transactionDate': 'Transaction date',
  'toolbar.issueDate': 'Issue date',
  'toolbar.policyAnniversaryPeriod': 'Policy anniversary period',

  // Action Panel
  'actionPanel.title.agent': 'Agent',
  'actionPanel.title.status': 'Status',
  'actionPanel.title.transactionDate': 'Transaction date',
  'actionPanel.title.datePeriod': 'Date period',
  'actionPanel.title.policyAnniversaryPeriod': 'Policy anniversary period',
  'actionPanel.title.searchForPolicyInfo': 'Search for policy holder/number',
  'actionPanel.subtitle.searchIndividual': 'Search individual',
  'actionPanel.subtitle.searchTip': 'Please fill in name or policy number',
  'actionPanel.hint.policyNumber': 'Input full policy number',
  'actionPanel.hint.dateRange':
    'Select date within 180 days before or after today',
  'actionPanel.placeholder.agentNameAgentCode': 'Agent name/ agent code',
  'actionPanel.placeholder.firstName': 'First name',
  'actionPanel.placeholder.lastName': 'Last name',
  'actionPanel.placeholder.policyHolderName': 'Name',
  'actionPanel.placeholder.policyNumber': 'Policy number',
  'actionPanel.option.all': 'All',
  'actionPanel.option.individual': 'Individual',
  'actionPanel.option.thisMonth': 'This month',
  'actionPanel.option.lastThirtyDays': 'Last 30 days',
  'actionPanel.option.lastSixMonths': 'Last 180 days',
  'actionPanel.search': 'Search',
  'actionPanel.confirm': 'Confirm',
  'actionPanel.reset': 'Reset',
  'actionPanel.from': 'From',
  'actionPanel.to': 'To',
  'actionPanel.error.dateFrom':
    'Please select a start date earlier than the end date',
  'actionPanel.error.dateTo':
    'Please select a end date later than the start date',

  // Headers
  'header.month': 'Month',
  'header.downloadPDF': 'Download PDF',
  'header.policyNumber': 'Policy number',
  'header.policyHolderName': 'Policy holder name',
  'header.policyAnniversaryDate': 'Policy\nanniversary date',
  'header.paymentDate': 'Payment date',

  // Information modal
  'tips.title': 'Tips',
  'tips.message.1':
    'You may select your own name from the Agent list. Also if you are a leader, then the names from your team will show here and you can select them',
  'tips.message.premiumHoliday':
    'Policies which premium payment are covered by the balance in their account value',
  'tips.message.anticipatedLapse':
    'Policies which overdue premium payment but still within the 61 days grace period',
  'tips.message.Lapsed':
    'Policies which overdue premium payment beyond the 61 days grace period',
  'downloadPassword.title': 'Download Password',
  'downloadPassword.message.1':
    'Downloaded documents in this module are password encrypted.',
  'downloadPassword.message.2': `Enter the planner's birthday as the default password when opening the files using this format:`,
  'downloadPassword.message.3': 'Export can only support up to 4500 rows.',

  // Download Report
  'download.downloading': 'Downloading',
  'download.downloadSuccess': 'Downloaded successfully',
};
