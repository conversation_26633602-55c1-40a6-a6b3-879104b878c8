import {
  ActionPanelChipConfig,
  ActionPanelSearchTypeConfig,
  PolicyInfoSearch,
} from 'types/report';

export const DEFAULT_POLICY_INFO: PolicyInfoSearch = {
  searchType: 'all',
  focusedChip: 'policyHolderName',
  policyHolderName: '',
  policyNumber: '',
};

export const SEARCH_TYPE_CONFIG: ActionPanelSearchTypeConfig = [
  { label: 'actionPanel.option.all', value: 'all' },
  { label: 'actionPanel.option.individual', value: 'individual' },
] as const;

export const CHIP_CONFIG: ActionPanelChipConfig = [
  {
    type: 'policyHolderName',
    label: 'actionPanel.placeholder.policyHolderName',
  },
  { type: 'policyNumber', label: 'actionPanel.placeholder.policyNumber' },
] as const;
