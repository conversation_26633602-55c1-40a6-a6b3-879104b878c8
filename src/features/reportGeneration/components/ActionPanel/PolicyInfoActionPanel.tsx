import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  Chip,
  Column,
  H6,
  H7,
  H8,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { validateMYCertificateNumber } from 'features/reportGeneration/my/tablet/ReportGenerationMY';
import {
  CommonModal,
  ModalFormAction,
} from 'features/reportGeneration/ph/components/CommonModal';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  ActionPanelChipConfig,
  ActionPanelSearchTypeConfig,
  PolicyInfoSearch,
} from 'types/report';
import { country } from 'utils/context';

const IS_MY = country === 'my';

/**
 * For both mobile and tablet
 */
export default function PolicyInfoActionPanel({
  visible,
  handleClose,
  contextValue,
  updateContextValue,
  //
  defaultPolicyInfo,
  searchTypeConfig = [],
  chipConfig = [],
  //
  title,
  searchTip,
  policyHolderNameError,
  policyNumberHint,
  policyNumberError,
  //
  primaryLabel,
  secondaryLabel,
  onPressSecondaryButton,
}: {
  visible: boolean;
  handleClose: () => void;
  contextValue: PolicyInfoSearch;
  updateContextValue: (contextValue: PolicyInfoSearch) => void;
  //
  defaultPolicyInfo: PolicyInfoSearch;
  searchTypeConfig: ActionPanelSearchTypeConfig;
  chipConfig: ActionPanelChipConfig;
  //
  title?: string;
  searchTip?: string;
  policyHolderNameError?: string;
  policyNumberHint?: string;
  policyNumberError?: string;
  //
  primaryLabel?: string;
  secondaryLabel?: string;
  onPressSecondaryButton?: (resetErrorState: () => void) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const keyboardShown = useKeyboardShown();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const isMultipleSearchType = searchTypeConfig.length > 1; // Check if there are multiple search types
  const isMultipleChip = chipConfig.length > 1; // Check if there are multiple chips

  // Extra space for keyboard
  const showExtraSpace = keyboardShown;

  const [tempPolicyInfo, setTempPolicyInfo] = useState(contextValue); // For UI display and saving state
  const [policyHolderNameOnError, setPolicyHolderNameOnError] = useState(false); // Error state for policyHolderName
  const [policyNumberOnError, setPolicyNumberOnError] = useState(false); // Error state for policyNumber

  // Reset error state
  const resetErrorState = () => {
    setPolicyHolderNameOnError(false);
    setPolicyNumberOnError(false);
  };

  // Disable search button
  const primaryDisabled =
    (tempPolicyInfo?.searchType === 'individual' &&
      !tempPolicyInfo?.policyHolderName &&
      !tempPolicyInfo?.policyNumber) ||
    (tempPolicyInfo?.searchType === 'individual' && policyHolderNameOnError) ||
    (tempPolicyInfo?.searchType === 'individual' && policyNumberOnError);

  // Dismiss panel
  const resetPanelToContextValue = () => setTempPolicyInfo(contextValue);

  // Policy holder name onChange
  const policyHolderNameOnChange = (value: string) => {
    const regex = /^[a-zA-Z ]*$/;
    const isValid = regex?.test(value);

    isValid
      ? setPolicyHolderNameOnError(false)
      : setPolicyHolderNameOnError(true);

    setTempPolicyInfo({
      ...tempPolicyInfo,
      policyHolderName: value,
    });
  };

  // Policy number onChange
  const policyNumberOnChange = (value: string) => {
    const numericValue = Number(value);

    const isValid = IS_MY
      ? validateMYCertificateNumber(value)
      : !isNaN(numericValue);

    isValid ? setPolicyNumberOnError(false) : setPolicyNumberOnError(true);

    setTempPolicyInfo({
      ...tempPolicyInfo,
      policyNumber: value,
    });
  };

  // Form action functions
  const formActionOnPrimaryPress = () => {
    if (tempPolicyInfo?.searchType === 'all') {
      setTempPolicyInfo(defaultPolicyInfo);
      updateContextValue(defaultPolicyInfo);
    }
    if (tempPolicyInfo?.searchType === 'individual') {
      updateContextValue(tempPolicyInfo);
    }
    resetErrorState();
    handleClose();
  };

  const formActionOnSecondaryPress = onPressSecondaryButton
    ? () => onPressSecondaryButton(resetErrorState)
    : () => {
        setTempPolicyInfo(defaultPolicyInfo);
        resetErrorState();
      };

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible={visible}
        onClose={() => {
          resetPanelToContextValue();
          handleClose();
        }}>
        <H6 fontWeight="bold" children={title} />

        <Column
          pt={isMultipleSearchType && isMultipleChip ? space[8] : space[4]}
          gap={space[6]}
          pb={isMultipleSearchType && isMultipleChip ? 0 : space[6]}>
          {isMultipleSearchType && (
            <Row px={space[4]} pb={space[4]}>
              <RadioButtonGroup value={tempPolicyInfo?.searchType}>
                {searchTypeConfig?.map((item, index) => (
                  <RadioButton
                    key={index}
                    value={item?.value}
                    label={t(item?.label)}
                    style={{ flex: 1 }}
                    onSelect={() => {
                      if (tempPolicyInfo?.searchType === item?.value) return;
                      setTempPolicyInfo({
                        ...tempPolicyInfo,
                        searchType: item?.value,
                      });
                    }}
                  />
                ))}
              </RadioButtonGroup>
            </Row>
          )}

          {tempPolicyInfo?.searchType === 'individual' && (
            <>
              {searchTip && (
                <H7
                  fontWeight={
                    isMultipleSearchType && isMultipleChip ? 'bold' : 'normal'
                  }
                  children={searchTip}
                  color={
                    isMultipleSearchType && isMultipleChip
                      ? colors.palette.fwdDarkGreen[100]
                      : colors.placeholder
                  }
                />
              )}

              {isMultipleChip && (
                <Row style={{ gap: space[1] }}>
                  {chipConfig?.map(({ type, label }) => (
                    <Chip
                      key={type}
                      size="large"
                      focus={tempPolicyInfo?.focusedChip === type}
                      label={t(label)}
                      onPress={() => {
                        // Reset policyHolderName and policyNumber when switching between chips
                        setTempPolicyInfo(prev => ({
                          ...prev,
                          focusedChip: type,
                          policyHolderName: '',
                          policyNumber: '',
                        }));
                        resetErrorState();
                      }}
                    />
                  ))}
                </Row>
              )}

              {tempPolicyInfo?.focusedChip === 'policyHolderName' && (
                <TextField
                  label={t('actionPanel.placeholder.policyHolderName')}
                  value={tempPolicyInfo?.policyHolderName}
                  onChange={value => policyHolderNameOnChange(value)}
                  isError={policyHolderNameOnError}
                  error={
                    policyHolderNameOnError ? policyHolderNameError : undefined
                  }
                  style={{ paddingBottom: space[2] }}
                />
              )}

              {tempPolicyInfo?.focusedChip === 'policyNumber' && (
                <TextField
                  label={t('actionPanel.placeholder.policyNumber')}
                  value={tempPolicyInfo?.policyNumber}
                  onChange={value => policyNumberOnChange(value)}
                  hint={policyNumberHint}
                  keyboardType="number-pad"
                  returnKeyType="done"
                  isError={policyNumberOnError}
                  error={policyNumberOnError ? policyNumberError : undefined}
                  style={{ paddingBottom: space[2] }}
                />
              )}
            </>
          )}
        </Column>

        <ModalFormAction
          hasShadow={false}
          primaryLabel={primaryLabel}
          onPrimaryPress={() => formActionOnPrimaryPress()}
          primaryDisabled={primaryDisabled}
          secondaryLabel={secondaryLabel}
          onSecondaryPress={() => formActionOnSecondaryPress()}
          style={{ paddingBottom: 0 }}
        />
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <ActionPanel
      visible={visible}
      handleClose={() => {
        resetPanelToContextValue();
        handleClose();
      }}
      title={t('actionPanel.title.searchForPolicyInfo')}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
      }}>
      <Column pt={space[4]} px={space[4]} gap={space[4]}>
        <Row pb={space[2]}>
          <RadioButtonGroup value={tempPolicyInfo?.searchType}>
            {searchTypeConfig?.map((item, index) => (
              <RadioButton
                key={index}
                value={item?.value}
                label={t(item?.label)}
                style={{ flex: 1 }}
                onSelect={() => {
                  if (tempPolicyInfo?.searchType === item?.value) return;
                  setTempPolicyInfo({
                    ...tempPolicyInfo,
                    searchType: item?.value,
                  });
                }}
              />
            ))}
          </RadioButtonGroup>
        </Row>

        {tempPolicyInfo?.searchType === 'individual' && (
          <>
            {searchTip && <H8 fontWeight="bold" children={searchTip} />}

            <Row style={{ gap: space[1] }}>
              {chipConfig?.map(({ type, label }) => (
                <Chip
                  key={type}
                  focus={tempPolicyInfo?.focusedChip === type}
                  label={t(label)}
                  onPress={() => {
                    // Reset policyHolderName and policyNumber when switching between chips
                    setTempPolicyInfo(prev => ({
                      ...prev,
                      focusedChip: type,
                      policyHolderName: '',
                      policyNumber: '',
                    }));
                    resetErrorState();
                  }}
                />
              ))}
            </Row>

            {tempPolicyInfo?.focusedChip === 'policyHolderName' && (
              <TextField
                label={t('actionPanel.placeholder.policyHolderName')}
                value={tempPolicyInfo?.policyHolderName}
                onChange={value => policyHolderNameOnChange(value)}
                isError={policyHolderNameOnError}
                error={
                  policyHolderNameOnError ? policyHolderNameError : undefined
                }
                style={{ paddingBottom: space[2] }}
              />
            )}

            {tempPolicyInfo?.focusedChip === 'policyNumber' && (
              <TextField
                label={t('actionPanel.placeholder.policyNumber')}
                value={tempPolicyInfo?.policyNumber}
                onChange={value => policyNumberOnChange(value)}
                hint={policyNumberHint}
                keyboardType="number-pad"
                returnKeyType="done"
                isError={policyNumberOnError}
                error={policyNumberOnError ? policyNumberError : undefined}
                style={{ paddingBottom: space[2] }}
              />
            )}
          </>
        )}
      </Column>

      {showExtraSpace && <Column height={230} />}

      <FormAction
        hasShadow={false}
        primaryLabel={primaryLabel}
        onPrimaryPress={() => formActionOnPrimaryPress()}
        primaryDisabled={primaryDisabled}
        secondaryLabel={secondaryLabel}
        onSecondaryPress={() => formActionOnSecondaryPress()}
      />
    </ActionPanel>
  );
}
