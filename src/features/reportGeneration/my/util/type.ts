import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';

export type TransactionReportScreenParams = {
  to: string;
  from: string;
  agent: MemberInfo;
  team: boolean;
  policyNumber: string;
};

export type TransactionReportScreenProps = {
  route: {
    key: string;
    name: string;
    params: TransactionReportScreenParams;
  };
  navigation: Record<string, unknown>;
};

export type ReportStatus = {
  code: string;
  meaning: string;
};

export type BiroReportStatus = {
  code: string;
  meaning: string;
  dropdownLabel: string;
};

export type SelectedReportStatus = {
  code: string;
  meaning: string;
  selected: boolean;
};

export type ReportPostBody = {
  label: string;
  path: string;
};
