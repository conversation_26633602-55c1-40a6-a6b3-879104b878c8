/**
 * This file contains the configuration for the data grid in report generation - Lapse report.
 */

import { t } from 'i18next';

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: t('reportGeneration:reportColumn.policyNumberAndpolicyHolderName'),
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: t('reportGeneration:reportColumn.agentCode'),
  },
  {
    type: 'agentName',
    title: t('reportGeneration:reportColumn.agentName'),
  },
  {
    type: 'product',
    title: t('reportGeneration:reportColumn.product'),
  },
  {
    type: 'registrationDate',
    title: t('reportGeneration:reportColumn.registrationDate'),
  },
  {
    type: 'decisionDate',
    title: t('reportGeneration:reportColumn.decisionDate'),
  },
  {
    type: 'status',
    title: t('reportGeneration:reportColumn.certificateStatus'),
  },
  {
    type: 'currency',
    title: t('reportGeneration:reportColumn.currency'),
  },
  {
    type: 'modalPremium',
    title: t('reportGeneration:reportColumn.modalPremium'),
  },
  {
    type: 'frequency',
    title: t('reportGeneration:reportColumn.frequency'),
  },
  {
    type: 'ace',
    title: t('reportGeneration:reportColumn.ace'),
  },
  {
    type: 'remark',
    title: t('reportGeneration:reportColumn.remark'),
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
export const APPNOTPROCEED_REPORT_POST_BODY = HEADERS.map(header => ({
  label: header.title,
  path: header.type,
}));
APPNOTPROCEED_REPORT_POST_BODY.unshift(
  {
    label: t('reportGeneration:reportColumn.policyNumber'),
    path: 'policyNumber',
  },
  {
    label: t('reportGeneration:reportColumn.policyHolderName'),
    path: 'policyHolderName',
  },
);
