import { t } from 'i18next';
import { BiroReportStatus, ReportStatus } from './type';

export const STATUS_LIST: ReportStatus[] = [
  { code: 'A1', meaning: 'Approve TPD' },
  { code: 'A2', meaning: 'Approve TI' },
  { code: 'A3', meaning: 'Approve CI' },
  { code: 'AP', meaning: 'Component change - Add prpsl' },
  { code: 'AR', meaning: 'Approved Claim w Refund Prem' },
  { code: 'CC', meaning: 'CI Fully Claim' },
  { code: 'CF', meaning: 'Cancelled from Inception' },
  { code: 'CL', meaning: 'Claimed' },
  { code: 'CP', meaning: 'Certificate Printed' },
  { code: 'D1', meaning: 'Reject TPD' },
  { code: 'D2', meaning: 'Reject TI' },
  { code: 'D3', meaning: 'Reject CI' },
  { code: 'DC', meaning: 'Contract Declined' },
  { code: 'DH', meaning: 'Approved Death Claim' },
  { code: 'DR', meaning: 'Rejected Death Claim' },
  { code: 'DV', meaning: 'Decline Claim with Cert Void' },
  { code: 'EX', meaning: 'Expiry' },
  { code: 'FL', meaning: 'Freelook Cancellation' },
  { code: 'HP', meaning: 'Reinstatement In Progress' },
  { code: 'IF', meaning: 'In Force' },
  { code: 'LA', meaning: 'Lapsed' },
  { code: 'LR', meaning: 'Lapse Reinstatement' },
  { code: 'MA', meaning: 'Contract Matured' },
  { code: 'ML', meaning: 'Manual Lapse' },
  { code: 'MP', meaning: 'Component changes - modify prp' },
  { code: 'NC', meaning: 'NB Counter-Offer' },
  { code: 'NP', meaning: 'NB Pending' },
  { code: 'NR', meaning: 'Reverse to Proposal' },
  { code: 'NT', meaning: 'Not taken Up' },
  { code: 'PO', meaning: 'Postponed' },
  { code: 'PS', meaning: 'Proposal' },
  { code: 'PU', meaning: 'Paid Up Contract' },
  { code: 'PW', meaning: 'Windforward Pending' },
  { code: 'R1', meaning: 'Rejection by Manager' },
  { code: 'R2', meaning: 'Auto Rejection' },
  { code: 'R3', meaning: 'Register CI' },
  { code: 'R4', meaning: 'Register TPD' },
  { code: 'R5', meaning: 'Register TI' },
  { code: 'RD', meaning: 'Registered Death Claim' },
  { code: 'SU', meaning: 'Contract Surrendered' },
  { code: 'TC', meaning: 'TPD Fully Claim' },
  { code: 'TR', meaning: 'Contract Terminated' },
  { code: 'UA', meaning: 'U/W Approved' },
  { code: 'UP', meaning: 'UW Processing' },
  { code: 'UW', meaning: 'Underwriting' },
  { code: 'VO', meaning: 'Void Proposal' },
  { code: 'VR', meaning: 'Vesting Registered' },
  { code: 'WD', meaning: 'Contract Withdrawn' },
];

export const BIRO_OVERVIEW_STATUS_LIST: BiroReportStatus[] = [
  {
    code: 'BiroApproved',
    meaning: t('reportGeneration:biroStatus.overview.biroApproved'),
    dropdownLabel: t('reportGeneration:biroStatus.dropdown.biroApproved'),
  },
  {
    code: 'BiroRejected',
    meaning: t('reportGeneration:biroStatus.overview.biroRejected'),
    dropdownLabel: t('reportGeneration:biroStatus.dropdown.biroRejected'),
  },
  {
    code: 'BiroPending',
    meaning: t('reportGeneration:biroStatus.overview.biroPending'),
    dropdownLabel: t('reportGeneration:biroStatus.dropdown.biroPending'),
  },
];
