/**
 * This file contains the configuration for the data grid in report generation - Lapse report.
 */
import { t } from 'i18next';

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: t('reportGeneration:reportColumn.policyNumberAndpolicyHolderName'),
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: t('reportGeneration:reportColumn.agentCode'),
  },
  {
    type: 'agentName',
    title: t('reportGeneration:reportColumn.agentName'),
  },
  {
    type: 'product',
    title: t('reportGeneration:reportColumn.product'),
  },
  {
    type: 'status',
    title: t('reportGeneration:reportColumn.status'),
  },
  {
    type: 'registrationDate',
    title: t('reportGeneration:reportColumn.registrationDate'),
  },
  {
    type: 'submissionDate',
    title: t('reportGeneration:reportColumn.submissionDate'),
  },
  {
    type: 'issueDate',
    title: t('reportGeneration:reportColumn.issueDate'),
  },
  {
    type: 'premiumDueDate',
    title: t('reportGeneration:reportColumn.premiumDueDate'),
  },
  {
    type: 'surrenderDate',
    title: t('reportGeneration:reportColumn.surrenderDate'),
  },
  {
    type: 'lastInstallmentDate',
    title: t('reportGeneration:reportColumn.lastInstallmentDate'),
  },
  {
    type: 'frequency',
    title: t('reportGeneration:reportColumn.frequency'),
  },
  {
    type: 'paymentMethod',
    title: t('reportGeneration:reportColumn.paymentMethod'),
  },
  {
    type: 'mandateStatus',
    title: t('reportGeneration:reportColumn.mandateStatus'),
  },
  {
    type: 'policyTerm',
    title: t('reportGeneration:reportColumn.policyTerm'),
  },
  {
    type: 'premiumPaymentTerm',
    title: t('reportGeneration:reportColumn.premiumPaymentTerm'),
  },
  {
    type: 'currency',
    title: t('reportGeneration:reportColumn.currency'),
  },
  {
    type: 'rtu',
    title: t('reportGeneration:reportColumn.rtu'),
  },
  {
    type: 'modalPremium',
    title: t('reportGeneration:reportColumn.modalPremium'),
  },
  {
    type: 'ace',
    title: t('reportGeneration:reportColumn.ace'),
  },
  {
    type: 'singlePremium',
    title: t('reportGeneration:reportColumn.singlePremium'),
  },
];

// add biroTxnDate and biroStatus to the content but remove the value of submissionDate, lastInstallmentDate,policyTerm, premiumPaymentTerm, singlePremium,
export const BIRO_HEADERS = [
  {
    type: 'agentCode',
    title: t('reportGeneration:reportColumn.agentCode'),
  },
  {
    type: 'agentName',
    title: t('reportGeneration:reportColumn.agentName'),
  },
  {
    type: 'product',
    title: t('reportGeneration:reportColumn.product'),
  },
  {
    type: 'biroTxnDate',
    title: t('reportGeneration:reportColumn.biroTransactionDate'),
  },
  {
    type: 'biroStatus',
    title: t('reportGeneration:reportColumn.biroTransactionStatus'),
  },
  {
    type: 'status',
    title: t('reportGeneration:reportColumn.status'),
  },
  {
    type: 'registrationDate',
    title: t('reportGeneration:reportColumn.registrationDate'),
  },
  {
    type: 'nullSubmissionDate',
    title: t('reportGeneration:reportColumn.submissionDate'),
  },
  {
    type: 'issueDate',
    title: t('reportGeneration:reportColumn.issueDate'),
  },
  {
    type: 'premiumDueDate',
    title: t('reportGeneration:reportColumn.premiumDueDate'),
  },
  {
    type: 'surrenderDate',
    title: t('reportGeneration:reportColumn.surrenderDate'),
  },
  {
    type: 'nullLastInstallmentDate',
    title: t('reportGeneration:reportColumn.lastInstallmentDate'),
  },
  {
    type: 'frequency',
    title: t('reportGeneration:reportColumn.frequency'),
  },
  {
    type: 'paymentMethod',
    title: t('reportGeneration:reportColumn.paymentMethod'),
  },
  {
    type: 'mandateStatus',
    title: t('reportGeneration:reportColumn.mandateStatus'),
  },
  {
    type: 'nullPolicyTerm',
    title: t('reportGeneration:reportColumn.policyTerm'),
  },
  {
    type: 'nullPremiumPaymentTerm',
    title: t('reportGeneration:reportColumn.premiumPaymentTerm'),
  },
  {
    type: 'currency',
    title: t('reportGeneration:reportColumn.currency'),
  },
  {
    type: 'rtu',
    title: t('reportGeneration:reportColumn.rtu'),
  },
  {
    type: 'modalPremium',
    title: t('reportGeneration:reportColumn.modalPremium'),
  },
  {
    type: 'ace',
    title: t('reportGeneration:reportColumn.ace'),
  },
  {
    type: 'nullSinglePremium',
    title: t('reportGeneration:reportColumn.singlePremium'),
  },
];

//  handle general status
export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);

export const INQUIRIES_REPORT_POST_BODY = [
  {
    label: t('reportGeneration:reportColumn.policyNumber'),
    path: 'policyNumber',
  },
  {
    label: t('reportGeneration:reportColumn.policyHolderName'),
    path: 'policyHolderName',
  },
  ...HEADERS.map(header => ({
    label: header.title,
    path: header.type,
  })),
];

// handle biro status
export const BIRO_CELL_RENDER_ORDER = BIRO_HEADERS.map(header => header?.type);

export const BIRO_INQUIRIES_REPORT_POST_BODY = [
  {
    label: t('reportGeneration:reportColumn.policyNumber'),
    path: 'policyNumber',
  },
  {
    label: t('reportGeneration:reportColumn.policyHolderName'),
    path: 'policyHolderName',
  },
  ...BIRO_HEADERS.map(header => ({
    label: header.title,
    path: header.type,
  })),
];
