/**
 * This file contains the configuration for the data grid in report generation - certificate transaction report.
 */
import { t } from 'i18next';

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: t('reportGeneration:reportColumn.policyNumberAndpolicyHolderName'),
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: t('reportGeneration:reportColumn.agentCode'),
  },
  {
    type: 'agentName',
    title: t('reportGeneration:reportColumn.agentName'),
  },
  {
    type: 'product',
    title: t('reportGeneration:reportColumn.product'),
  },
  {
    type: 'issueDate',
    title: t('reportGeneration:reportColumn.issueDate'),
  },
  {
    type: 'transactionDate',
    title: t('reportGeneration:reportColumn.transactionDate'),
  },
  {
    type: 'frequency',
    title: t('reportGeneration:reportColumn.frequency'),
  },
  {
    type: 'paymentMethod',
    title: t('reportGeneration:reportColumn.paymentMethod'),
  },
  {
    type: 'mandateStatus',
    title: t('reportGeneration:reportColumn.mandateStatus'),
  },
  {
    type: 'modalPremium',
    title: t('reportGeneration:reportColumn.modalPremium'),
  },

  {
    type: 'ace',
    title: t('reportGeneration:reportColumn.ace'),
  },
  {
    type: 'tranReference',
    title: t('reportGeneration:reportColumn.tranReference'),
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
export const TRANSACTION_REPORT_POST_BODY = HEADERS.map(header => ({
  label: header.title,
  path: header.type,
}));
TRANSACTION_REPORT_POST_BODY.unshift(
  {
    label: t('reportGeneration:reportColumn.policyNumber'),
    path: 'policyNumber',
  },
  {
    label: t('reportGeneration:reportColumn.policyHolderName'),
    path: 'policyHolderName',
  },
);
