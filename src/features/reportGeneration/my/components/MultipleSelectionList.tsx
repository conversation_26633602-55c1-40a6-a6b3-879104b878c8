import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  H6,
  Icon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, TouchableOpacity, View } from 'react-native';
import { ReportStatus, SelectedReportStatus } from '../util/type';

export default function MultipleSelectionList({
  title,
  visible,
  onClose,
  statusList,
  selectedStatus,
  handleSelected,
}: {
  title: string;
  visible: boolean;
  onClose: () => void;
  statusList: ReportStatus[];
  selectedStatus: SelectedReportStatus[];
  handleSelected?: ({ selected }: { selected: SelectedReportStatus[] }) => void;
}) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [searchText, setSearchText] = useState('');

  const isSelectAllPresent = selectedStatus.length === statusList.length;

  const baseList = [
    // Always include SELECTALL at the top
    {
      code: 'SELECTALL',
      meaning: 'Select all',
      selected: isSelectAllPresent,
    },
    ...statusList.map(item => ({
      ...item,
      selected:
        selectedStatus.some(
          status => status.code === item.code && status.selected,
        ) ?? false,
    })),
  ];

  // search text to find out the status
  const filteredSelectionState = searchText
    ? baseList.filter(item =>
        item.meaning.toLowerCase().includes(searchText.toLowerCase()),
      )
    : baseList;

  const handleItemPress = (code: string) => {
    const updatedItem = filteredSelectionState.map(item =>
      item.code === code ? { ...item, selected: !item.selected } : item,
    );
    handleSelected?.({
      selected: updatedItem,
    });
  };

  const handleSelectAllPress = () => {
    const isSelectAllSelected = filteredSelectionState.every(
      item => item.selected,
    );

    handleSelected?.({
      selected: filteredSelectionState.map(item => ({
        ...item,
        selected: !isSelectAllSelected,
      })),
    });
  };

  const selectedItems = filteredSelectionState.filter(
    item => item.selected && item.code !== 'SELECTALL',
  );
  const hasSelectedItems = selectedItems.length > 0;

  const isSelectAllSelected = filteredSelectionState
    .filter(item => item.code !== 'SELECTALL')
    .every(item => item.selected);

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalContainer>
          <View
            style={{
              borderRadius: sizes[4],
              padding: space[6],
              paddingBottom: space[12],
              width: '80%',
              height: '90%',
              backgroundColor: colors.palette.white,
            }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={onClose}>
                <Icon.Close size={sizes[6]} fill={colors.onBackground} />
              </TouchableOpacity>
            </Row>
            <View style={{ paddingHorizontal: space[6], flex: 1 }}>
              <H6
                fontWeight="bold"
                color={colors.onBackground}
                style={{ paddingBottom: space[4] }}>
                {title}
              </H6>
              <TextField
                inputContainerStyle={{
                  borderRadius: sizes[12],
                  height: sizes[11],
                }}
                value={searchText}
                placeholder={t('searchForStatus')}
                onChangeText={text => setSearchText(text)}
              />
              {/* Selected Items with delete function at the top */}
              <ScrollView
                horizontal
                style={{
                  paddingTop: hasSelectedItems ? space[3] : 0,
                  paddingBottom: hasSelectedItems ? space[11] : space[6],
                }}>
                {isSelectAllSelected ? (
                  <TouchableOpacity onPress={handleSelectAllPress}>
                    <Row
                      backgroundColor={colors.palette.fwdOrange[5]}
                      alignItems="center"
                      paddingX={space[3]}
                      paddingY={space[2]}
                      gap={space[1]}
                      borderRadius={space[11]}
                      borderWidth={2}
                      borderColor={colors.primary}>
                      <Typography.H8 fontWeight="medium">
                        {t('selectedAll')}
                      </Typography.H8>
                      <Icon.Close size={sizes[5]} />
                    </Row>
                  </TouchableOpacity>
                ) : (
                  selectedItems.map(item => (
                    <TouchableOpacity
                      key={item.code}
                      onPress={() => handleItemPress(item.code)}>
                      <Row
                        backgroundColor={colors.palette.fwdOrange[5]}
                        alignItems="center"
                        marginRight={space[1]}
                        paddingX={space[3]}
                        paddingY={space[2]}
                        gap={space[1]}
                        borderRadius={space[11]}
                        borderWidth={2}
                        borderColor={colors.primary}>
                        <Typography.H8 fontWeight="medium">
                          {item.meaning}
                        </Typography.H8>
                        <Icon.Close size={sizes[5]} />
                      </Row>
                    </TouchableOpacity>
                  ))
                )}
              </ScrollView>
              <Box>
                <Typography.Body color={colors.palette.fwdGreyDarker}>
                  {t('pleaseSelect')}
                </Typography.Body>
              </Box>
              {/* Status list is shown in vertical view with tick icon */}
              <ScrollView contentContainerStyle={{}}>
                <View>
                  {filteredSelectionState.map(item => {
                    return (
                      <TouchableOpacity
                        key={item.code}
                        onPress={() =>
                          item.code === 'SELECTALL'
                            ? handleSelectAllPress()
                            : handleItemPress(item.code)
                        }>
                        <Row
                          key={item.code}
                          justifyContent="space-between"
                          paddingY={space[3]}
                          borderBottom={1}
                          borderBottomColor={colors.palette.fwdGrey[100]}>
                          <Typography.LargeLabel>
                            {item.meaning}
                          </Typography.LargeLabel>
                          {item.code === 'SELECTALL' && isSelectAllSelected && (
                            <Icon.Tick size={sizes[4]} />
                          )}
                          {item.code !== 'SELECTALL' &&
                            !isSelectAllSelected &&
                            filteredSelectionState.find(
                              selectItem => selectItem.code == item.code,
                            )?.selected && <Icon.Tick size={sizes[4]} />}
                        </Row>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </ScrollView>
              <Row
                gap={space[4]}
                justifyContent="center"
                alignItems="baseline"
                paddingTop={space[6]}>
                <Button
                  text={t('reset')}
                  variant="secondary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    setSearchText('');
                    // Resetting the selection state will select all items.
                    handleSelected?.({
                      selected: baseList.map(item => ({
                        ...item,
                        selected: true,
                      })),
                    });
                  }}
                />
                <Button
                  text={t('confirm')}
                  variant="primary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    const selectedItems = filteredSelectionState.filter(
                      item => item.selected,
                    );
                    handleSelected?.({ selected: selectedItems });
                    onClose();
                  }}
                />
              </Row>
            </View>
          </View>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { colors, sizes, space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));
