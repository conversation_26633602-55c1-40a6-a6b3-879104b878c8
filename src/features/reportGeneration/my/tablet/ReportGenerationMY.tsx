import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Column,
  CubePictogramIcon,
  H6,
  Icon,
  Label,
  LargeBody,
  Row,
  SmallBody,
  SmallLabel,
  TextField,
  Typography,
} from 'cube-ui-components';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SelectAgentModal from '../components/SelectAgentModal';
import { format, subDays, subMonths } from 'date-fns';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import { DatePeriodSearch } from 'types/report';
import MultipleSelectionList from '../components/MultipleSelectionList';
import { BIRO_OVERVIEW_STATUS_LIST, STATUS_LIST } from '../util/statusList';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import DatePeriodActionPanelMY from '../components/DatePeriodActionPanel';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
  BiroReportStatus,
  ReportStatus,
  SelectedReportStatus,
} from '../util/type';

const ICON_SIZE = 40;

const today = format(new Date(), 'yyyy-MM-dd');
const lastTwoMonths = format(subMonths(new Date(), 2), 'yyyy-MM-dd');
const lastThirtyDays = format(subDays(new Date(), 29), 'yyyy-MM-dd'); // 29 as today is included
const last180Days = format(subDays(new Date(), 179), 'yyyy-MM-dd'); // 179 as today is included
const DEFAULT_DATE_PERIOD: DatePeriodSearch = {
  datePeriodType: 'DUE',
  from: lastTwoMonths,
  to: today,
};

export default function ReportGenerationMY() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors, sizes, space } = useTheme();
  const { top } = useSafeAreaInsets();
  const { t } = useTranslation('reportGeneration');
  const [ANPDatePeriodButton, setANPDatePeriodButton] = useState('last2months');
  const [DDRDatePeriodButton, setDDRDatePeriodButton] = useState('last2months');
  const [ANPdatePeriodPanel, setANPDatePeriodPanel] = useState(false);
  const [DDRdatePeriodPanel, setDDRDatePeriodPanel] = useState(false);
  const [isTransactionReportDisabled, setIsTransactionReportDisabled] =
    useState(true);
  const [isCertificateTouched, setIsCertificateTouched] = useState(false);
  const [certificateNumber, setCertificateNumber] = useState<string>('');
  const [datePeriod, updateDatePeriod] =
    useState<DatePeriodSearch>(DEFAULT_DATE_PERIOD);
  const [applicationNotProceedDatePeriod, setApplicationNotProceedDatePeriod] =
    useState(DEFAULT_DATE_PERIOD);
  const [dueDateReportDatePeriod, setDueDateReportDatePeriod] =
    useState(DEFAULT_DATE_PERIOD);
  const [cirReportType, setCirReportType] = useState('general');

  // handle agent selection
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(
    memberInfoList[0],
  );
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const handleSelectedAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };
  useEffect(() => {
    if (memberInfoList.length > 0 && !selectedAgent) {
      setSelectedAgent(memberInfoList[0]);
    }
  }, [memberInfoList, selectedAgent]);

  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };
  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;

  // handle status selection
  const [showStatusSelectionList, setShowStatusSelectionList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<SelectedReportStatus[]>(
    STATUS_LIST.map(item => ({
      ...item,
      selected: true,
    })),
  );

  const onCloseShowStatusSelectionList = () => {
    setShowStatusSelectionList(false);
  };

  const handleSelectedStatus = ({
    selected,
  }: {
    selected: SelectedReportStatus[];
  }) => {
    const selectedItems = selected.filter(item => item.code !== 'SELECTALL');
    setSelectedStatus(selectedItems);
  };

  // Handles removing a selected status from the selectedStatus array
  const handleRemoveStatus = (statusToRemove: ReportStatus) => {
    setSelectedStatus(
      selectedStatus.filter(status => status.code !== statusToRemove.code),
    );
  };

  const selectedGeneralStatusCount = selectedStatus.length;

  const CARD_CONFIG = [
    {
      title: t('applicationNotProceed'),
      icon: <CubePictogramIcon.MedicalReportError size={ICON_SIZE} />,
    },
    {
      title: t('dueDateReport'),
      icon: <CubePictogramIcon.Timer size={ICON_SIZE} />,
    },
    {
      title: t('certificateTransactionReport'),
      icon: <CubePictogramIcon.CreditCardOnHand size={ICON_SIZE} />,
    },
    {
      title: t('certificateInquiriesReport'),
      icon: <CubePictogramIcon.InformationDocument size={ICON_SIZE} />,
    },
  ];

  const DATEPERIOD_CONFIG = [
    {
      title: t('last2months'),
      value: 'last2months',
    },
    {
      title: t('last30days'),
      value: 'last30days',
    },
    {
      title: t('last180days'),
      value: 'last180days',
    },
    {
      title: t('customise'),
      value: 'customise',
    },
  ];

  useMemo(() => {
    ANPDatePeriodButton === 'customise'
      ? setANPDatePeriodPanel(true)
      : setANPDatePeriodPanel(false);
    DDRDatePeriodButton === 'customise'
      ? setDDRDatePeriodPanel(true)
      : setDDRDatePeriodPanel(false);
  }, [ANPDatePeriodButton, DDRDatePeriodButton]);

  // Certificate Transaction Report - Validation
  useEffect(() => {
    setIsTransactionReportDisabled(
      !validateMYCertificateNumber(certificateNumber),
    );
  }, [certificateNumber, isCertificateTouched]);

  // Inquiries Report - BIRO Status
  const [selectedBIROStatus, setSelectedBIROStatus] = useState<
    BiroReportStatus[]
  >(BIRO_OVERVIEW_STATUS_LIST);

  return (
    <>
      <DatePeriodActionPanelMY
        visible={ANPdatePeriodPanel}
        handleClose={() => setANPDatePeriodPanel(false)}
        contextValue={applicationNotProceedDatePeriod}
        updateContextValue={setApplicationNotProceedDatePeriod}
        defaultDatePeriod={DEFAULT_DATE_PERIOD}
        disableTypeChip={true}
      />
      <DatePeriodActionPanelMY
        visible={DDRdatePeriodPanel}
        handleClose={() => setDDRDatePeriodPanel(false)}
        contextValue={dueDateReportDatePeriod}
        updateContextValue={setDueDateReportDatePeriod}
        defaultDatePeriod={DEFAULT_DATE_PERIOD}
        disableTypeChip={true}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectedAgent}
        selectedAgent={selectedAgent}
        setSelectedAgent={setSelectedAgent}
      />
      <MultipleSelectionList
        title={t('generalStatus')}
        visible={showStatusSelectionList}
        onClose={onCloseShowStatusSelectionList}
        statusList={STATUS_LIST}
        selectedStatus={selectedStatus}
        handleSelected={handleSelectedStatus}
      />
      <View style={{ paddingTop: top, backgroundColor: colors.background }} />

      <KeyboardAwareScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={{
          paddingHorizontal: space[8],
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          gap: space[3],
        }}>
        <Row justifyContent="space-between" alignItems="center">
          <H6
            fontWeight="bold"
            children={t('reportGeneration')}
            style={{
              paddingTop: space[8],
              paddingBottom: space[7],
            }}
          />
          {isLeader && (
            <Row
              alignItems="center"
              justifyContent="space-between"
              gap={space[1]}>
              <TouchableOpacity
                disabled={isTeamSelected}
                onPress={() => {
                  setShowSelectedAgentList(true);
                }}>
                <Row
                  gap={space[2]}
                  paddingX={space[3]}
                  paddingY={space[2]}
                  backgroundColor={
                    isTeamSelected
                      ? colors.palette.fwdGrey[20]
                      : colors.background
                  }
                  borderRadius={sizes[2]}
                  borderWidth={1}
                  borderColor={colors.palette.fwdGrey[100]}>
                  <SmallBody
                    color={
                      isTeamSelected
                        ? colors.palette.fwdGrey[100]
                        : colors.onBackground
                    }>
                    Selected agent
                  </SmallBody>
                  <Row gap={space[1]} alignItems="center">
                    <Label
                      fontWeight="normal"
                      color={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }>
                      {selectedAgent?.agentName ?? '--'}
                    </Label>
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }
                    />
                  </Row>
                </Row>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setIsTeamSelected(!isTeamSelected);
                  // If team is selected, set the login member as selected agent
                  setSelectedAgent(memberInfoList[0]);
                }}>
                <Row
                  gap={space[2]}
                  paddingX={space[3]}
                  paddingY={space[2]}
                  backgroundColor={
                    isTeamSelected
                      ? colors.palette.fwdOrange[5]
                      : colors.background
                  }
                  borderRadius={sizes[2]}
                  borderWidth={isTeamSelected ? 2 : 1}
                  borderColor={
                    isTeamSelected
                      ? colors.primary
                      : colors.palette.fwdGrey[100]
                  }>
                  <Checkbox
                    value={isTeamSelected}
                    onChange={() => {
                      setIsTeamSelected(!isTeamSelected);
                      // If team is selected, set the login member as selected agent
                      setSelectedAgent(memberInfoList[0]);
                    }}
                  />
                  <Row gap={space[1]} alignItems="center">
                    <Label>{t('toolbar.team')}</Label>
                  </Row>
                </Row>
              </TouchableOpacity>
            </Row>
          )}
        </Row>
        <Row gap={space[3]}>
          {CARD_CONFIG.map((cardItem, index) => {
            if (index < 2) {
              return (
                <OverviewCard key={index}>
                  <Row gap={space[3]} alignItems="center">
                    {cardItem.icon}
                    <H6 fontWeight="bold" children={cardItem.title} />
                  </Row>
                  <Row gap={space[3]} alignItems="center">
                    <SmallLabel color={colors.palette.fwdGreyDarkest}>
                      {t('dateType')}
                    </SmallLabel>
                    <Chip
                      label={index == 0 ? t('registrationDate') : t('dueDate')}
                      focus={true}
                      onPress={() => {
                        console.log('pressed chip');
                      }}
                    />
                  </Row>
                  <Column gap={space[2]}>
                    <SmallLabel color={colors.palette.fwdGreyDarkest}>
                      {t('datePeriod')}
                    </SmallLabel>
                    <Row gap={space[2]}>
                      {DATEPERIOD_CONFIG.map((item, index) => {
                        return (
                          <Chip
                            key={index}
                            label={item.title}
                            focus={
                              cardItem.title === t('applicationNotProceed')
                                ? item.value === ANPDatePeriodButton
                                : item.value === DDRDatePeriodButton
                            }
                            onPress={() => {
                              cardItem.title === t('applicationNotProceed')
                                ? setANPDatePeriodButton(item.value)
                                : setDDRDatePeriodButton(item.value);
                              cardItem.title === t('applicationNotProceed')
                                ? setApplicationNotProceedDatePeriod({
                                    ...applicationNotProceedDatePeriod,
                                    from:
                                      item.value === 'last2months'
                                        ? lastTwoMonths
                                        : item.value === 'last30days'
                                        ? lastThirtyDays
                                        : item.value === 'last180days'
                                        ? last180Days
                                        : applicationNotProceedDatePeriod.from,
                                    to: today,
                                  })
                                : setDueDateReportDatePeriod({
                                    ...dueDateReportDatePeriod,
                                    from:
                                      item.value === 'last2months'
                                        ? lastTwoMonths
                                        : item.value === 'last30days'
                                        ? lastThirtyDays
                                        : item.value === 'last180days'
                                        ? last180Days
                                        : dueDateReportDatePeriod.from,
                                    to: today,
                                  });
                            }}
                          />
                        );
                      })}
                    </Row>
                  </Column>
                  <LargeBody>
                    {cardItem.title === t('applicationNotProceed')
                      ? `${formatDate(
                          applicationNotProceedDatePeriod.from,
                        )} to ${formatDate(applicationNotProceedDatePeriod.to)}`
                      : `${formatDate(
                          dueDateReportDatePeriod.from,
                        )} to ${formatDate(dueDateReportDatePeriod.to)}`}
                  </LargeBody>
                  <Box alignItems="flex-end">
                    <Button
                      variant="primary"
                      text={t('generate')}
                      size="default"
                      onPress={() => {
                        if (!selectedAgent) {
                          console.error('No agent selected');
                          return;
                        }
                        if (cardItem.title === t('applicationNotProceed')) {
                          console.log('pressed generate report');
                          navigation.navigate('ApplicationNotProceedScreen', {
                            to: applicationNotProceedDatePeriod.to,
                            from: applicationNotProceedDatePeriod.from,
                            agent: selectedAgent,
                            team: isTeamSelected,
                          });
                        }
                        if (cardItem.title === t('dueDateReport')) {
                          navigation.navigate('DueDateReportScreen', {
                            to: dueDateReportDatePeriod.to,
                            from: dueDateReportDatePeriod.from,
                            agent: selectedAgent,
                            team: isTeamSelected,
                          });
                        }
                      }}
                    />
                  </Box>
                </OverviewCard>
              );
            }
          })}
        </Row>
        <Row gap={space[3]}>
          {CARD_CONFIG.map((item, index) => {
            if (index >= 2) {
              return (
                <OverviewCard key={index}>
                  <Row gap={space[3]} alignItems="center">
                    {item.icon}
                    <H6 fontWeight="bold" children={item.title} />
                  </Row>
                  {index === 3 ? (
                    <>
                      <Row gap={space[3]} alignItems="center">
                        <SmallLabel color={colors.palette.fwdGreyDarkest}>
                          {t('reportType')}
                        </SmallLabel>
                        <Chip
                          label={t('general')}
                          focus={cirReportType === 'general'}
                          onPress={() => {
                            setCirReportType('general');
                            console.log('pressed chip');
                          }}
                        />
                        <Chip
                          label={t('BIRO')}
                          focus={cirReportType === 'BIRO'}
                          onPress={() => {
                            setCirReportType('BIRO');
                            console.log('pressed chip');
                          }}
                        />
                      </Row>
                      <Column gap={space[2]} h={sizes[22]}>
                        {cirReportType === 'general' ? (
                          <>
                            <SmallLabel color={colors.palette.fwdGreyDarkest}>
                              {t('statusSelected')} (
                              {selectedStatus ? selectedGeneralStatusCount : 0})
                            </SmallLabel>
                            {selectedGeneralStatusCount > 0 && (
                              <ScrollView horizontal>
                                <Row gap={space[2]} alignItems="center">
                                  {/* STATUS_LIST – initialized using values from the backend configuration */}
                                  {selectedGeneralStatusCount ===
                                  STATUS_LIST.length ? (
                                    <Chip
                                      label={t('allStatus')}
                                      focus={true}
                                      onPress={() => {
                                        setShowStatusSelectionList(true);
                                      }}
                                    />
                                  ) : (
                                    selectedStatus.map((status, index) => (
                                      <Chip
                                        key={index}
                                        label={status.meaning}
                                        focus={true}
                                        onPress={() =>
                                          handleRemoveStatus(status)
                                        }
                                        icon={props => (
                                          <Icon.Close
                                            {...props}
                                            width={sizes[5]}
                                            height={sizes[5]}
                                          />
                                        )}
                                      />
                                    ))
                                  )}
                                </Row>
                              </ScrollView>
                            )}

                            <TouchableOpacity
                              style={{
                                flexDirection: 'row',
                                gap: space[1],
                              }}
                              onPress={() => {
                                setShowStatusSelectionList(true);
                              }}>
                              <Icon.Create
                                size={sizes[4]}
                                fill={colors.palette.fwdAlternativeOrange[100]}
                              />
                              <Typography.H8
                                fontWeight="medium"
                                color={
                                  colors.palette.fwdAlternativeOrange[100]
                                }>
                                {t('toolbar.editStatus')}
                              </Typography.H8>
                            </TouchableOpacity>
                          </>
                        ) : (
                          <>
                            <SmallLabel color={colors.palette.fwdGreyDarkest}>
                              {`${t('statusSelected')} (${
                                selectedBIROStatus
                                  ? selectedBIROStatus.length
                                  : 0
                              })`}
                            </SmallLabel>
                            <ScrollView horizontal>
                              <Row
                                gap={space[2]}
                                alignItems="center"
                                paddingBottom={space[6]}>
                                {BIRO_OVERVIEW_STATUS_LIST.map(
                                  (status, index) => (
                                    <Chip
                                      key={index}
                                      label={status.meaning}
                                      focus={selectedBIROStatus.some(
                                        selected =>
                                          selected.code === status.code,
                                      )}
                                      onPress={() => {
                                        const isSelected =
                                          selectedBIROStatus.some(
                                            selected =>
                                              selected.code === status.code,
                                          );
                                        const updatedStatus = isSelected
                                          ? selectedBIROStatus.filter(
                                              selected =>
                                                selected.code !== status.code,
                                            )
                                          : [...selectedBIROStatus, status];
                                        setSelectedBIROStatus(updatedStatus);
                                      }}
                                    />
                                  ),
                                )}
                              </Row>
                            </ScrollView>
                          </>
                        )}
                      </Column>
                      <Box alignItems="flex-end">
                        <Button
                          variant="primary"
                          text={t('generate')}
                          size="default"
                          onPress={() => {
                            if (!selectedAgent) {
                              console.error('No agent selected');
                              return;
                            }
                            navigation.navigate('InquiriesReportScreen', {
                              to: today,
                              from: lastTwoMonths,
                              agent: selectedAgent,
                              team: isTeamSelected,
                              selectedGeneralStatus: selectedStatus,
                              generalStatusList: STATUS_LIST,
                              biro: cirReportType === 'BIRO',
                              biroSelectedStatus:
                                cirReportType === 'BIRO'
                                  ? selectedBIROStatus
                                  : BIRO_OVERVIEW_STATUS_LIST,
                            });
                          }}
                        />
                      </Box>
                    </>
                  ) : (
                    <>
                      <Box gap={space[3]}>
                        <Typography.H8 color={colors.palette.fwdGreyDarkest}>
                          {t('certificateTransactionReportDetails')}
                        </Typography.H8>
                        <TextField
                          key={item.title}
                          onChange={value => {
                            setCertificateNumber(value);
                            setIsCertificateTouched(true);
                          }}
                          label={t('certificateNumber')}
                          placeholder={t('certificateNumber')}
                          hint={t('digitHint')}
                          error={
                            isCertificateTouched && isTransactionReportDisabled
                              ? t(
                                  'toolbar.certificateTransactionReport.error.hints',
                                )
                              : undefined
                          }
                        />
                      </Box>
                      <Box alignItems="flex-end">
                        <Button
                          disabled={isTransactionReportDisabled}
                          variant="primary"
                          text={t('generate')}
                          size="default"
                          onPress={() => {
                            if (!selectedAgent) {
                              console.error('No agent selected');
                              return;
                            }
                            navigation.navigate('TransactionReportScreen', {
                              to: today,
                              from: lastTwoMonths,
                              agent: selectedAgent,
                              team: isTeamSelected,
                              policyNumber: certificateNumber,
                            });
                          }}
                        />
                      </Box>
                    </>
                  )}
                </OverviewCard>
              );
            }
          })}
        </Row>
      </KeyboardAwareScrollView>
    </>
  );
}

const OverviewCard = styled.View(({ theme }) => {
  const { colors, space, sizes } = theme;
  return {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: sizes[4],
    padding: space[4],
    gap: space[3],
  };
});

export const validateMYCertificateNumber = (certificateNumber: string) => {
  return (
    certificateNumber.length === 8 && /^[a-z0-9]+$/i.test(certificateNumber)
  );
};
