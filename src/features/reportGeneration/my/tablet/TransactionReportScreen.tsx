import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Icon,
  Label,
  Row,
  SmallBody,
  Typography,
} from 'cube-ui-components';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import _ from 'lodash';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import {
  useGetTransactionReport,
  usePostTransactionReport,
} from 'hooks/useReportGeneration';
import ReportDataGrid from 'features/reportGeneration/ph/components/DataGrid/ReportDataGrid';
import Animated, { LinearTransition } from 'react-native-reanimated';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from '../util/transactionReportTableConfig';
import { TouchableOpacity } from 'react-native';
import DateSelectModal from '../components/DateSelectModal';
import SelectAgentModal from '../components/SelectAgentModal';
import PolicyInfoActionPanel from 'features/reportGeneration/components/ActionPanel/PolicyInfoActionPanel';
import { TransactionReportScreenProps } from '../util/type';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import {
  ActionPanelChipConfig,
  ActionPanelSearchTypeConfig,
  DatePeriodType,
  PolicyInfoSearch,
} from 'types/report';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { FilterView, ExportCsv } from './ApplicationNotProceedScreen';

const getDefaultPolicyInfo: (policyNumber?: string) => PolicyInfoSearch = (
  policyNumber?: string,
) => ({
  searchType: 'individual',
  focusedChip: 'policyNumber',
  policyHolderName: '',
  policyNumber: String(policyNumber || ''),
});

export default function TransactionReportScreen(
  props: TransactionReportScreenProps,
) {
  const { agent, to, from, team, policyNumber } = props.route.params;
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(agent);
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(team);
  const [showDateSelectModal, setShowDateSelectModal] = useState(false);
  const [policyInfo, updatePolicyInfo] = useState<PolicyInfoSearch>(
    getDefaultPolicyInfo(policyNumber),
  );
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);

  const [dateRange, setDateRange] = useState<{
    from: string;
    to: string;
    datePeriodType: DatePeriodType;
  }>({
    from: from,
    to: to,
    datePeriodType: 'ISS',
  });

  const {
    data: transactionReportData,
    isLoading: isTransactionReportLoading,
    isError,
  } = useGetTransactionReport({
    agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
    team: isTeamSelected,
    from: dateRange.from,
    to: dateRange.to,
    dateType: dateRange.datePeriodType,
    transactionReportPolicyInfo: {
      policyHolderName: policyInfo.policyHolderName,
      policyNumber: policyInfo.policyNumber,
    },
  });

  transactionReportData?.data?.forEach(
    (item: {
      tranReference?: string;
      description?: string;
      mandateStatus?: string;
    }) => {
      // mandateStatus will not display the field if it is null or undefined - backend
      if (!item.mandateStatus) {
        item.mandateStatus = '--';
      }
    },
  );

  const { mutateAsync: saveCsv, isLoading: isSavingCsv } =
    usePostTransactionReport();

  const onSaveCsv = async () => {
    try {
      await saveCsv({
        agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
        from: dateRange.from,
        to: dateRange.to,
        team: isTeamSelected,
        dateType: dateRange.datePeriodType,
        transactionReportPolicyInfo: {
          policyHolderName: policyInfo.policyHolderName,
          policyNumber: policyInfo.policyNumber,
        },
        isLeader: isLeader ?? false,
      });
    } catch (error) {
      console.error('Error saving CSV:', error);
    }
  };

  const handleDateRangeChange = ({
    to,
    from,
    dateType,
  }: {
    to: string;
    from: string;
    dateType?: DatePeriodType;
  }) => {
    setDateRange({
      from: from,
      to: to,
      datePeriodType: dateType ?? 'ISS',
    });
  };

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;

  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  const DATEPERIOD_CONFIG = [
    {
      title: t('last2months'),
      value: 'last2months',
    },
    {
      title: t('last30days'),
      value: 'last30days',
    },
    {
      title: t('last180days'),
      value: 'last180days',
    },
    {
      title: t('customise'),
      value: 'customise',
    },
  ];

  const DATETYPE_CONFIG = [
    {
      title: t('toolbar.issueDate'),
      value: 'ISS',
    },
    {
      title: t('toolbar.transactionDate'),
      value: 'EFF',
    },
  ];

  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };

  const handleSelectAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };
  return (
    <>
      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
        defaultPolicyInfo={getDefaultPolicyInfo()}
        searchTypeConfig={SEARCH_TYPE_CONFIG}
        chipConfig={CHIP_CONFIG}
        title={t('certificateNumber')}
        searchTip={t('actionPanel.subtitle.searchTip.certNo')}
        policyNumberError={t(
          'toolbar.certificateTransactionReport.error.hints',
        )}
        primaryLabel={t('confirm')}
        secondaryLabel={t('actionPanel.cancel')}
        onPressSecondaryButton={resetErrorState => {
          resetErrorState();
          setOpenPolicyHolderPanel(false);
        }}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectAgent}
        selectedAgent={selectedAgent}
        setSelectedAgent={setSelectedAgent}
      />
      <DateSelectModal
        visible={showDateSelectModal}
        handleClose={() => {
          setShowDateSelectModal(false);
        }}
        datePeriodConfig={DATEPERIOD_CONFIG}
        title="Date period"
        defaultDateRange={{
          datePeriodType: dateRange.datePeriodType,
          from: dateRange.from,
          to: dateRange.to,
        }}
        handleDateRangeChange={handleDateRangeChange}
        dateTypeConfig={DATETYPE_CONFIG}
      />
      <TabletScreenHeader
        route={'TransactionReportScreen'}
        customTitle="Certificate transaction report"
        isLeftArrowBackShown
        showBottomSeparator={false}
        rightChildren={
          (transactionReportData?.summary?.caseCount ?? 0) > 0 ? (
            <ExportCsv onSaveCsv={onSaveCsv} isSavingCsv={isSavingCsv} />
          ) : undefined
        }
      />
      {/* First - white row */}
      <Box padding={space[4]} gap={space[2]}>
        <Row gap={space[1]}>
          {isLeader && (
            <>
              <TouchableOpacity
                disabled={isTeamSelected}
                onPress={() => {
                  setShowSelectedAgentList(true);
                }}>
                <FilterView>
                  <SmallBody
                    color={
                      isTeamSelected
                        ? colors.palette.fwdGrey[100]
                        : colors.onBackground
                    }>
                    {t('toolbar.selectedAgent')}
                  </SmallBody>
                  <Row gap={space[1]} alignItems="center">
                    <Label
                      fontWeight="medium"
                      color={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }>
                      {selectedAgent?.agentName ?? '--'}
                    </Label>
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }
                    />
                  </Row>
                </FilterView>
              </TouchableOpacity>
              <FilterView customTeamView={isTeamSelected}>
                <Checkbox
                  value={isTeamSelected}
                  onChange={() => {
                    setIsTeamSelected(!isTeamSelected);
                    // If team is selected, set the login member as selected agent
                    setSelectedAgent(memberInfoList[0]);
                  }}
                />
                <Label>{t('toolbar.team')}</Label>
              </FilterView>
            </>
          )}
          <TouchableOpacity
            onPress={() => {
              setOpenPolicyHolderPanel(true);
            }}>
            <FilterView
              style={{
                backgroundColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[5]
                    : colors.background,
                borderColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100],
                borderWidth:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? 2
                    : 1,
              }}>
              <SmallBody>{t('certificateNumber')}</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">
                  {policyInfo.policyHolderName ||
                    policyInfo.policyNumber ||
                    'All'}
                </Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setShowDateSelectModal(true);
            }}>
            <FilterView>
              <SmallBody>
                {dateRange.datePeriodType === 'ISS'
                  ? t('toolbar.issueDate')
                  : t('toolbar.transactionDate')}
              </SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">{`${dateFormatUtil(
                  dateRange.from,
                )} - ${dateFormatUtil(dateRange.to)}`}</Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
        </Row>
        <Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {t('totalResultNum', {
              totalResultNum: transactionReportData?.summary.caseCount ?? 0,
            })}
          </Typography.Body>
        </Row>
      </Box>
      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ReportDataGrid
          darkMode={false}
          isLoading={isTransactionReportLoading}
          isError={isError}
          data={transactionReportData?.data}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
        />
      </Animated.View>
    </>
  );
}

const SEARCH_TYPE_CONFIG: ActionPanelSearchTypeConfig = [
  { label: 'actionPanel.option.individual', value: 'individual' },
] as const;

const CHIP_CONFIG: ActionPanelChipConfig = [
  { type: 'policyNumber', label: 'actionPanel.placeholder.policyNumber' },
] as const;
