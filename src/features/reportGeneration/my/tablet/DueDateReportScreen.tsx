import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Column,
  Icon,
  Label,
  LargeLabel,
  Row,
  SmallBody,
  Typography,
} from 'cube-ui-components';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import {
  useGetDueDateReport,
  usePostDueDateReport,
} from 'hooks/useReportGeneration';
import ReportDataGrid from 'features/reportGeneration/ph/components/DataGrid/ReportDataGrid';
import Animated, { LinearTransition } from 'react-native-reanimated';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from '../util/dueDateReportTableConfig';
import { TouchableOpacity } from 'react-native';
import styled from '@emotion/native';
import DateSelectModal from '../components/DateSelectModal';
import SelectAgentModal from '../components/SelectAgentModal';
import PolicyInfoActionPanel from 'features/reportGeneration/components/ActionPanel/PolicyInfoActionPanel';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import {
  CHIP_CONFIG,
  DEFAULT_POLICY_INFO,
  SEARCH_TYPE_CONFIG,
} from 'features/reportGeneration/components/ActionPanel/config/policyInfo';
import { FilterView, ExportCsv } from './ApplicationNotProceedScreen';
import { PolicyInfoSearch } from 'types/report';

/**
 * For both mobile and tablet
 */
export default function DueDateReportScreen(props: any) {
  const { agent, to, from, team } = props.route.params;
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(agent);
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(team);
  const [showDateSelectModal, setShowDateSelectModal] = useState(false);
  const [policyInfo, updatePolicyInfo] = useState<PolicyInfoSearch>({
    searchType: 'all',
    focusedChip: 'policyNumber',
    policyHolderName: '',
    policyNumber: '',
  });
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: from,
    to: to,
    datePeriodType: 'DUE',
  });

  console.log('policyInfo', policyInfo);

  const handleDateRangeChange = ({
    to,
    from,
  }: {
    to: string;
    from: string;
  }) => {
    setDateRange({
      from: from,
      to: to,
      datePeriodType: 'DUE',
    });
  };

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  // console.log('memberInfoList', memberInfoList);
  // console.log('isLeader', isLeader);
  // console.log('hasNoDownline', hasNoDownline);

  const DATEPERIOD_CONFIG = [
    {
      title: t('last2months'),
      value: 'last2months',
    },
    {
      title: t('last30days'),
      value: 'last30days',
    },
    {
      title: t('last180days'),
      value: 'last180days',
    },
    {
      title: t('customise'),
      value: 'customise',
    },
  ];

  const {
    data: dueDateReportData,
    isLoading: isDueDateReportLoading,
    isError,
  } = useGetDueDateReport({
    agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
    team: isTeamSelected,
    from: dateRange.from,
    to: dateRange.to,
    dueDateReportPolicyInfo: {
      policyHolderName: policyInfo.policyHolderName,
      policyNumber: policyInfo.policyNumber,
    },
  });

  const { mutateAsync: saveCsv, isLoading: isSavingCsv } =
    usePostDueDateReport();

  const onSaveCsv = async () => {
    try {
      await saveCsv({
        agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
        team: isTeamSelected,
        from: dateRange.from,
        to: dateRange.to,
        dueDateReportPolicyInfo: {
          policyHolderName: policyInfo.policyHolderName,
          policyNumber: policyInfo.policyNumber,
        },
        isLeader: isLeader ?? false,
      });
    } catch (error) {
      console.error(`Error saving CSV:`, error);
    }
  };

  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };

  const handleSelectAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };

  return (
    <>
      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
        //
        defaultPolicyInfo={DEFAULT_POLICY_INFO}
        searchTypeConfig={SEARCH_TYPE_CONFIG}
        chipConfig={CHIP_CONFIG}
        //
        title={t('actionPanel.title.searchForPolicyInfo')}
        searchTip={t('actionPanel.subtitle.searchTip')}
        policyHolderNameError={'Invalid input'}
        policyNumberHint={t('actionPanel.hint.policyNumber')}
        policyNumberError={'Invalid input'}
        //
        primaryLabel={t('actionPanel.search')}
        secondaryLabel={t('actionPanel.reset')}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectAgent}
        selectedAgent={selectedAgent}
        setSelectedAgent={setSelectedAgent}
      />
      <DateSelectModal
        visible={showDateSelectModal}
        handleClose={() => {
          setShowDateSelectModal(false);
        }}
        datePeriodConfig={DATEPERIOD_CONFIG}
        title="Due date"
        defaultDateRange={{
          datePeriodType: 'DUE',
          from: dateRange.from,
          to: dateRange.to,
        }}
        handleDateRangeChange={handleDateRangeChange}
      />
      <TabletScreenHeader
        route={'DueDateReportScreen'}
        customTitle="Due Date Report"
        isLeftArrowBackShown
        showBottomSeparator={false}
        rightChildren={
          (dueDateReportData?.summary?.caseCount ?? 0) > 0 ? (
            <ExportCsv onSaveCsv={onSaveCsv} isSavingCsv={isSavingCsv} />
          ) : undefined
        }
      />
      <Box padding={space[4]} gap={space[2]}>
        <Row gap={space[1]}>
          {isLeader && (
            <>
              <TouchableOpacity
                disabled={isTeamSelected}
                onPress={() => {
                  setShowSelectedAgentList(true);
                }}>
                <FilterView>
                  <SmallBody
                    color={
                      isTeamSelected
                        ? colors.palette.fwdGrey[100]
                        : colors.onBackground
                    }>
                    {t('toolbar.selectedAgent')}
                  </SmallBody>
                  <Row gap={space[1]} alignItems="center">
                    <Label
                      fontWeight="medium"
                      color={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }>
                      {selectedAgent?.agentName ?? '--'}
                    </Label>
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={
                        isTeamSelected
                          ? colors.palette.fwdGrey[100]
                          : colors.onBackground
                      }
                    />
                  </Row>
                </FilterView>
              </TouchableOpacity>
              <FilterView customTeamView={isTeamSelected}>
                <Checkbox
                  value={isTeamSelected}
                  onChange={() => {
                    setIsTeamSelected(!isTeamSelected);
                    // If team is selected, set the login member as selected agent
                    setSelectedAgent(memberInfoList[0]);
                  }}
                />
                <Label>{t('toolbar.team')}</Label>
              </FilterView>
            </>
          )}
          <TouchableOpacity
            onPress={() => {
              setShowDateSelectModal(true);
            }}>
            <FilterView>
              <SmallBody>{t('dueDate')}</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">{`${formatDate(
                  dateRange.from,
                )} - ${formatDate(dateRange.to)}`}</Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setOpenPolicyHolderPanel(true);
            }}>
            <FilterView
              style={{
                backgroundColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[5]
                    : colors.background,
                borderColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100],
                borderWidth:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? 2
                    : 1,
              }}>
              <SmallBody>Certificate owner/number</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">
                  {policyInfo.policyHolderName ||
                    policyInfo.policyNumber ||
                    'All'}
                </Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
        </Row>
        <Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {t('totalResultNum', {
              totalResultNum: dueDateReportData?.summary.caseCount ?? 0,
            })}
          </Typography.Body>
        </Row>
      </Box>
      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ReportDataGrid
          darkMode={false}
          isLoading={isDueDateReportLoading}
          isError={isError}
          data={dueDateReportData?.data}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
        />
      </Animated.View>
    </>
  );
}
