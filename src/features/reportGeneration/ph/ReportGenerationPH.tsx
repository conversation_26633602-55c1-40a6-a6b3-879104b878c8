import React, { useContext, useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { Column, H6, H7, CubePictogramIcon, Row } from 'cube-ui-components';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types/navigation';
import { useTranslation } from 'react-i18next';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import {
  useGetLapseReport,
  useGetPremiumReceivedReport,
} from 'hooks/useReportGeneration';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { flattenTeamHierarchy } from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import SectionTab from './components/SectionTab';
import {
  endDayOfMonth,
  startDayOfMonth,
  ToolbarContext,
} from './ToolbarProvider';
import { Toolbar } from './components/Toolbar';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import styled from '@emotion/native';
import _ from 'lodash';
import useCheckIsLeader from '../hooks/useCheckIsLeader';

const ICON_SIZE = 40;

/**
 * For both mobile and tablet
 */
export default function ReportGenerationPH() {
  const { top } = useSafeAreaInsets();
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { isLeader } = useCheckIsLeader();

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { selectedAgent, updateSelectedAgent, checkedTeam, updateCheckedTeam } =
    useContext(ToolbarContext);

  /**
   *  Update default agentId to ToolbarContext
   */
  useEffect(() => {
    updateSelectedAgent(selfAgentCode ?? '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Getting team hierarchy data
   */
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  /**
   * Getting Lapsed policies report & Premium received report data for cases count and sales amount
   */
  const { data: lapseReportData, isLoading: isLPLoading } = useGetLapseReport({
    agentId: selectedAgent,
    status: 'LAPSED',
    team: checkedTeam,
  });
  const { data: premiumReceivedReportData, isLoading: isPRLoading } =
    useGetPremiumReceivedReport({
      agentId: selectedAgent,
      from: startDayOfMonth,
      to: endDayOfMonth,
      team: checkedTeam,
    });
  const isLoading = isLPLoading || isPRLoading;

  const BUSINESS_AND_CLIENTELE_REPORT_TABS_CONFIG = [
    {
      type: 'lapsedPolicies',
      title: t('lapsedPolicies'),
      icon: <CubePictogramIcon.InformationDocument size={ICON_SIZE} />,
      onPress: () => navigation.navigate('LapsedPoliciesReportScreen'),
      cases: lapseReportData?.summary?.caseCount ?? 0,
      sales: lapseReportData?.summary?.sum ?? 0,
    },
    {
      type: 'premiumReceived',
      title: t('premiumReceived'),
      icon: <CubePictogramIcon.Transaction size={ICON_SIZE} />,
      onPress: () => navigation.navigate('PremiumReceivedReportScreen'),
      cases: premiumReceivedReportData?.summary?.caseCount ?? 0,
      sales: premiumReceivedReportData?.summary?.sum ?? 0,
    },
    {
      type: 'creditCardExpiration',
      title: t('creditCardExpiration'),
      icon: <CubePictogramIcon.CreditCardOnHand size={ICON_SIZE} />,
      onPress: () => navigation.navigate('CreditCardExpirationReportScreen'),
    },
    {
      type: 'unsuccessfulAdaAca',
      title: t('unsuccessfulAdaAca'),
      icon: <CubePictogramIcon.MedicalReportError size={ICON_SIZE} />,
      onPress: () => navigation.navigate('UnsuccessfulAdaAcaScreen'),
    },
  ];

  const PERSONAL_REPORT_TABS_CONFIG = [
    {
      type: 'policyAnniversary',
      title: t('policy'),
      icon: <CubePictogramIcon.Forms size={ICON_SIZE} />,
      onPress: () => {
        navigation.navigate('PolicyAnniversaryListScreen');
      },
    },
    {
      type: 'commissionStatement',
      title: t('commission'),
      icon: <CubePictogramIcon.CashGrowth size={ICON_SIZE} />,
      onPress: () => {
        navigation.navigate('ReportGenerationListScreen', {
          reportStatement: 'commission',
        });
      },
    },
    {
      type: 'taxStatement',
      title: t('tax'),
      icon: <CubePictogramIcon.Bill size={ICON_SIZE} />,
      onPress: () => {
        navigation.navigate('ReportGenerationListScreen', {
          reportStatement: 'tax',
        });
      },
    },
    {
      type: 'persistency',
      title: t('persistency'),
      icon: <CubePictogramIcon.Percentage size={ICON_SIZE} />,
      onPress: () => {
        navigation.navigate('ReportGenerationListScreen', {
          reportStatement: 'persistency',
        });
      },
    },
  ];

  console.log(isTabletMode);

  return (
    <>
      {isTabletMode ? (
        <View style={{ paddingTop: top, backgroundColor: colors.background }} /> // Safe area view for tablet
      ) : (
        <ScreenHeader
          route={'ReportGeneration'}
          customTitle={t('reportGeneration')}
        />
      )}

      <ScrollView
        bounces={false}
        showsVerticalScrollIndicator={false}
        style={{
          paddingHorizontal: isTabletMode ? space[8] : space[4],
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          gap: space[4],
          paddingBottom: isTabletMode ? 0 : space[36], // Screen bottom space for mobile
        }}>
        {isTabletMode && (
          <H6
            fontWeight="bold"
            children={t('reportGeneration')}
            style={{
              paddingTop: space[8],
              paddingBottom: isLeader && !hasNoDownline ? 0 : space[6],
            }}
          />
        )}

        <Column>
          <View
            style={
              isTabletMode && [
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                },
              ]
            }>
            <H7
              fontWeight="bold"
              children={t('type.businessAndClienteleReport')}
              style={{
                paddingTop: isTabletMode ? 0 : space[4],
                paddingBottom: isLeader && !hasNoDownline ? 0 : space[4],
              }}
            />

            {isLeader && !hasNoDownline && (
              <Toolbar.MainContainer>
                <Row gap={space[2]}>
                  <Column style={[!isTabletMode && { flex: 1 }]}>
                    <Toolbar.SelectAgent
                      data={memberInfoList}
                      value={selectedAgent}
                      disabled={isTeamLoading || checkedTeam}
                      onChange={(item: string) => updateSelectedAgent(item)}
                    />
                  </Column>
                  <Toolbar.Team
                    checked={checkedTeam}
                    onPress={() => {
                      updateCheckedTeam(!checkedTeam);
                      updateSelectedAgent(selfAgentCode as string);
                    }}
                  />
                </Row>
              </Toolbar.MainContainer>
            )}
          </View>

          <Container>
            {BUSINESS_AND_CLIENTELE_REPORT_TABS_CONFIG.map(
              ({ type, title, icon, onPress, cases, sales }) => {
                const showCaseAndSales =
                  type === 'lapsedPolicies' || type === 'premiumReceived';
                return (
                  <SectionTab
                    key={type}
                    type={type}
                    icon={icon}
                    title={title}
                    onPress={() => onPress()}
                    showCaseAndSales={showCaseAndSales}
                    cases={cases}
                    sales={sales}
                    isLoading={isLoading}
                  />
                );
              },
            )}
          </Container>
        </Column>

        <Column>
          <H7
            fontWeight="bold"
            children={t('type.myPersonalReport')}
            style={{ paddingVertical: space[4] }}
          />
          <Container>
            {PERSONAL_REPORT_TABS_CONFIG.map(
              ({ type, title, icon, onPress }) => {
                return (
                  <SectionTab
                    key={type}
                    icon={icon}
                    title={title}
                    onPress={() => onPress()}
                  />
                );
              },
            )}
          </Container>
        </Column>
      </ScrollView>
    </>
  );
}

const Container = styled.View(({ theme }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    flexDirection: isTabletMode ? 'row' : 'column',
    flexWrap: isTabletMode ? 'wrap' : 'nowrap',
    gap: theme.space[3],
  };
});
