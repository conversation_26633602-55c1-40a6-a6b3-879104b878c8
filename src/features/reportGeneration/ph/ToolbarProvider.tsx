import {
  addDays,
  compareDesc,
  endOfMonth,
  format,
  startOfMonth,
  subDays,
  subMonths,
} from 'date-fns';
import { createContext, useState } from 'react';
import {
  Status,
  Duration,
  DatePeriodType,
  PolicyInfoSearch,
  DatePeriodSearch,
} from 'types/report';
import * as ScreenOrientation from 'expo-screen-orientation';
import { Week } from './phone/PolicyAnniversaryListScreen';

/**
 * PhStackNavigator subscribe to the context - allowing all (report generation) screens have access to the context
 * Migrate to use Zustand if other countries have the same use case
 */

/**
 * Screen orientation functions - For 'Full table' view
 */
export const startLandscape = async () => {
  await ScreenOrientation.unlockAsync();
  await ScreenOrientation.lockAsync(
    ScreenOrientation.OrientationLock.LANDSCAPE_LEFT,
  );
};
export const endLandscape = async () => {
  await ScreenOrientation.unlockAsync();
  await ScreenOrientation.lockAsync(
    ScreenOrientation.OrientationLock.PORTRAIT_UP,
  );
};

/**
 * Dates
 *
 * Stored in ToolbarContext format - 'yyyy-MM-dd'
 * API query params format - 'yyyy-MM-dd'
 * Date picker value format - Date
 */
export const today = format(new Date(), 'yyyy-MM-dd');
export const sixDaysFromToday = format(addDays(new Date(), 6), 'yyyy-MM-dd');
export const lastThirtyDays = format(subDays(new Date(), 29), 'yyyy-MM-dd'); // 29 as today is included
export const lastTwoMonths = format(subMonths(new Date(), 2), 'yyyy-MM-dd');
export const lastSixMonths = format(subDays(new Date(), 179), 'yyyy-MM-dd'); // 179 as today is included
export const nextSixMonths = format(addDays(new Date(), 179), 'yyyy-MM-dd'); // 179 as today is included
export const startDayOfMonth = format(startOfMonth(new Date()), 'yyyy-MM-dd');
export const endDayOfMonth = format(endOfMonth(new Date()), 'yyyy-MM-dd');

export const checkCustomDuration = (from: string, to: string) => {
  if (!from || !to) return false;
  // Check if the 'from' and 'to' date with correct order
  const isValid = compareDesc(new Date(from), new Date(to)) !== -1;
  return isValid;
};

/**
 * Context
 */
export const ToolbarContext = createContext<{
  landscape: boolean;
  updateLandscape: (landscape: boolean) => void;
  // ------------
  showToolBar: boolean;
  updateShowToolbar: (show: boolean) => void;
  // ------------
  selectedAgent: string;
  updateSelectedAgent: (id: string) => void;
  checkedTeam: boolean;
  updateCheckedTeam: (checked: boolean) => void;
  /**
   * Lapsed policies
   */
  status: Status;
  updateStatus: (status: Status) => void;
  lapseReportPolicyInfo: PolicyInfoSearch;
  updateLapseReportPolicyInfo: (
    lapseReportPolicyInfo: PolicyInfoSearch,
  ) => void;
  /**
   * Premium received
   */
  transactionDate: { duration: Duration; from: string; to: string };
  updateTransactionDate: (date: {
    duration: Duration;
    from: string;
    to: string;
  }) => void;
  premiumReceivedPolicyInfo: PolicyInfoSearch;
  updatePremiumReceivedPolicyInfo: (
    premiumReceivedPolicyInfo: PolicyInfoSearch,
  ) => void;
  /**
   * Credit card expiration
   */
  creditCardExpiationDatePeriod: {
    datePeriodType: DatePeriodType;
    from: string;
    to: string;
  };
  updateCreditCardExpiationDatePeriod: (period: {
    datePeriodType: DatePeriodType;
    from: string;
    to: string;
  }) => void;
  creditCardExpiationPolicyInfo: PolicyInfoSearch;
  updateCreditCardExpiationPolicyInfo: (
    creditCardExpiationPolicyInfo: PolicyInfoSearch,
  ) => void;
  /**
   * Unsuccessful ADA/ACA
   */
  unsuccessfulAdaAcaDatePeriod: {
    datePeriodType: DatePeriodType;
    from: string;
    to: string;
  };
  updateUnsuccessfulAdaAcaDatePeriod: (period: {
    datePeriodType: DatePeriodType;
    from: string;
    to: string;
  }) => void;
  unsuccessfulAdaAcaPolicyInfo: PolicyInfoSearch;
  updateUnsuccessfulAdaAcaPolicyInfo: (
    premiumReceivedPolicyInfo: PolicyInfoSearch,
  ) => void;
  /**
   * Policy anniversary
   */
  period: { duration: Week; from: string; to: string };
  updatePeriod: (period: { duration: Week; from: string; to: string }) => void;
  /**
   * Rest all context values
   */
  resetContextValues: () => void;
}>({
  landscape: false,
  updateLandscape: () => null,
  // ------------
  showToolBar: true,
  updateShowToolbar: () => null,
  // ------------
  selectedAgent: '',
  updateSelectedAgent: () => null,
  checkedTeam: false,
  updateCheckedTeam: () => null,
  /**
   * Lapsed policies
   */
  status: 'LAPSED', // default 'LAPSED'
  updateStatus: () => null,
  lapseReportPolicyInfo: {
    searchType: 'all', // default searchType 'all'
    focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
    policyHolderName: '',
    policyNumber: '',
  },
  updateLapseReportPolicyInfo: () => null,
  /**
   * Premium received
   */
  transactionDate: { duration: 'thisMonth', from: '', to: '' }, // default 'thisMonth'
  updateTransactionDate: () => null,
  premiumReceivedPolicyInfo: {
    searchType: 'all', // default searchType 'all'
    focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
    policyHolderName: '',
    policyNumber: '',
  },
  updatePremiumReceivedPolicyInfo: () => null,
  /**
   * Credit card expiration
   */
  creditCardExpiationDatePeriod: {
    datePeriodType: 'ISSUEDATE', // default searchType 'ISSUEDATE'
    from: '',
    to: '',
  },
  updateCreditCardExpiationDatePeriod: () => null,
  creditCardExpiationPolicyInfo: {
    searchType: 'all', // default searchType 'all'
    focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
    policyHolderName: '',
    policyNumber: '',
  },
  updateCreditCardExpiationPolicyInfo: () => null,
  /**
   * Unsuccessful ADA/ACA
   */
  unsuccessfulAdaAcaDatePeriod: {
    datePeriodType: 'ISSUEDATE', // default searchType 'ISSUEDATE'
    from: '',
    to: '',
  },
  updateUnsuccessfulAdaAcaDatePeriod: () => null,
  unsuccessfulAdaAcaPolicyInfo: {
    searchType: 'all', // default searchType 'all'
    focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
    policyHolderName: '',
    policyNumber: '',
  },
  updateUnsuccessfulAdaAcaPolicyInfo: () => null,
  /**
   * Policy anniversary
   */
  period: { duration: 'week1', from: '', to: '' }, // default 'week1'
  updatePeriod: () => null,
  /**
   * Reest all context values
   */
  resetContextValues: () => null,
});

const ToolbarProvider = ({ children }: { children: React.ReactNode }) => {
  // Screen orientation
  const [landscape, setLandscape] = useState<boolean>(false);
  const updateLandscape = (landscape: boolean) => setLandscape(landscape);

  // Show toolbar
  const [showToolBar, setShowToolbar] = useState<boolean>(true);
  const updateShowToolbar = (show: boolean) => setShowToolbar(show);

  // Selected agent
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const updateSelectedAgent = (id: string) => setSelectedAgent(id);

  // Team
  const [checkedTeam, setCheckedTeam] = useState<boolean>(false);
  const updateCheckedTeam = (checked: boolean) => setCheckedTeam(checked);

  // Lapsed policies - Status
  const [status, setStatus] = useState<Status>('LAPSED'); // default 'LAPSED'
  const updateStatus = (status: Status) => setStatus(status);

  // Lapsed policies - Policy holder/number
  const [lapseReportPolicyInfo, setLapseReportPolicyInfo] =
    useState<PolicyInfoSearch>({
      searchType: 'all', // default searchType 'all'
      focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
      policyHolderName: '',
      policyNumber: '',
    });
  const updateLapseReportPolicyInfo = (
    lapseReportPolicyInfo: PolicyInfoSearch,
  ) => setLapseReportPolicyInfo(lapseReportPolicyInfo);

  // Premium received - Transaction date
  const [transactionDate, setTransactionDate] = useState<{
    duration: Duration;
    from: string;
    to: string;
  }>({
    duration: 'thisMonth', // default 'thisMonth'
    from: startDayOfMonth,
    to: endDayOfMonth,
  });
  const updateTransactionDate = (date: {
    duration: Duration;
    from: string;
    to: string;
  }) => setTransactionDate(date);

  // Premium received - Policy holder/number
  const [premiumReceivedPolicyInfo, setPremiumReceivedPolicyInfo] =
    useState<PolicyInfoSearch>({
      searchType: 'all', // default searchType 'all'
      focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
      policyHolderName: '',
      policyNumber: '',
    });
  const updatePremiumReceivedPolicyInfo = (
    premiumReceivedPolicyInfo: PolicyInfoSearch,
  ) => setPremiumReceivedPolicyInfo(premiumReceivedPolicyInfo);

  // Credit card expiration - Date period
  const [creditCardExpiationDatePeriod, setCreditCardExpiationDatePeriod] =
    useState<DatePeriodSearch>({
      datePeriodType: 'ISSUEDATE',
      from: startDayOfMonth,
      to: endDayOfMonth,
    });
  const updateCreditCardExpiationDatePeriod = (period: DatePeriodSearch) =>
    setCreditCardExpiationDatePeriod(period);

  // Credit card expiration - Policy holder/number
  const [creditCardExpiationPolicyInfo, setCreditCardExpiationPolicyInfo] =
    useState<PolicyInfoSearch>({
      searchType: 'all', // default searchType 'all'
      focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
      policyHolderName: '',
      policyNumber: '',
    });
  const updateCreditCardExpiationPolicyInfo = (
    creditCardExpiationPolicyInfo: PolicyInfoSearch,
  ) => setCreditCardExpiationPolicyInfo(creditCardExpiationPolicyInfo);

  // Unsuccessful ADA/ACA - Date period
  const [unsuccessfulAdaAcaDatePeriod, setUnsuccessfulAdaAcaDatePeriod] =
    useState<DatePeriodSearch>({
      datePeriodType: 'ISSUEDATE',
      from: lastTwoMonths,
      to: today,
    });
  const updateUnsuccessfulAdaAcaDatePeriod = (period: DatePeriodSearch) =>
    setUnsuccessfulAdaAcaDatePeriod(period);

  // Unsuccessful ADA/ACA - Policy holder/number
  const [unsuccessfulAdaAcaPolicyInfo, setUnsuccessfulAdaAcaPolicyInfo] =
    useState<PolicyInfoSearch>({
      searchType: 'all', // default searchType 'all'
      focusedChip: 'policyHolderName', // default focusedChip 'policyHolderName'
      policyHolderName: '',
      policyNumber: '',
    });
  const updateUnsuccessfulAdaAcaPolicyInfo = (
    unsuccessfulAdaAcaPolicyInfo: PolicyInfoSearch,
  ) => setUnsuccessfulAdaAcaPolicyInfo(unsuccessfulAdaAcaPolicyInfo);

  // Policy anniversary - Policy anniversary period
  const [period, setPeriod] = useState<{
    duration: Week;
    from: string;
    to: string;
  }>({
    duration: 'week1',
    from: today,
    to: sixDaysFromToday,
  });
  const updatePeriod = (period: { duration: Week; from: string; to: string }) =>
    setPeriod(period);

  // Reset all context values
  const resetContextValues = () => {
    setLandscape(false);
    setShowToolbar(true);
    setSelectedAgent('');
    setCheckedTeam(false);
    setStatus('LAPSED');
    setLapseReportPolicyInfo({
      searchType: 'all',
      focusedChip: 'policyHolderName',
      policyHolderName: '',
      policyNumber: '',
    });
    setTransactionDate({
      duration: 'thisMonth',
      from: startDayOfMonth,
      to: endDayOfMonth,
    });
    setPremiumReceivedPolicyInfo({
      searchType: 'all',
      focusedChip: 'policyHolderName',
      policyHolderName: '',
      policyNumber: '',
    });
    setCreditCardExpiationDatePeriod({
      datePeriodType: 'ISSUEDATE',
      from: startDayOfMonth,
      to: endDayOfMonth,
    });
    setCreditCardExpiationPolicyInfo({
      searchType: 'all',
      focusedChip: 'policyHolderName',
      policyHolderName: '',
      policyNumber: '',
    });
    setUnsuccessfulAdaAcaDatePeriod({
      datePeriodType: 'ISSUEDATE',
      from: lastTwoMonths,
      to: today,
    });
    setUnsuccessfulAdaAcaPolicyInfo({
      searchType: 'all',
      focusedChip: 'policyHolderName',
      policyHolderName: '',
      policyNumber: '',
    });
    setPeriod({
      duration: 'week1',
      from: today,
      to: sixDaysFromToday,
    });
  };

  const contextValue = {
    landscape,
    updateLandscape,
    // ------------
    showToolBar,
    updateShowToolbar,
    // ------------
    selectedAgent,
    updateSelectedAgent,
    checkedTeam,
    updateCheckedTeam,
    /**
     * Lapsed policies
     */
    status,
    updateStatus,
    lapseReportPolicyInfo,
    updateLapseReportPolicyInfo,
    /**
     * Premium received
     */
    transactionDate,
    updateTransactionDate,
    premiumReceivedPolicyInfo,
    updatePremiumReceivedPolicyInfo,
    /**
     * Credit card expiration
     */
    creditCardExpiationDatePeriod,
    updateCreditCardExpiationDatePeriod,
    creditCardExpiationPolicyInfo,
    updateCreditCardExpiationPolicyInfo,
    /**
     * Unsuccessful ADA/ACA
     */
    unsuccessfulAdaAcaDatePeriod,
    updateUnsuccessfulAdaAcaDatePeriod,
    unsuccessfulAdaAcaPolicyInfo,
    updateUnsuccessfulAdaAcaPolicyInfo,
    /**
     * Policy anniversary
     */
    period,
    updatePeriod,
    /**
     * Rest all context values
     */
    resetContextValues,
  };

  return (
    <ToolbarContext.Provider value={contextValue}>
      {children}
    </ToolbarContext.Provider>
  );
};

export default ToolbarProvider;
