import { useTheme } from '@emotion/react';
import { Column, Icon, LargeLabel, Row } from 'cube-ui-components';
import InformationModal from 'features/reportGeneration/ph/components/InformationModal';
import { flattenTeamHierarchy } from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { useGetLapseReport } from 'hooks/useReportGeneration';
import _ from 'lodash';
import PhoneScreenHeader from 'navigation/components/ScreenHeader/phone';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import React, { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import Animated, { FadeOut, LinearTransition } from 'react-native-reanimated';
import PolicyInfoActionPanel from '../components/ActionPanel/PolicyInfoActionPanel';
import StatusActionPanel, {
  STATUS_CONFIG,
} from './components/ActionPanel/StatusActionPanel';
import FloatingActionButton, {
  CloseButton,
} from './phone/components/FloatingActionButton';
import { policyInfoHandler, Toolbar } from './components/Toolbar';
import { endLandscape, ToolbarContext } from './ToolbarProvider';
import ReportDataGrid from './components/DataGrid/ReportDataGrid';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from './components/DataGrid/tablesConfig/lapseReport';
import useCheckIsLeader from '../hooks/useCheckIsLeader';
import Autocomplete from 'components/Autocomplete';
import { Status } from 'types/report';
import {
  CHIP_CONFIG,
  DEFAULT_POLICY_INFO,
  SEARCH_TYPE_CONFIG,
} from 'features/reportGeneration/components/ActionPanel/config/policyInfo';

/**
 * For both mobile and tablet
 */
export default function LapsedPoliciesReportScreen() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { isLeader } = useCheckIsLeader();

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  const {
    landscape,
    updateLandscape,
    showToolBar,
    updateShowToolbar,
    selectedAgent,
    updateSelectedAgent,
    checkedTeam,
    updateCheckedTeam,
    status,
    updateStatus,
    lapseReportPolicyInfo: policyInfo,
    updateLapseReportPolicyInfo: updatePolicyInfo,
  } = useContext(ToolbarContext);

  const [infoModalVisible, setInfoModalVisible] = useState(false);
  const [openStatusPanel, setOpenStatusPanel] = useState(false);
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);

  const { data, isLoading, isError } = useGetLapseReport({
    agentId: selectedAgent,
    status,
    lapseReportPolicyInfo: policyInfo,
    team: checkedTeam,
  });
  const reportItemList = data?.data || [];

  useEffect(() => {
    updateShowToolbar(true);

    return () => {
      if (!isTabletMode) {
        endLandscape();
        updateLandscape(false);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {!landscape && (
        <>
          {isTabletMode ? (
            <TabletScreenHeader
              route={'LapsedPoliciesReportScreen'}
              customTitle={t('lapsedReport')}
              isLeftArrowBackShown
              showBottomSeparator={false}
              rightChildren={
                <Row alignItems="center">
                  <TouchableOpacity
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: space[2],
                    }}
                    onPress={() => setInfoModalVisible(true)}>
                    <Icon.InfoCircle fill={colors.secondary} />
                    <LargeLabel fontWeight="bold" children={'info'} />
                  </TouchableOpacity>
                </Row>
              }
            />
          ) : (
            <PhoneScreenHeader
              route={'LapsedPoliciesReportScreen'}
              customTitle={t('lapsedReport')}
              isLeftArrowBackShown
              rightChildren={
                <Row gap={space[4]}>
                  {/* <TouchableOpacity onPress={() => console.log('Download')}>
                    <Icon.Download fill={colors.secondary} />
                  </TouchableOpacity> */}
                  <TouchableOpacity onPress={() => setInfoModalVisible(true)}>
                    <Icon.InfoCircle fill={colors.secondary} />
                  </TouchableOpacity>
                </Row>
              }
            />
          )}

          {showToolBar && (
            <Animated.View layout={LinearTransition} exiting={FadeOut}>
              <Toolbar.MainContainer>
                <Toolbar.ScrollableContainer>
                  {isLeader && !hasNoDownline && (
                    <>
                      <Toolbar.SelectAgent
                        data={memberInfoList}
                        value={selectedAgent}
                        disabled={checkedTeam || isTeamLoading}
                        onChange={(item: string) => updateSelectedAgent(item)}
                      />
                      <Toolbar.Team
                        checked={checkedTeam}
                        onPress={() => {
                          updateCheckedTeam(!checkedTeam);
                          updateSelectedAgent(selfAgentCode as string);
                        }}
                      />
                    </>
                  )}
                  {isTabletMode ? (
                    <Autocomplete
                      renderInput={() => (
                        <Column
                          flex={1}
                          // Fix column height for fixing UI issue when rendering Autocomplete dropdown with Selected agent button and Team button
                          h={isLeader && !hasNoDownline ? 44 : 'auto'}>
                          <Toolbar.Status
                            status={status}
                            onPress={() => setOpenStatusPanel(true)}
                          />
                        </Column>
                      )}
                      value={status}
                      data={STATUS_CONFIG as any}
                      getItemLabel={(item: any) => t(item?.label)}
                      getItemValue={(item: any) => item?.value}
                      onChange={value => updateStatus(value as Status)}
                      style={{ flex: 1 }}
                      modalStyle={{ width: 220, marginLeft: 2 }}
                    />
                  ) : (
                    <Toolbar.Status
                      status={status}
                      onPress={() => setOpenStatusPanel(true)}
                    />
                  )}

                  <Toolbar.PolicyInfo
                    policyHolder={policyInfoHandler(policyInfo)}
                    onPress={() => setOpenPolicyHolderPanel(true)}
                  />
                </Toolbar.ScrollableContainer>

                <Toolbar.DataGridInfo count={reportItemList?.length} />
              </Toolbar.MainContainer>
            </Animated.View>
          )}
        </>
      )}

      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ReportDataGrid
          darkMode={!isTabletMode}
          isLoading={isLoading}
          isError={isError}
          data={reportItemList}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
        />
      </Animated.View>

      {/* ----- Floating UI ----- */}

      <InformationModal
        fullVersion
        visible={infoModalVisible}
        setIsVisible={setInfoModalVisible}
      />

      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
        //
        defaultPolicyInfo={DEFAULT_POLICY_INFO}
        searchTypeConfig={SEARCH_TYPE_CONFIG}
        chipConfig={CHIP_CONFIG}
        //
        title={t('actionPanel.title.searchForPolicyInfo')}
        searchTip={t('actionPanel.subtitle.searchTip')}
        policyHolderNameError={'Invalid input'}
        policyNumberHint={t('actionPanel.hint.policyNumber')}
        policyNumberError={'Invalid input'}
        //
        primaryLabel={t('actionPanel.search')}
        secondaryLabel={t('actionPanel.reset')}
      />

      {/* ----- Mobile only floating UI ----- */}

      {landscape && !isTabletMode && <CloseButton />}

      {!_.isEmpty(data) && !landscape && !isTabletMode && (
        <FloatingActionButton fullTable />
      )}

      {!isTabletMode && (
        <StatusActionPanel
          visible={openStatusPanel}
          handleClose={() => setOpenStatusPanel(false)}
          contextValue={status}
          updateContextValue={updateStatus}
        />
      )}
    </>
  );
}
