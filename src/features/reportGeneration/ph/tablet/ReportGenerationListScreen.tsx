import React, { useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon, LargeLabel, Row } from 'cube-ui-components';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import InformationModal from 'features/reportGeneration/ph/components/InformationModal';
import { useGetReportListByReportStatement } from 'hooks/useReportGeneration';
import ReportList from './components/ReportList';

/**
 * Commission statement, Tax statement, Persistency
 */
export default function ReportGenerationListScreen() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');

  const route =
    useRoute<RouteProp<RootStackParamList, 'ReportGenerationListScreen'>>();
  const reportStatement = route?.params?.reportStatement;

  const [infoModalVisible, setInfoModalVisible] = useState<boolean>(false);

  const { data, isLoading, isError } =
    useGetReportListByReportStatement(reportStatement);
  const listData = data?.documentList ?? [];

  return (
    <>
      <ScreenHeader
        route={'ReportGenerationListScreen'}
        customTitle={t(reportStatement)}
        isLeftArrowBackShown
        showBottomSeparator={false}
        rightChildren={
          <Row alignItems="center">
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: space[2],
              }}
              onPress={() => setInfoModalVisible(true)}>
              <Icon.InfoCircle fill={colors.secondary} />
              <LargeLabel fontWeight="bold" children={'info'} />
            </TouchableOpacity>
          </Row>
        }
      />

      <View style={{ flex: 1 }}>
        <ReportList
          isLoading={isLoading}
          isError={isError}
          listData={listData}
          reportStatement={reportStatement}
        />
      </View>

      {/* ----- Floating UI ----- */}

      <InformationModal
        visible={infoModalVisible}
        setIsVisible={setInfoModalVisible}
      />
    </>
  );
}
