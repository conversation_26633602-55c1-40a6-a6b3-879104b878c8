import React, { useContext, useEffect, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import {
  // addToast,
  // LoadingIndicator,
  Icon,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import InformationModal from 'features/reportGeneration/ph/components/InformationModal';
import PolicyAnniversaryList from '../tablet/components/PolicyAnniversaryList';
import { Toolbar } from '../components/Toolbar';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ToolbarContext } from '../ToolbarProvider';
import { useGetPolicyAnniversaryReport } from 'hooks/useReportGeneration';
import Animated, { FadeOut, LinearTransition } from 'react-native-reanimated';
import PolicyAnniversaryPeriodActionPanel from '../components/ActionPanel/PolicyAnniversaryPeriodActionPanel';

type WeekNumber = 0 | 1 | 2 | 3 | 4;
export type Week = `week${WeekNumber}`;

export default function PolicyAnniversaryListScreen() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');

  const { showToolBar, updateShowToolbar, period, updatePeriod } =
    useContext(ToolbarContext);

  const [infoModalVisible, setInfoModalVisible] = useState(false);
  const [openPeriodPanel, setOpenPeriodPanel] = useState(false);

  const { data, isLoading, isError } = useGetPolicyAnniversaryReport({
    from: period?.from,
    to: period?.to,
  });

  const duration = `${dateFormatUtil(period?.from)} - ${dateFormatUtil(
    period?.to,
  )}`;

  // const [isDownloadComplete, setIsDownloadComplete] = useState(false);

  // const onPressDownload = async (item: any) => {
  //   addToast([
  //     {
  //       message: t(`download.downloading`),
  //       IconLeft: <LoadingIndicator />,
  //     },
  //   ]);
  //   setTimeout(() => {
  //     setIsDownloadComplete(true);
  //   }, 500);
  // };

  // useEffect(() => {
  //   isDownloadComplete &&
  //     addToast([
  //       {
  //         message: t(`download.downloadSuccess`),
  //         IconLeft: <Icon.Tick size={sizes[6]} />,
  //       },
  //     ]);
  //   setIsDownloadComplete(false);
  // }, [isDownloadComplete]);

  useEffect(() => {
    updateShowToolbar(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ScreenHeader
        route={'PolicyAnniversaryListScreen'}
        customTitle={t('policy')}
        isLeftArrowBackShown
        showBottomSeparator={false}
        // rightChildren={
        //   <Row alignItems="center">
        //     <TouchableOpacity
        //       style={{
        //         flexDirection: 'row',
        //         alignItems: 'center',
        //         gap: space[2],
        //       }}
        //       onPress={() => setInfoModalVisible(true)}>
        //       <Icon.InfoCircle fill={colors.secondary} />
        //       <LargeLabel fontWeight="bold" children={'info'} />
        //     </TouchableOpacity>
        //   </Row>
        // }
      />

      {showToolBar && (
        <Animated.View layout={LinearTransition} exiting={FadeOut}>
          <Toolbar.MainContainer>
            <Toolbar.ScrollableContainer>
              <Toolbar.Date
                title={t('toolbar.policyAnniversaryPeriod')}
                duration={duration}
                onPress={() => setOpenPeriodPanel(true)}
              />
            </Toolbar.ScrollableContainer>

            <Toolbar.DataGridInfo
              count={data?.length}
              showResultCount={true}
              showMaxCountReminder={false}
              showSeparator={false}
            />
          </Toolbar.MainContainer>
        </Animated.View>
      )}

      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <PolicyAnniversaryList
          isLoading={isLoading}
          isError={isError}
          listData={data}
        />
      </Animated.View>

      {/* ----- Floating UI ----- */}

      <InformationModal
        visible={infoModalVisible}
        setIsVisible={setInfoModalVisible}
      />

      <PolicyAnniversaryPeriodActionPanel
        visible={openPeriodPanel}
        handleClose={() => setOpenPeriodPanel(false)}
        contextValue={period}
        updateContextValue={updatePeriod}
      />
    </>
  );
}
