import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Body, Column, Icon, Label, LargeBody, Row } from 'cube-ui-components';
import _ from 'lodash';
import React, { useContext, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, TouchableOpacity } from 'react-native';
import { RootStackParamList } from 'types';
import { PolicyAnniversaryItem } from 'types/report';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ToolbarContext } from '../../ToolbarProvider';
import { CELL_HEIGHT } from '../../components/DataGrid/utils';
import { Placeholder } from '../../components/PlaceHolder';
import { SolidArrow } from 'features/reportGeneration/assets/SolidArrowSVG';

const HEADER_HEIGHT = 48;

const CONTAINER_WIDTH = {
  DUMMY: '10%', // There will be 2 dummy columns
  NUMBER: '20%',
  HOLDER_NAME: '46%',
  DATE: '14%',
} as const;

export default function PolicyAnniversaryList({
  isLoading,
  isError,
  listData,
}: {
  isLoading: boolean;
  isError: boolean;
  listData?: PolicyAnniversaryItem[];
}) {
  const { updateShowToolbar } = useContext(ToolbarContext);

  const prevScrollY = useRef(0);

  // Sort list data by policy anniversary date
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const handleSortPress = () => {
    !sortDirection
      ? setSortDirection('asc')
      : setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  };

  const sortedListData = useMemo(() => {
    return listData?.sort((a, b) =>
      sortDirection === 'desc'
        ? new Date(b?.policyDate).getTime() - new Date(a?.policyDate).getTime()
        : new Date(a?.policyDate).getTime() - new Date(b?.policyDate).getTime(),
    );
  }, [listData, sortDirection]);

  if (isLoading) {
    return (
      <Column flex={1}>
        <TableHeader disablePress={true} showSortIcon={false} />
        <Placeholder.Loading />
      </Column>
    );
  }

  if (isError) {
    return (
      <Column flex={1}>
        <TableHeader disablePress={true} showSortIcon={false} />
        <Placeholder.LoadingError />
      </Column>
    );
  }

  if (_.isEmpty(listData)) {
    return <Placeholder.EmptyRecord />;
  }

  return (
    <FlatList
      data={sortedListData}
      stickyHeaderIndices={[0]}
      ListHeaderComponent={() => (
        <TableHeader
          handleSortPress={handleSortPress}
          sortDirection={sortDirection}
        />
      )}
      keyExtractor={(item, index) => `listItem_${item?.policyNumber}_${index}`}
      renderItem={({ item, index }) => (
        <TableItem item={item} rowIndex={index} />
      )}
      ItemSeparatorComponent={Separator}
      bounces={false}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      onScroll={event => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < prevScrollY.current || offsetY <= 0) {
          updateShowToolbar(true);
        } else if (offsetY > prevScrollY.current) {
          updateShowToolbar(false);
        }
        prevScrollY.current = offsetY;
      }}
      ListFooterComponent={
        <>
          <Separator />
          <Row h={CELL_HEIGHT * 2} />
        </>
      }
    />
  );
}

function TableHeader({
  disablePress, // props from parent when isLoading/ isError
  showSortIcon, // props from parent when isLoading/ isError
  handleSortPress,
  sortDirection,
}: {
  disablePress?: boolean;
  showSortIcon?: boolean;
  handleSortPress?: () => void;
  sortDirection?: 'asc' | 'desc' | null;
}) {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();

  const HEADER_CONFIG = [
    {
      type: 'policyNumber',
      title: t('header.policyNumber'),
      disablePress: disablePress ?? true,
      showSortIcon: showSortIcon ?? false,
      width: CONTAINER_WIDTH.NUMBER,
    },
    {
      type: 'policyHolderName',
      title: t('header.policyHolderName'),
      disablePress: disablePress ?? true,
      showSortIcon: showSortIcon ?? false,
      width: CONTAINER_WIDTH.HOLDER_NAME,
    },
    {
      type: 'policyAnniversaryDate',
      title: 'Policy anniversary date',
      disablePress: disablePress ?? false,
      showSortIcon: showSortIcon ?? true,
      width: CONTAINER_WIDTH.DATE,
    },
  ] as const;

  return (
    <Row bgColor={colors.primary}>
      <Column width={CONTAINER_WIDTH.DUMMY} />
      {HEADER_CONFIG.map(
        ({ type, title, disablePress, showSortIcon, width }) => {
          return (
            <TouchableOpacity
              key={type}
              disabled={disablePress}
              onPress={handleSortPress}
              style={{
                flexDirection: 'row',
                width: width,
                height: HEADER_HEIGHT,
                backgroundColor: colors.primary,
                alignItems: 'center',
                gap: space[1],
              }}>
              <Label children={title} color={colors.onPrimary} />

              {showSortIcon &&
                (!sortDirection ? (
                  <SolidArrow.UpLight fill={colors.palette.fwdOrange[50]} />
                ) : sortDirection === 'asc' ? (
                  <SolidArrow.UpLight />
                ) : (
                  <SolidArrow.DownLight />
                ))}
            </TouchableOpacity>
          );
        },
      )}
      <Column width={CONTAINER_WIDTH.DUMMY} />
    </Row>
  );
}

function TableItem({
  item,
  rowIndex,
}: {
  item: PolicyAnniversaryItem;
  rowIndex: number;
}) {
  const { space, sizes, colors } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  // TODO: Update navigation function
  const onPressItem = (item: PolicyAnniversaryItem) => {
    navigation.navigate('PoliciesPOS', {
      screen: 'POSDetail',
      params: {
        type: 'policy',
        policyId: item?.policyNumber,
        status: 'NBPending',
      },
    });
  };

  return (
    <TouchableOpacity onPress={() => onPressItem(item)}>
      <Row
        h={CELL_HEIGHT}
        alignItems="center"
        bgColor={
          rowIndex % 2 === 0 ? colors.background : colors.palette.fwdGrey[20]
        }>
        <Column width={CONTAINER_WIDTH.DUMMY} />

        <Column width={CONTAINER_WIDTH.NUMBER}>
          <Row alignItems="center" gap={space[5]}>
            <Label
              children={String(rowIndex + 1)}
              color={colors.palette.fwdDarkGreen[50]}
            />
            <Row gap={space[1]} alignItems="center">
              <Icon.Document size={sizes[4]} />
              <Body
                children={item?.policyNumber ?? '--'}
                color={colors.primary}
              />
            </Row>
          </Row>
        </Column>

        <Column width={CONTAINER_WIDTH.HOLDER_NAME}>
          <LargeBody
            fontWeight="bold"
            children={
              policyHolderHandler({
                firstName: item?.firstName,
                middleName: item?.middleName,
                lastName: item?.lastName,
              }) ?? '--'
            }
            style={{ paddingRight: space[4] }} // avoid text overflow
            numberOfLines={1}
            ellipsizeMode="tail"
          />
        </Column>

        <Column width={CONTAINER_WIDTH.DATE}>
          <Row alignItems="center" gap={space[15]}>
            <LargeBody
              children={dateFormatUtil(item?.policyDate) ?? '--'}
              color={colors.palette.fwdDarkGreen[100]}
            />
            <Icon.ChevronRight fill={colors.primary} />
          </Row>
        </Column>

        <Column width={CONTAINER_WIDTH.DUMMY} />
      </Row>
    </TouchableOpacity>
  );
}

function Separator() {
  const { colors } = useTheme();
  return <Column height={1} backgroundColor={colors.palette.fwdGrey[100]} />;
}

const policyHolderHandler = (policyHolder: {
  firstName?: string;
  middleName?: string;
  lastName?: string;
}) => {
  const { firstName, middleName, lastName } = policyHolder;

  const trimmedMiddleName = middleName?.trim(); // Handle middleName with spaces
  const isMiddleNameEmpty = _.isEmpty(trimmedMiddleName);

  if (!firstName) return lastName;
  if (firstName && !middleName && !lastName) return firstName;
  if (firstName && isMiddleNameEmpty && lastName)
    return `${firstName} ${lastName}`;
  if (firstName && !isMiddleNameEmpty && lastName)
    return `${firstName} ${trimmedMiddleName} ${lastName}`;
  return '--';
};
