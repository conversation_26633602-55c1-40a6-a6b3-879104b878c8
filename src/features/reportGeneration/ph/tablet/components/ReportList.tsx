import { TouchableOpacity, FlatList } from 'react-native';
import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  Column,
  H7,
  H8,
  Icon,
  Label,
  LoadingIndicator,
  Row,
  addToast,
} from 'cube-ui-components';
import { ReportListItem, ReportStatement } from 'types/report';
import { format } from 'date-fns';
import {
  getPersistencyReport,
  getReportAsync,
} from 'hooks/useReportGeneration';
import { useTranslation } from 'react-i18next';
import { Placeholder } from '../../components/PlaceHolder';
import _ from 'lodash';
import { CELL_HEIGHT } from '../../components/DataGrid/utils';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { SolidArrow } from 'features/reportGeneration/assets/SolidArrowSVG';

const HEADER_HEIGHT = 48;

const CONTAINER_WIDTH = {
  DUMMY: '10%', // There will be 2 dummy columns
  DATE: '40%',
  DOWNLOAD: '40%',
} as const;

export default function ReportList({
  isLoading,
  listData,
  isError,
  reportStatement,
}: {
  isLoading: boolean;
  isError: boolean;
  listData?: ReportListItem[];
  reportStatement: ReportStatement;
}) {
  const { sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');

  // Sort list data by upload date
  const [sortOrder, setSortOrder] = useState<'DESC' | 'ASC'>('DESC');

  const handleSortPress = () =>
    sortOrder === 'DESC' ? setSortOrder('ASC') : setSortOrder('DESC');

  const sortedListData = useMemo(() => {
    return listData?.sort((a, b) =>
      sortOrder === 'DESC'
        ? new Date(b?.uploadDate).getTime() - new Date(a?.uploadDate).getTime()
        : new Date(a?.uploadDate).getTime() - new Date(b?.uploadDate).getTime(),
    );
  }, [listData, sortOrder]);

  // Download report
  const [isDownloadComplete, setIsDownloadComplete] = useState(false);

  const onPressDownload = async (item: ReportListItem) => {
    addToast([
      {
        message: t(`download.downloading`),
        IconLeft: <LoadingIndicator />,
      },
    ]);

    if (item?.documentCode === 'PERSISTENCY') {
      getPersistencyReport({
        documentCode: item?.documentCode,
        tenant: 'ph',
        uploadDate: item?.uploadDate,
      }).then(error => {
        if (!error) {
          setTimeout(() => {
            setIsDownloadComplete(true);
          }, 500);
        }
      });
    } else {
      getReportAsync({
        documentIndex: item?.documentIndex,
        documentCode: item?.documentCode,
        tenant: 'ph',
      }).then(error => {
        if (!error) {
          setTimeout(() => {
            setIsDownloadComplete(true);
          }, 500);
        }
      });
    }
  };

  useEffect(() => {
    isDownloadComplete &&
      addToast([
        {
          message: t(`download.downloadSuccess`),
          IconLeft: <Icon.Tick size={sizes[6]} />,
        },
      ]);
    setIsDownloadComplete(false);
  }, [isDownloadComplete]);

  if (isLoading) {
    return (
      <Column flex={1}>
        <TableHeader
          reportStatement={reportStatement}
          disablePress={true}
          showSortIcon={false}
        />
        <Placeholder.Loading />
      </Column>
    );
  }

  if (isError) {
    return (
      <Column flex={1}>
        <TableHeader
          reportStatement={reportStatement}
          disablePress={true}
          showSortIcon={false}
        />
        <Placeholder.LoadingError />
      </Column>
    );
  }

  if (_.isEmpty(listData)) {
    return <Placeholder.EmptyRecord />;
  }

  return (
    <FlatList
      data={sortedListData}
      stickyHeaderIndices={[0]}
      ListHeaderComponent={() => (
        <TableHeader
          reportStatement={reportStatement}
          sortOrder={sortOrder}
          handleSortPress={handleSortPress}
        />
      )}
      renderItem={({
        item,
        index,
      }: {
        item: ReportListItem;
        index: number;
      }) => (
        <TableItem
          key={'listItem_' + index}
          reportStatement={reportStatement}
          item={item}
          index={index}
          onPressDownload={onPressDownload}
        />
      )}
      ItemSeparatorComponent={SeparatorComponent}
      bounces={false}
    />
  );
}

const TableHeader = ({
  reportStatement,
  disablePress = false, // props from parent when isLoading/ isError
  showSortIcon = true, // props from parent when isLoading/ isError
  sortOrder,
  handleSortPress,
}: {
  reportStatement?: ReportStatement;
  disablePress?: boolean;
  showSortIcon?: boolean;
  sortOrder?: 'DESC' | 'ASC';
  handleSortPress?: () => void;
}) => {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();

  const isCommissionStatement = reportStatement === 'commission';

  return (
    <Row
      height={HEADER_HEIGHT}
      backgroundColor={colors.primary}
      alignItems="center">
      <Column width={CONTAINER_WIDTH.DUMMY} />
      {isCommissionStatement ? (
        <TouchableOpacity
          hitSlop={ICON_HIT_SLOP}
          disabled={disablePress}
          onPress={handleSortPress}
          style={{
            width: CONTAINER_WIDTH.DATE,
            flexDirection: 'row',
            alignItems: 'center',
            gap: space[1],
          }}>
          <H8 color={colors.palette.white} children={t('header.paymentDate')} />
          {showSortIcon && sortOrder === 'ASC' ? (
            <SolidArrow.UpLight />
          ) : (
            <SolidArrow.DownLight />
          )}
        </TouchableOpacity>
      ) : (
        <Column width={CONTAINER_WIDTH.DATE}>
          <H8 color={colors.palette.white} children={t('header.month')} />
        </Column>
      )}
      <Column width={CONTAINER_WIDTH.DOWNLOAD} alignItems="flex-end">
        <H8 color={colors.palette.white} children={t('header.downloadPDF')} />
      </Column>
      <Column width={CONTAINER_WIDTH.DUMMY} />
    </Row>
  );
};
//&& sortOrder === 'ASC'

const TableItem = ({
  item,
  index,
  onPressDownload,
  reportStatement,
}: {
  item: ReportListItem;
  index: number;
  onPressDownload: (item: ReportListItem) => void;
  reportStatement: ReportStatement;
}) => {
  const { space, sizes, colors, borderRadius } = useTheme();

  const locale = reportStatement === 'commission' ? 'LLLL d, yyyy' : 'LLL yyyy';

  return (
    <Row
      height={CELL_HEIGHT}
      backgroundColor={
        index % 2 == 0 ? colors.palette.white : colors.palette.fwdGrey[20]
      }
      alignItems="center">
      <Column width={CONTAINER_WIDTH.DUMMY} />

      <Column width={CONTAINER_WIDTH.DATE}>
        <Row flex={1} alignItems="center" gap={space[5]}>
          <Label color={colors.palette.fwdDarkGreen[50]} children={index + 1} />
          <H7
            color={colors.palette.fwdDarkGreen[100]}
            children={format(new Date(item?.uploadDate), locale)}
          />
        </Row>
      </Column>

      <Column width={CONTAINER_WIDTH.DOWNLOAD} paddingRight={space[6]}>
        <TouchableOpacity
          onPress={() => onPressDownload(item)}
          style={{
            alignSelf: 'flex-end',
            padding: space[2],
            backgroundColor: colors.primaryVariant3,
            borderWidth: 1,
            borderColor: colors.primaryVariant2,
            borderRadius: borderRadius.full,
          }}>
          <Icon.Download
            size={sizes[6]}
            fill={colors.palette.fwdAlternativeOrange[100]}
          />
        </TouchableOpacity>
      </Column>

      <Column width={CONTAINER_WIDTH.DUMMY} />
    </Row>
  );
};

const SeparatorComponent = () => {
  const { colors } = useTheme();
  return <Column height={1} backgroundColor={colors.palette.fwdGrey[100]} />;
};
