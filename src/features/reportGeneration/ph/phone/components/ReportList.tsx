import { TouchableOpacity, FlatList } from 'react-native';
import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  Column,
  H7,
  H8,
  Icon,
  Label,
  LoadingIndicator,
  Row,
  addToast,
} from 'cube-ui-components';
import { ReportListItem, ReportStatement } from 'types/report';
import { format } from 'date-fns';
import {
  getPersistencyReport,
  getReportAsync,
} from 'hooks/useReportGeneration';
import { useTranslation } from 'react-i18next';
import { Placeholder } from '../../components/PlaceHolder';
import _ from 'lodash';

export default function ReportList({
  isLoading,
  listData,
  isError,
  reportStatement,
}: {
  isLoading: boolean;
  isError: boolean;
  listData?: ReportListItem[];
  reportStatement: ReportStatement;
}) {
  const { sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');

  // Sort list data by upload date
  const [sortOrder, setSortOrder] = useState<'DESC' | 'ASC'>('DESC');

  const handleSortPress = () =>
    sortOrder === 'DESC' ? setSortOrder('ASC') : setSortOrder('DESC');

  const sortedListData = useMemo(() => {
    return listData?.sort((a, b) =>
      sortOrder === 'DESC'
        ? new Date(b?.uploadDate).getTime() - new Date(a?.uploadDate).getTime()
        : new Date(a?.uploadDate).getTime() - new Date(b?.uploadDate).getTime(),
    );
  }, [listData, sortOrder]);

  // Download report
  const [isDownloadComplete, setIsDownloadComplete] = useState(false);

  const onPressDownload = async (item: ReportListItem) => {
    addToast([
      {
        message: t(`download.downloading`),
        IconLeft: <LoadingIndicator />,
      },
    ]);

    if (item?.documentCode === 'PERSISTENCY') {
      getPersistencyReport({
        documentCode: item?.documentCode,
        tenant: 'ph',
        uploadDate: item?.uploadDate,
      }).then(error => {
        if (!error) {
          setTimeout(() => {
            setIsDownloadComplete(true);
          }, 500);
        }
      });
    } else {
      getReportAsync({
        documentIndex: item?.documentIndex,
        documentCode: item?.documentCode,
        tenant: 'ph',
      }).then(error => {
        if (!error) {
          setTimeout(() => {
            setIsDownloadComplete(true);
          }, 500);
        }
      });
    }
  };

  useEffect(() => {
    isDownloadComplete &&
      addToast([
        {
          message: t(`download.downloadSuccess`),
          IconLeft: <Icon.Tick size={sizes[6]} />,
        },
      ]);
    setIsDownloadComplete(false);
  }, [isDownloadComplete]);

  if (isLoading) {
    return (
      <Column flex={1}>
        <TableHeader
          reportStatement={reportStatement}
          disablePress={true}
          showSortIcon={false}
        />
        <Placeholder.Loading />
      </Column>
    );
  }

  if (isError) {
    return (
      <Column flex={1}>
        <TableHeader
          reportStatement={reportStatement}
          disablePress={true}
          showSortIcon={false}
        />
        <Placeholder.LoadingError />
      </Column>
    );
  }

  if (_.isEmpty(listData)) {
    return <Placeholder.EmptyRecord />;
  }

  return (
    <FlatList
      data={sortedListData}
      stickyHeaderIndices={[0]}
      ListHeaderComponent={() => (
        <TableHeader
          reportStatement={reportStatement}
          handleSortPress={handleSortPress}
        />
      )}
      renderItem={({
        item,
        index,
      }: {
        item: ReportListItem;
        index: number;
      }) => (
        <TableItem
          key={'listItem_' + index}
          reportStatement={reportStatement}
          item={item}
          index={index}
          onPressDownload={onPressDownload}
        />
      )}
      ItemSeparatorComponent={SeparatorComponent}
      bounces={false}
    />
  );
}

const TableHeader = ({
  reportStatement,
  disablePress = false, // props from parent when isLoading/ isError
  showSortIcon = true, // props from parent when isLoading/ isError
  handleSortPress,
}: {
  reportStatement?: ReportStatement;
  disablePress?: boolean;
  showSortIcon?: boolean;
  handleSortPress?: () => void;
}) => {
  const { t } = useTranslation('reportGeneration');
  const { colors, sizes, space } = useTheme();

  const isCommissionStatement = reportStatement === 'commission';

  return (
    <Row
      h={sizes[12]}
      p={space[3]}
      bgColor={colors.secondary}
      alignItems="center">
      {isCommissionStatement ? (
        <TouchableOpacity
          disabled={disablePress}
          onPress={handleSortPress}
          style={{
            flex: 5,
            flexDirection: 'row',
            alignItems: 'center',
            gap: space[1],
          }}>
          <H8 color={colors.palette.white} children={t('header.paymentDate')} />
          {showSortIcon && <Icon.SolidArrowUp fill={colors.placeholder} />}
        </TouchableOpacity>
      ) : (
        <Column flex={5}>
          <H8 color={colors.palette.white} children={t('header.month')} />
        </Column>
      )}
      <Column flex={2}>
        <H8 color={colors.palette.white} children={t('header.downloadPDF')} />
      </Column>
    </Row>
  );
};

const TableItem = ({
  item,
  index,
  onPressDownload,
  reportStatement,
}: {
  item: ReportListItem;
  index: number;
  onPressDownload: (item: ReportListItem) => void;
  reportStatement: ReportStatement;
}) => {
  const { space, sizes, colors } = useTheme();
  const iconSize = sizes[6];

  const locale = reportStatement === 'commission' ? 'LLLL d, yyyy' : 'LLL yyyy';

  return (
    <Row
      height={sizes[17]}
      backgroundColor={
        index % 2 == 0 ? colors.palette.white : colors.palette.fwdGrey[20]
      }
      alignItems="center"
      padding={space[3]}
      flex={1}>
      <Column justifyContent="center" flex={5}>
        <Row flex={1} alignItems="center" gap={space[4]}>
          <Label color={colors.palette.fwdDarkGreen[50]}>{index + 1}</Label>
          <H7 color={colors.palette.fwdDarkGreen[100]}>
            {format(new Date(item?.uploadDate), locale)}
          </H7>
        </Row>
      </Column>
      <Column justifyContent="center" flex={2}>
        <TouchableOpacity
          style={{ alignItems: 'center', justifyContent: 'center' }}
          onPress={() => onPressDownload(item)}>
          <Icon.Download
            size={iconSize}
            fill={colors.palette.fwdAlternativeOrange[100]}
          />
        </TouchableOpacity>
      </Column>
    </Row>
  );
};

const SeparatorComponent = () => {
  const { colors } = useTheme();
  return (
    <Column height={1} backgroundColor={colors.palette.fwdGrey[100]}></Column>
  );
};
