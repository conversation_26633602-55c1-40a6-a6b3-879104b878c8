import React, { useContext } from 'react';
import { TouchableOpacity } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { H8, Icon, Row } from 'cube-ui-components';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import {
  ToolbarContext,
  endLandscape,
  startLandscape,
} from '../../ToolbarProvider';

export default function FloatingActionButton({
  fullTable,
}: {
  fullTable?: boolean;
}) {
  const { t } = useTranslation('reportGeneration');
  const { sizes, colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { updateLandscape } = useContext(ToolbarContext);

  const onFullTablePress = () => {
    startLandscape();
    updateLandscape(true);
  };

  return (
    <Container bottom={bottom}>
      <Row>
        {fullTable && (
          <TouchableOpacity onPress={() => onFullTablePress()}>
            <Row alignItems="center" gap={space[1]}>
              <Icon.Expand size={sizes[5]} fill={colors.secondary} />
              <H8 children={t('fullTable')} />
            </Row>
          </TouchableOpacity>
        )}
      </Row>
    </Container>
  );
}

/**
 * Close button in 'Full table' view
 */
export function CloseButton() {
  const { sizes, colors, borderRadius } = useTheme();
  const { updateLandscape } = useContext(ToolbarContext);
  return (
    <TouchableOpacity
      onPress={() => {
        endLandscape();
        updateLandscape(false);
      }}
      style={{
        position: 'absolute',
        right: 50,
        top: 50,
        //
        width: sizes[10],
        height: sizes[10],
        backgroundColor: colors.background,
        borderRadius: borderRadius.full,
        alignItems: 'center',
        justifyContent: 'center',
        //
        shadowColor: colors.palette.fwdGreyDark[100],
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 5,
      }}>
      <Icon.Close fill={colors.palette.fwdDarkGreen[100]} />
    </TouchableOpacity>
  );
}

const Container = styled.View<{
  bottom: number;
}>(({ theme, bottom }) => ({
  position: 'absolute',
  bottom: theme.space[15],
  // bottom: theme.space[6] + bottom,
  zIndex: 9999,
  alignSelf: 'center',
  //
  borderRadius: 24, // value from Figma
  backgroundColor: theme.colors.background,
  paddingHorizontal: theme.space[4],
  paddingVertical: theme.space[2],
  justifyContent: 'center',
  alignItems: 'center',
  //
  shadowColor: theme.colors.palette.black,
  shadowOpacity: 0.3,
  shadowOffset: { width: 0, height: 4 },
  shadowRadius: 4,
  elevation: 5,
}));
