import React, { useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import { RouteProp, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import InformationModal from 'features/reportGeneration/ph/components/InformationModal';
import ReportList from './components/ReportList';
import { useGetReportListByReportStatement } from 'hooks/useReportGeneration';

/**
 * Commission statement, Tax statement, Persistency
 */
export default function ReportGenerationListScreen() {
  const { colors } = useTheme();
  const { t } = useTranslation('reportGeneration');

  const route =
    useRoute<RouteProp<RootStackParamList, 'ReportGenerationListScreen'>>();
  const reportStatement = route?.params?.reportStatement;

  const [infoModalVisible, setInfoModalVisible] = useState<boolean>(false);

  const { data, isLoading, isError } =
    useGetReportListByReportStatement(reportStatement);
  const listData = data?.documentList ?? [];

  return (
    <>
      <ScreenHeader
        route={'ReportGenerationListScreen'}
        customTitle={t(reportStatement)}
        isLeftArrowBackShown
        rightChildren={
          <TouchableOpacity onPress={() => setInfoModalVisible(true)}>
            <Icon.InfoCircle fill={colors.secondary} />
          </TouchableOpacity>
        }
      />

      <View style={{ flex: 1 }}>
        <ReportList
          isLoading={isLoading}
          isError={isError}
          listData={listData}
          reportStatement={reportStatement}
        />
      </View>

      {/* ----- Floating UI ----- */}

      <InformationModal
        visible={infoModalVisible}
        setIsVisible={setInfoModalVisible}
      />
    </>
  );
}
