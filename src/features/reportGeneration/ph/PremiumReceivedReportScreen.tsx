import React, { useContext, useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon, Row } from 'cube-ui-components';
import PhoneScreenHeader from 'navigation/components/ScreenHeader/phone';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import InformationModal from 'features/reportGeneration/ph/components/InformationModal';
import ReportDataGrid from './components/DataGrid/ReportDataGrid';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from './components/DataGrid/tablesConfig/premiumReceived';
import FloatingActionButton, {
  CloseButton,
} from './phone/components/FloatingActionButton';
import _ from 'lodash';
import { useGetPremiumReceivedReport } from 'hooks/useReportGeneration';
import { policyInfoHandler, Toolbar } from './components/Toolbar';
import { endLandscape, ToolbarContext } from './ToolbarProvider';
import TransactionDateActionPanel from './components/ActionPanel/TransactionDateActionPanel';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTranslation } from 'react-i18next';
import PolicyInfoActionPanel from '../components/ActionPanel/PolicyInfoActionPanel';
import Animated, { FadeOut, LinearTransition } from 'react-native-reanimated';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import { flattenTeamHierarchy } from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useCheckIsLeader from '../hooks/useCheckIsLeader';
import {
  CHIP_CONFIG,
  DEFAULT_POLICY_INFO,
  SEARCH_TYPE_CONFIG,
} from 'features/reportGeneration/components/ActionPanel/config/policyInfo';

/**
 * For both mobile and tablet
 */
export default function PremiumReceivedReportScreen() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { isLeader } = useCheckIsLeader();

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  const {
    landscape,
    updateLandscape,
    showToolBar,
    updateShowToolbar,
    selectedAgent,
    updateSelectedAgent,
    checkedTeam,
    updateCheckedTeam,
    transactionDate,
    updateTransactionDate,
    premiumReceivedPolicyInfo: policyInfo,
    updatePremiumReceivedPolicyInfo: updatePolicyInfo,
  } = useContext(ToolbarContext);

  // const [infoModalVisible, setInfoModalVisible] = useState<boolean>(false);
  const [openTransactionDatePanel, setOpenTransactionDatePanel] =
    useState<boolean>(false);
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);

  const { data, isLoading, isError } = useGetPremiumReceivedReport({
    agentId: selectedAgent,
    from: transactionDate?.from,
    to: transactionDate?.to,
    premiumReceivedPolicyInfo: policyInfo,
    team: checkedTeam,
  });
  const reportItemList = data?.data || [];

  const duration = `${dateFormatUtil(transactionDate?.from)} - ${dateFormatUtil(
    transactionDate?.to,
  )}`;

  useEffect(() => {
    updateShowToolbar(true);

    return () => {
      if (!isTabletMode) {
        endLandscape();
        updateLandscape(false);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {!landscape && (
        <>
          {isTabletMode ? (
            <TabletScreenHeader
              route={'PremiumReceivedReportScreen'}
              customTitle={t('premiumReceived')}
              isLeftArrowBackShown
              showBottomSeparator={false}
              rightChildren={
                <Row alignItems="center">
                  {/* Hide for the 1st phrase */}
                  {/* <TouchableOpacity
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: space[2],
                    }}
                    onPress={() => setInfoModalVisible(true)}>
                    <Icon.InfoCircle fill={colors.secondary} />
                    <LargeLabel fontWeight="bold" children={'info'} />
                  </TouchableOpacity> */}
                </Row>
              }
            />
          ) : (
            <PhoneScreenHeader
              route={'PremiumReceivedReportScreen'}
              customTitle={t('premiumReceived')}
              isLeftArrowBackShown
              rightChildren={
                <Row gap={space[4]}>
                  {/* Hide for the 1st phrase */}
                  {/* <TouchableOpacity onPress={() => console.log('Download')}>
                    <Icon.Download fill={colors.secondary} />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setInfoModalVisible(true)}>
                    <Icon.InfoCircle fill={colors.secondary} />
                  </TouchableOpacity> */}
                </Row>
              }
            />
          )}

          {showToolBar && (
            <Animated.View layout={LinearTransition} exiting={FadeOut}>
              <Toolbar.MainContainer>
                <Toolbar.ScrollableContainer>
                  {isLeader && !hasNoDownline && (
                    <>
                      <Toolbar.SelectAgent
                        data={memberInfoList}
                        value={selectedAgent}
                        disabled={checkedTeam || isTeamLoading}
                        onChange={(item: string) => updateSelectedAgent(item)}
                      />
                      <Toolbar.Team
                        checked={checkedTeam}
                        onPress={() => {
                          updateCheckedTeam(!checkedTeam);
                          updateSelectedAgent(selfAgentCode as string);
                        }}
                      />
                    </>
                  )}
                  <Toolbar.Date
                    title={t('toolbar.transactionDate')}
                    duration={duration}
                    onPress={() => setOpenTransactionDatePanel(true)}
                  />
                  <Toolbar.PolicyInfo
                    policyHolder={policyInfoHandler(policyInfo)}
                    onPress={() => setOpenPolicyHolderPanel(true)}
                  />
                </Toolbar.ScrollableContainer>

                <Toolbar.DataGridInfo count={reportItemList?.length} />
              </Toolbar.MainContainer>
            </Animated.View>
          )}
        </>
      )}

      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ReportDataGrid
          darkMode={!isTabletMode}
          isLoading={isLoading}
          isError={isError}
          data={reportItemList}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
        />
      </Animated.View>

      {/* ----- Floating UI ----- */}

      {/* <InformationModal
        fullVersion={false}
        visible={infoModalVisible}
        setIsVisible={setInfoModalVisible}
      /> */}

      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
        //
        defaultPolicyInfo={DEFAULT_POLICY_INFO}
        searchTypeConfig={SEARCH_TYPE_CONFIG}
        chipConfig={CHIP_CONFIG}
        //
        title={t('actionPanel.title.searchForPolicyInfo')}
        searchTip={t('actionPanel.subtitle.searchTip')}
        policyHolderNameError={'Invalid input'}
        policyNumberHint={t('actionPanel.hint.policyNumber')}
        policyNumberError={'Invalid input'}
        //
        primaryLabel={t('actionPanel.search')}
        secondaryLabel={t('actionPanel.reset')}
      />

      <TransactionDateActionPanel
        visible={openTransactionDatePanel}
        handleClose={() => setOpenTransactionDatePanel(false)}
        contextValue={transactionDate}
        updateContextValue={updateTransactionDate}
      />

      {/* ----- Mobile only floating UI ----- */}

      {landscape && !isTabletMode && <CloseButton />}

      {!_.isEmpty(data) && !landscape && !isTabletMode && (
        <FloatingActionButton fullTable />
      )}
    </>
  );
}
