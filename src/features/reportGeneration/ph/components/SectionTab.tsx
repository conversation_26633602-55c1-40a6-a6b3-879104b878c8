import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { useWindowDimensions } from 'react-native';
import { formatCurrency } from 'utils';

/**
 * Tab width in mobile - 100%
 * Tab width in tablet - Calculated based on the window width
 */
export default function SectionTab({
  type,
  icon,
  title,
  onPress,
  showCaseAndSales = false,
  cases,
  sales,
  isLoading,
}: {
  type?: string;
  icon: JSX.Element;
  title: string;
  onPress: () => void;
  showCaseAndSales?: boolean;
  cases?: number;
  sales?: number;
  isLoading?: boolean;
}) {
  const { colors, space, sizes } = useTheme();

  const caseText = Number(cases) <= 1 ? ' case' : ' cases';

  const duration =
    type === 'lapsedPolicies'
      ? ' as of this month'
      : type === 'premiumReceived'
      ? ' / this month'
      : '';

  const amountType =
    type === 'lapsedPolicies'
      ? 'Sales APE'
      : type === 'premiumReceived'
      ? 'Renewal premium'
      : '';

  // Tablet tab width
  const { width } = useWindowDimensions();
  const tabContainerWidth =
    width / 2 - space[8] - space[8] - space[3] - space[4]; // Container width = windowWidth / 2 - paddingLeft - paddingRight - gap - ?

  return (
    <TabContainer onPress={onPress} tabContainerWidth={tabContainerWidth}>
      {icon}

      <Column flex={1} gap={space[1]}>
        <Typography.H7
          fontWeight="bold"
          color={colors.secondary}
          children={title}
        />

        {showCaseAndSales && (
          <Row alignItems="baseline">
            {isLoading ? (
              <Skeleton width={sizes[10]} height={sizes[4]} radius={2} />
            ) : (
              <Typography.H6
                fontWeight="bold"
                color={colors.secondary}
                children={cases ?? '--'}
              />
            )}
            <Typography.H6
              fontWeight="bold"
              color={colors.secondary}
              children={caseText}
            />
            <Typography.H8 color={colors.placeholder} children={duration} />
          </Row>
        )}

        {showCaseAndSales && (
          <Typography.H8>
            <Typography.H8
              color={colors.placeholder}
              children={`${amountType}: PHP `}
            />
            {isLoading ? (
              <Skeleton width={sizes[16]} height={10} radius={2} />
            ) : (
              <Typography.H8
                color={colors.placeholder}
                children={formatCurrency(sales, 0) ?? '--'}
              />
            )}
          </Typography.H8>
        )}
      </Column>

      <Icon.ChevronRight fill={colors.palette.fwdGreyDarker} />
    </TabContainer>
  );
}

const TabContainer = styled.TouchableOpacity(
  ({
    theme,
    tabContainerWidth,
  }: {
    theme?: Theme;
    tabContainerWidth: number;
  }) => {
    const { isTabletMode } = useLayoutAdoptionCheck();
    return {
      width: isTabletMode ? tabContainerWidth : '100%',
      flexDirection: 'row',
      backgroundColor: theme?.colors.background,
      borderRadius: theme?.borderRadius.large,
      paddingVertical: theme?.space[4],
      paddingLeft: theme?.space[4],
      paddingRight: theme?.space[2],
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: theme?.space[3],
    };
  },
);
