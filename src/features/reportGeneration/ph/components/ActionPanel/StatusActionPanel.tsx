import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  Column,
  RadioButton,
  RadioButtonGroup,
} from 'cube-ui-components';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Status } from 'types/report';
import { Toolbar } from '../Toolbar';

export const STATUS_CONFIG = [
  { type: 'lapsed', value: 'LAPSED', label: 'lapsed' },
  {
    type: 'premiumHoliday ',
    value: 'PREMIUM_HOLIDAY',
    label: 'premiumHoliday',
  },
  {
    type: 'anticipatedLapse',
    value: 'ANTICIPATED_LAPSE',
    label: 'anticipatedLapse',
  },
] as const;

/**
 * For mobile only
 * Using in lapsed report
 */
export default function StatusActionPanel({
  visible,
  handleClose,
  contextValue,
  updateContextValue,
}: {
  visible: boolean;
  handleClose: () => void;
  contextValue: Status;
  updateContextValue: (contextValue: Status) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => handleClose()}
      title={t('actionPanel.title.status')}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
      }}>
      <Column p={space[4]}>
        <RadioButtonGroup
          value={contextValue}
          onChange={value => {
            updateContextValue(value);
            handleClose();
          }}>
          {STATUS_CONFIG.map(({ type, value, label }, index) => (
            <Fragment key={`status_${type}`}>
              <RadioButton
                value={value}
                label={t(label)}
                labelStyle={{ paddingVertical: space[2] }}
              />
              {STATUS_CONFIG.length - 1 > index && <Toolbar.ItemSeparator />}
            </Fragment>
          ))}
        </RadioButtonGroup>
      </Column>
    </ActionPanel>
  );
}
