import React, { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import {
  ActionPanel,
  Column,
  H6,
  <PERSON><PERSON><PERSON>l,
  <PERSON>er,
  Row,
  SmallLabel,
  // DatePicker,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import { addDays, compareDesc, format } from 'date-fns';
import {
  sixDaysFromToday,
  today,
} from 'features/reportGeneration/ph/ToolbarProvider';
import { Week } from '../../phone/PolicyAnniversaryListScreen';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import {
  CommonModal,
  ModalFormAction,
} from 'features/reportGeneration/ph/components/CommonModal';

/**
 * For both mobile and tablet
 * Using in policy anniversary report
 */
export default function PolicyAnniversaryPeriodActionPanel({
  visible,
  handleClose,
  contextValue,
  updateContextValue,
}: {
  visible: boolean;
  handleClose: () => void;
  contextValue: {
    duration: Week;
    from: string;
    to: string;
  };
  updateContextValue: (contextValue: {
    duration: Week;
    from: string;
    to: string;
  }) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const [isCustomDurationValid, setIsCustomDurationValid] = useState(true);

  const [tempPeriod, setTempPeriod] = useState(contextValue); // For UI display and saving state

  const PERIOD_CONFIG = Array.from({ length: 4 }, (_, i) =>
    generateWeekConfig(i * 7, i + 1),
  );

  const handleSelectedChipChange = (week: Week) => {
    if (!week) return;
    setTempPeriod({
      duration: week,
      from: PERIOD_CONFIG.filter(item => item?.value === week)[0]?.from,
      to: PERIOD_CONFIG.filter(item => item?.value === week)[0]?.to,
    });
    return;
  };

  // Check if custom duration is valid
  useEffect(() => {
    const checkCustomDuration = (from: string, to: string) => {
      if (!from || !to) return;
      const result = compareDesc(new Date(from), new Date(to));
      result === 1
        ? setIsCustomDurationValid(true)
        : setIsCustomDurationValid(false);
      return;
    };
    checkCustomDuration(tempPeriod?.from, tempPeriod?.to);
  }, [tempPeriod?.from, tempPeriod?.to]);

  // Reset panel
  const resetPanelToContextValue = () => setTempPeriod(contextValue);

  // Form action functions
  const formActionOnPrimaryPress = () => {
    updateContextValue(tempPeriod);
    handleClose();
  };

  const formActionOnSecondaryPress = () => {
    setTempPeriod({
      duration: 'week1',
      from: today,
      to: sixDaysFromToday,
    });
  };

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible={visible}
        width={'80%'}
        onClose={() => {
          resetPanelToContextValue();
          handleClose();
        }}>
        <Column gap={space[6]}>
          <H6
            fontWeight="bold"
            children={t('actionPanel.title.policyAnniversaryPeriod')}
          />

          <Picker
            type="chip"
            size="large"
            containerStyle={{ flexWrap: 'wrap' }}
            items={PERIOD_CONFIG}
            value={tempPeriod?.duration}
            onChange={week => handleSelectedChipChange(week as Week)}
          />

          <Row justifyContent="space-between">
            <Column flex={1} gap={space[1]}>
              <SmallLabel
                children={t('actionPanel.from')}
                color={colors.placeholder}
              />
              <LargeLabel children={dateFormatUtil(tempPeriod?.from)} />
            </Column>

            <Column flex={1} gap={space[1]}>
              <SmallLabel
                children={t('actionPanel.to')}
                color={colors.placeholder}
              />
              <LargeLabel children={dateFormatUtil(tempPeriod?.to)} />
            </Column>
          </Row>
        </Column>

        <ModalFormAction
          hasShadow={false}
          primaryLabel={t('actionPanel.confirm')}
          onPrimaryPress={() => formActionOnPrimaryPress()}
          primaryDisabled={!isCustomDurationValid}
          secondaryLabel={t('actionPanel.reset')}
          onSecondaryPress={() => formActionOnSecondaryPress()}
          style={{
            paddingTop: space[10],
            paddingBottom: 0,
          }}
        />
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <ActionPanel
      visible={visible}
      handleClose={() => {
        resetPanelToContextValue();
        handleClose();
      }}
      title={t('actionPanel.title.policyAnniversaryPeriod')}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: space[4] + bottom,
          ios: 0,
        }),
      }}>
      <Column pt={space[4]} px={space[4]} gap={space[4]}>
        <Picker
          type="chip"
          size="medium"
          containerStyle={{ flexWrap: 'wrap', gap: space[1] }}
          items={PERIOD_CONFIG}
          value={tempPeriod?.duration}
          onChange={week => handleSelectedChipChange(week as Week)}
        />

        <Row pb={space[4]} justifyContent="space-between">
          <Column flex={1} gap={space[1]}>
            <SmallLabel
              children={t('actionPanel.from')}
              color={colors.placeholder}
            />
            <LargeLabel
              fontWeight="bold"
              children={dateFormatUtil(tempPeriod?.from)}
            />
          </Column>

          <Column flex={1} gap={space[1]}>
            <SmallLabel
              children={t('actionPanel.to')}
              color={colors.placeholder}
            />
            <LargeLabel
              fontWeight="bold"
              children={dateFormatUtil(tempPeriod?.to)}
            />
          </Column>
        </Row>

        {/* <Column>
          <SmallLabel children={t('actionPanel.from')} />
          <DatePicker
            value={new Date(tempPeriod?.from)}
            onChange={date =>
              setTempPeriod({
                ...tempPeriod,
                duration: 'week0', // week0 is custom duration
                from: format(date, 'yyyy-MM-dd'),
              })
            }
            modalTitle={t('actionPanel.from')}
            inputContainerStyle={{
              borderTopWidth: 0,
              borderLeftWidth: 0,
              borderRightWidth: 0,
            }}
          />
        </Column> */}

        {/* <Column>
          <SmallLabel children={t('actionPanel.to')} />
          <DatePicker
            value={new Date(tempPeriod?.to)}
            onChange={date =>
              setTempPeriod({
                ...tempPeriod,
                duration: 'week0', // week0 is custom duration
                to: format(date, 'yyyy-MM-dd'),
              })
            }
            modalTitle={t('actionPanel.to')}
            inputContainerStyle={{
              borderTopWidth: 0,
              borderLeftWidth: 0,
              borderRightWidth: 0,
            }}
          />
        </Column> */}
      </Column>

      <FormAction
        hasShadow={false}
        primaryLabel={t('actionPanel.confirm')}
        onPrimaryPress={() => formActionOnPrimaryPress()}
        primaryDisabled={!isCustomDurationValid}
        secondaryLabel={t('actionPanel.reset')}
        onSecondaryPress={() => formActionOnSecondaryPress()}
      />
    </ActionPanel>
  );
}

const generateWeekConfig = (startDayOffset: number, weekNumber: number) => {
  const startDate = addDays(new Date(), startDayOffset);
  const endDate = addDays(startDate, 6);
  return {
    label: `${format(startDate, 'dd LLL')} - ${format(endDate, 'dd LLL')}`,
    value: `week${weekNumber}`,
    from: format(startDate, 'yyyy-MM-dd'),
    to: format(endDate, 'yyyy-MM-dd'),
  };
};
