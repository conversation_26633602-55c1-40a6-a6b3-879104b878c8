import React, { useState, forwardRef, useMemo } from 'react';
import { ViewProps } from 'react-native';
import useLatest from 'hooks/useLatest';
import { Toolbar } from '../../Toolbar';
import useBoundStore from 'hooks/useBoundStore';
import SearchableDropdownPanel from './SearchableDropdownPanel';

export type SearchableDropdownProps<T, V> = ViewProps & {
  label?: string;
  subLabel?: string;
  modalTitle?: string;
  title: string;
  actionLabel?: string;
  disabled?: boolean;
  preventPopup?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemSubLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  getDisplayedLabel?: (item: T) => string;
  getExternalDisplayedLabel?: () => string;
  searchable?: boolean;
  searchMode?: 'auto' | 'manual';
  searchLabel?: string;
  onQuery?: (text: string) => void;
  multiline?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  value?: V;
  onChange?: (value: V) => void;
};

/**
 * For both mobile and tablet
 */
function SearchableDropdownInner<T, V>({
  label,
  subLabel,
  modalTitle,
  title,
  actionLabel,
  disabled,
  preventPopup,
  hint,
  error,
  isError,
  value,
  onChange,
  data,
  getItemLabel,
  getItemSubLabel,
  getItemValue,
  getDisplayedLabel,
  getExternalDisplayedLabel,
  searchable,
  searchMode,
  searchLabel,
  onQuery,
  multiline,
  onFocus,
  onBlur,
  ...viewProps
}: SearchableDropdownProps<T, V>) {
  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const [openPanel, setOpenPanel] = useState(false);

  const getItemValueRef = useLatest(getItemValue);
  const getDisplayedLabelRef = useLatest(getDisplayedLabel || getItemLabel);
  const getExternalDisplayedLabelRef = useLatest(getExternalDisplayedLabel);
  const displayedLabel = useMemo(() => {
    if (getExternalDisplayedLabelRef.current) {
      return getExternalDisplayedLabelRef.current();
    }
    const selectedItem = data?.find?.(
      item => getItemValueRef.current?.(item) === value,
    );
    return selectedItem
      ? getDisplayedLabelRef.current?.(selectedItem)
      : undefined;
  }, [
    data,
    getDisplayedLabelRef,
    getExternalDisplayedLabelRef,
    getItemValueRef,
    value,
  ]);

  return (
    <>
      <Toolbar.ReportFilterButton
        title={title}
        label={displayedLabel}
        disabled={disabled}
        focused={Boolean(value !== selfAgentCode)}
        onPress={() => setOpenPanel(true)}
      />

      {openPanel && (
        <SearchableDropdownPanel
          data={data}
          value={value}
          title={modalTitle ?? label}
          actionLabel={actionLabel}
          searchable={searchable}
          searchMode={searchMode}
          searchLabel={searchLabel ?? label}
          onQuery={onQuery}
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          getItemSubLabel={getItemSubLabel}
          onDismiss={() => {
            setOpenPanel(false);
            onQuery?.('');
            onBlur?.();
          }}
          onDone={(item: V) => onChange?.(item)}
          onClear={() => onChange?.(selfAgentCode as V)}
        />
      )}
    </>
  );
}

const SearchableDropdown = forwardRef(SearchableDropdownInner) as <T, V>(
  props: SearchableDropdownProps<T, V> & object,
) => ReturnType<typeof SearchableDropdownInner>;
export default SearchableDropdown;
