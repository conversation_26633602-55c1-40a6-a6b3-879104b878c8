import { useTheme } from '@emotion/react';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  KEYBOARD_STATE,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import { BottomSheetDefaultFooterProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetFooter/types';
import { Portal } from '@gorhom/portal';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import {
  EmptySearchResult,
  getSelected,
  SearchInput,
  Separator,
} from 'components/SearchableDropdownPanel';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  Button,
  Column,
  H6,
  Icon,
  RadioButton,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import BottomSheetFooterSpace from 'features/eApp/components/phone/common/BottomSheetFooterSpace';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useDebounceFn from 'features/policy/hooks/useDebounceFn';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useLatest from 'hooks/useLatest';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  LayoutChangeEvent,
  Pressable,
  ScrollViewProps,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Animated, { runOnJS, useAnimatedStyle } from 'react-native-reanimated';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { CommonModal, ModalFormAction } from '../../CommonModal';

export type SearchableDropdownPanelProps<T, V> = {
  title?: string;
  actionLabel?: string;
  listTitle?: string;
  data?: T[];
  searchable?: boolean;
  searchMode?: 'auto' | 'manual' | 'manual-trigger';
  searchLabel?: string;
  onSearchTriggered?: () => void;
  onQuery?: (text: string) => void;
  getItemLabel: (item: T) => string;
  getItemSubLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  onDismiss?: () => void;
  emptyMessage?: string;
  snapMode?: 'fixed' | 'dynamic';
  estimatedItemSize?: number;
  loading?: boolean;
  onClear?: () => void;
  value?: V;
  onDone?: (value: V) => void;
};

/**
 * For both mobile and tablet
 */
export default function SearchableDropdownPanel<T, V>({
  title,
  actionLabel = 'Done',
  listTitle,
  data,
  searchable,
  searchMode = 'auto',
  searchLabel = 'Search',
  onSearchTriggered,
  onQuery: onQueryAction,
  getItemLabel,
  getItemSubLabel,
  getItemValue,
  onDismiss: onDismissAction,
  onDone,
  value,
  emptyMessage = 'Adjust keyword to get better result or try another search.',
  snapMode = 'fixed',
  estimatedItemSize = 67,
  loading,
  onClear: onClearAction,
}: SearchableDropdownPanelProps<T, V>) {
  const { space, colors } = useTheme();
  const { height } = useSafeAreaFrame();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const bottomSheetProps = useBottomSheet();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const flatListRef = useRef<FlashList<T>>(null);
  const getItemLabelRef = useLatest(getItemLabel);
  const getItemSubLabelRef = useLatest(getItemSubLabel);
  const getItemValueRef = useLatest(getItemValue);
  const onDoneRef = useLatest(onDone);
  const onQueryActionRef = useLatest(onQueryAction);

  const [query, setQuery] = useState('');
  const [checkedValue, setCheckedValue] = useState(value);
  const [footerHeight, setFooterHeight] = useState(0);

  const snapPoints = useMemo(
    () => [height - space[25], height - space[25]],
    [height, space],
  );

  const hasQuery = useMemo(() => !!query.trim(), [query]);

  /**
   * Data
   */
  const filteredData = useMemo(() => {
    if (/manual/.test(searchMode)) {
      return data;
    } else {
      return data?.filter(item => {
        return String(getItemLabelRef.current(item))
          .concat(String(getItemSubLabelRef.current(item)))
          .toLowerCase()
          .includes(query.trim().toLowerCase());
      });
    }
  }, [searchMode, data, getItemLabelRef, getItemSubLabelRef, query]);

  const extraData = useMemo(
    () =>
      Array.isArray(checkedValue)
        ? filteredData?.filter(item =>
            checkedValue.includes(getItemValueRef.current(item)),
          )
        : filteredData?.find(
            item => checkedValue === getItemValueRef.current(item),
          )
        ? [
            filteredData?.find(
              item => checkedValue === getItemValueRef.current(item),
            ),
          ]
        : [],
    [checkedValue, filteredData, getItemValueRef],
  );

  /**
   * Trigger
   */
  const autoTriggering = searchMode !== 'manual-trigger';
  const [searchTriggered, setSearchTriggered] = useState(autoTriggering);
  // if auto triggering = true, list is rendered by default
  // else check search triggered flag
  const shouldRenderList = autoTriggering || searchTriggered;

  /**
   * Querying and dismissing functions
   */
  const onQuery = useCallback(
    (text: string) => {
      if (/manual/.test(searchMode)) {
        onQueryActionRef.current?.(text);
      }
      setQuery(text);
    },
    [onQueryActionRef, searchMode],
  );

  const onTextInputClear = useCallback(() => {
    onQuery('');
  }, [onQuery]);

  const onClear = useCallback(() => {
    onQuery('');
    bottomSheetProps.bottomSheetRef.current?.close();
    setCheckedValue(undefined);
    onClearAction?.();
  }, [onQuery, onClearAction, bottomSheetProps.bottomSheetRef]);

  const onSelectItem = useCallback((newValue: V) => {
    setCheckedValue(newValue);
  }, []);

  const onDismiss = () => {
    if (onDismissAction) runOnJS(onDismissAction)();
  };

  /**
   * Auto scrolling to selected item
   */
  const autoScrolling = () => {
    if (!Array.isArray(value) && value && data) {
      const selectedIndex = data.findIndex?.(
        item => getItemValueRef.current(item) === value,
      );
      if (selectedIndex && selectedIndex > 0) {
        flatListRef.current?.scrollToIndex({
          index: selectedIndex,
          animated: false,
        });
      }
    }
  };

  useEffect(() => {
    if (!Array.isArray(checkedValue) && checkedValue && data) {
      const selectedIndex = data.findIndex?.(
        item => getItemValueRef.current(item) === checkedValue,
      );
      if (selectedIndex && selectedIndex > 0) {
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: selectedIndex,
            viewPosition: 0.1,
            animated: false,
          });
        }, 0);
      }
    }
  }, [data, checkedValue, getItemValueRef]);

  /**
   * Tablet render item
   */
  const renderTabletItem = useCallback<ListRenderItem<T>>(
    props => {
      const { item } = props;
      return (
        <TabletItem<V>
          label={getItemLabelRef.current(item)}
          value={getItemValueRef.current(item)}
          selected={getSelected(getItemValueRef.current(item), checkedValue)}
          onSelect={onSelectItem}
          subLabel={getItemSubLabelRef.current(item)}
        />
      );
    },
    [
      checkedValue,
      getItemLabelRef,
      getItemSubLabelRef,
      getItemValueRef,
      onSelectItem,
    ],
  );

  /**
   * Mobile render item
   */
  const renderItem = useCallback<ListRenderItem<T>>(
    props => {
      const { item } = props;
      return (
        <Item<V>
          label={getItemLabelRef.current(item)}
          value={getItemValueRef.current(item)}
          selected={getSelected(getItemValueRef.current(item), checkedValue)}
          onSelect={onSelectItem}
          subLabel={getItemSubLabelRef.current(item)}
        />
      );
    },
    [
      checkedValue,
      getItemLabelRef,
      getItemSubLabelRef,
      getItemValueRef,
      onSelectItem,
    ],
  );

  const onFooterLayout = useCallback((e: LayoutChangeEvent) => {
    setFooterHeight(e.nativeEvent.layout.height);
  }, []);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <Footer
          {...props}
          onClear={onClear}
          style={
            isNarrowScreen
              ? styles.smallContentContainerStyle
              : styles.contentContainerStyle
          }
          actionLabel={actionLabel}
          disabled={shouldRenderList ? !checkedValue : !hasQuery}
          loading={loading}
          onLayout={onFooterLayout}
          footerHeight={footerHeight}
          onDone={() => {
            if (shouldRenderList) {
              if (checkedValue) {
                (onDoneRef.current as (v: V) => void)?.(checkedValue as V);
                bottomSheetProps.bottomSheetRef.current?.close();
              }
            } else {
              setSearchTriggered(true);
              onSearchTriggered?.();
            }
          }}
        />
      );
    },
    [
      onClear,
      isNarrowScreen,
      actionLabel,
      shouldRenderList,
      checkedValue,
      hasQuery,
      loading,
      onFooterLayout,
      footerHeight,
      onDoneRef,
      bottomSheetProps.bottomSheetRef,
      onSearchTriggered,
    ],
  );

  /**
   * Tablet mode: Modal
   */
  if (isTabletMode)
    return (
      <CommonModal
        visible
        width={'80%'}
        disableKeyboardAvoidingViewBehavior
        onShow={() => autoScrolling()}
        onClose={() => onDismiss()}>
        <Column h="100%">
          <H6
            fontWeight="bold"
            children={'Agent'}
            style={{ paddingBottom: space[6] }}
          />

          {searchable && (
            <TabletSearchInput
              editable={autoTriggering || !searchTriggered}
              searchLabel={searchLabel}
              query={query}
              onQuery={onQuery}
              onClear={() => {
                if (!autoTriggering) setSearchTriggered(false);
                onTextInputClear();
              }}
            />
          )}

          {shouldRenderList && (
            <FlashList
              ref={flatListRef}
              keyboardShouldPersistTaps="always"
              keyboardDismissMode="on-drag"
              data={filteredData}
              extraData={extraData}
              keyExtractor={item => String(getItemValueRef.current(item))}
              estimatedItemSize={estimatedItemSize}
              ListHeaderComponent={() => (
                <>
                  <Row paddingY={space[2]} alignItems="center">
                    <Row flex={1}>
                      <Column px={space[6]} />
                      <Typography.H7
                        children={'Agent name'}
                        color={colors.palette.fwdGreyDarker}
                      />
                    </Row>

                    <Typography.H7
                      children={'Agent role and agent code'}
                      color={colors.palette.fwdGreyDarker}
                      style={{ flex: 1 }}
                    />
                  </Row>
                  <Separator />
                </>
              )}
              renderItem={data => renderTabletItem(data)}
              ItemSeparatorComponent={Separator}
              ListEmptyComponent={
                !loading && emptyMessage ? (
                  <EmptySearchResult message={emptyMessage} />
                ) : null
              }
            />
          )}

          <ModalFormAction
            hasShadow={false}
            primaryLabel={'Confirm'}
            onPrimaryPress={() => {
              if (shouldRenderList) {
                if (checkedValue) {
                  (onDoneRef.current as (v: V) => void)?.(checkedValue as V);
                  bottomSheetProps.bottomSheetRef.current?.close();
                }
              } else {
                setSearchTriggered(true);
                onSearchTriggered?.();
              }
              onDismiss();
            }}
            primaryDisabled={false}
            secondaryLabel={'Reset'}
            onSecondaryPress={() => {
              onClear();
              onDismiss();
            }}
            style={{ paddingTop: space[6], paddingBottom: 0 }}
          />
        </Column>
      </CommonModal>
    );

  /**
   * Mobile mode: BottomSheet
   */
  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          style={[bottomSheetProps.style, styles.contentContainerStyle]}
          onChange={() => autoScrolling()}
          onDismiss={onDismiss}
          snapPoints={snapPoints}
          index={snapMode === 'dynamic' ? 0 : 1}
          footerComponent={renderFooter}>
          <Box px={isNarrowScreen ? space[1] : space[2]}>
            {Boolean(title) && (
              <Typography.H6
                fontWeight="bold"
                children={title}
                style={{ paddingVertical: space[4] }}
              />
            )}
            {searchable && (
              <SearchInput
                editable={autoTriggering || !searchTriggered}
                searchLabel={searchLabel}
                query={query}
                onQuery={onQuery}
                onClear={() => {
                  if (!autoTriggering) {
                    setSearchTriggered(false);
                  }
                  onTextInputClear();
                }}
              />
            )}
          </Box>
          {shouldRenderList && (
            <>
              {Boolean(listTitle) && (
                <Box pb={space[3]} px={space[2]}>
                  <Typography.Body
                    color={colors.placeholder}
                    children={listTitle}
                  />
                </Box>
              )}
              <FlashList
                ref={flatListRef}
                contentContainerStyle={
                  isNarrowScreen
                    ? styles.smallContentContainerStyle
                    : styles.contentContainerStyle
                }
                keyboardShouldPersistTaps="always"
                keyboardDismissMode="on-drag"
                data={filteredData}
                extraData={extraData}
                keyExtractor={item => String(getItemValueRef.current(item))}
                estimatedItemSize={estimatedItemSize}
                renderItem={data => renderItem(data)}
                ItemSeparatorComponent={Separator}
                ListEmptyComponent={
                  !loading && emptyMessage ? (
                    <EmptySearchResult message={emptyMessage} />
                  ) : null
                }
                ListFooterComponent={BottomSheetFooterSpace}
                renderScrollComponent={
                  BottomSheetScrollView as React.ComponentType<ScrollViewProps>
                }
              />
            </>
          )}
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}

/**
 * Mobile use
 */
const Footer = memo(
  ({
    actionLabel,
    disabled,
    loading,
    onDone,
    onClear,
    onLayout,
    footerHeight: initialFooterHeight = 0,
    ...props
  }: {
    actionLabel: string;
    disabled: boolean;
    loading?: boolean;
    onDone: () => void;
    onClear?: () => void;
    onLayout?: ((event: LayoutChangeEvent) => void) | undefined;
    footerHeight?: number;
  } & BottomSheetDefaultFooterProps) => {
    const { bottom: bottomInset } = useSafeAreaInsets();
    const { space, colors } = useTheme();
    const { isWideScreen } = useWindowAdaptationHelpers();
    const { animatedKeyboardState } = useBottomSheetInternal();
    const [footerHeight, setFooterHeight] = useState(initialFooterHeight);
    const animatedStyle = useAnimatedStyle(
      () => ({
        transform: [
          {
            translateY:
              animatedKeyboardState.value === KEYBOARD_STATE.SHOWN
                ? footerHeight
                : 0,
          },
        ],
      }),
      [animatedKeyboardState.value, footerHeight],
    );

    return (
      <BottomSheetFooter {...props}>
        <Animated.View
          onLayout={e => {
            onLayout?.(e);
            setFooterHeight(e.nativeEvent.layout.height);
          }}
          style={[
            animatedStyle,
            {
              paddingTop: space[4],
              paddingBottom: space[4] + bottomInset,
              backgroundColor: colors.background,
              flexDirection: 'row',
              justifyContent: 'center',
            },
          ]}>
          <Row flex={1} gap={space[2]}>
            <Button
              text={'Reset'}
              variant="secondary"
              disabled={disabled}
              onPress={onClear}
              style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
              loading={loading}
            />
            <Button
              text={actionLabel}
              disabled={disabled}
              onPress={onDone}
              style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
              loading={loading}
            />
          </Row>
        </Animated.View>
      </BottomSheetFooter>
    );
  },
);

type ItemProps<V> = {
  value: V;
  label: string;
  subLabel?: string;
  selected: boolean;
  onSelect: (value: V) => void;
};

const Item = React.memo(
  <V,>({ value, label, subLabel, selected, onSelect }: ItemProps<V>) => {
    const { space, colors } = useTheme();
    return (
      <Pressable onPress={() => onSelect(value)}>
        <Row paddingY={space[2]} gap={space[1]}>
          <RadioButton
            value={String(value)}
            selected={selected}
            onSelect={() => onSelect(value)}
          />
          <Column flex={1}>
            <Typography.H7
              color={colors.palette.fwdDarkGreen[100]}
              children={label}
            />
            <Typography.H8
              color={colors.palette.fwdDarkGreen[50]}
              children={subLabel}
            />
          </Column>
        </Row>
      </Pressable>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

/**
 * Tablet use
 */
const TabletSearchInput = ({
  searchLabel,
  query: initialQuery,
  editable,
  onQuery,
  onClear,
}: {
  searchLabel?: string;
  query?: string;
  editable?: boolean;
  onQuery?: (text: string) => void;
  onClear?: () => void;
}) => {
  const [query, setQuery] = useState(initialQuery || '');
  const { space, colors, sizes } = useTheme();

  const onChangeText = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const latestOnQuery = useLatest(onQuery);

  useDebounceFn(
    () => {
      latestOnQuery.current?.(query);
    },
    500,
    [query],
  );

  const onClearFn = useCallback(() => {
    setQuery('');
    onClear?.();
  }, [onClear]);

  return (
    <TextField
      style={{ marginTop: 10, marginBottom: space[4] }}
      label={searchLabel}
      left={<Icon.Search fill={colors.placeholder} />}
      value={query}
      onChangeText={onChangeText}
      editable={editable}
      focusable={editable}
      right={
        query ? (
          <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={onClearFn}>
            <Icon.CloseCircle fill={colors.secondary} size={sizes[5]} />
          </TouchableOpacity>
        ) : null
      }
    />
  );
};

const TabletItem = React.memo(
  <V,>({ value, label, subLabel, selected, onSelect }: ItemProps<V>) => {
    const { space } = useTheme();
    return (
      <Pressable onPress={() => onSelect(value)}>
        <Row paddingY={space[2]} alignItems="center">
          <Row flex={1} gap={space[4]} alignItems="center">
            <RadioButton
              value={String(value)}
              selected={selected}
              onSelect={() => onSelect(value)}
            />
            <Typography.H7 children={label} />
          </Row>

          <Typography.H7 children={subLabel} style={{ flex: 1 }} />
        </Row>
      </Pressable>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

/**
 * Common
 */
const styles = StyleSheet.create({
  contentContainerStyle: {
    paddingHorizontal: 8,
  },
  smallContentContainerStyle: {
    paddingHorizontal: 4,
  },
});
