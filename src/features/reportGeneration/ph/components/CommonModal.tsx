import React from 'react';
import Modal from 'react-native-modal';
import {
  FlexStyle,
  StyleProp,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@emotion/react';
import { Button, Column, Icon, Row } from 'cube-ui-components';
import { SafeAreaView } from 'react-native-safe-area-context';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

/**
 * Basic modal component with a backdrop
 */
export function CommonModal({
  visible,
  width,
  disableKeyboardAvoidingViewBehavior,
  showCloseButton,
  onShow,
  onClose,
  children,
}: {
  visible: boolean;
  width?: FlexStyle['width'];
  disableKeyboardAvoidingViewBehavior?: boolean;
  showCloseButton?: boolean;
  onShow?: () => void;
  onClose: () => void;
  children: React.ReactNode;
}) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { colors, space, sizes, borderRadius } = useTheme();

  return (
    <Modal
      isVisible={visible}
      onShow={onShow}
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      backdropOpacity={0.5}
      onBackdropPress={() => onClose()}
      style={{ alignItems: 'center' }}>
      <KeyboardAvoidingView
        behavior={disableKeyboardAvoidingViewBehavior ? undefined : 'padding'}
        pointerEvents="box-none"
        style={{
          flex: 1,
          width: width ? width : '60%',
          justifyContent: 'center',
        }}>
        <View
          style={{
            borderRadius: borderRadius.large,
            backgroundColor: colors.background,
            //
            paddingTop: showCloseButton ? space[6] : space[12],
            paddingBottom: isTabletMode ? space[12] : space[6],
            paddingLeft: showCloseButton ? space[6] : space[12],
            paddingRight: showCloseButton ? space[6] : space[12],
          }}>
          {showCloseButton && (
            <TouchableOpacity
              onPress={() => onClose()}
              style={{ alignSelf: 'flex-end' }}>
              <Icon.Close size={sizes[6]} fill={colors.secondary} />
            </TouchableOpacity>
          )}
          <Column px={showCloseButton && isTabletMode ? space[6] : 0}>
            {children}
          </Column>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const BUTTON_WIDTH = 200;

export function ModalFormAction({
  hasShadow = true,
  primaryDisabled,
  primaryLoading,
  onPrimaryPress,
  primaryLabel = 'Next',
  primarySublabel,
  secondaryDisabled,
  secondaryLoading,
  secondaryLabel,
  onSecondaryPress,
  style,
  children,
}: {
  hasShadow?: boolean;
  primaryDisabled?: boolean;
  primaryLoading?: boolean;
  primaryLabel?: string;
  primarySublabel?: string;
  onPrimaryPress: () => void;
  secondaryDisabled?: boolean;
  secondaryLoading?: boolean;
  secondaryLabel?: string;
  onSecondaryPress?: () => void;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}) {
  const { space, colors } = useTheme();

  return (
    <SafeAreaView
      edges={['bottom']}
      style={[
        {
          padding: space[4],
          backgroundColor: colors.background,
          borderTopWidth: hasShadow ? 1 : 0,
          borderColor: colors.palette.fwdGrey[100],
          gap: space[4],
        },
        style,
      ]}>
      {children}
      <Row gap={space[4]} justifyContent="center">
        {secondaryLabel && (
          <Button
            variant="secondary"
            loading={secondaryLoading}
            disabled={secondaryDisabled}
            onPress={onSecondaryPress}
            text={secondaryLabel}
            style={{ width: BUTTON_WIDTH }}
          />
        )}
        <Button
          loading={primaryLoading}
          disabled={primaryDisabled}
          onPress={onPrimaryPress}
          text={primaryLabel}
          subtext={primarySublabel}
          style={{ width: BUTTON_WIDTH }}
        />
      </Row>
    </SafeAreaView>
  );
}
