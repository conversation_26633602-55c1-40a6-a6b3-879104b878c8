import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { Text, View } from 'react-native';
import { CommonModal } from './CommonModal';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function InformationModal({
  visible,
  setIsVisible,
  fullVersion, // For LapsedPoliciesReportScreen: Displaying lapsed policies tips + download password info
}: {
  visible: boolean;
  setIsVisible: (value: React.SetStateAction<boolean>) => void;
  fullVersion?: boolean;
}) {
  const { t } = useTranslation('reportGeneration');
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <CommonModal
      visible={visible}
      width={isTabletMode ? '60%' : '100%'}
      showCloseButton
      onClose={() => setIsVisible(false)}>
      {fullVersion ? (
        <>
          <Typography.H6
            fontWeight="bold"
            children={t('information')}
            style={{ marginBottom: space[4] }}
          />

          <Typography.H7
            fontWeight="bold"
            children={t('tips.title')}
            style={{ marginBottom: space[3] }}
          />
          <TipsContent />

          {/* Hide for the 1st phrase */}
          {/* <Separator />

              <Typography.H7
                fontWeight="bold"
                children={t('downloadPassword.title')}
                style={{ marginBottom: space[3] }}
              />
              <DownloadPasswordContent /> */}
        </>
      ) : (
        <>
          <Typography.H6
            fontWeight="bold"
            children={t('downloadPassword.title')}
            style={{ marginBottom: space[4] }}
          />
          <DownloadPasswordContent />
        </>
      )}
    </CommonModal>
  );
}

function TipsContent() {
  const { space } = useTheme();
  const { t } = useTranslation('reportGeneration');

  const TIPS = [
    // Hide for the 1st phrase
    // { category: '', tip: t('tips.message.1') },
    {
      category: t('premiumHoliday'),
      tip: t('tips.message.premiumHoliday'),
    },
    {
      category: t('anticipatedLapse'),
      tip: t('tips.message.anticipatedLapse'),
    },
    {
      category: t('lapsed'),
      tip: t('tips.message.Lapsed'),
    },
  ];

  return (
    <View style={{ gap: space[4] }}>
      {TIPS.map(({ category, tip }) => {
        return (
          <Row key={'tips_' + category} pr={space[2]} gap={space[2]}>
            <Dot />
            <Column>
              <Text>
                <Typography.Body
                  fontWeight="bold"
                  children={category + (category && ' : ')}
                />
                <Typography.Body children={tip} />
              </Text>
            </Column>
          </Row>
        );
      })}
    </View>
  );
}

function DownloadPasswordContent() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  return (
    <View style={{ gap: space[4] }}>
      <Typography.Body children={t('downloadPassword.message.1')} />
      <Text>
        <Typography.Body children={t('downloadPassword.message.2')} />
        <Typography.Body
          fontWeight="bold"
          children={' ddMmmyyyy (e.g. 21Jan1984)'}
          style={{ color: colors.primary }}
        />
      </Text>
      <Typography.Body children={t('downloadPassword.message.3')} />
    </View>
  );
}

const Dot = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.onBackground,
  width: 6,
  height: 6,
  borderRadius: theme.borderRadius.full,
  marginTop: theme.space[2], // adjust dot display position
}));

const Separator = styled.View(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginVertical: theme.space[6],
}));
