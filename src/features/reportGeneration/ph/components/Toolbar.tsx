import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, Column, H8, Icon, Row, SmallBody } from 'cube-ui-components';
import CheckboxOutlineSVG from 'features/reportGeneration/assets/CheckboxOutlineSVG';
import FocusedCheckboxSVG from 'features/reportGeneration/assets/FocusedCheckboxSVG';
import { useTranslation } from 'react-i18next';
import { ScrollView, View } from 'react-native';
import { type Status as StatusType } from 'types/report';
import SearchableDropdown from './ActionPanel/SearchableDropdown/SearchableDropdown';
import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

// const BAR_HEIGHT = 76; // 52(button height) + 24(padding vertical)
// const BTN_HEIGHT = 52;

function MainContainer({ children }: { children: React.ReactNode }) {
  const { space } = useTheme();
  return (
    <Column py={space[3]} gap={space[2]}>
      {children}
    </Column>
  );
}

function ScrollableContainer({ children }: { children: React.ReactNode }) {
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <ScrollView
      horizontal
      bounces={!isTabletMode}
      showsHorizontalScrollIndicator={false}
      // style={{ maxHeight: BAR_HEIGHT }}
      contentContainerStyle={{ paddingHorizontal: space[3], gap: space[1] }}>
      {children}
    </ScrollView>
  );
}

/**
 * Team checkbox/ button component for the toolbar
 */
function Team({ checked, onPress }: { checked: boolean; onPress: () => void }) {
  const { space, sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');
  return (
    <ButtonContainer
      focused={checked}
      onPress={onPress}
      style={{
        maxWidth: sizes[22],
        paddingLeft: space[2],
        paddingRight: space[3],
        paddingVertical: space[2],
        justifyContent: 'center',
      }}>
      <Row alignItems="center" gap={space[1]}>
        {checked ? <FocusedCheckboxSVG /> : <CheckboxOutlineSVG />}
        <Body children={t('toolbar.team')} />
      </Row>
    </ButtonContainer>
  );
}

/**
 * Reusable filter button component for the toolbar
 * For both mobile and tablet
 */
function ReportFilterButton({
  title,
  label,
  disabled,
  focused,
  onPress,
}: {
  title: string;
  label: string | undefined;
  focused?: boolean;
  disabled?: boolean;
  onPress: () => void;
}) {
  const { colors, space, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <ButtonContainer
      disabled={disabled}
      focused={focused}
      onPress={onPress}
      style={{
        flex: 1,
        paddingLeft: space[3],
        paddingRight: space[2],
        paddingVertical: space[2],
        justifyContent: 'center',
      }}>
      <Row alignItems="center" justifyContent="space-between" gap={space[1]}>
        <View
          style={{
            flexDirection: isTabletMode ? 'row' : 'column',
            alignItems: isTabletMode ? 'center' : undefined,
            gap: isTabletMode ? space[2] : 0,
          }}>
          <SmallBody
            children={title}
            color={
              disabled
                ? colors.palette.fwdGrey[100]
                : colors.palette.fwdDarkGreen[100]
            }
          />
          <Body
            fontWeight="medium"
            children={label ?? '--'}
            color={
              disabled
                ? colors.palette.fwdGrey[100]
                : colors.palette.fwdDarkGreen[100]
            }
          />
        </View>

        <Icon.ChevronDown
          fill={
            focused
              ? colors.primary
              : disabled
              ? colors.palette.fwdGrey[100]
              : colors.palette.fwdDarkGreen[100]
          }
          size={sizes[4]}
        />
      </Row>
    </ButtonContainer>
  );
}

/**
 * Toolbar buttons
 */
function SelectAgent({
  data,
  value,
  disabled,
  onChange,
}: {
  data: MemberInfo[];
  value?: string;
  disabled: boolean;
  onChange: (item: string) => void;
}) {
  const { t } = useTranslation('reportGeneration');
  return (
    <SearchableDropdown<MemberInfo, string>
      data={data}
      searchable
      disabled={disabled}
      title={t('toolbar.selectedAgent')}
      modalTitle={t('actionPanel.title.agent')}
      searchLabel={t('actionPanel.placeholder.agentNameAgentCode')}
      getItemLabel={item => item?.agentName}
      getItemSubLabel={item => `${item?.designation} - ${item?.agentCode}`}
      value={value}
      getItemValue={item => item?.agentCode}
      onChange={onChange}
      actionLabel={t('actionPanel.confirm')}
    />
  );
}

function Status({
  status,
  onPress,
}: {
  status: StatusType;
  onPress: () => void;
}) {
  const { t } = useTranslation('reportGeneration');

  const labelMapping = {
    LAPSED: t('lapsed'),
    PREMIUM_HOLIDAY: t('premiumHoliday'),
    ANTICIPATED_LAPSE: t('anticipatedLapse'),
  } as const;

  return (
    <ReportFilterButton
      title={t('toolbar.status')}
      label={labelMapping?.[status] ?? '--'}
      onPress={onPress}
    />
  );
}

function PolicyInfo({
  policyHolder,
  onPress,
}: {
  policyHolder?: string;
  onPress: () => void;
}) {
  const { t } = useTranslation('reportGeneration');
  return (
    <ReportFilterButton
      title={t('toolbar.policyInfo')}
      label={policyHolder ?? '--'}
      onPress={onPress}
    />
  );
}

function Date({
  title,
  duration,
  onPress,
}: {
  title?: string;
  duration?: string;
  onPress: () => void;
}) {
  return (
    <ReportFilterButton
      title={title ?? '--'}
      label={duration ?? '--'}
      onPress={onPress}
    />
  );
}

/**
 * Result(s) count
 */
function ResultCount({ count }: { count?: number }) {
  const { colors, space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('reportGeneration');
  return (
    <H8
      children={`${t('totalResult')} (${count ?? '--'})`}
      color={colors.palette.fwdGreyDarkest}
      style={{ paddingLeft: isTabletMode ? 0 : space[3] }}
    />
  );
}

/**
 * For both mobile and tablet
 * Result(s) count + reminder text display
 */
function DataGridInfo({
  count,
  showResultCount = true,
  showSeparator = true,
  showMaxCountReminder = true,
}: {
  count?: number;
  showResultCount?: boolean;
  showSeparator?: boolean;
  showMaxCountReminder?: boolean;
}) {
  const { colors, space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('reportGeneration');
  return (
    <View
      style={{
        flexDirection: isTabletMode ? 'row' : 'column',
        paddingLeft: isTabletMode ? space[3] : 0,
        paddingVertical: isTabletMode ? space[1] : 0,
        gap: isTabletMode ? 0 : space[2],
      }}>
      {/*  Result(s) count */}
      {showResultCount && (
        <H8
          children={`${t('totalResult')} (${count ?? '--'})`}
          color={colors.palette.fwdGreyDarkest}
          style={{ paddingLeft: isTabletMode ? 0 : space[3] }}
        />
      )}

      {/* Separator */}
      {showSeparator && isTabletMode && (
        <H8 children={'  |  '} color={colors.palette.fwdGreyDarkest} />
      )}

      {/* Max count reminder */}
      {showMaxCountReminder && (
        <H8
          children={'Displaying 4500 results max'}
          color={colors.palette.fwdGreyDarkest}
          style={{ paddingLeft: isTabletMode ? 0 : space[3] }}
        />
      )}
    </View>
  );
}

/**
 * Item separator
 */
function ItemSeparator() {
  const { colors, space } = useTheme();
  return <Row h={1} bgColor={colors.palette.fwdGrey[100]} my={space[3]} />;
}

export const Toolbar = {
  MainContainer,
  ScrollableContainer,
  //
  ReportFilterButton,
  SelectAgent,
  Team,
  Status,
  PolicyInfo,
  Date,
  //
  ResultCount,
  DataGridInfo,
  //
  ItemSeparator,
};

/**
 * Helper function to handle policy holder/number display
 */
export const policyInfoHandler = (policyHolder: {
  policyHolderName: string;
  policyNumber: string;
}) => {
  const { policyHolderName, policyNumber } = policyHolder;

  if (!policyHolderName && !policyNumber) return 'All';

  let result = policyHolderName ? policyHolderName : '';

  if (policyNumber) result = `${result}${result && ', '}${policyNumber}`;

  return result;
};

const ButtonContainer = styled.TouchableOpacity<{
  focused?: boolean;
}>(({ theme, focused }) => ({
  // height: BTN_HEIGHT,
  borderWidth: focused ? 2 : 1,
  borderColor: focused
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.small,
  backgroundColor: focused
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  margin: focused ? 0 : 1, // UI handling to prevent border width change
}));
