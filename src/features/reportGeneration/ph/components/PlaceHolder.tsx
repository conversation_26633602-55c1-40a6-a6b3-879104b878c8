import React from 'react';
import { useTheme } from '@emotion/react';
import {
  Body,
  Column,
  LargeLabel,
  LoadingIndicator,
  Icon,
} from 'cube-ui-components';
import NoRecordSVG from 'features/policy/assets/NoRecordSVG';

const PADDING_TOP = 80; // space[20]
const ICON_AND_MESSAGE_GAP = 16; // space[4]

export const DATA_GRID_LOADING_HEADER = Array.from(
  { length: 15 },
  (_, index) => {
    if (index === 0) return { type: `dummy_header_${index}`, title: '--/\n--' };
    return { type: `dummy_header_${index}`, title: '--' };
  },
);

function Loading() {
  const { colors, sizes } = useTheme();
  return (
    <Column
      flex={1}
      bgColor={colors.background}
      alignItems="center"
      pt={PADDING_TOP}
      gap={ICON_AND_MESSAGE_GAP}>
      <LoadingIndicator size={sizes[12]} />
      <LargeLabel
        children={'Please wait we are retrieving data'}
        color={colors.placeholder}
      />
    </Column>
  );
}

function LoadingError() {
  const { colors, sizes } = useTheme();
  return (
    <Column
      flex={1}
      bgColor={colors.background}
      alignItems="center"
      pt={PADDING_TOP}
      gap={ICON_AND_MESSAGE_GAP}>
      <Icon.WarningFill size={sizes[12]} fill={colors.palette.alertRed} />
      <LargeLabel
        children={'Data retrieved failed. Please visit again later'}
        color={colors.placeholder}
      />
    </Column>
  );
}

function EmptyRecord() {
  const { colors } = useTheme();
  return (
    <Column alignItems="center" pt={PADDING_TOP} gap={ICON_AND_MESSAGE_GAP}>
      <NoRecordSVG />
      <Body children={'Empty record'} color={colors.palette.fwdGreyDarker} />
    </Column>
  );
}

export const Placeholder = {
  Loading,
  LoadingError,
  EmptyRecord,
};
