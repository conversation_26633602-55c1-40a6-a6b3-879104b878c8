import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { AnimatedFlashList } from '@shopify/flash-list';
import { Column, Icon, Label, LargeLabel, Row } from 'cube-ui-components';
import { SolidArrow } from 'features/reportGeneration/assets/SolidArrowSVG';
import useCheckIsLeader from 'features/reportGeneration/hooks/useCheckIsLeader';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import _ from 'lodash';
import React, {
  memo,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Animated,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { RootStackParamList } from 'types';
import { PolicyStatus } from 'types/policy';
import { ReportItem } from 'types/report';
import { formatCurrency } from 'utils';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { endLandscape, ToolbarContext } from '../../ToolbarProvider';
import { DATA_GRID_LOADING_HEADER, Placeholder } from '../PlaceHolder';
import {
  CELL_HEIGHT,
  CELL_WIDTH,
  CELL_WIDTH_FACTOR_LARGE,
  CELL_WIDTH_FACTOR_SMALL,
  reorderObjectKeys,
  type Header,
} from './utils';

const IS_PH = country === 'ph';
const IS_MY = country === 'my';
const IS_IB = country === 'ib';

const SHOW_CURRENCY = IS_PH;

export default function ReportDataGrid({
  darkMode,
  isLoading,
  isError,
  data = [],
  freezeHeader = [],
  headers = [],
  cellRenderOrder = [],
}: {
  darkMode: boolean;
  isLoading: boolean;
  isError: boolean;
  data?: ReportItem[];
  freezeHeader: Header[];
  headers: Header[];
  cellRenderOrder: string[];
}) {
  const { colors } = useTheme();

  const { updateShowToolbar } = useContext(ToolbarContext);

  const prevScrollY = useRef(0);
  const freezeScrollY = useRef(new Animated.Value(0)).current;
  const scrollY = useRef(new Animated.Value(0)).current;

  const freezeColScrollRef = useRef<any>(null);
  const colScrollRef = useRef<any>(null);

  const activeColRef = useRef<'freeze' | 'normal' | undefined>();

  const onFreezeScroll = useMemo(
    () =>
      Animated.event(
        [{ nativeEvent: { contentOffset: { y: freezeScrollY } } }],
        {
          useNativeDriver: true,
          listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
            const offsetY = event.nativeEvent.contentOffset.y;
            if (activeColRef.current === 'freeze' && colScrollRef.current) {
              colScrollRef.current.scrollToOffset({
                offset: offsetY,
                animated: false,
              });
            }

            if (offsetY < prevScrollY.current || offsetY <= 0) {
              updateShowToolbar(true);
            } else if (offsetY >= prevScrollY.current) {
              updateShowToolbar(false);
            }

            prevScrollY.current = offsetY;
          },
        },
      ),
    [freezeScrollY, updateShowToolbar],
  );

  const onScroll = useMemo(
    () =>
      Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
        useNativeDriver: true,
        listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
          const offsetY = event.nativeEvent.contentOffset.y;
          if (activeColRef.current === 'normal' && freezeColScrollRef.current) {
            freezeColScrollRef.current.scrollToOffset({
              offset: offsetY,
              animated: false,
            });
          }
        },
      }),
    [scrollY],
  );

  const onFreezeScrollBeginDrag = useCallback(
    () => (activeColRef.current = 'freeze'),
    [],
  );
  const onScrollBeginDrag = useCallback(
    () => (activeColRef.current = 'normal'),
    [],
  );
  const onScrollAnimationEnd = useCallback(
    () => (activeColRef.current = undefined),
    [],
  );

  /**
   * Sorting
   */
  const [sortedColumn, setSortedColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (columnType: string) => {
    if (sortedColumn === columnType) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortedColumn(columnType);
      setSortDirection('desc');
    }
  };

  const getSortKey = (column: string) => {
    // Handling empty space and upper/lower case for sorting
    if (column === 'policyHolderName' || column === 'insuredName') {
      return (data: ReportItem) => data?.[column]?.trim()?.toLowerCase();
    }
    // Convert string to number for sorting
    if (
      column === 'sumInsured' ||
      column === 'rtu' ||
      column === 'basicPremium' ||
      column === 'riderPremium' ||
      column === 'salesAPE' ||
      column === 'singlePremium' ||
      column === 'policyTerm' ||
      column === 'premiumPaymentTerm'
    ) {
      return (data: ReportItem) => Number(data?.[column]);
    }
    return column;
  };

  const sortedData = useMemo(() => {
    if (!sortedColumn) return data;

    const sortKey = getSortKey(sortedColumn);

    return _.orderBy(data, [sortKey, sortedColumn], [sortDirection]);
  }, [data, sortedColumn, sortDirection]);

  if (isLoading) {
    return (
      <Column flex={1}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={DATA_GRID_LOADING_HEADER}
          showSortIcon={false}
        />
        <Placeholder.Loading />
      </Column>
    );
  }

  if (isError) {
    return (
      <Column flex={1}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={DATA_GRID_LOADING_HEADER}
          showSortIcon={false}
        />
        <Placeholder.LoadingError />
      </Column>
    );
  }

  if (_.isEmpty(data)) {
    return <Placeholder.EmptyRecord />;
  }

  return (
    <Row flex={1}>
      <Column
        style={{
          backgroundColor: colors.palette.fwdGreyDark[100],
          shadowColor: colors.palette.fwdGreyDark[100],
          shadowOffset: { width: 3, height: -1 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          zIndex: 1,
        }}>
        <MemoizedHeaderComponent
          darkMode={darkMode}
          headerArray={freezeHeader}
          showSortIcon
          onHeaderPress={handleSort}
          sortedColumn={sortedColumn}
          sortDirection={sortDirection}
        />
        <AnimatedFlashList
          ref={freezeColScrollRef}
          data={sortedData}
          keyExtractor={(item, index) =>
            `freezeCol_${item?.policyNumber}_${index}`
          }
          renderItem={({ item, index }) => (
            <MemoizedFreezeCell item={item} rowIndex={index} />
          )}
          ItemSeparatorComponent={() => <Separator />}
          ListFooterComponent={() => (
            <>
              <Separator />
              <Row h={CELL_HEIGHT * 2} />
            </>
          )}
          nestedScrollEnabled
          bounces={false}
          showsVerticalScrollIndicator={false}
          onScroll={onFreezeScroll}
          scrollEventThrottle={16}
          estimatedItemSize={CELL_HEIGHT + 1} // add 1 to the height to account for the separator
          disableAutoLayout
          removeClippedSubviews
          onScrollBeginDrag={onFreezeScrollBeginDrag}
          onScrollAnimationEnd={onScrollAnimationEnd}
        />
      </Column>

      <ScrollView
        horizontal
        nestedScrollEnabled
        bounces={false}
        showsHorizontalScrollIndicator={false}>
        <Column>
          <MemoizedHeaderComponent
            darkMode={darkMode}
            headerArray={headers}
            showSortIcon
            onHeaderPress={handleSort}
            sortedColumn={sortedColumn}
            sortDirection={sortDirection}
          />
          <AnimatedFlashList
            ref={colScrollRef}
            data={sortedData}
            keyExtractor={(item, index) => `col_${item?.policyNumber}_${index}`}
            renderItem={({ item, index }) => (
              <MemoizedRowComponent
                item={item}
                rowIndex={index}
                cellRenderOrder={cellRenderOrder}
              />
            )}
            ItemSeparatorComponent={() => <Separator />}
            ListFooterComponent={() => (
              <>
                <Separator />
                <Row h={CELL_HEIGHT * 2} />
              </>
            )}
            nestedScrollEnabled
            bounces={false}
            showsVerticalScrollIndicator={false}
            onScroll={onScroll}
            scrollEventThrottle={16}
            estimatedItemSize={CELL_HEIGHT + 1} // add 1 to the height to account for the separator
            disableAutoLayout
            removeClippedSubviews
            onScrollBeginDrag={onScrollBeginDrag}
            onScrollAnimationEnd={onScrollAnimationEnd}
          />
        </Column>
      </ScrollView>
    </Row>
  );
}

const MemoizedHeaderComponent = memo(function HeaderComponent({
  darkMode,
  headerArray,
  showSortIcon,
  onHeaderPress,
  sortedColumn,
  sortDirection,
}: {
  darkMode: boolean;
  headerArray: Header[];
  showSortIcon?: boolean;
  onHeaderPress?: (columnType: string) => void;
  sortedColumn?: string | null;
  sortDirection?: 'asc' | 'desc';
}) {
  const { colors, space } = useTheme();
  const { isLeader } = useCheckIsLeader();

  return (
    <Row bgColor={darkMode ? colors.palette.fwdDarkGreen[100] : colors.primary}>
      {headerArray?.map(({ type, title }) => {
        const width = factorizeCellWidth(type);

        if ((type === 'agentCode' || type === 'agentName') && !isLeader) {
          return null;
        }

        return (
          <TouchableOpacity
            key={type}
            onPress={onHeaderPress && (() => onHeaderPress(type))}
            style={{
              flexDirection: 'row',
              width: width,
              height: CELL_HEIGHT,
              padding: space[3],
              backgroundColor: darkMode
                ? colors.palette.fwdDarkGreen[100]
                : colors.primary,
              alignItems: 'center',
              gap: space[1],
            }}>
            <Label children={title} color={colors.onPrimary} />

            {showSortIcon && sortedColumn === type ? (
              sortDirection === 'asc' ? (
                darkMode ? (
                  <Icon.SolidArrowUp />
                ) : (
                  <SolidArrow.UpLight />
                )
              ) : darkMode ? (
                <Icon.SolidArrowDown />
              ) : (
                <SolidArrow.DownLight />
              )
            ) : darkMode ? (
              <Icon.SolidArrowUp fill={colors.palette.fwdDarkGreen[50]} />
            ) : (
              <SolidArrow.UpLight fill={colors.palette.fwdOrange[50]} />
            )}
          </TouchableOpacity>
        );
      })}
    </Row>
  );
});

const MemoizedRowComponent = memo(function RowComponent({
  item,
  rowIndex,
  cellRenderOrder,
}: {
  item: ReportItem;
  rowIndex: number;
  cellRenderOrder: string[];
}) {
  const { colors } = useTheme();
  const { isLeader } = useCheckIsLeader();

  const currency = item?.currency ?? '';
  const orderedItem = reorderObjectKeys(item, cellRenderOrder) ?? {};

  return (
    <Row
      bgColor={
        rowIndex % 2 === 0 ? colors.background : colors.palette.fwdGrey[20]
      }>
      {Object.entries(orderedItem).map(([key, value], index) => {
        if ((key === 'agentCode' || key === 'agentName') && !isLeader) {
          return null;
        }

        if (
          key === 'premiumDueDate' ||
          key === 'submissionDate' ||
          key === 'issueDate' ||
          key === 'surrenderDate' ||
          key === 'transactionDate' ||
          key === 'expiredDate' ||
          key === 'registrationDate' ||
          key === 'decisionDate' ||
          key === 'lastInstallmentDate' ||
          key === 'biroTxnDate'
        ) {
          return (
            <MemoizedCell
              key={index}
              dataKey={key}
              data={dateFormatUtil(value)}
            />
          );
        }

        if (
          key === 'sumInsured' ||
          key === 'rtu' ||
          key === 'basicPremium' ||
          key === 'riderPremium' ||
          key === 'salesAPE' ||
          key === 'singlePremium' ||
          key === 'amount' ||
          key === 'ace' ||
          key === 'modalPremium' ||
          key === 'duePremium'
        ) {
          if (SHOW_CURRENCY) {
            return (
              <MemoizedCell
                key={index}
                dataKey={key}
                data={`${currency} ${formatCurrency(value, 0)}`}
              />
            );
          }
          return (
            <MemoizedCell
              key={index}
              dataKey={key}
              data={formatCurrency(value, 0)}
            />
          );
        }

        if (key === 'currency') {
          return (
            <MemoizedCell
              key={index}
              dataKey={key}
              data={IS_PH ? value : IS_MY || IS_IB ? 'RM' : '--'}
            />
          );
        }

        return <MemoizedCell key={index} dataKey={key} data={value} />;
      })}
    </Row>
  );
});

const MemoizedFreezeCell = memo(function FreezeCell({
  item,
  rowIndex,
}: {
  item: ReportItem;
  rowIndex: number;
}) {
  const { space, colors, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { updateLandscape } = useContext(ToolbarContext);

  // Check if the policy is downline policy
  const agentId = useBoundStore(state => state.auth.agentCode);
  const isDownlinePolicy = agentId !== item?.agentCode;

  const onPressItem = (item: ReportItem) => {
    if (!isTabletMode) {
      endLandscape();
      updateLandscape(false);
    }

    if (IS_MY) {
      navigation.navigate('ExistingPolicyDetail', {
        type: 'policy',
        policyId: item?.policyNumber,
        status: (item?.status as PolicyStatus) ?? 'due',
      });
    } else {
      navigation.navigate('PoliciesPOS', {
        screen: 'POSDetail',
        params: {
          type: 'policy',
          policyId: item?.policyNumber,
          status: 'NBPending',
        },
      });
    }
  };

  return (
    <TouchableOpacity
      disabled={isDownlinePolicy}
      onPress={() => onPressItem(item)}>
      <Row
        w={CELL_WIDTH}
        h={CELL_HEIGHT}
        py={space[3]}
        alignItems="center"
        bgColor={
          rowIndex % 2 === 0 ? colors.background : colors.palette.fwdGrey[20]
        }>
        <Label
          children={String(rowIndex + 1)}
          color={colors.palette.fwdDarkGreen[50]}
          style={{ width: space[10], textAlign: 'center' }}
        />
        <Column gap={2}>
          <Row gap={space[1]} alignItems="center">
            <Icon.Document
              size={sizes[4]}
              fill={
                isDownlinePolicy
                  ? colors.palette.fwdDarkGreen[100]
                  : colors.palette.fwdAlternativeOrange[100]
              }
            />
            <Label
              fontWeight={isDownlinePolicy ? 'normal' : 'medium'}
              children={item?.policyNumber ?? '--'}
              color={
                isDownlinePolicy
                  ? colors.palette.fwdDarkGreen[100]
                  : colors.palette.fwdAlternativeOrange[100]
              }
            />
          </Row>
          <LargeLabel
            fontWeight="medium"
            children={item?.policyHolderName ?? '--'}
            style={{ width: CELL_WIDTH - space[10] - space[3] }} // space[10] is the width of the row number width, space[3] is the padding right
            numberOfLines={2}
            ellipsizeMode="tail"
          />
        </Column>
      </Row>
    </TouchableOpacity>
  );
});

const MemoizedCell = memo(function Cell({
  dataKey,
  data,
}: {
  dataKey: string;
  data: number | string;
}) {
  const { space } = useTheme();

  const width = factorizeCellWidth(dataKey);

  return (
    <Column w={width} h={CELL_HEIGHT} p={space[3]} justifyContent="center">
      <LargeLabel
        children={data ?? '--'}
        numberOfLines={2}
        ellipsizeMode="tail"
      />
    </Column>
  );
});

const factorizeCellWidth = (type: string) => {
  if (type === 'agentCode' || type === 'branch' || (type === 'status' && IS_PH))
    return CELL_WIDTH * CELL_WIDTH_FACTOR_SMALL;

  if (
    type === 'product' ||
    type === 'accountHolderName' ||
    type === 'address' ||
    type === 'policyHolderEmail'
  )
    return CELL_WIDTH * CELL_WIDTH_FACTOR_LARGE;

  return CELL_WIDTH;
};

const Separator = () => {
  const { colors } = useTheme();
  return <Row h={1} bgColor={colors.palette.fwdGrey[100]} />;
};
