/**
 * This file contains the configuration for the data grid in report generation - Unsuccessful ADA/ACA.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Policy number/\nPolicy holder name',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'adaAcaActivation',
    title: 'ADA/ACA Activation',
  },
  {
    type: 'failureReason',
    title: 'Failure reason',
  },
  {
    type: 'transactionDate',
    title: 'Transaction date',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'insuredName',
    title: 'Insured name',
  },
  {
    type: 'premiumDueDate',
    title: 'Premium due date',
  },
  {
    type: 'issueDate',
    title: 'Issue date',
  },
  {
    type: 'currency',
    title: 'Currency',
  },
  {
    type: 'amount',
    title: 'Amount\n(PHP/USD)',
  },
  {
    type: 'riderPremium',
    title: 'Rider premium\n(PHP/USD)',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'paymentMethod',
    title: 'Payment method',
  },
  {
    type: 'accountHolderName',
    title: 'Account holder name',
  },
  {
    type: 'cardNumber',
    title: 'Card number',
  },
  {
    type: 'bankName',
    title: 'Bank name',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
