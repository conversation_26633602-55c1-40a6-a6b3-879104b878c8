/**
 * This file contains the configuration for the data grid in report generation - Credit card expiration.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Policy number/\nPolicy holder name',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'cardHolderName',
    title: 'Card holder name',
  },
  {
    type: 'cardNumber',
    title: 'Card number',
  },
  {
    type: 'expiredDate',
    title: 'Expired date',
  },
  {
    type: 'premiumDueDate',
    title: 'Premium due date',
  },
  {
    type: 'issueDate',
    title: 'Issue date',
  },
  {
    type: 'currency',
    title: 'Currency',
  },
  {
    type: 'basicPremium',
    title: 'Basic premium\n(PHP/USD)',
  },
  {
    type: 'riderPremium',
    title: 'Rider premium\n(PHP/USD)',
  },
  {
    type: 'rtu',
    title: 'RTU',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'paymentMethod',
    title: 'Payment method',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'insuredName',
    title: 'Insured name',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
