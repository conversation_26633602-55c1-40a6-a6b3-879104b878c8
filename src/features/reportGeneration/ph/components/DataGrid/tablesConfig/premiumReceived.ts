/**
 * This file contains the configuration for the data grid in report generation - Premium received.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Policy number/\nPolicy holder name',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'insuredName',
    title: 'Insured name',
  },
  {
    type: 'issueDate',
    title: 'Issue date',
  },
  {
    type: 'transactionDate',
    title: 'Transaction date',
  },
  // Hide Surrender date column - CUBEPH-3923
  // {
  //   type: 'surrenderDate',
  //   title: 'Surrender date',
  // },
  {
    type: 'currency',
    title: 'Currency',
  },
  {
    type: 'basicPremium',
    title: 'Basic premium\n(PHP/USD)',
  },
  {
    type: 'singlePremium',
    title: 'Single premium\n(PHP/USD)',
  },
  {
    type: 'riderPremium',
    title: 'Rider premium\n(PHP/USD)',
  },
  {
    type: 'rtu',
    title: 'RTU',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'paymentMethod',
    title: 'Payment method',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
