/**
 * This file contains the configuration for the data grid in report generation - Lapse report.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Policy number/\nPolicy holder name',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'branch',
    title: 'Branch',
  },
  {
    type: 'status',
    title: 'Status',
  },
  {
    type: 'premiumDueDate',
    title: 'Premium due date',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'insuredName',
    title: 'Insured name',
  },
  {
    type: 'submissionDate',
    title: 'Submission date',
  },
  {
    type: 'issueDate',
    title: 'Issue date',
  },
  {
    type: 'surrenderDate',
    title: 'Surrender date',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'sumInsured',
    title: 'Sum insured\n(PHP/USD)',
  },
  {
    type: 'currency',
    title: 'Currency',
  },
  {
    type: 'rtu',
    title: 'RTU',
  },
  {
    type: 'basicPremium',
    title: 'Basic premium\n(PHP/USD)',
  },
  {
    type: 'riderPremium',
    title: 'Rider premium\n(PHP/USD)',
  },
  {
    type: 'salesAPE',
    title: 'Sales APE\n(PHP/USD)',
  },
  {
    type: 'singlePremium',
    title: 'Single premium\n(PHP/USD)',
  },
  {
    type: 'paymentMethod',
    title: 'Payment method',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
