// Styles - Assuming each cell has a fixed width for simplicity
export const CELL_WIDTH = 164;
export const CELL_HEIGHT = 80;

export const CELL_WIDTH_FACTOR_SMALL = 0.75; // For branch and status cell
export const CELL_WIDTH_FACTOR_LARGE = 1.25; // For product cell

// Header configuration
export interface Header {
  type: string;
  title: string;
}

// Reorder object keys by array
export const reorderObjectKeys = (
  obj: Record<string, any>,
  order: string[],
) => {
  if (!obj || !order) return;

  const orderedObj: Record<string, any> = {};

  order?.forEach(key => {
    orderedObj[key] = Object.prototype.hasOwnProperty.call(obj, key)
      ? obj[key]
      : undefined;
  });

  return orderedObj;
};
