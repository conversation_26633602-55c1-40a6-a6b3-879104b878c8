import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function CheckboxOutlineSVG(props: SvgIconProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.364 4H5.636C4.733 4 4 4.733 4 5.636v12.728C4 19.267 4.733 20 5.636 20h12.728c.903 0 1.636-.733 1.636-1.636V5.636C20 4.733 19.267 4 18.364 4zM5.636 2A3.636 3.636 0 002 5.636v12.728A3.636 3.636 0 005.636 22h12.728A3.636 3.636 0 0022 18.364V5.636A3.636 3.636 0 0018.364 2H5.636z"
        fill="#B3B6B8"
      />
    </Svg>
  );
}
