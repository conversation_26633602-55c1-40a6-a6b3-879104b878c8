import Svg, { Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

function SolidArrowUpLightSVG(props: SvgIconProps): JSX.Element {
  const { colors } = useTheme();
  return (
    <Svg
      width={props.width || props.size || 16}
      height={props.height || props.size || 16}
      viewBox="0 0 16 16"
      fill="none">
      <Path
        d="M7.42195 0.299794C7.71651 -0.0999326 8.28349 -0.099932 8.57805 0.299795L11.841 4.72776C12.2209 5.24337 11.8772 6 11.2629 6L4.73707 6C4.12284 6 3.77907 5.24337 4.15902 4.72776L7.42195 0.299794Z"
        fill={props.fill ?? colors.palette.white}
        fillRule="evenodd"
        clipRule="evenodd"
      />
      <Path
        d="M8.57805 15.7002C8.28349 16.0999 7.71651 16.0999 7.42195 15.7002L4.15902 11.2722C3.77907 10.7566 4.12284 10 4.73707 10L11.2629 10C11.8772 10 12.2209 10.7566 11.841 11.2722L8.57805 15.7002Z"
        fill={colors.palette.fwdOrange[50]}
      />
    </Svg>
  );
}

function SolidArrowDownLightSVG(props: SvgIconProps) {
  const { colors } = useTheme();
  return (
    <Svg
      width={props.width || props.size || 16}
      height={props.height || props.size || 16}
      viewBox="0 0 16 16"
      fill="none">
      <Path
        d="M7.42195 0.299794C7.71651 -0.0999326 8.28349 -0.099932 8.57805 0.299795L11.841 4.72776C12.2209 5.24337 11.8772 6 11.2629 6L4.73707 6C4.12284 6 3.77907 5.24337 4.15902 4.72776L7.42195 0.299794Z"
        fill={colors.palette.fwdOrange[50]}
        fillRule="evenodd"
        clipRule="evenodd"
      />
      <Path
        d="M8.57805 15.7002C8.28349 16.0999 7.71651 16.0999 7.42195 15.7002L4.15902 11.2722C3.77907 10.7566 4.12284 10 4.73707 10L11.2629 10C11.8772 10 12.2209 10.7566 11.841 11.2722L8.57805 15.7002Z"
        fill={props.fill ?? colors.palette.white}
      />
    </Svg>
  );
}

export const SolidArrow = {
  UpLight: SolidArrowUpLightSVG,
  DownLight: SolidArrowDownLightSVG,
};
