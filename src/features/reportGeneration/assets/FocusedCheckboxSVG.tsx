import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

export default function FocusedCheckboxSVG(props: SvgIconProps) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5 13.329l3.921 3.921L19.51 6.662l-.296-.296a1.25 1.25 0 00-1.767 0l-8.526 8.526-1.858-1.859a1.25 1.25 0 00-1.767 0L5 13.33z"
        fill="#fff"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.636 2A3.636 3.636 0 002 5.636v12.728A3.636 3.636 0 005.636 22h12.728A3.636 3.636 0 0022 18.364V5.636A3.636 3.636 0 0018.364 2H5.636zM5 13.329l3.921 3.921L19.51 6.662l-.296-.296a1.25 1.25 0 00-1.767 0l-8.526 8.526-1.858-1.859a1.25 1.25 0 00-1.767 0L5 13.33z"
        fill="#E87722"
      />
    </Svg>
  );
}
