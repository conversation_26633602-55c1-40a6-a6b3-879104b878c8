import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { InferType, array, boolean, object, ref, string } from 'yup';
import { requiredMessage } from '../constants/cffErrorMessages';
import { MY_CHILD_RELATIONSHIP } from 'constants/optionList';
import { mysAnnualIncomeAmount, name, nullableDate } from 'features/eApp/validations/eAppCommonSchema';

export type ChildDependentDetailFormSchemaType = InferType<
  typeof childDependentDetailFormRequiredSchema
>;

export const { defaultDate: dateOfBirthDefaultDate } =
  getDateOfBirthDropdownProps();

export const initialChildDependentDetailFormData: ChildDependentDetailFormSchemaType =
  {
    //child
    id: '',
    fullName: '',
    relationship: '',
    yearToSupport: '',
    gender: '',
    primaryIdType: '',
    additionalIDType: '',
    maritalStatus: '',
    annualIncome: '',
    identificationNumber: '',
    additionalIdentification: '',
    dob: null,
    occupation: MY_CHILD_RELATIONSHIP,
    annualIncomeAmount: '',
    nationality: '',
    countryOfBirth: '',
  };

export const childDependentDetailFormRequiredSchema = object({
  id: string(),
  isMainInsured: boolean(),
  dob: nullableDate(),
  primaryIdType: string(),
  additionalIDType: string(),
  maritalStatus: string(),
  gender: string().required(requiredMessage),
  identificationNumber: string(),
  additionalIdentification: string().test(
    'required-additional-id',
    requiredMessage,
    (value, ctx) => {
      const additionalIdType = ctx.resolve(ref('additionalIDType'));
      if (additionalIdType) return Boolean(value);
      return true;
    },
  ),
  occupation: string(),
  occupationDescription: string(),
  annualIncome: string(),
  annualIncomeAmount: mysAnnualIncomeAmount(
    'annualIncome',
    'annualIncomeAmount',
  ),
  fullName: name(),
  relationship: string().required(requiredMessage),
  yearToSupport: string().required(requiredMessage),
  nationality: string(),
  countryOfBirth: string(),
});

export const childDependentDetailFormValidationSchema = object({
  data: array(childDependentDetailFormRequiredSchema).required(),
});

export type ChildDependentDetailFormValidationSchema = InferType<
  typeof childDependentDetailFormValidationSchema
>;
