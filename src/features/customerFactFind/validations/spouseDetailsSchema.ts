import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { boolean, InferType, object, ref, string } from 'yup';
import { requiredMessage } from '../constants/cffErrorMessages';
import { formPhoneNumber } from 'utils/validation/customValidation';
import {
  CORRESPONDENCE_ADDRESS_OPTION,
  MY_COUNTRY,
  MY_MOBILE_CODE,
  OWNER_ADDRESS_OPTION,
} from 'constants/optionList';
import {
  mysAddressSchema,
  mysAnnualIncomeAmount,
  mysIdNumber,
  name,
  normalizedSpaceString,
  nullableDate,
  occupationDetailRequired,
} from 'features/eApp/validations/eAppCommonSchema';
import { invalidFormatMessage } from 'features/eApp/validations/eAppErrorMessages';
import { MAX_EMAIL_LENGTH } from 'features/coverageDetails/validation/common/constant';

export type SpouseDetailFormSchemaType = InferType<
  typeof spouseDetailFormValidationSchema
>;
export const { defaultDate: dateOfBirthDefaultDate } =
  getDateOfBirthDropdownProps();

export const initialSpouseDetailFormData: SpouseDetailFormSchemaType = {
  id: '',
  relationship: '',
  title: '',
  smokingHabit: '',
  maritalStatus: '',
  dob: null,
  primaryIdType: '',
  additionalIDType: '',
  source: '',
  fullName: '',
  gender: '',
  ethnicity: '',
  religion: '',
  identificationNumber: '',
  additionalIdentification: '',
  //nationality details
  nationality: MY_COUNTRY,
  countryOfBirth: '',
  stateOfBirth: '',
  cityOfBirth: '',
  cityName: '',
  //occupation details
  occupation: '',
  occupationGroup: '',
  occupationDescription: '',
  nameOfBusiness: '',
  exactDuties: '',
  annualIncomeAmount: '',
  natureOfWork: '',
  annualIncome: '',
  //contact details
  mobileCountryCode: MY_MOBILE_CODE,
  email: '',
  mobileNumber: '',
  homeCountryCode: MY_MOBILE_CODE,
  homeNumber: '',
  officeCountryCode: MY_MOBILE_CODE,
  officeNumber: '',
  //address information
  correspondenceAddress: OWNER_ADDRESS_OPTION,
  correspondenceAddressLine1: '',
  correspondenceAddressLine2: '',
  correspondenceAddressLine3: '',
  correspondencePostCode: '',
  correspondenceCity: '',
  correspondenceState: '',
  correspondenceCountry: MY_COUNTRY,
  residentialAddress: CORRESPONDENCE_ADDRESS_OPTION,
  residentialAddressLine1: '',
  residentialAddressLine2: '',
  residentialAddressLine3: '',
  residentialPostCode: '',
  residentialCity: '',
  residentialState: '',
  residentialCountry: MY_COUNTRY,
  businessAddress: '',
  businessAddressLine1: '',
  businessAddressLine2: '',
  businessAddressLine3: '',
  businessPostCode: '',
  businessCity: '',
  businessState: '',
  businessCountry: MY_COUNTRY,
};

export const spouseAddressInfoValidationSchema = object({
  ...mysAddressSchema(
    ['correspondence', 'residential', 'business'],
    'occupationGroup',
  ),
});

export const spouseDetailFormValidationSchema = object({
  id: string(),
  isMainInsured: boolean(),
  title: string().required(requiredMessage),
  smokingHabit: string().required(requiredMessage),
  relationship: string(),
  maritalStatus: string().required(requiredMessage),
  dob: nullableDate(),
  primaryIdType: string().required(requiredMessage),
  additionalIDType: string(),
  source: string().required(requiredMessage),
  fullName: name().required(requiredMessage),
  gender: string().required(requiredMessage),
  ethnicity: string().required(requiredMessage),
  religion: string().required(requiredMessage),
  identificationNumber: mysIdNumber('primaryIdType', 'dob', 'gender').required(
    requiredMessage,
  ),
  additionalIdentification: normalizedSpaceString().test(
    'required-additional-id',
    requiredMessage,
    (value, ctx) => {
      const additionalIdType = ctx.resolve(ref('additionalIDType'));
      if (additionalIdType) return Boolean(value);
      return true;
    },
  ),
  nationality: string().required(requiredMessage),
  countryOfBirth: string().required(requiredMessage),
  stateOfBirth: string().required(requiredMessage),
  cityOfBirth: string().required(requiredMessage),
  cityName: normalizedSpaceString().required(requiredMessage),
  occupation: string().required(requiredMessage),
  occupationGroup: string(),
  occupationDescription: string().required(requiredMessage),
  nameOfBusiness: occupationDetailRequired('occupationGroup'),
  exactDuties: occupationDetailRequired('occupationGroup'),
  annualIncomeAmount: mysAnnualIncomeAmount(
    'annualIncome',
    'annualIncomeAmount',
  ),
  natureOfWork: occupationDetailRequired('occupationGroup'),
  annualIncome: occupationDetailRequired('occupationGroup'),
  email: normalizedSpaceString()
    .trim()
    .lowercase()
    .max(MAX_EMAIL_LENGTH, invalidFormatMessage)
    .email(invalidFormatMessage)
    .required(requiredMessage),
  mobileCountryCode: string().required(requiredMessage),
  mobileNumber: formPhoneNumber('mobileCountryCode').required(requiredMessage),
  homeCountryCode: string(),
  homeNumber: formPhoneNumber('homeCountryCode'),
  officeCountryCode: string(),
  officeNumber: formPhoneNumber('officeCountryCode'),
  ...mysAddressSchema(
    ['correspondence', 'residential', 'business'],
    'occupationGroup',
  ),
});
