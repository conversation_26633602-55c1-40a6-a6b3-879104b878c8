import {
  InferType,
  array,
  boolean,
  date,
  number,
  object,
  ref,
  string,
} from 'yup';
import { requiredMessage } from '../constants/cffErrorMessages';
import { normalizedSpaceString } from 'features/eApp/validations/eAppCommonSchema';

export const initialFinancialInvestmentPreferenceFormData: FinancialInvestmentPreferenceFormSchemaType =
  {
    risk: -1,
  };

export const initialFinancialStatementAndAnalysisFormData: FinancialStatementAndAnalysisFormSchemaType =
  {
    hasCertificate: false,
    certificates: [
      {
        policyHolders: '',
        lifeAssured: '',
        productType: '',
        insurer: '',
        deathBenefit: '',
        disabilityBenefit: '',
        criticalIllnessBenefit: '',
        otherBenefit: '',
        frequency: '',
        contribution: '',
        maturityDate: null,
      },
    ],
  };
export const initialFinancialIncomeProtectionFormData: FinancialIncomeProtectionFormSchemaType =
  {
    annualIncome: '',
    replacedYears: '',
    existingLifeInsurance: '',
    additionalAmount: '',
  };
export const initialFinancialRetirementFormData: FinancialRetirementFormSchemaType =
  {
    retirementYears: '',
    annualIncome: '',
    replacedYears: '',
    existingRetirementIncome: '',
    hasOtherIncome: false,
    otherIncome: '',
  };
export const initialFinancialChildEducationFormData: FinancialChildEducationFormSchemaType =
  {
    informations: [
      {
        name: '',
        age: '',
        yearsToTertiaryEducation: '',
        existingFund: '',
        additionalAmount: '',
      },
    ],
  };
export const initialFinancialSavingsFormData: FinancialSavingsFormSchemaType = {
  expected: '',
  meetYears: '',
  additionalAmount: '',
};
export const initialFinancialInvestmentsFormData: FinancialInvestmentsFormSchemaType =
  {
    initialAmount: '',
    regularAmount: '',
    duration: '',
    expectedMonthly: '',
    payoutPeriod: '',
  };
export const initialFinancialMedicalPlanningFormData: FinancialMedicalPlanningFormSchemaType =
  {
    hasMHIT: false,
    neededAmountForMedicalPlanning: '',
    neededAmountForCriticalIllness: '',
    existingProtectionForMedicalPlanning: '',
    existingProtectionForCriticalIllness: '',
    expiryAgeForMedicalPlanning: '',
    expiryAgeForCriticalIllness: '',
    existingProtectionForCurrentEmployerForMedicalPlanning: '',
    existingProtectionForCurrentEmployerForCriticalIllness: '',
    additionalAmountForMedicalPlanning: '',
    additionalAmountForCriticalIllness: '',
  };
export const initialFinancialPlanningFormData: FinancialPlanningFormSchemaType =
  {
    proposedParticipantMonthly: '',
    proposedParticipantYearly: '',
    proposedParticipantLumpSum: '',
    spouseMonthly: '',
    spouseYearly: '',
    spouseLumpSum: '',
  };

export const financialInvestmentPreferenceFormRequiredSchema = {
  risk: number()
    .test(
      'required',
      requiredMessage,
      value => value !== undefined && value >= 0,
    )
    .required(requiredMessage),
};

export const financialInvestmentPreferenceFormValidationSchema = object({
  ...financialInvestmentPreferenceFormRequiredSchema,
});

export type FinancialInvestmentPreferenceFormSchemaType = InferType<
  typeof financialInvestmentPreferenceFormValidationSchema
>;

export const financialStatementAndAnalysisFormRequiredSchema = {
  hasCertificate: boolean(),
  certificates: array().when('hasCertificate', {
    is: true,
    then: schema =>
      schema.of(
        object().shape({
          policyHolders: string().required(requiredMessage),
          lifeAssured: normalizedSpaceString().required(requiredMessage),
          productType: string().required(requiredMessage),
          insurer: string().required(requiredMessage),
          deathBenefit: string().required(requiredMessage),
          disabilityBenefit: string().required(requiredMessage),
          criticalIllnessBenefit: string().required(requiredMessage),
          otherBenefit: string(),
          frequency: string().required(requiredMessage),
          contribution: string().required(requiredMessage),
          maturityDate: date()
            .nullable()
            .test({
              name: 'validate-date',
              test: value => value instanceof Date,
              message: requiredMessage,
            }),
        }),
      ),
    otherwise: schema => schema.optional().nullable(),
  }),
};

export const financialStatementAndAnalysisFormValidationSchema = object({
  ...financialStatementAndAnalysisFormRequiredSchema,
});

export type FinancialStatementAndAnalysisFormSchemaType = InferType<
  typeof financialStatementAndAnalysisFormValidationSchema
>;

export const financialIncomeProtectionFormRequiredSchema = {
  annualIncome: string().required(requiredMessage),
  replacedYears: string().required(requiredMessage),
  existingLifeInsurance: string().required(requiredMessage),
  additionalAmount: string(),
};

export const financialIncomeProtectionFormValidationSchema = object({
  ...financialIncomeProtectionFormRequiredSchema,
});

export type FinancialIncomeProtectionFormSchemaType = InferType<
  typeof financialIncomeProtectionFormValidationSchema
>;
export const financialRetirementFormRequiredSchema = {
  retirementYears: string().required(requiredMessage),
  annualIncome: string().required(requiredMessage),
  replacedYears: string().required(requiredMessage),
  existingRetirementIncome: string().required(requiredMessage),
  hasOtherIncome: boolean().required(requiredMessage),
  otherIncome: string().when('hasOtherIncome', {
    is: true,
    then: schema => schema.required(requiredMessage),
    otherwise: schema => schema.optional(),
  }),
  additionalAmount: string(),
};

export const financialRetirementFormValidationSchema = object({
  ...financialRetirementFormRequiredSchema,
});

export type FinancialRetirementFormSchemaType = InferType<
  typeof financialRetirementFormValidationSchema
>;

export const financialChildEducationFormRequiredSchema = {
  informations: array().of(
    object().shape({
      name: normalizedSpaceString().required(requiredMessage),
      age: string().required(requiredMessage),
      yearsToTertiaryEducation: string().required(requiredMessage),
      existingFund: string().required(requiredMessage),
      additionalAmount: string().required(requiredMessage),
    }),
  ),
};

export const financialChildEducationFormValidationSchema = object({
  ...financialChildEducationFormRequiredSchema,
});

export type FinancialChildEducationFormSchemaType = InferType<
  typeof financialChildEducationFormValidationSchema
>;

export const financialSavingsFormRequiredSchema = {
  expected: string().required(requiredMessage),
  meetYears: string().required(requiredMessage),
  additionalAmount: string(),
};

export const financialSavingsFormValidationSchema = object({
  ...financialSavingsFormRequiredSchema,
});

export type FinancialSavingsFormSchemaType = InferType<
  typeof financialSavingsFormValidationSchema
>;

export const financialInvestmentsFormRequiredSchema = {
  initialAmount: string().required(requiredMessage),
  regularAmount: string().required(requiredMessage),
  duration: string().required(requiredMessage),
  expectedMonthly: string().required(requiredMessage),
  payoutPeriod: string().required(requiredMessage),
};

export const financialInvestmentsFormValidationSchema = object({
  ...financialInvestmentsFormRequiredSchema,
});

export type FinancialInvestmentsFormSchemaType = InferType<
  typeof financialInvestmentsFormValidationSchema
>;

export const financialMedicalPlanningFormRequiredSchema = {
  hasMHIT: boolean(),
  neededAmountForMedicalPlanning: string().required(requiredMessage),
  neededAmountForCriticalIllness: string().required(requiredMessage),
  existingProtectionForMedicalPlanning: string().required(requiredMessage),
  existingProtectionForCriticalIllness: string().required(requiredMessage),
  expiryAgeForMedicalPlanning: string().test(
    'required-age',
    requiredMessage,
    (age, ctx) => {
      const isAgeValid = Boolean(String(age));
      const hasMHIT = ctx.resolve(ref('hasMHIT'));
      const existingProtectionForMedicalPlanning = ctx.resolve(
        ref('existingProtectionForMedicalPlanning'),
      );
      if (hasMHIT) {
        if (Number(existingProtectionForMedicalPlanning) > 0) {
          return isAgeValid;
        } else {
          return true;
        }
      }
      return isAgeValid;
    },
  ),
  expiryAgeForCriticalIllness: string().test(
    'required-age',
    requiredMessage,
    (age, ctx) => {
      const isAgeValid = Boolean(String(age));
      const hasMHIT = ctx.resolve(ref('hasMHIT'));
      const existingProtectionForCriticalIllness = ctx.resolve(
        ref('existingProtectionForCriticalIllness'),
      );
      if (hasMHIT) {
        if (Number(existingProtectionForCriticalIllness) > 0) {
          return isAgeValid;
        } else {
          return true;
        }
      }
      return isAgeValid;
    },
  ),
  existingProtectionForCurrentEmployerForMedicalPlanning: string().when(
    'hasMHIT',
    {
      is: true,
      then: schema => schema.required(requiredMessage),
    },
  ),
  existingProtectionForCurrentEmployerForCriticalIllness: string().when(
    'hasMHIT',
    {
      is: true,
      then: schema => schema.required(requiredMessage),
    },
  ),
  additionalAmountForMedicalPlanning: string(),
  additionalAmountForCriticalIllness: string(),
};

export const financialMedicalPlanningFormValidationSchema = object({
  ...financialMedicalPlanningFormRequiredSchema,
});

export type FinancialMedicalPlanningFormSchemaType = InferType<
  typeof financialMedicalPlanningFormValidationSchema
>;

const getRequiredWhenHasSpouseSchema = () => {
  return string().when('hasSpouse', {
    is: 'yes',
    then: schema => schema.required(requiredMessage),
    otherwise: schema => schema.optional(),
  });
};
export const financialPlanningFormRequiredSchema = {
  hasSpouse: string(),
  proposedParticipantMonthly: string().required(requiredMessage),
  proposedParticipantYearly: string().required(requiredMessage),
  proposedParticipantLumpSum: string().required(requiredMessage),
  spouseMonthly: getRequiredWhenHasSpouseSchema(),
  spouseYearly: getRequiredWhenHasSpouseSchema(),
  spouseLumpSum: getRequiredWhenHasSpouseSchema(),
} as const;

export const financialPlanningFormValidationSchema = object({
  ...financialPlanningFormRequiredSchema,
});

export type FinancialPlanningFormSchemaType = InferType<
  typeof financialPlanningFormValidationSchema
>;
