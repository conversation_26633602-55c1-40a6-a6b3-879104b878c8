import * as yup from 'yup';
import { AdviceData } from '../utils/store/customerFactFindStore';
import { array, object } from 'yup';
import _ from 'lodash';
import {
  requiredMessage
} from '../constants/cffErrorMessages';
import { normalizedSpaceString } from 'features/eApp/validations/eAppCommonSchema';

export const OTHER_REASON_FOR_RECOMMENDATION = 'O';

export const recordOfAdviceRequiredSchema = {
  disclosure: yup.string(),
  preference: yup.string(),
  disclosureReason: yup.string(),

  advices: array()
    .defined()
    .of(
      object()
        .shape({
          nameOfPersonCoverage: yup.string().required(requiredMessage),
          productType: yup.string().required(requiredMessage),
          paymentFrequency: yup.string().required(requiredMessage),
          contribution: yup.string().required(requiredMessage),
          sumCovered: yup.string().required(requiredMessage),
          term: yup.string().required(requiredMessage),
          additionalBenefits: normalizedSpaceString(),
          isParticipate: yup.bool().required(requiredMessage),
          reason1: yup.string().required(requiredMessage),
          reason2: yup.string().optional(),
          reason3: yup.string().optional(),
          otherReason: normalizedSpaceString().test(
            'other-reason-required',
            requiredMessage,
            (value, ctx) => {
              const reason1 = ctx.resolve(yup.ref('reason1'));
              const reason2 = ctx.resolve(yup.ref('reason2'));
              const reason3 = ctx.resolve(yup.ref('reason3'));
              if (
                reason1 === OTHER_REASON_FOR_RECOMMENDATION ||
                reason2 === OTHER_REASON_FOR_RECOMMENDATION ||
                reason3 === OTHER_REASON_FOR_RECOMMENDATION
              ) {
                if (!value) {
                  return false;
                }
              }
              return true;
            },
          ),
          index: yup.number(),
          isCompleted: yup.bool(),
          type: yup.string().optional().oneOf(['PRIMARY', 'SECONDARY']),
        })
        .test({
          name: 'noDuplicatedReason',
          test: item => {
            if (_.isEmpty(item.reason1)) {
              return true;
            }
            if (_.isEmpty(item.reason2) && _.isEmpty(item.reason3)) {
              return true;
            }
            return (
              item.reason1 !== item.reason2 &&
              item.reason1 !== item.reason3 &&
              (item.reason2 !== item.reason3 ||
                _.isEmpty(item.reason2) ||
                _.isEmpty(item.reason3))
            );
          },
          message: 'Duplicated reason',
        }),
    )
    .test({
      name: 'isParticipateRequired',
      test: value => {
        return (value ?? []).some(item => item.isParticipate);
      },
      message: 'Need to participate in at least 1',
    }),
};

export const recordOfAdviceValidationSchema = object({
  ...recordOfAdviceRequiredSchema,
});

export type RecordOfAdviceSchemaFormType = yup.InferType<
  typeof recordOfAdviceValidationSchema
>;

export const initialRecordAdvicesFormData: AdviceData = {
  disclosure: '',
  disclosureReason: '',
  preference: '',
  advices: [
    {
      index: 0,
      nameOfPersonCoverage: '',
      productType: '',
      paymentFrequency: '',
      contribution: '',
      sumCovered: '',
      term: '',
      additionalBenefits: '',
      isParticipate: false,
      reason1: '',
      reason2: '',
      reason3: '',
      isCompleted: false,
    },
  ],
};

export const newRecordOfAdviceItem = {
  nameOfPersonCoverage: '',
  productType: '',
  paymentFrequency: '',
  contribution: '',
  sumCovered: '',
  term: '',
  additionalBenefits: '',
  isParticipate: false,
  reason1: '',
  reason2: '',
  reason3: '',
  isCompleted: false,
};
