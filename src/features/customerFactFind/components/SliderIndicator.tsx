import * as React from 'react';
import Svg, { G, Defs, Re<PERSON>, Circle } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function SliderIndicator(props: SvgIconProps) {
  return (
    <Svg width={56} height={56} viewBox="0 0 57 56" fill="none" {...props}>
      <G filter="url(#filter0_d_19134_11798)">
        <Circle cx={28.5} cy={26} r={24} fill="#E87722" />
        <Circle cx={28.5} cy={26} r={22.5} stroke="#fff" strokeWidth={3} />
      </G>
      <Rect x={27.5} y={20} width={2} height={13} rx={1} fill="#fff" />
      <Rect x={22.5} y={20} width={2} height={13} rx={1} fill="#fff" />
      <Rect x={32.5} y={20} width={2} height={13} rx={1} fill="#fff" />
      <Defs></Defs>
    </Svg>
  );
}
