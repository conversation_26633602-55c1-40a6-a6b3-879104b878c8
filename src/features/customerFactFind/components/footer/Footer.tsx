import React from 'react';
import { Box, Button, H6, Row, Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import {
  Image,
  StyleProp,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import LoadingIndicator from 'components/LoadingIndicator';
import IncompleteFields from 'components/IncompleteFields';
import { useThrottle } from 'hooks/useThrottle';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';

export const Footer = ({
  enableDocument,
  isDocumentLoading,
  onPrimaryPress,
  primaryLabel,
  primaryStyle,
  isPrimaryDisabled,
  isPrimaryLoading,
  onDocumentPress,
  totalIncompleteRequiredFields,
  focusOnIncompleteField,
  note,
  activeStep,
}: {
  isDocumentLoading?: boolean;
  isPrimaryDisabled?: boolean;
  isPrimaryLoading?: boolean;
  enableDocument?: boolean;
  onPrimaryPress?: () => void;
  onDocumentPress?: () => void;
  primaryLabel?: string;
  primaryStyle?: StyleProp<ViewStyle>;
  totalIncompleteRequiredFields?: number;
  focusOnIncompleteField?: () => void;
  note?: string;
  activeStep?: number;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { space, colors } = useTheme();
  const storeActiveStep = useCustomerFactFindStore(
    state => state.activeStep,
  );

  const throttledOnPrimaryPress = useThrottle(onPrimaryPress);

  if (storeActiveStep !== activeStep) {
    return null;
  }

  return (
    <Container>
      {enableDocument && (
        <TouchableOpacity
          disabled={isDocumentLoading}
          onPress={onDocumentPress}>
          <Row ml={space[6]} alignItems="center">
            <Image
              source={require('../../assets/FloatingPDF.png')}
              style={{ width: 155, height: 105, marginTop: -22 }}
              resizeMode="contain"
            />
            <Box w={space[3]} />
            <H6 fontWeight="bold">{t('customerFactFind:previewDocument')}</H6>
            <Box w={space[2]} />
            {isDocumentLoading ? (
              <LoadingIndicator />
            ) : (
              <Icon.ChevronUp fill={colors.secondary} />
            )}
          </Row>
        </TouchableOpacity>
      )}
      <IncompleteFields
        incompleteCount={totalIncompleteRequiredFields}
        focusNext={focusOnIncompleteField}
        style={{ marginLeft: space[6] }}
      />
      {Boolean(note) && (
        <Box ml={space[6]}>
          <H6 fontWeight="bold" color={colors.primary}>
            {note}
          </H6>
        </Box>
      )}
      <Box flex={1} />
      <Row
        alignItems="center"
        justifyContent="flex-end"
        my={space[4]}
        mr={space[6]}>
        <Button
          disabled={isPrimaryDisabled}
          loading={isPrimaryLoading}
          text={primaryLabel ?? t('customerFactFind:next')}
          style={[{ width: 162 }, primaryStyle]}
          onPress={throttledOnPrimaryPress}
        />
      </Row>
    </Container>
  );
};

const Container = styled(View)(({ theme: { sizes, colors } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  width: '100%',
  backgroundColor: colors.palette.white,
  borderTopWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  height: sizes[21],
}));
