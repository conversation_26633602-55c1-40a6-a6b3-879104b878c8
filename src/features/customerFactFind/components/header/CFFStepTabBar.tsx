import React, { memo } from 'react';
import { Body, Box, Row } from 'cube-ui-components';
import Svg, { Path } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { TFuncKey } from 'i18next';
export interface CFFStepTabBarProps {
  activeStep: number;
  processingStep: number;
  updateStep: (idx: number) => void;
}

export const cffRoutes: Array<{ key: string; title: string }> = [
  {
    key: 'customerPreference',
    title: 'customerFactFind:customerPreference.title',
  },
  {
    key: 'personalDetails',
    title: 'customerFactFind:personalDetails.title',
  },
  {
    key: 'financialStatement',
    title: 'customerFactFind:financialStatement.title',
  },
  {
    key: 'recordOfAdvice',
    title: 'customerFactFind:recordOfAdvice.title',
  },
  {
    key: 'confirmationOfAdvice',
    title: 'customerFactFind:confirmationOfAdvice.title',
  },
];

const CFFStepTabBar = ({
  activeStep,
  processingStep,
  updateStep,
}: CFFStepTabBarProps) => {
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();
  const onClickTitle = (idx: number) => {
    updateStep(idx);
  };

  return (
    <Row
      bgColor={colors.background}
      justifyContent="space-between"
      pt={space[3]}
      borderBottomWidth={space[1]}
      borderBottomColor={colors.primary}
      alignItems="center">
      {cffRoutes.map((step, idx) => (
        <React.Fragment key={idx}>
          <ItemStep
            title={t(step.title as TFuncKey<['customerFactFind']>)}
            completed={idx + 1 < processingStep}
            disabled={idx + 1 > processingStep}
            active={activeStep === idx + 1}
            onPress={() => onClickTitle(idx + 1)}
          />
          {idx !== cffRoutes.length - 1 && (
            <Box
              height={space[1] / 2}
              backgroundColor={colors.palette.fwdGrey['100']}
              flex={1}
            />
          )}
        </React.Fragment>
      ))}
    </Row>
  );
};

const ItemStep = memo(
  ({
    title,
    completed,
    active,
    disabled,
    onPress,
  }: {
    title: string;
    completed: boolean;
    active: boolean;
    disabled?: boolean;
    onPress: () => void;
  }) => {
    const { colors, space } = useTheme();
    return (
      <Box flexDirection="row">
        {active ? <ActiveStepLeft size={space[9]} /> : <Box w={space[9]} />}
        <ButtonStep active={active} onPress={onPress} disabled={disabled}>
          {completed ? (
            <IconTickCircle
              fill={active ? colors.palette.white : colors.primary}
            />
          ) : (
            <IconCircle
              fill={
                disabled
                  ? colors.palette.fwdGreyDark
                  : active
                  ? colors.palette.white
                  : colors.secondary
              }
            />
          )}

          <Box w={space[1]} />
          <Body
            fontWeight="bold"
            color={
              disabled
                ? colors.palette.fwdGreyDark
                : active
                ? colors.palette.white
                : completed
                ? colors.primary
                : colors.secondary
            }>
            {title}
          </Body>
        </ButtonStep>
        {active ? <ActiveStepRight size={space[9]} /> : <Box w={space[9]} />}
      </Box>
    );
  },
);

const ButtonStep = styled.Pressable<{ active?: boolean }>(
  ({ theme, active }) => {
    return {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: active ? theme.colors.primary : 'transparent',
      height: theme.space[9],
      marginHorizontal: -1,
    };
  },
);

export default CFFStepTabBar;

const IconTickCircle = ({
  fill = '#E87722',
}: {
  fill?: string;
}): JSX.Element => {
  return (
    <Svg width="19" height="18" viewBox="0 0 18 18" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.75 6.55953L12.5177 6.31C12.133 5.89671 11.5087 5.89671 11.124 6.31L7.6856 10.0031L6.87603 9.13349C6.49129 8.7202 5.86703 8.7202 5.48229 9.13349L5.25 9.38301L7.6856 12L12.75 6.55953Z"
        fill={fill}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9 15.75C12.7279 15.75 15.75 12.7279 15.75 9C15.75 5.27208 12.7279 2.25 9 2.25C5.27208 2.25 2.25 5.27208 2.25 9C2.25 12.7279 5.27208 15.75 9 15.75ZM9 17.25C13.5563 17.25 17.25 13.5563 17.25 9C17.25 4.44365 13.5563 0.75 9 0.75C4.44365 0.75 0.75 4.44365 0.75 9C0.75 13.5563 4.44365 17.25 9 17.25Z"
        fill={fill}
      />
    </Svg>
  );
};

const IconCircle = ({ fill }: { fill: string }): JSX.Element => {
  return (
    <Svg width="19" height="18" viewBox="0 0 19 18" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.25 15C12.5637 15 15.25 12.3137 15.25 9C15.25 5.68629 12.5637 3 9.25 3C5.93629 3 3.25 5.68629 3.25 9C3.25 12.3137 5.93629 15 9.25 15ZM9.25 16.5C13.3921 16.5 16.75 13.1421 16.75 9C16.75 4.85786 13.3921 1.5 9.25 1.5C5.10786 1.5 1.75 4.85786 1.75 9C1.75 13.1421 5.10786 16.5 9.25 16.5Z"
        fill={fill}
      />
    </Svg>
  );
};

const ActiveStepLeft = ({ size }: { size: number }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 36 36" fill="none">
      <Path
        d="M21.4696 8.26191C24.1523 3.18003 29.427 0 35.1735 0V31V36V41H0.173515V36H0C4.19895 36 8.05314 33.6764 10.0134 29.9631L21.4696 8.26191Z"
        fill="#E87722"
      />
    </Svg>
  );
};

const ActiveStepRight = ({ size }: { size: number }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 36 36" fill="none">
      <Path
        d="M13.8781 8.26191C11.1954 3.18003 5.92067 0 0.174141 0V31V36V41H35.1741V36H35.3477C31.1487 36 27.2945 33.6764 25.3343 29.9631L13.8781 8.26191Z"
        fill="#E87722"
      />
    </Svg>
  );
};
