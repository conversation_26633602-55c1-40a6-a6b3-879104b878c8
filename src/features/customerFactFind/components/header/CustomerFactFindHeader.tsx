import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import SaveModal from 'components/SaveModal';
import { Box, Icon, LargeBody, addToast } from 'cube-ui-components';
import { useCFFSaveParty } from 'features/customerFactFind/hooks/useCFFSaveParty';
import { useSaveCFF } from 'features/customerFactFind/hooks/useSaveCFF';
import { useAlert } from 'hooks/useAlert';
import React, { forwardRef, useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { RootStackParamList } from 'types';
import GATracking from 'utils/helper/gaTracking';
import CFFScreenHeader, { CFFScreenHeaderRef } from './CFFScreenHeader';

export type CustomerFactFindHeaderRef = CFFScreenHeaderRef;

const CustomerFactFindHeader = forwardRef<
  CFFScreenHeaderRef,
  { title?: string; label?: string }
>(({ title, label }, ref): JSX.Element => {
  const { t } = useTranslation(['customerFactFind']);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { name: screenName } = useRoute();

  const { colors, space } = useTheme();
  const [showSaveModal, setShowSaveModal] = useState(false);
  const goingBack = useRef(false);
  const { isSaving, onSaveCFF } = useSaveCFF();
  const { isSaving: isSavingParty, onCFFSaveParty } = useCFFSaveParty();
  const { alertError } = useAlert();

  const onCancelSaveModal = useCallback(() => {
    setShowSaveModal(false);
  }, []);

  const onConfirmSaveModal = useCallback(async () => {
    if (isSaving || isSavingParty) return;
    try {
      await onCFFSaveParty();
      await onSaveCFF();
    } catch {
      alertError(t('customerFactFind:failedToSave'));
      return;
    }
    addToast([
      {
        message: t('customerFactFind:saved'),
        IconLeft: <Icon.Tick fill={colors.palette.white} />,
      },
    ]);
    setShowSaveModal(false);
    GATracking.logButtonPress({
      screenName,
      screenClass: 'CFF flow',
      actionType: 'non_cta_button',
      buttonName: 'Confirm',
    });

    GATracking.logCustomEvent('fn_assessment', {
      action_type: 'fna_save',
    });

    if (goingBack.current) {
      navigation.navigate('Main');
    } else {
      navigation.navigate('Main', {
        screen: 'Home',
      });
    }
  }, [
    isSaving,
    isSavingParty,
    t,
    colors.palette.white,
    onSaveCFF,
    onCFFSaveParty,
    alertError,
    navigation,
  ]);

  const onDenySaveModal = useCallback(() => {
    setShowSaveModal(false);
    GATracking.logButtonPress({
      screenName,
      screenClass: 'CFF flow',
      actionType: 'non_cta_button',
      buttonName: 'Cancel',
    });

    GATracking.logCustomEvent('fn_assessment', {
      action_type: 'fna_exit',
    });

    if (goingBack.current) {
      navigation.canGoBack() && navigation.goBack();
    } else {
      navigation.navigate('Main', {
        screen: 'Home',
      });
    }
  }, [navigation]);

  const onBackPress = () => {
    goingBack.current = true;
    setShowSaveModal(true);
    GATracking.logButtonPress({
      screenName,
      screenClass: 'CFF flow',
      actionType: 'non_cta_button',
      buttonName: 'Back',
    });
  };

  return (
    <>
      <CFFScreenHeader
        ref={ref}
        title={title ?? t('customerFactFind:title')}
        left={
          <TouchableOpacity onPress={onBackPress}>
            <Icon.ArrowLeft size={space[6]} fill={colors.secondary} />
          </TouchableOpacity>
        }
        right={
          <TouchableOpacity
            onPress={() => {
              goingBack.current = false;
              setShowSaveModal(true);
            }}>
            <Box flexDirection="row" alignItems="center">
              <Icon.Home fill={colors.secondary} />
              <Box w={space[1]} />
              <LargeBody fontWeight="bold">
                {label ?? t('customerFactFind:home')}
              </LargeBody>
            </Box>
          </TouchableOpacity>
        }
      />

      <SaveModal
        dialogVisible={showSaveModal}
        onCancel={onCancelSaveModal}
        title={t('customerFactFind:saveModal.title')}
        denyLabel={t('customerFactFind:saveModal.dontSave')}
        saveLabel={t('customerFactFind:saveModal.save')}
        subTitle={t('customerFactFind:saveModal.desc')}
        onConfirm={onConfirmSaveModal}
        onDeny={onDenySaveModal}
        isLoading={isSaving || isSavingParty}
      />
    </>
  );
});

export default CustomerFactFindHeader;
