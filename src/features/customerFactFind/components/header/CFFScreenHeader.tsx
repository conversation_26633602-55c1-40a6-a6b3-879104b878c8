import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';

import { useTheme } from '@emotion/react';
import { Box, ExtraLargeBody, Icon } from 'cube-ui-components';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
} from 'react';
import { BackHandler, TouchableOpacity } from 'react-native';
import { RootStackParamList } from 'types';
import GATracking from 'utils/helper/gaTracking';

interface Props {
  title?: string;
  left?: 'back' | React.ReactElement<unknown> | React.ComponentType<unknown>;
  right?: React.ReactElement<unknown> | React.ComponentType<unknown>;
}

export type CFFScreenHeaderRef = {
  goBack: () => void;
};

const CFFScreenHeader = forwardRef<CFFScreenHeaderRef, Props>(
  ({ title, left, right }: Props, ref): JSX.Element => {
    const { space, colors } = useTheme();
    const navigation = useNavigation<NavigationProp<RootStackParamList>>();
    const { name: screenName } = useRoute();
    const goBack = useCallback(() => {
      if (!navigation.canGoBack()) return;

      GATracking.logButtonPress({
        screenName,
        screenClass: 'CFF flow',
        actionType: 'non_cta_button',
        buttonName: 'Back',
      });
      navigation.goBack();
    }, [navigation]);

    useImperativeHandle(
      ref,
      () => ({
        goBack,
      }),
      [goBack],
    );

    useEffect(() => {
      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          goBack();
          return true;
        },
      );

      return () => subscription.remove();
    }, [goBack]);

    let LeftAction: React.ReactElement | undefined;
    if (left) {
      if (typeof left === 'string') {
        LeftAction = (
          <TouchableOpacity onPress={goBack}>
            <Icon.ArrowLeft size={space[6]} fill={colors.secondary} />
          </TouchableOpacity>
        );
      } else if (React.isValidElement(left)) {
        LeftAction = left;
      } else {
        LeftAction = React.createElement(left);
      }
    }

    let RightAction: React.ReactElement | undefined;
    if (right) {
      if (React.isValidElement(right)) {
        RightAction = right;
      } else {
        RightAction = React.createElement(right);
      }
    }

    return (
      <Box
        flexDirection="row"
        px={space[4]}
        py={space[2]}
        alignItems="center"
        justifyContent="space-between">
        <Box flexDirection="row" alignItems="center">
          {LeftAction && LeftAction}
          <Box w={space[3]} />
          <ExtraLargeBody fontWeight="bold">{title}</ExtraLargeBody>
        </Box>

        {RightAction && RightAction}
      </Box>
    );
  },
);

export default CFFScreenHeader;
