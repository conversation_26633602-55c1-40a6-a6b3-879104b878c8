import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Icon, LargeLabel, Row } from 'cube-ui-components';
import React from 'react';
import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import SelectIcon from '../icons/SelectIcon';

interface Props {
  title: string;
  selected: boolean;
  done: boolean;
  alerted?: boolean;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}

const StateSectionHeader = (props: Props) => {
  const { title, selected, done, alerted, onPress, style } = props;
  const { colors, sizes } = useTheme();
  return (
    <TouchableOpacity onPress={onPress} style={style}>
      <Row backgroundColor={colors.background} alignItems="center">
        {done ? (
          <Icon.TickCircle
            size={20}
            fill={selected ? colors.primary : colors.palette.alertGreen}
          />
        ) : alerted ? (
          <Icon.InfoCircleFill size={20} fill={colors.error} />
        ) : (
          <SelectIcon fill={selected ? colors.primary : colors.secondary} />
        )}

        <Box w={sizes[2]} />
        <Box flex={1} minW={211}>
          <LargeLabel
            color={selected ? colors.primary : colors.onSurface}
            fontWeight={selected ? 'bold' : undefined}>
            {title}
          </LargeLabel>
        </Box>
        <Icon.ChevronRight
          size={16}
          fill={selected ? colors.primary : colors.palette.fwdGreyDark}
        />
      </Row>
    </TouchableOpacity>
  );
};

export default StateSectionHeader;
