import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Box, LargeLabel, Row, SmallLabel } from 'cube-ui-components';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import { createElement } from 'react';
import Tooltip from '../../../../components/Tooltip';

export default function GoalItem({
  position,
  icon,
  label,
  tooltip,
}: {
  position: number;
  icon: React.ComponentType<SvgPictogramProps>;
  label: string;
  tooltip: string;
}) {
  const { t } = useTranslation(['customerFactFind']);
  const { space, sizes, colors, borderRadius } = useTheme();
  return (
    <Box
      flex={1}
      borderRadius={borderRadius.small}
      overflow="hidden"
      borderWidth={1}
      borderColor={colors.palette.fwdGrey[100]}>
      <Box
        pos="absolute"
        py={space[1]}
        px={space[2]}
        backgroundColor={colors.palette.fwdYellow[100]}
        borderBottomRightRadius={borderRadius.small}>
        <SmallLabel fontWeight="medium">
          {t(
            'customerFactFind:confirmationOfAdvice.priorityOrderAndFinancialGoals.priority',
            { position },
          )}
        </SmallLabel>
      </Box>
      <Row
        alignItems="center"
        mt={31}
        ml={space[3]}
        mr={space[2]}
        mb={space[3]}
        gap={space[1]}>
        {createElement(icon, { size: sizes[10] })}
        <LargeLabel fontWeight="bold" style={{ flex: 1 }}>
          {label}
        </LargeLabel>
        <Tooltip content={tooltip} />
      </Row>
    </Box>
  );
}
