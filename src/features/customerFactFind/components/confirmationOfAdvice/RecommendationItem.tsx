import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { Box, H6, Label, LargeLabel, Row } from 'cube-ui-components';
import { Image } from 'react-native';
import { formatCurrencyWithMask } from 'utils';
import { useProductImageQuery } from 'features/productSelection/hooks/useProducts';

export default function RecommendationItem({
  index,
  last,
  productImage,
  productType,
  insuredName,
  paymentFrequency,
  paymentTerm,
  premium,
  sumAssured,
  additionalBenefits,
  participation,
}: {
  index: number;
  last: boolean;
  productImage?: string;
  productType: string;
  insuredName: string;
  paymentFrequency: string;
  paymentTerm: number;
  premium: number;
  sumAssured: number;
  additionalBenefits: string | null;
  participation: boolean;
}) {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['customerFactFind', 'common']);
  const { data: productThumbnail } = useProductImageQuery(productImage);

  return (
    <Box
      pt={space[6]}
      py={last ? space[0] : space[6]}
      borderColor={colors.palette.fwdGrey[100]}
      borderTopWidth={index === 0 ? 0 : 1}>
      <H6 fontWeight="bold">
        {t('customerFactFind:confirmationOfAdvice.recommendations.order', {
          position: index + 1,
        })}
      </H6>
      <Row mt={space[6]}>
        <Row gap={space[3]} flexBasis="25%">
          <Image
            source={{ uri: productThumbnail }}
            style={{
              width: 42,
              height: 42,
              borderRadius: borderRadius.small,
              backgroundColor: colors.surface,
            }}
            resizeMode="cover"
          />
          <Box gap={space[1]} flex={1}>
            <Label color={colors.palette.fwdGreyDarker}>
              {t(
                'customerFactFind:confirmationOfAdvice.recommendations.productType',
              )}
            </Label>
            <LargeLabel fontWeight="medium">{productType}</LargeLabel>
          </Box>
        </Row>
        <Box gap={space[1]} flex={1} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.personCoverage',
            )}
          </Label>
          <LargeLabel fontWeight="medium">{insuredName}</LargeLabel>
        </Box>
        <Box gap={space[1]} flex={1} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.paymentFrequency',
            )}
          </Label>
          <LargeLabel fontWeight="medium">{paymentFrequency}</LargeLabel>
        </Box>
        <Box gap={space[1]} flex={1} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.paymentTerm',
            )}
          </Label>
          <LargeLabel fontWeight="medium">
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.paymentTerm.years',
              {
                paymentTerm,
              },
            )}
          </LargeLabel>
        </Box>
      </Row>
      <Row mt={space[4]}>
        <Box gap={space[1]} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.contribution',
            )}
          </Label>
          <LargeLabel fontWeight="medium">
            {t('common:withCurrency', {
              amount: formatCurrencyWithMask(premium, 2),
            })}
          </LargeLabel>
        </Box>
        <Box gap={space[1]} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.sumCovered',
            )}
          </Label>
          <LargeLabel fontWeight="medium">
            {t('common:withCurrency', {
              amount: formatCurrencyWithMask(sumAssured, 2),
            })}
          </LargeLabel>
        </Box>
        <Box gap={space[1]} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.additionalBenefits',
            )}
          </Label>
          <LargeLabel fontWeight="medium">
            {additionalBenefits === null
              ? t('customerFactFind:confirmationOfAdvice.recommendations.NA')
              : additionalBenefits}
          </LargeLabel>
        </Box>
        <Box gap={space[1]} flexBasis="25%">
          <Label color={colors.palette.fwdGreyDarker}>
            {t(
              'customerFactFind:confirmationOfAdvice.recommendations.participation',
            )}
          </Label>
          <LargeLabel fontWeight="medium">
            {participation
              ? t('customerFactFind:yes')
              : t('customerFactFind:no')}
          </LargeLabel>
        </Box>
      </Row>
    </Box>
  );
}
