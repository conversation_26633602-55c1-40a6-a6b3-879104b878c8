import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import MarkdownText, { useMarkdownStyle } from 'components/MarkdownText';
import { Body, Box, H6, LargeBody, LargeLabel, Row } from 'cube-ui-components';
import { prioritizationKeyToIcon } from 'features/customerFactFind/constants/prioritizations';
import { useCFFSaveParty } from 'features/customerFactFind/hooks/useCFFSaveParty';
import { useSaveCFF } from 'features/customerFactFind/hooks/useSaveCFF';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import { useGetOwb } from 'features/eApp/hooks/healthQuestions/useGetOwb';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import PdfViewer, {
  PdfGenerator,
} from 'features/pdfViewer/components/PdfViewer';
import { useGetProductList } from 'features/productSelection/hooks/useProducts';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useGenerateCFFPdf } from 'hooks/useGenerateCFFPdf';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase, useGetCaseManually } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useToggle from 'hooks/useToggle';
import { TFuncKey } from 'i18next';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { CaseStatus } from 'types/case';
import { ProductName } from 'types/products';
import { formatSignatureDate } from 'utils/helper/formatUtil';
import GATracking from 'utils/helper/gaTracking';
import { Footer } from '../footer/Footer';
import { CFFCustomerAcknowledgement } from '../modals/CFFCustomerAcknowledgementModal';
import SectionContainer from '../SectionContainer';
import GoalItem from './GoalItem';
import RecommendationItem from './RecommendationItem';

export default function ConfirmationOfAdvice() {
  const { colors, space, borderRadius, typography } = useTheme();
  const [showCFFAckModal, setShowCFFAckModal] = useState(false);
  const [viewingPdf, showPdf, hidePdf] = useToggle();
  const { t } = useTranslation(['customerFactFind']);

  const { dispatch } = useNavigation<NavigationProp<RootStackParamList>>();
  const cffStore = useCustomerFactFindStore();
  const { data: agentProfile } = useGetAgentProfile();
  const { mutateAsync: generateCFFPdf, isLoading: isPDFGenerating } =
    useGenerateCFFPdf();
  const { getOwb } = useGetOwb();
  const goals = cffStore.customerPreference.prioritization;
  const recordOfAdvices = cffStore.recordOfAdvices.advices;
  const { personalDetails } = cffStore;
  const { isSaving, onSaveCFF } = useSaveCFF();
  const { isSaving: isSavingParty, onCFFSaveParty } = useCFFSaveParty();
  const { mutateAsync: getCase } = useGetCaseManually();

  const channel = useGetCubeChannel();
  const { data: productList } = useGetProductList({ channel });
  const { data: optionList } = useGetOptionList();

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);

  const recommendationItems = useMemo(() => {
    return recordOfAdvices
      .filter(item => item.isParticipate)
      .map(item => {
        const selectedProduct = productList?.find(
          (p: { productName: ProductName | undefined }) =>
            getProductName(p.productName) === item.productType,
        );

        return {
          productType: getProductName(selectedProduct?.productName),
          productImage: selectedProduct?.productThumbnailUrl,
          insuredName: item.nameOfPersonCoverage,
          paymentFrequency:
            (optionList?.PAYMENTMODE4_CFF?.options
              ? [
                  ...optionList.PAYMENTMODE4_CFF.options,
                  {
                    label: t('customerFactFind:singlePremium'),
                    value: t('customerFactFind:singlePremium'),
                  },
                ]
              : []
            ).find(option => item.paymentFrequency === option.value)?.label ||
            '',
          paymentTerm: Number(item.term || 0),
          premium: Number(item.contribution || 0),
          sumAssured: Number(item.sumCovered || 0),
          additionalBenefits:
            item.additionalBenefits === undefined
              ? null
              : item.additionalBenefits,
          participation: item.isParticipate,
        };
      });
  }, [optionList?.PAYMENTMODE4_CFF?.options, productList, recordOfAdvices, t]);

  const { alertError } = useAlert();

  const onPressPreviewDocument = useCallback<PdfGenerator>(async () => {
    try {
      if (!caseObj) return;
      try {
        await onCFFSaveParty();
        await onSaveCFF();
      } catch {
        alertError(t('customerFactFind:failedToSave'));
        return;
      }
      const owb = await getOwb();
      if (!owb) return;
      const base64 = await generateCFFPdf(owb);
      return {
        base64,
        fileName: `CFF document - ${formatSignatureDate(new Date())}`,
      };
    } catch {
      alertError(t('customerFactFind:failedToGeneratePdf'));
    }
  }, [
    alertError,
    caseObj,
    generateCFFPdf,
    getOwb,
    onCFFSaveParty,
    onSaveCFF,
    t,
  ]);

  const mdStyle = useMarkdownStyle();

  return (
    <Box flex={1}>
      <Container>
        <SectionContainer
          title={t('customerFactFind:confirmationOfAdvice.title')}>
          <Box p={space[6]}>
            <Box
              p={space[4]}
              mb={space[3]}
              backgroundColor={colors.primaryVariant3}
              borderRadius={borderRadius['small']}>
              <MarkdownText
                style={{
                  body: {
                    ...mdStyle.body,
                    fontSize: typography.largeBody.size,
                    lineHeight: typography.largeBody.lineHeight,
                  },
                }}>
                {t(
                  'customerFactFind:confirmationOfAdvice.confirmationStatement',
                  {
                    ownerName:
                      personalDetails.certificateOwnerDetail.fullName || 'N/A',
                    agentName: agentProfile?.person?.fullName || 'N/A',
                  },
                )}
              </MarkdownText>
            </Box>
            <LargeBody>
              {t('customerFactFind:confirmationOfAdvice.prioritization')}
            </LargeBody>
            <Box h={space[8]} />
            <H6 fontWeight="bold">
              {t(
                'customerFactFind:confirmationOfAdvice.priorityOrderAndFinancialGoals',
              )}
            </H6>
            <Box h={space[2]} />
            <Body>
              {t(
                'customerFactFind:confirmationOfAdvice.priorityOrderAndFinancialGoals.note',
              )}
            </Body>
            <Box mt={space[6]} gap={space[5]}>
              <Row gap={space[3]}>
                {goals.slice(0, 3).map((item, index) => (
                  <GoalItem
                    key={index}
                    position={index + 1}
                    icon={prioritizationKeyToIcon[item.key]}
                    label={t(
                      `customerFactFind:customerPreference.${item.key}` as TFuncKey<
                        ['customerFactFind']
                      >,
                    )}
                    tooltip={t(
                      `customerFactFind:customerPreference.${item.key}.tooltip` as TFuncKey<
                        ['customerFactFind']
                      >,
                    )}
                  />
                ))}
              </Row>
              <Row gap={space[3]}>
                {goals.slice(3, 6).map((item, index) => (
                  <GoalItem
                    key={index}
                    position={index + 3 + 1}
                    icon={prioritizationKeyToIcon[item.key]}
                    label={t(
                      `customerFactFind:customerPreference.${item.key}` as TFuncKey<
                        ['customerFactFind']
                      >,
                    )}
                    tooltip={t(
                      `customerFactFind:customerPreference.${item.key}.tooltip` as TFuncKey<
                        ['customerFactFind']
                      >,
                    )}
                  />
                ))}
              </Row>
            </Box>
          </Box>
        </SectionContainer>
        <SectionContainer
          title={t('customerFactFind:confirmationOfAdvice.recommendations')}>
          <Box p={space[6]}>
            <LargeLabel>
              {t(
                'customerFactFind:confirmationOfAdvice.recommendations.description',
                {
                  ownerName:
                    personalDetails.certificateOwnerDetail.fullName || 'N/A',
                },
              )}
            </LargeLabel>
            {recommendationItems.map((item, index, arr) => (
              <RecommendationItem
                key={index}
                index={index}
                last={arr.length - 1 === index}
                {...item}
              />
            ))}
          </Box>
        </SectionContainer>
        <Box h={space[18]} />
      </Container>
      <Footer
        activeStep={5}
        isDocumentLoading={isPDFGenerating}
        enableDocument
        onDocumentPress={showPdf}
        onPrimaryPress={() => setShowCFFAckModal(true)}
        primaryLabel={t('customerFactFind:startApplication')}
        primaryStyle={{ width: 200 }}
      />
      <CFFCustomerAcknowledgement
        visible={showCFFAckModal}
        onDismiss={() => {
          setShowCFFAckModal(false);
        }}
        isSubmitting={isSaving || isSavingParty}
        onAgree={async () => {
          if (!caseObj || !caseId) return;
          cffStore.updateStep();
          await onSaveCFF({
            isAcknowledge: true,
          });
          setShowCFFAckModal(false);
          const latestCaseObj = await getCase(caseId);
          if (latestCaseObj.latestStatus === CaseStatus.IN_APP) {
            GATracking.logButtonPress({
              screenName: 'CustomerFactFind',
              screenClass: 'CFF flow',
              actionType: 'non_cta_button',
              buttonName: 'Agree',
            });

            GATracking.logCustomEvent('fn_assessment', {
              action_type: 'fna_complete',
            });

            dispatch(StackActions.replace('EApp'));
          } else {
            alertError(t('customerFactFind:failedToSave'));
            cffStore.updateStep(5);
          }
        }}
      />
      <PdfViewer
        visible={viewingPdf}
        title={t('customerFactFind:title')}
        onClose={hidePdf}
        pdfGenerator={onPressPreviewDocument}
        downloadable
      />
    </Box>
  );
}

const Container = styled.ScrollView(({ theme: { space, colors } }) => ({
  paddingTop: space[5],
  paddingHorizontal: space[8],
  backgroundColor: colors.surface,
}));
