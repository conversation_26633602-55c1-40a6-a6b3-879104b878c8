import * as React from 'react';
import {
  <PERSON>Handler,
  StyleProp,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
} from 'react-native';
import Animated, {
  Easing,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import useLatest from 'hooks/useLatest';

export interface ModalProps {
  /**
   * Determines whether clicking outside the modal dismiss it.
   */
  dismissable?: boolean;
  /**
   * Callback that is called when the user dismisses the modal.
   */
  onDismiss?: () => void;
  /**
   * Accessibility label for the overlay. This is read by the screen reader when the user taps outside the modal.
   */
  overlayAccessibilityLabel?: string;
  /**
   * Determines Whether the modal is visible.
   */
  visible: boolean;
  /**
   * Content of the `Modal`.
   */
  children: React.ReactNode;
  /**
   * Style for the content of the modal
   */
  contentContainerStyle?: StyleProp<ViewStyle>;
  /**
   * Style for the wrapper of the modal.
   * Use this prop to change the default wrapper style or to override safe area insets with marginTop and marginBottom.
   */
  style?: StyleProp<ViewStyle>;
  backdropColor?: string;
}

/**
 * The Modal component is a simple way to present content above an enclosing view.
 * To render the `Modal` above other components, you'll need to wrap it with the [`Portal`](portal.html) component.
 *
 * <div class="screenshots">
 *   <figure>
 *     <img class="medium" src="screenshots/modal.gif" />
 *   </figure>
 * </div>
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { Modal, Portal, Text, Button, Provider } from 'react-native-paper';
 *
 * const MyComponent = () => {
 *   const [visible, setVisible] = React.useState(false);
 *
 *   const showModal = () => setVisible(true);
 *   const hideModal = () => setVisible(false);
 *   const containerStyle = {backgroundColor: 'white', padding: 20};
 *
 *   return (
 *     <Provider>
 *       <Portal>
 *         <Modal visible={visible} onDismiss={hideModal} contentContainerStyle={containerStyle}>
 *           <Text>Example Modal.  Click outside this area to dismiss.</Text>
 *         </Modal>
 *       </Portal>
 *       <Button style={{marginTop: 30}} onPress={showModal}>
 *         Show
 *       </Button>
 *     </Provider>
 *   );
 * };
 *
 * export default MyComponent;
 * ```
 */
function Modal({
  dismissable = false,
  visible = false,
  overlayAccessibilityLabel = 'Close modal',
  onDismiss = () => {
    // do nothing
  },
  children,
  contentContainerStyle,
  backdropColor = 'rgba(0,0,0,0.5)',
  style,
}: ModalProps) {
  const { animation } = useTheme();
  const visibleRef = React.useRef(visible);

  React.useEffect(() => {
    visibleRef.current = visible;
  });

  const onDismissCallback = useLatest(onDismiss);

  const opacity = useSharedValue(visible ? 1 : 0);

  const [rendered, setRendered] = React.useState(visible);

  if (visible && !rendered) {
    setRendered(true);
  }

  const showModal = React.useCallback(() => {
    opacity.value = withTiming(1, {
      duration: animation.duration,
      easing: Easing.out(Easing.cubic),
    });
  }, [opacity, animation.duration]);

  const hideModal = React.useCallback(() => {
    opacity.value = withTiming(
      0,
      {
        duration: animation.duration,
        easing: Easing.out(Easing.cubic),
      },
      finished => {
        if (!finished) {
          return;
        }

        if (visible) {
          runOnJS(onDismissCallback.current)();
        }

        if (visibleRef.current) {
          runOnJS(showModal)();
        } else {
          runOnJS(setRendered)(false);
        }
      },
    );
  }, [opacity, animation.duration, visible, onDismissCallback, showModal]);

  React.useEffect(() => {
    if (!visible) {
      return undefined;
    }

    const onHardwareBackPress = () => {
      if (dismissable) {
        hideModal();
      }

      return true;
    };

    const subscription = BackHandler.addEventListener(
      'hardwareBackPress',
      onHardwareBackPress,
    );
    return () => subscription.remove();
  }, [dismissable, hideModal, visible]);

  const prevVisible = React.useRef<boolean | null>(null);

  React.useEffect(() => {
    if (prevVisible.current !== visible) {
      if (visible) {
        showModal();
      } else {
        hideModal();
      }
    }
    prevVisible.current = visible;
  });

  const animatedStyle = useAnimatedStyle(
    () => ({
      opacity: opacity.value,
    }),
    [opacity.value],
  );

  if (!rendered) return null;

  return (
    <Animated.View
      pointerEvents={visible ? 'auto' : 'none'}
      accessibilityViewIsModal
      accessibilityLiveRegion="polite"
      style={StyleSheet.absoluteFill}
      onAccessibilityEscape={hideModal}>
      <TouchableWithoutFeedback
        accessibilityLabel={overlayAccessibilityLabel}
        accessibilityRole="button"
        disabled={!dismissable}
        onPress={dismissable ? hideModal : undefined}
        importantForAccessibility="no">
        <Backdrop backgroundColor={backdropColor} style={animatedStyle} />
      </TouchableWithoutFeedback>
      <Wrapper style={style} pointerEvents="box-none">
        <Content
          style={[animatedStyle, contentContainerStyle]}
          pointerEvents="box-none">
          {children}
        </Content>
      </Wrapper>
    </Animated.View>
  );
}

export default Modal;

const Backdrop = styled(Animated.View)<{ backgroundColor?: string }>(
  ({ backgroundColor }) => ({
    backgroundColor,
    flex: 1,
  }),
);

const Wrapper = styled(View)(() => ({
  ...StyleSheet.absoluteFillObject,
  justifyContent: 'center',
}));

const Content = styled(Animated.View)(() => ({
  backgroundColor: 'transparent',
  justifyContent: 'center',
}));
