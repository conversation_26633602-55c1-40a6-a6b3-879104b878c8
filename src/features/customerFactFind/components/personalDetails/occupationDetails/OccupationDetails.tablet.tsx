import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Body,
  Box,
  Row,
  SmallBody,
  TextField,
  Column,
  CurrencyTextField,
  LargeBody,
} from 'cube-ui-components';
import { CertificateOwnerDetailFormSchemaType } from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import React, { useMemo, useEffect } from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IncomeRange, Nationality } from 'types/optionList';
import Autocomplete from '../../../../../components/Autocomplete';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import AutocompletePopup from '../../../../../components/AutocompletePopup';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { RiderCode } from 'types/quotation';
import { INCOME_GREATER_THAN_200K } from 'constants/optionList';
import SectionContainer from '../../SectionContainer';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CHANNELS } from 'types/channel';

interface Props {
  isFromSI?: boolean;
  isMainInsured?: boolean;
  control: Control<CertificateOwnerDetailFormSchemaType>;
}

const OccupationDetails = (props: Props) => {
  const { isFromSI, isMainInsured, control } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { space, colors } = useTheme();
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;

  const quotation = useSelectedQuotation();

  const hasAddRider = useMemo(() => {
    const addRider = quotation?.plans?.find(
      plan => plan?.pid === RiderCode.ADIA,
    );
    return addRider !== null && addRider !== undefined;
  }, [quotation]);

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: 'annualIncomeAmount', control });

  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: 'occupationDescription', control });

  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({ name: 'occupationGroup', control });

  const occupationValue = useWatch({
    name: 'occupation',
    control: control,
  });

  const annualIncomeValue = useWatch({
    name: 'annualIncome',
    control: control,
  });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  const { occupationClass, occupationGroup, occupationDescription } =
    useOccupationClass(occupationValue);

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
    onChangeOccupationGroup(occupationGroup);
  }, [
    occupationDescription,
    occupationGroup,
    onChangeOccupationDescription,
    onChangeOccupationGroup,
  ]);

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.occupationTitle')}>
      <Content>
        {/* 1 */}
        <RowBox>
          <Input
            control={control}
            as={AutocompletePopup<{ value: string; label: string }, string>}
            name="occupation"
            label={t('customerFactFind:personalDetails.form.occupation')}
            data={optionList?.OCCUPATION.options ?? []}
            disabled={isLoadingOptionList || isFromSI}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1 }}
            searchable
          />
          <Column flex={1} />
        </RowBox>

        {/* Row 1.2 */}
        {occupationValue && (
          <Row
            flex={1}
            borderRadius={space[1]}
            borderColor={colors.palette.fwdGrey[100]}
            borderWidth={1}
            padding={space[4]}>
            <Box flex={1}>
              <SmallBody color={colors.palette.fwdGreyDarker}>
                {t('customerFactFind:occupationDetails.occupationClass')}
              </SmallBody>
              <Box mt={space[1]}>
                <Body>{occupationClass?.label.en}</Body>
              </Box>
            </Box>
            <Box flex={4}>
              <SmallBody color={colors.palette.fwdGreyDarker}>
                {t('customerFactFind:occupationDetails.occupationDescription')}
              </SmallBody>
              <Box mt={space[1]}>
                <Body>{occupationDescription}</Body>
              </Box>
            </Box>
          </Row>
        )}

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="nameOfBusiness"
            label={t('customerFactFind:personalDetails.form.nameOfBusiness')}
            style={{ flex: 1 }}
          />
          <Input
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name="natureOfWork"
            label={t('customerFactFind:personalDetails.form.natureOfWork')}
            data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1 }}
            searchable
          />
        </RowBox>

        {/* 3 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="exactDuties"
            label={t('customerFactFind:personalDetails.form.exactDuties')}
            style={{ flex: 1 }}
          />
          <Input
            control={control}
            as={Autocomplete<IncomeRange, string>}
            name="annualIncome"
            style={{ flex: 1 }}
            label={t('customerFactFind:personalDetails.form.annualIncome')}
            data={optionList?.INCOME_RANGE.options ?? []}
            disabled={isLoadingOptionList || (isMainInsured && hasAddRider)}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={CurrencyTextField}
            name="annualIncomeAmount"
            label={t(
              'customerFactFind:personalDetails.form.annualIncomeAmount',
            )}
            style={{ flex: 1 }}
            disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
          />
          <Column flex={1} />
        </RowBox>

        {/* 5 */}
        {isBanca && (
          <RowBox>
            <Reminder>
              {t('customerFactFind:personalDetails.form.reminder')}
            </Reminder>
          </RowBox>
        )}
      </Content>
    </SectionContainer>
  );
};

export default OccupationDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  paddingTop: 34,
  gap: 27,
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
}));

const Reminder = styled(LargeBody)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.primaryVariant3,
    borderRadius: borderRadius.small,
    padding: space[4],
    flex: 1,
    color: colors.primary,
  }),
);
