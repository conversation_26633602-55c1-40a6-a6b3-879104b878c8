import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { Box, Button, H6, LargeBody, Row } from 'cube-ui-components';
import { memo } from 'react';

export const DeleteModal = memo(function DeleteModal({
  dialogVisible,
  onConfirm,
  onDeny,
  title,
  subTitle,
  denyLabel,
  removeLabel,
  isDeleting,
}: {
  title?: string;
  subTitle?: string;
  dialogVisible: boolean;
  denyLabel: string;
  removeLabel: string;
  isDeleting?: boolean;
  onConfirm: () => void;
  onDeny: () => void;
}) {
  const { space } = useTheme();
  return (
    <DialogPhone visible={dialogVisible}>
      <Box p={space[6]}>
        <H6 fontWeight="bold">{title}</H6>
        <Box h={space[4]} />
        <LargeBody>{subTitle}</LargeBody>
        <Box h={space[6]} />
        <Row>
          <Button
            style={{ flex: 1 }}
            text={denyLabel}
            variant="secondary"
            size="medium"
            onPress={onDeny}
            disabled={isDeleting}
          />
          <Box w={space[4]} />
          <Button
            style={{ flex: 1 }}
            text={removeLabel}
            variant="primary"
            size="medium"
            onPress={onConfirm}
            loading={isDeleting}
          />
        </Row>
      </Box>
    </DialogPhone>
  );
});
