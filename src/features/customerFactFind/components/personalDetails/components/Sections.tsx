import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Body,
  Box,
  Column,
  Icon,
  Label,
  LargeBody,
  Row,
} from 'cube-ui-components';
import React, { useEffect, useMemo, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import { shallow } from 'zustand/shallow';

export interface ItemSectionProps {
  name: string;
  title: string;
  subtitle?: string;
  content?: React.ComponentType<unknown> | React.ReactElement<unknown>;
  subItems?: {
    name: string;
    title: string;
    subtitle?: string;
    content?: React.ComponentType<unknown> | React.ReactElement<unknown>;
  }[];
}

export interface SectionsProps {
  items: ItemSectionProps[];
  note?: string;
  initialPath?: string;
  activePath: string | undefined;
  setActivePath: (path: string) => void;
}

export default function Sections({
  items,
  note,
  initialPath,
  activePath,
  setActivePath,
}: SectionsProps) {
  const { sizes, space, colors, borderRadius } = useTheme();

  const { hasSpouse, setHasSpouse, hasChild, setHasChild } =
    useCustomerFactFindStore(
      state => ({
        hasSpouse: state.hasSpouse,
        setHasSpouse: state.setHasSpouse,
        hasChild: state.hasChild,
        setHasChild: state.setHasChild,
      }),
      shallow,
    );

  const actualInitialPath = useMemo(
    () => getInitialPath(initialPath, items),
    [initialPath, items],
  );
  useEffect(() => {
    if (actualInitialPath) {
      setActivePath(actualInitialPath);
    }
  }, [actualInitialPath, setActivePath]);

  const renderDefaultItem = (
    item: ItemSectionProps,
    index: number,
    selected: boolean,
    hasSubItemSelected: boolean,
    color: string | undefined,
    hasSubItems: boolean | undefined,
  ) => {
    return (
      <TouchableOpacity
        disabled={hasSubItems}
        key={item.name}
        onPress={() => setActivePath(item.name)}
        style={{
          paddingTop: space[index === 0 ? 0 : 5],
          paddingBottom: space[index === items.length - 1 ? 0 : 5],
          borderBottomWidth: index === items.length - 1 ? 0 : 1,
          borderColor: colors.palette.fwdGrey[100],
        }}>
        <Row
          bgColor={colors.background}
          justifyContent="space-between"
          alignItems="center">
          <Box flex={1}>
            <LargeBody
              fontWeight={selected || hasSubItemSelected ? 'bold' : 'normal'}
              color={color}>
              {item.title}
            </LargeBody>
            {Boolean(item.subtitle) && (
              <>
                <Box h={space[2]} />
                <Body color={colors.placeholder}>{item.subtitle}</Body>
              </>
            )}
          </Box>
          {!hasSubItems && (
            <Icon.ChevronRight
              width={sizes[4]}
              height={sizes[4]}
              fill={color}
            />
          )}
        </Row>
        {item.subItems && (
          <Column ml={space[4]} mt={space[5]} gap={space[5]}>
            {item.subItems.map(subItem => {
              const paths = activePath?.split('.') ?? [];
              const subItemSelected =
                paths.length === 2 &&
                paths[0] === item.name &&
                paths[1] === subItem.name;
              const subItemColor = subItemSelected
                ? colors.primary
                : colors.secondary;
              return (
                <TouchableOpacity
                  key={subItem.title}
                  onPress={() => setActivePath(`${item.name}.${subItem.name}`)}
                  hitSlop={ICON_HIT_SLOP}>
                  <Row
                    bgColor={colors.background}
                    justifyContent="space-between"
                    alignItems="center">
                    <Box flex={1}>
                      <LargeBody
                        fontWeight={subItemSelected ? 'bold' : 'normal'}
                        color={subItemColor}>
                        {subItem.title}
                      </LargeBody>
                      {Boolean(subItem.subtitle) && (
                        <>
                          <Box h={space[2]} />
                          <Body color={colors.placeholder}>
                            {subItem.subtitle}
                          </Body>
                        </>
                      )}
                    </Box>
                    <Icon.ChevronRight
                      width={sizes[4]}
                      height={sizes[4]}
                      fill={subItemColor}
                    />
                  </Row>
                </TouchableOpacity>
              );
            })}
          </Column>
        )}
      </TouchableOpacity>
    );
  };

  const renderOptionalItem = (
    item: ItemSectionProps,
    index: number,
    selected: boolean,
    color: string | undefined,
  ) => {
    const isSpouseInsured = item.name === 'spouse' && hasSpouse;
    const isChildInsured = item.name === 'childDependent' && hasChild;

    return (
      <TouchableOpacity
        key={item.name}
        onPress={() => onActiveOptionalItem(item)}
        style={{
          paddingTop: space[index === 0 ? 0 : 5],
          paddingBottom: space[index === items.length - 1 ? 0 : 5],
          borderBottomWidth: index === items.length - 1 ? 0 : 1,
          borderColor: colors.palette.fwdGrey[100],
        }}>
        <Row
          bgColor={colors.background}
          justifyContent="space-between"
          alignItems="center">
          {!isSpouseInsured && !isChildInsured && (
            <Box pr={space[2]}>
              <Icon.Plus
                width={sizes[4]}
                height={sizes[4]}
                fill={colors.palette.fwdOrange[100]}
              />
            </Box>
          )}

          <Box flex={1}>
            <LargeBody fontWeight={selected ? 'bold' : 'normal'} color={color}>
              {item.title}
            </LargeBody>

            {Boolean(item.subtitle) && (
              <>
                <Box h={space[2]} />
                <Body color={colors.placeholder}>{item.subtitle}</Body>
              </>
            )}
          </Box>

          {(isSpouseInsured || isChildInsured) && (
            <Icon.ChevronRight
              width={sizes[4]}
              height={sizes[4]}
              fill={color}
            />
          )}
        </Row>
      </TouchableOpacity>
    );
  };

  const onActiveOptionalItem = (item: ItemSectionProps) => {
    setActivePath(item.name);

    item.name === 'spouse' && setHasSpouse(true);
    item.name === 'childDependent' && setHasChild(true);
  };

  return (
    <>
      <Row flex={1} pl={space[8]} bgColor={colors.surface} gap={space[6]}>
        <Box
          flexBasis="25%"
          alignSelf="flex-start"
          mt={space[6]}
          bgColor={colors.background}
          borderRadius={borderRadius.large}
          p={space[4]}>
          {items.map((item, index) => {
            const paths = activePath?.split('.') ?? [];
            const selected = paths.length === 1 && paths[0] === item.name;
            const hasSubItemSelected = paths?.[0] === item.name;
            const color = selected ? colors.primary : colors.secondary;
            const hasSubItems = item.subItems && item.subItems.length > 0;

            return item.name === 'spouse' || item.name === 'childDependent'
              ? renderOptionalItem(item, index, selected, color)
              : renderDefaultItem(
                  item,
                  index,
                  selected,
                  hasSubItemSelected,
                  color,
                  hasSubItems,
                );
          })}
          {Boolean(note) && (
            <Box mt={space[5]}>
              <Label>{note}</Label>
            </Box>
          )}
        </Box>

        <Box flex={1}>
          {items.map(item => {
            const paths = activePath?.split('.') ?? [];
            const selected = paths.length === 1 && paths[0] === item.name;
            const hasSubItems = item.subItems && item.subItems.length > 0;

            if (hasSubItems) {
              return item.subItems?.map(subItem => {
                if (subItem.content) {
                  const subItemSelected =
                    paths.length === 2 &&
                    paths[0] === item.name &&
                    paths[1] === subItem.name;
                  const content = subItem.content;
                  return (
                    <>
                      {content && (
                        <Content key={subItem.name} active={subItemSelected}>
                          {React.isValidElement(content)
                            ? content
                            : React.createElement(content)}
                        </Content>
                      )}
                    </>
                  );
                } else {
                  return null;
                }
              });
            } else {
              if (item.content) {
                return (
                  <Content key={item.name} active={selected}>
                    {React.isValidElement(item.content)
                      ? item.content
                      : React.createElement(item.content)}
                  </Content>
                );
              } else {
                return null;
              }
            }
          })}
        </Box>
      </Row>
    </>
  );
}

const Content = ({
  active,
  children,
}: {
  active?: boolean;
  children: React.ReactNode;
}) => {
  const [rendered, setRendered] = useState(active);

  if (active && !rendered) {
    setRendered(true);
  }

  if (!rendered) return null;

  return (
    <View
      style={[
        StyleSheet.absoluteFill,
        {
          opacity: active ? 1 : 0,
          zIndex: active ? 1 : -1,
        },
      ]}>
      {children}
    </View>
  );
};

const getInitialPath = (initialPath = '', items: SectionsProps['items']) => {
  if (initialPath) return initialPath;
  if (items.length === 0) return undefined;
  if (items[0].subItems && items[0].subItems.length > 0)
    return `${items[0].name}.${items[0].subItems[0].name}`;
  else return items[0].name;
};
