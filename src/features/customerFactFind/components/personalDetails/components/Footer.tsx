import React from 'react';
import { Box, But<PERSON>, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';

export const Footer = ({
  onSave,
  onNext,
  disabledNext = false,
}: {
  onSave: () => void;
  onNext: () => void;
  disabledNext?: boolean;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { sizes } = useTheme();
  return (
    <Container>
      <Row alignItems="center" justifyContent="flex-end">
        <Button
          disabled={disabledNext}
          loading={false}
          text={t('customerFactFind:save')}
          variant="secondary"
          style={{ paddingEnd: sizes[5], width: 116 }}
          onPress={onSave}
        />
        <Button
          disabled={disabledNext}
          loading={false}
          text={t('customerFactFind:next')}
          style={{ width: 161, paddingEnd: sizes[2] }}
          onPress={onNext}
        />
      </Row>
    </Container>
  );
};
const Container = styled(Box)(({ theme: { sizes, colors } }) => ({
  flex: 1,
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  width: '100%',
  backgroundColor: colors.palette.white,
  padding: sizes[4],
  paddingEnd: sizes[6],
  paddingLeft: sizes[6],
}));
