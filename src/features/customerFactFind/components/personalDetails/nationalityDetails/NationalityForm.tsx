import Input from 'components/Input';
import { Column, Row, TextField } from 'cube-ui-components';
import { isEmpty } from 'ramda';
import React, { useEffect, useMemo } from 'react';
import {
  Control,
  UseFormClearErrors,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Country, Nationality, OptionList, State } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { MY_COUNTRY } from 'constants/optionList';
import AutocompletePopup from '../../../../../components/AutocompletePopup';
import { useTheme } from '@emotion/react';
import ReadOnlyField from 'features/eApp/components/tablet/common/ReadOnlyField';

export interface NationalityFields {
  nationality: string;
  countryOfBirth: string;
  stateOfBirth: string;
  cityOfBirth: string;
  cityName: string;
}

interface Props {
  isFromSI?: boolean;
  disabled?: boolean;
  isMainInsured?: boolean;
  control: Control<NationalityFields>;
  setValue: UseFormSetValue<NationalityFields>;
  clearErrors: UseFormClearErrors<NationalityFields>;
  readonlyFields?: (keyof NationalityFields)[];
}

export default function NationalityForm({
  isFromSI,
  disabled,
  control,
  isMainInsured,
  setValue,
  clearErrors,
  readonlyFields = [],
}: Props) {
  const { t } = useTranslation(['customerFactFind']);
  const { space, colors } = useTheme();

  const nationality = useWatch({
    name: 'nationality',
    control,
  });
  const countryOfBirth = useWatch({
    name: 'countryOfBirth',
    control,
  });
  const stateOfBirth = useWatch({
    name: 'stateOfBirth',
    control,
  });
  const cityOfBirth = useWatch({
    name: 'cityOfBirth',
    control,
  });
  const cityName = useWatch({
    name: 'cityName',
    control,
  });

  const { data: rawOptionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const optionList = rawOptionList as OptionList<'my'> | undefined;

  const stateList = useMemo(() => {
    const countryOption = countryOfBirth
      ? (optionList?.COUNTRY.options ?? []).find(
          i => String(i.value) === countryOfBirth,
        )
      : undefined;

    return countryOption
      ? (optionList?.STATE.options ?? []).filter(
          i => i.key === countryOption.lookupKey,
        )
      : [];
  }, [countryOfBirth, optionList]);

  const cityList = useMemo(() => {
    const stateOption = stateOfBirth
      ? (optionList?.STATE.options ?? []).find(
          i => String(i.value) === stateOfBirth,
        )
      : undefined;

    return stateOption
      ? (optionList?.CITY.options ?? []).filter(
          i => i.key === stateOption.lookupKey,
        )
      : [];
  }, [stateOfBirth, optionList]);

  const isMY = countryOfBirth === MY_COUNTRY || countryOfBirth === '';

  useEffect(() => {
    if (isMY) {
      if (isEmpty(cityOfBirth)) {
        setValue('cityName', '');
      } else {
        const cityOption = optionList?.CITY.options.find(
          i => i.value === cityOfBirth,
        );
        setValue('cityName', cityOption?.label || '');
      }
    }
  }, [cityOfBirth, isMY, optionList, setValue]);
  const shouldHighlight = readonlyFields.length > 0;

  return (
    <Column pt={34} px={space[6]} bgColor={colors.background}>
      {/* 1 */}
      <Row gap={space[6]}>
        {readonlyFields.includes('nationality') ? (
          <ReadOnlyField
            label={t('customerFactFind:personalDetails.form.nationality')}
            value={
              optionList?.NATIONALITY.options.find(o => o.value === nationality)
                ?.label
            }
            withRowHasInput
          />
        ) : (
          <Input
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name="nationality"
            label={t('customerFactFind:personalDetails.form.nationality')}
            data={optionList?.NATIONALITY.options ?? []}
            disabled={
              isLoadingOptionList || isFromSI || disabled
            }
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1, marginTop: 7 }}
            searchable
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
        )}
        {readonlyFields.includes('countryOfBirth') ? (
          <ReadOnlyField
            label={t('customerFactFind:personalDetails.form.countryOfBirth')}
            value={
              optionList?.COUNTRY.options.find(o => o.value === countryOfBirth)
                ?.label
            }
          />
        ) : (
          <Input
            control={control}
            as={AutocompletePopup<Country, string>}
            name="countryOfBirth"
            label={t('customerFactFind:personalDetails.form.countryOfBirth')}
            data={optionList?.COUNTRY.options ?? []}
            disabled={isLoadingOptionList || disabled}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1, marginTop: 7 }}
            onChange={(value: string) => {
              const isMY = value === 'MYS';
              if (!isMY) {
                setValue('cityOfBirth', 'Other');
                setValue('cityName', '');
              }
              clearErrors('cityOfBirth');
              clearErrors('cityName');
              if (!isEmpty(cityOfBirth) && isMY) {
                const cityOption = optionList?.CITY.options.find(
                  i => i.value === cityOfBirth,
                );
                setValue('cityName', cityOption?.label || '');
              } else {
                setValue('cityName', '');
              }
            }}
            searchable
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
        )}
      </Row>
      {/* 2 */}
      <Row gap={space[6]} mt={space[5]}>
        {readonlyFields.includes('stateOfBirth') ? (
          <ReadOnlyField
            label={t('customerFactFind:personalDetails.form.stateOfBirth')}
            value={
              optionList?.STATE.options.find(o => o.value === stateOfBirth)
                ?.label
            }
          />
        ) : (
          <Input
            control={control}
            as={AutocompletePopup<State, string>}
            name="stateOfBirth"
            style={{ flex: 1, marginTop: 7 }}
            label={t('customerFactFind:personalDetails.form.stateOfBirth')}
            disabled={
              isEmpty(countryOfBirth) || isLoadingOptionList || disabled
            }
            data={stateList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            onChange={() => {
              setValue('cityOfBirth', isMY ? '' : 'Other');
              setValue('cityName', '');
            }}
            searchable
            shouldHighlightOnUntouched={value =>
              !isEmpty(countryOfBirth) && shouldHighlight && !value
            }
          />
        )}
        {isMY ? (
          readonlyFields.includes('cityOfBirth') ? (
            <ReadOnlyField
              label={t('customerFactFind:personalDetails.form.cityOfBirth')}
              value={
                optionList?.CITY.options.find(o => o.value === cityOfBirth)
                  ?.label
              }
            />
          ) : (
            <Input
              control={control}
              as={AutocompletePopup<{ value: string; label: string }, string>}
              name="cityOfBirth"
              style={{ flex: 1, marginTop: 7 }}
              label={t('customerFactFind:personalDetails.form.cityOfBirth')}
              disabled={
                isEmpty(countryOfBirth) || isLoadingOptionList || disabled
              }
              data={cityList}
              getItemLabel={item => item.label}
              getItemValue={item => item.value}
              searchable
              shouldHighlightOnUntouched={value =>
                !isEmpty(countryOfBirth) && shouldHighlight && !value
              }
            />
          )
        ) : readonlyFields.includes('cityOfBirth') ? (
          <ReadOnlyField
            label={t('customerFactFind:personalDetails.form.cityOfBirth')}
            value={cityOfBirth}
          />
        ) : (
          <Input
            control={control}
            as={TextField}
            name="cityOfBirth"
            style={{ flex: 1, marginTop: 7 }}
            label={t('customerFactFind:personalDetails.form.cityOfBirth')}
            disabled={true}
          />
        )}
      </Row>

      {/* 3 */}
      <Row gap={space[6]} mt={space[5]}>
        {readonlyFields.includes('cityName') ? (
          <ReadOnlyField
            label={t('customerFactFind:personalDetails.form.cityName')}
            value={cityName}
          />
        ) : (
          <Input
            control={control}
            as={TextField}
            name="cityName"
            label={t('customerFactFind:personalDetails.form.cityName')}
            style={{ flex: 1 }}
            disabled={isMY || countryOfBirth === '' || disabled}
            shouldHighlightOnUntouched={value =>
              !isMY && countryOfBirth !== '' && shouldHighlight && !value
            }
          />
        )}
        <Column flex={1} />
      </Row>
    </Column>
  );
}
