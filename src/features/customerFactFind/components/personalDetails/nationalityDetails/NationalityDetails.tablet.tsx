import { CertificateOwnerDetailFormSchemaType } from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import React from 'react';
import { Control, UseFormClearErrors, UseFormSetValue } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import NationalityForm, { NationalityFields } from './NationalityForm';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import SectionContainer from '../../SectionContainer';

interface Props {
  isFromSI?: boolean;
  control: Control<CertificateOwnerDetailFormSchemaType>;
  setValue: UseFormSetValue<CertificateOwnerDetailFormSchemaType>;
  clearErrors: UseFormClearErrors<CertificateOwnerDetailFormSchemaType>;
  isMainInsured?: boolean;
}

const NationalityDetails = (props: Props) => {
  const { isFromSI, control, isMainInsured, setValue, clearErrors } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { space } = useTheme();

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.nationalityTitle')}>
      <NationalityForm
        isFromSI={isFromSI}
        isMainInsured={isMainInsured}
        control={control as unknown as Control<NationalityFields>}
        setValue={setValue as unknown as UseFormSetValue<NationalityFields>}
        clearErrors={
          clearErrors as unknown as UseFormClearErrors<NationalityFields>
        }
      />
      <Box h={space[6]} />
    </SectionContainer>
  );
};

export default NationalityDetails;
