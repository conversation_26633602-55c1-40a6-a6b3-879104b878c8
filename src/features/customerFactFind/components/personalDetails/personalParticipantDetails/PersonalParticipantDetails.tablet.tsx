import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { <PERSON>umn, Picker, Row, TextField } from 'cube-ui-components';
import { CertificateOwnerDetailFormSchemaType } from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormResetField,
  UseFormSetValue,
  useController,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ViewStyle } from 'react-native';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import Autocomplete from '../../../../../components/Autocomplete';
import DatePickerCalendar from '../../../../../components/DatePickerCalendar';
import { useGetOptionList, useGetTitleList } from 'hooks/useGetOptionList';
import {
  AddIDType,
  CubeTitle,
  Ethnicity,
  IdType,
  LeadSource,
  Marital,
  Religion,
} from 'types/optionList';
import { Gender, MaritalStatus } from 'types/person';
import IdNumberField from 'components/IdNumberField';
import { MY_COUNTRY, NEW_NRIC, PASSPORT } from 'constants/optionList';
import NameField from 'components/NameField';
import { convertNewNRICtoDOBAndGender } from 'features/eApp/utils/idNumberUtils';
import SectionContainer from '../../SectionContainer';
import Ocr from 'components/Ocr/Ocr';
import { useOcr } from 'features/customerFactFind/hooks/useOcr';
import OcrValidationErrorDialog from './OcrValidationErrorDialog';
import { useUpdatePrimaryIdType } from 'features/customerFactFind/hooks/useUpdatePrimaryIdType';
import useBoundStore from 'hooks/useBoundStore';

const { maxDate, minDate, defaultDate } = getDateOfBirthDropdownProps();

interface Props {
  isFromSI?: boolean;
  isMainInsured?: boolean;
  isPO?: boolean;
  control: Control<CertificateOwnerDetailFormSchemaType>;
  setValue: UseFormSetValue<CertificateOwnerDetailFormSchemaType>;
  getValues: UseFormGetValues<CertificateOwnerDetailFormSchemaType>;
  disableGenderAndDob: boolean;
  disableMaritalStatus: boolean;
  resetField?: UseFormResetField<CertificateOwnerDetailFormSchemaType>;
  ocrCapture?: ReturnType<typeof useOcr>['ocrCapture'];
}

const PersonalParticipantDetails = (props: Props) => {
  const {
    isFromSI,
    isMainInsured,
    isPO,
    control,
    disableGenderAndDob,
    disableMaritalStatus,
    resetField,
    setValue,
    getValues,
    ocrCapture,
  } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();
  const [personalAge, setPersonalAge] = useState<string>('');
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();
  const caseId = useBoundStore(state => state.case.caseId);

  const { maleTitles: MALES, femaleTitles: FEMALES } = useGetTitleList(
    optionList?.CUBE_TITLE.options ?? [],
  );

  const {
    field: { onChange: onChangeDob },
  } = useController({
    name: 'dob',
    control,
  });

  const personalDob = useWatch({
    name: 'dob',
    control: control,
  });

  const personalTitle = useWatch({
    name: 'title',
    control: control,
  });

  const {
    field: { onChange: onChangeGender },
  } = useController({
    name: 'gender',
    control,
  });

  const personalGender = useWatch({
    name: 'gender',
    control: control,
  });

  const personalIdentificationNumber = useWatch({
    name: 'identificationNumber',
    control: control,
  });

  const additionalIDType = useWatch({
    name: 'additionalIDType',
    control: control,
  });

  const additionalIdentification = useWatch({
    name: 'additionalIdentification',
    control: control,
  });

  const personalPrimaryIdType = useWatch({
    name: 'primaryIdType',
    control: control,
  });

  const {
    field: { onChange: onChangeMaritalStatus },
  } = useController({
    name: 'maritalStatus',
    control,
  });

  const {
    field: { onChange: onChangePrimaryIdType },
  } = useController({
    name: 'primaryIdType',
    control,
  });

  const nationality = useWatch({
    name: 'nationality',
    control,
  });

  const isNricNew = personalPrimaryIdType === NEW_NRIC;

  useEffect(() => {
    if (isNricNew && !disableGenderAndDob) {
      const { dateOfBirth } = convertNewNRICtoDOBAndGender(
        personalIdentificationNumber,
      );
      if (dateOfBirth) {
        onChangeDob(dateOfBirth);
      }
    }
  }, [
    disableGenderAndDob,
    isNricNew,
    personalIdentificationNumber,
  ]);

  useEffect(() => {
    if (personalDob) {
      setPersonalAge(`${calculateAge(new Date(personalDob))}`);
    } else {
      setPersonalAge('');
    }
  }, [personalDob]);

  useEffect(() => {
    if (personalTitle && !disableGenderAndDob) {
      onChangeGender(
        MALES.findIndex(e => e?.value === personalTitle) !== -1
          ? Gender.MALE
          : Gender.FEMALE,
      );
    }
  }, [personalTitle]);

  useEffect(() => {
    if (disableMaritalStatus) {
      onChangeMaritalStatus(
        optionList?.MARITAL.options.find(
          e => e?.value === MaritalStatus.MARRIED,
        )?.value ?? '',
      );
    }
  }, [
    disableMaritalStatus,
    onChangeMaritalStatus,
    optionList?.MARITAL.options,
  ]);
  
  const { isDisabledOcr } = useUpdatePrimaryIdType(
    onChangePrimaryIdType,
    nationality,
  );

  const titleList = useMemo(() => {
    let list = optionList?.CUBE_TITLE.options ?? [];
    if (disableGenderAndDob && personalGender) {
      list = personalGender === Gender.MALE ? MALES : FEMALES;
    }
    return list;
  }, [
    optionList?.CUBE_TITLE.options,
    disableGenderAndDob,
    personalGender,
    MALES,
    FEMALES,
  ]);

  const id = useWatch({ name: 'id', control });
  const {
    ocrRef,
    ocrImage,
    ocrCapture: tempOcrCapture,
    ocrValidationResult,
    ocrValidationMismatchFields,
    ocrValidationErrorVisible,
    onFinishOcr,
    onDeleteOcr,
    onRetake,
    onSkip,
    onRecreateQuote,
  } = useOcr({
    partyId: id,
    setValue,
    getValues,
  });

  if (ocrCapture) {
    ocrCapture.current = tempOcrCapture.current;
  }

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.participantTitle')}>
      <Content>
        {isPO && !isDisabledOcr && (
          <>
            <Ocr
              ref={ocrRef}
              caseId={caseId ?? ''}
              partyId={id ?? ''}
              defaultImage={ocrImage}
              onFinish={onFinishOcr}
              onDelete={onDeleteOcr}
              style={{ marginTop: 0 }}
            />
            <OcrValidationErrorDialog
              visible={ocrValidationErrorVisible}
              result={ocrValidationResult}
              fields={ocrValidationMismatchFields}
              onRetake={onRetake}
              onSkip={onSkip}
              onRecreateQuote={onRecreateQuote}
            />
          </>
        )}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<CubeTitle, string>}
            name="title"
            label={t('customerFactFind:personalDetails.form.title')}
            disabled={isLoadingOptionList}
            data={titleList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
          />
          <Input
            control={control}
            as={NameField}
            name="fullName"
            label={t('customerFactFind:personalDetails.form.fullName')}
            style={{ flex: 1 }}
            disabled={isFromSI}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<IdType, string>}
            name="primaryIdType"
            label={t('customerFactFind:personalDetails.form.primaryIdType')}
            data={optionList?.ID_TYPE.options ?? []}
            disabled
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1 }}
          />
          <Input
            control={control}
            as={IdNumberField}
            idType={personalPrimaryIdType}
            name="identificationNumber"
            label={t(
              'customerFactFind:personalDetails.form.identificationNumber',
            )}
            style={{ flex: 1 }}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<AddIDType, string>}
            name="additionalIDType"
            label={
              additionalIDType
                ? t(
                    'customerFactFind:personalDetails.form.additionalIDType.required',
                  )
                : t('customerFactFind:personalDetails.form.additionalIDType')
            }
            data={optionList?.ADD_ID_TYPE.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
            onChange={(value: string | null) => {
              // For the case empty value
              if (value === additionalIDType || value === '') {
                resetField?.('additionalIdentification');
                resetField?.('additionalIDType');
              }
            }}
          />
          <Input
            control={control}
            as={TextField}
            name="additionalIdentification"
            label={
              additionalIDType
                ? t(
                    'customerFactFind:personalDetails.form.additionalIdentification.required',
                  )
                : t(
                    'customerFactFind:personalDetails.form.additionalIdentification',
                  )
            }
            style={{ flex: 1 }}
            value={additionalIDType ? additionalIdentification : ''}
            disabled={!additionalIDType}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Picker}
            name="gender"
            label={t('customerFactFind:gender')}
            disabled
            items={optionList?.GENDER.options.map(o => ({
              value: o.value,
              text: o.label,
            }))}
            style={{ flex: 1 }}
          />

          <Row gap={space[4]} flex={1}>
            <Input
              control={control}
              as={DatePickerCalendar}
              name="dob"
              label={t('customerFactFind:personalDetails.form.dateOfBirth')}
              defaultDate={personalDob ?? defaultDate}
              minDate={minDate}
              maxDate={maxDate}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              hint={t('customerFactFind:personalDetails.form.dateOfBirth.hint')}
              style={{ flex: 4 }}
              disabled={disableGenderAndDob || isNricNew || isFromSI}
            />
            <TextField
              label={t('customerFactFind:personalDetails.form.age')}
              value={personalAge}
              disabled
              style={{ flex: 1.5 }}
              inputStyle={{ color: colors.palette.fwdGreyDarker } as ViewStyle}
            />
          </Row>
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Ethnicity, string>}
            name="ethnicity"
            style={{ flex: 1 }}
            label={t('customerFactFind:personalDetails.form.race')}
            data={optionList?.ETHNICITY.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />

          <Input
            control={control}
            as={Autocomplete<Religion, string>}
            name="religion"
            style={{ flex: 1 }}
            label={t('customerFactFind:personalDetails.form.religion')}
            data={optionList?.RELIGION.options ?? []}
            disabled={isLoadingOptionList || (isFromSI && isMainInsured)}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Marital, string>}
            name="maritalStatus"
            label={t('customerFactFind:personalDetails.form.maritalStatus')}
            data={optionList?.MARITAL.options ?? []}
            disabled={isLoadingOptionList || disableMaritalStatus}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1 }}
          />

          <Input
            control={control}
            as={Picker}
            name="smokingHabit"
            label={t('customerFactFind:smokingHabit')}
            disabled={isFromSI}
            items={optionList?.SMK_STATUS.options.map(o => ({
              value: o.value,
              text: o.label,
            }))}
            style={{ flex: 1 }}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<LeadSource, string>}
            name="source"
            label={t('customerFactFind:personalDetails.form.source')}
            data={optionList?.LEAD_SOURCE.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
          />
          <Column flex={1} />
        </RowBox>
      </Content>
    </SectionContainer>
  );
};

export default PersonalParticipantDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  gap: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
}));
