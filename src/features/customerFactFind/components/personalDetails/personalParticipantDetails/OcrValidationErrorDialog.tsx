import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { Button, Column, H6, LargeBody, LargeLabel } from 'cube-ui-components';
import React, { useMemo } from 'react';
import { TouchableOpacity } from 'react-native';
import { Gender } from 'types/person';
import {
  MismatchFields,
  OcrValidationResult,
} from 'features/eAppV2/common/utils/validateOcr';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTranslation } from 'react-i18next';

export default function OcrValidationErrorDialog({
  visible,
  result,
  fields,
  onRetake,
  onSkip,
  onRecreateQuote,
}: {
  visible: boolean;
  result: OcrValidationResult;
  fields: MismatchFields;
  onRetake: () => void;
  onSkip: () => void;
  onRecreateQuote: () => void;
}) {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();

  const mismatchFields = useMemo(() => {
    return (
      ['firstName', 'lastName', 'fullName', 'gender', 'dateOfBirth'] as const
    )
      .filter(field => field in fields)
      .map(field => {
        switch (field) {
          case 'firstName':
          case 'lastName':
          case 'fullName':
            return t('eApp:ocr.error.field', {
              key: t(`eApp:ocr.error.${field}`),
              value: fields[field] || t('eApp:ocr.error.missing'),
            });
          case 'gender':
            return t('eApp:ocr.error.field', {
              key: t(`eApp:ocr.error.${field}`),
              value:
                fields[field] === Gender.MALE
                  ? t('eApp:ocr.error.gender.male')
                  : fields[field] === Gender.FEMALE
                  ? t('eApp:ocr.error.gender.female')
                  : t('eApp:ocr.error.missing'),
            });
          case 'dateOfBirth':
            return t('eApp:ocr.error.field', {
              key: t(`eApp:ocr.error.${field}`),
              value: fields[field]
                ? dateFormatUtil(fields[field])
                : t('eApp:ocr.error.missing'),
            });
        }
      });
  }, [fields, t]);

  return (
    <DialogPhone visible={visible}>
      <H6 fontWeight="bold">{t('eApp:ocr.error.mismatch')}</H6>
      <Column marginY={space[4]}>
        {mismatchFields.map(label => (
          <LargeBody key={label} color={colors.error}>
            {label}
          </LargeBody>
        ))}
      </Column>
      <LargeLabel>
        {result === OcrValidationResult.NameMismatch &&
          t('eApp:ocr.error.nameMismatchWarning')}
        {result === OcrValidationResult.DobOrGenderMismatch &&
          t('eApp:ocr.error.dobOrGenderMismatchWarning')}
      </LargeLabel>
      <Column mt={space[6]} alignItems="center">
        <Button
          style={{
            alignSelf: 'stretch',
            maxWidth: isWideScreen ? 400 : undefined,
          }}
          text={t('eApp:ocr.error.retake')}
          onPress={onRetake}
        />
        <Button
          style={{
            alignSelf: 'stretch',
            maxWidth: isWideScreen ? 400 : undefined,
            marginTop: space[3],
          }}
          variant="secondary"
          text={t('eApp:ocr.error.skip')}
          onPress={onSkip}
        />
        <TouchableOpacity onPress={onRecreateQuote}>
          <LargeLabel
            style={{ marginTop: space[6] }}
            fontWeight="bold"
            color={colors.primary}>
            {t('eApp:ocr.error.newApplication')}
          </LargeLabel>
        </TouchableOpacity>
      </Column>
    </DialogPhone>
  );
}
