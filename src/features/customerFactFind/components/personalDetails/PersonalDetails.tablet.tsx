import styled from '@emotion/native';
import {
  Box,
  Button,
  Column,
  H6,
  H7,
  <PERSON>con,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import {
  ChildDependentDetailFormValidationSchema,
  childDependentDetailFormValidationSchema,
  initialChildDependentDetailFormData,
} from 'features/customerFactFind/validations/childDependentDetailSchema';
import {
  SpouseDetailFormSchemaType,
  initialSpouseDetailFormData,
  spouseDetailFormValidationSchema,
} from 'features/customerFactFind/validations/spouseDetailsSchema';
import {
  CertificateOwnerDetailFormSchemaType,
  initialCertificateOwnerDetailFormData,
  certificateOwnerDetailFormValidationSchema,
} from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useForm,
  useWatch,
} from 'react-hook-form';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { shallow } from 'zustand/shallow';
import { Footer } from '../footer/Footer';
import AddressInformation from './addressInformation/AddressInformation.tablet';
import ChildDependentDetail from './childDependentDetail/ChildDependentDetail.tablet';
import ContactDetails from './contactDetails/ContactDetails.tablet';
import NationalityDetails from './nationalityDetails/NationalityDetails.tablet';
import OccupationDetails from './occupationDetails/OccupationDetails.tablet';
import PersonalParticipantDetails from './personalParticipantDetails/PersonalParticipantDetails.tablet';
import { useTranslation } from 'react-i18next';
import Sections, { ItemSectionProps } from './components/Sections';
import { calculateAge } from 'utils/helper/calculateAge';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { PartyRole } from 'types/party';
import { Relationship } from 'types/optionList';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { AddressInfo } from './addressInformation/AddressInformationForm';
import { RelationshipValue } from 'features/proposal/types';
import { TouchableOpacity } from 'react-native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { useTheme } from '@emotion/react';
import LoadingIndicator from 'components/LoadingIndicator';
import useToggle from 'hooks/useToggle';
import DialogPhone from 'components/Dialog.phone';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CHANNELS } from 'types/channel';
import VulnerableConsumerDeclaration from './vulnerableConsumerDeclaration/VulnerableConsumerDeclaration';
import { useDeleteParty } from 'hooks/useParty';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import ErrorSVG from 'features/customerFactFind/assets/ErrorSVG';
import ErrorDialog from '../errorDialog/ErrorDialog';
import { MaritalStatus } from 'types/person';
import { useCFFSaveParty } from 'features/customerFactFind/hooks/useCFFSaveParty';
import { useOcr, useSaveOcr } from 'features/customerFactFind/hooks/useOcr';

const PersonalDetails = () => {
  const { sizes, colors, space } = useTheme();
  const [
    isRemovingSectionModalVisible,
    showRemovingSectionModal,
    hideRemovingSectionModal,
  ] = useToggle();
  const [isRemovingSection, setIsRemovingSection] = useState(false);

  const [visible, show, hide] = useToggle();
  const ocrCapture =
    useRef<ReturnType<typeof useOcr>['ocrCapture']['current']>();
  const { saveOcr } = useSaveOcr();

  const {
    updateCertificateOwnerDetail,
    updateSpouseDetail,
    updateChildDependentDetail,
    personalDetails,
    processingStep,
    updateStep,
    hasSpouse,
    setHasSpouse,
    hasChild,
    setHasChild,
    financialStatement,
    saveFinancialChildEducation,
    setCertificateOwnerId,
    setSpouseId,
  } = useCustomerFactFindStore(
    state => ({
      updateCertificateOwnerDetail: state.updateCertificateOwnerDetail,
      updateSpouseDetail: state.updateSpouseDetail,
      updateChildDependentDetail: state.updateChildDependentDetail,
      personalDetails: state.personalDetails,
      processingStep: state.processingStep,
      updateStep: state.updateStep,
      hasSpouse: state.hasSpouse,
      setHasSpouse: state.setHasSpouse,
      hasChild: state.hasChild,
      setHasChild: state.setHasChild,
      financialStatement: state.financialStatement,
      saveFinancialChildEducation: state.saveFinancialChildEducation,
      setCertificateOwnerId: state.setCertificateOwnerId,
      setSpouseId: state.setSpouseId,
    }),
    shallow,
  );

  const certificateOwnerScrollRef = useRef<KeyboardAwareScrollView>(null);
  const spouseScrollRef = useRef<KeyboardAwareScrollView>(null);
  const childScrollRef = useRef<KeyboardAwareScrollView>(null);

  const certificateOwnerValidationResolver = useYupResolver(
    certificateOwnerDetailFormValidationSchema,
  );
  const spouseDetailsValidationResolver = useYupResolver(
    spouseDetailFormValidationSchema,
  );
  const childDependentValidationResolver = useYupResolver(
    childDependentDetailFormValidationSchema,
  );

  const {
    control: certificateOwnerFormControl,
    setValue: setCertificateOwnerValue,
    getValues: getCertificateOwnerValue,
    trigger: triggerCertificateOwner,
    clearErrors: clearErrorsCertificateOwner,
    watch: watchCertificateOwner,
    formState: { isValid: isPersonalDetailValid },
    resetField: resetPersonalDetailField,
    reset: resetPersonalDetailForm,
  } = useForm<CertificateOwnerDetailFormSchemaType>({
    mode: 'onBlur',
    defaultValues:
      personalDetails.certificateOwnerDetail ||
      initialCertificateOwnerDetailFormData,
    resolver: certificateOwnerValidationResolver,
  });

  const {
    control: spouseDetailFormControl,
    setValue: setSpouseDetailValue,
    getValues: getSpouseDetailValue,
    trigger: triggerSpouseDetail,
    clearErrors: clearErrorsSpouseDetail,
    watch: watchSpouseDetail,
    reset: resetSpouseDetailValue,
    resetField: resetSpouseDetailField,
    formState: { isValid: isSpouseDetailValid },
  } = useForm<SpouseDetailFormSchemaType>({
    mode: 'onBlur',
    defaultValues: personalDetails.spouseDetail || initialSpouseDetailFormData,
    resolver: spouseDetailsValidationResolver,
  });

  const {
    control: childDependentFormControl,
    setValue: setChildDependentValue,
    getValues: getChildDependentValue,
    reset: resetChildDependentValue,
    watch: watchChildDependentValue,
    formState: { isValid: isChildDetailValid },
  } = useForm<ChildDependentDetailFormValidationSchema>({
    mode: 'onBlur',
    defaultValues: {
      data: personalDetails.childDependentDetail || [
        initialChildDependentDetailFormData,
      ],
    },
    resolver: childDependentValidationResolver,
  });

  const { t } = useTranslation(['customerFactFind']);
  const [activePath, setActivePath] = useState('certificateOwner');

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { data: optionList } = useGetOptionList();
  const hasSpouseInsured = useMemo(
    () =>
      (optionList?.RELATIONSHIP.options as Relationship<string, 'my'>[])
        .filter(
          i =>
            (
              caseObj?.parties?.filter(
                p =>
                  p.roles.includes(PartyRole.INSURED) &&
                  !p.roles.includes(PartyRole.PROPOSER) &&
                  p.relationship === i.value,
              ) || []
            ).length > 0,
        )
        ?.some?.(option => option.group === RelationshipValue.SPOUSE),
    [caseObj?.parties, optionList?.RELATIONSHIP.options],
  );
  const hasChildInsured = useMemo(
    () =>
      (optionList?.RELATIONSHIP.options as Relationship<string, 'my'>[])
        .filter(
          i =>
            (
              caseObj?.parties?.filter(
                p =>
                  p.roles.includes(PartyRole.INSURED) &&
                  !p.roles.includes(PartyRole.PROPOSER) &&
                  p.relationship === i.value,
              ) || []
            ).length > 0,
        )
        ?.some?.(option => option.group !== RelationshipValue.SPOUSE),
    [caseObj?.parties, optionList?.RELATIONSHIP.options],
  );
  const isSpouseMainInsured = useMemo(
    () =>
      hasSpouseInsured &&
      caseObj?.parties?.find(p => p.id === personalDetails.spouseDetail.id)
        ?.isMainInsured,
    [caseObj?.parties, hasSpouseInsured, personalDetails.spouseDetail.id],
  );

  const { mutateAsync: deleteParty } = useDeleteParty();
  const { onCFFSaveParty } = useCFFSaveParty();
  const [isSaving, setIsSaving] = useState(false);
  const goNext = async () => {
    setIsSaving(true);
    let certificateOwnerDetail = personalDetails.certificateOwnerDetail;
    let spouseDetail = personalDetails.spouseDetail;
    let childDependentDetail = personalDetails.childDependentDetail;
    try {
      certificateOwnerDetail = getCertificateOwnerValue();
      updateCertificateOwnerDetail(certificateOwnerDetail);
      if (hasSpouse) {
        spouseDetail = getSpouseDetailValue();
        updateSpouseDetail(spouseDetail);
      }
      if (hasChild) {
        childDependentDetail = getChildDependentValue().data;
        updateChildDependentDetail(childDependentDetail);
        // update child education
        if (processingStep === 2) {
          const newChildEducation = financialStatement.childEducation
            .informations
            ? [...financialStatement.childEducation.informations]
            : [
                {
                  name: '',
                  age: '',
                  yearsToTertiaryEducation: '',
                  existingFund: '',
                  additionalAmount: '',
                },
              ];

          childDependentDetail.forEach((child, idx) => {
            if (idx <= newChildEducation.length - 1) {
              newChildEducation[idx] = {
                ...newChildEducation[idx],
                name: child.fullName || '',
                age: child.dob ? String(calculateAge(child.dob)) : '',
              };
            } else {
              newChildEducation.push({
                name: child.fullName || '',
                age: child.dob ? String(calculateAge(child.dob)) : '',
                yearsToTertiaryEducation: '',
                existingFund: '',
                additionalAmount: '',
              });
            }
          });

          saveFinancialChildEducation({
            informations: newChildEducation,
          });
        }
      }
      const childIds: string[] = Array(childDependentDetail.length).fill('');
      await onCFFSaveParty(
        async (role, idx, id) => {
          switch (role) {
            case 'owner':
              setCertificateOwnerValue('id', id);
              setCertificateOwnerId(id);
              await saveOcr(PartyRole.PROPOSER, id, ocrCapture.current);
              break;
            case 'spouse':
              setSpouseDetailValue('id', id);
              setSpouseId(id);
              break;
            case 'child':
              childIds[idx] = id;
              break;
          }
        },
        {
          personalDetails: {
            certificateOwnerDetail,
            spouseDetail,
            childDependentDetail,
          },
        },
      );
      const newChildDependentDetail = [...getChildDependentValue().data].map(
        (c, idx) => ({
          ...c,
          id: childIds[idx],
        }),
      );
      setChildDependentValue('data', newChildDependentDetail);
      updateChildDependentDetail(newChildDependentDetail);
    } catch {
      show();
      return;
    } finally {
      setIsSaving(false);
    }
    updateStep();
  };

  const handleSubmit = () => {
    if (activePath === 'certificateOwner') {
      if (hasSpouse) {
        setActivePath('spouse');
      } else if (hasChild) {
        setActivePath('childDependent');
      } else if (isPersonalDetailValid) {
        goNext();
      }
    } else if (activePath === 'spouse') {
      if (hasChild && isSpouseDetailValid) {
        setActivePath('childDependent');
      } else if (isPersonalDetailValid && isSpouseDetailValid) {
        goNext();
      }
    } else if (activePath === 'childDependent') {
      if (
        isPersonalDetailValid &&
        (!hasSpouse || isSpouseDetailValid) &&
        isChildDetailValid
      ) {
        goNext();
      }
    }
  };

  const ownerOccupationGroup = watchCertificateOwner('occupationGroup');
  const spouseOccupationGroup = watchSpouseDetail('occupationGroup');

  const certificateOwnerIncompletenessStatus = useIncompleteFields({
    control: certificateOwnerFormControl,
    schema: certificateOwnerDetailFormValidationSchema,
    watch: watchCertificateOwner,
    scrollRef: certificateOwnerScrollRef,
    scrollTo: ({ y }) =>
      certificateOwnerScrollRef.current?.scrollToPosition?.(0, y || 0, true),
  });

  const spouseIncompletenessStatus = useIncompleteFields({
    control: spouseDetailFormControl,
    schema: spouseDetailFormValidationSchema,
    watch: watchSpouseDetail,
    scrollRef: spouseScrollRef,
    scrollTo: ({ y }) =>
      spouseScrollRef.current?.scrollToPosition?.(0, y || 0, true),
  });

  const childIncompletenessStatus = useIncompleteFields({
    control: childDependentFormControl,
    schema: childDependentDetailFormValidationSchema,
    watch: watchChildDependentValue,
    scrollRef: childScrollRef,
    scrollTo: ({ y }) =>
      childScrollRef.current?.scrollToPosition?.(0, y || 0, true),
  });

  const totalIncompleteRequiredFields = useMemo(() => {
    switch (activePath) {
      case 'certificateOwner':
        return certificateOwnerIncompletenessStatus.totalIncompleteRequiredFields;
      case 'spouse':
        return spouseIncompletenessStatus.totalIncompleteRequiredFields;
      case 'childDependent':
        return childIncompletenessStatus.totalIncompleteRequiredFields;
    }
  }, [
    activePath,
    certificateOwnerIncompletenessStatus.totalIncompleteRequiredFields,
    childIncompletenessStatus.totalIncompleteRequiredFields,
    spouseIncompletenessStatus.totalIncompleteRequiredFields,
  ]);

  const focusOnNextIncompleteField = useMemo(() => {
    switch (activePath) {
      case 'certificateOwner':
        return certificateOwnerIncompletenessStatus.focusOnNextIncompleteField;
      case 'spouse':
        return spouseIncompletenessStatus.focusOnNextIncompleteField;
      case 'childDependent':
        return childIncompletenessStatus.focusOnNextIncompleteField;
    }
  }, [
    activePath,
    certificateOwnerIncompletenessStatus.focusOnNextIncompleteField,
    childIncompletenessStatus.focusOnNextIncompleteField,
    spouseIncompletenessStatus.focusOnNextIncompleteField,
  ]);

  const channel = useGetCubeChannel();

  useEffect(() => {
    setCertificateOwnerValue('agentChannel', channel);
  }, [channel, setCertificateOwnerValue]);

  const cOMaritalStatus = watchCertificateOwner('maritalStatus');

  const removeSpouse = useCallback(async () => {
    setIsRemovingSection(true);
    try {
      if (
        caseId &&
        personalDetails.spouseDetail.id &&
        caseObj?.parties?.find(p => p.id === personalDetails.spouseDetail.id)
      ) {
        await deleteParty({
          caseId,
          partyId: personalDetails.spouseDetail.id,
        });
      }
      updateSpouseDetail(initialSpouseDetailFormData);
      resetSpouseDetailValue(initialSpouseDetailFormData);
      setActivePath('certificateOwner');
      setHasSpouse(false);
    } finally {
      setIsRemovingSection(false);
    }
  }, [
    caseId,
    caseObj?.parties,
    deleteParty,
    personalDetails.spouseDetail.id,
    resetSpouseDetailValue,
    setHasSpouse,
    updateSpouseDetail,
  ]);

  useEffect(() => {
    if (cOMaritalStatus !== MaritalStatus.MARRIED && !hasSpouseInsured) {
      removeSpouse();
    }
  }, [cOMaritalStatus, hasSpouseInsured, removeSpouse]);

  const isCOMarried = useMemo(() => {
    return cOMaritalStatus === MaritalStatus.MARRIED;
  }, [cOMaritalStatus]);

  const ownerAddressValues = watchCertificateOwner([
    'correspondenceAddress',
    'correspondenceAddressLine1',
    'correspondenceAddressLine2',
    'correspondenceAddressLine3',
    'correspondencePostCode',
    'correspondenceCity',
    'correspondenceState',
    'correspondenceCountry',
    'residentialAddress',
    'residentialAddressLine1',
    'residentialAddressLine2',
    'residentialAddressLine3',
    'residentialPostCode',
    'residentialCity',
    'residentialState',
    'residentialCountry',
    'businessAddress',
    'businessAddressLine1',
    'businessAddressLine2',
    'businessAddressLine3',
    'businessPostCode',
    'businessCity',
    'businessState',
    'businessCountry',
  ]);

  const ownerAddress = useMemo(
    () => ({
      correspondenceAddress: ownerAddressValues[0],
      correspondenceAddressLine1: ownerAddressValues[1],
      correspondenceAddressLine2: ownerAddressValues[2],
      correspondenceAddressLine3: ownerAddressValues[3],
      correspondencePostCode: ownerAddressValues[4],
      correspondenceCity: ownerAddressValues[5],
      correspondenceState: ownerAddressValues[6],
      correspondenceCountry: ownerAddressValues[7],
      residentialAddress: ownerAddressValues[8],
      residentialAddressLine1: ownerAddressValues[9],
      residentialAddressLine2: ownerAddressValues[10],
      residentialAddressLine3: ownerAddressValues[11],
      residentialPostCode: ownerAddressValues[12],
      residentialCity: ownerAddressValues[13],
      residentialState: ownerAddressValues[14],
      residentialCountry: ownerAddressValues[15],
      businessAddress: ownerAddressValues[16],
      businessAddressLine1: ownerAddressValues[17],
      businessAddressLine2: ownerAddressValues[18],
      businessAddressLine3: ownerAddressValues[19],
      businessPostCode: ownerAddressValues[20],
      businessCity: ownerAddressValues[21],
      businessState: ownerAddressValues[22],
      businessCountry: ownerAddressValues[23],
    }),
    [
      ownerAddressValues[0],
      ownerAddressValues[1],
      ownerAddressValues[2],
      ownerAddressValues[3],
      ownerAddressValues[4],
      ownerAddressValues[5],
      ownerAddressValues[6],
      ownerAddressValues[7],
      ownerAddressValues[8],
      ownerAddressValues[9],
      ownerAddressValues[10],
      ownerAddressValues[11],
      ownerAddressValues[12],
      ownerAddressValues[13],
      ownerAddressValues[14],
      ownerAddressValues[15],
      ownerAddressValues[16],
      ownerAddressValues[17],
      ownerAddressValues[18],
      ownerAddressValues[19],
      ownerAddressValues[20],
      ownerAddressValues[21],
      ownerAddressValues[22],
      ownerAddressValues[23],
    ],
  );

  const sections = useMemo(
    () =>
      [
        {
          name: 'certificateOwner',
          title: t('customerFactFind:personalDetails.menu.personal'),
          content: (
            <ScrollViewContainer
              ref={certificateOwnerScrollRef}
              keyboardShouldPersistTaps="handled"
              enableResetScrollToCoords={false}>
              <PersonalParticipantDetails
                isFromSI
                isPO
                control={certificateOwnerFormControl}
                setValue={setCertificateOwnerValue}
                getValues={getCertificateOwnerValue}
                disableGenderAndDob={true}
                disableMaritalStatus={hasSpouseInsured}
                resetField={resetPersonalDetailField}
                ocrCapture={ocrCapture}
              />
              <NationalityDetails
                isFromSI
                control={certificateOwnerFormControl}
                setValue={setCertificateOwnerValue}
                clearErrors={clearErrorsCertificateOwner}
              />
              <OccupationDetails
                isMainInsured={getCertificateOwnerValue('isMainInsured')}
                isFromSI
                control={certificateOwnerFormControl}
              />
              <ContactDetails
                isOwner
                control={certificateOwnerFormControl}
                setValue={setCertificateOwnerValue}
              />
              <AddressInformation
                isFromSI
                ownerAddress={ownerAddress}
                control={
                  certificateOwnerFormControl as unknown as Control<AddressInfo>
                }
                setValue={
                  setCertificateOwnerValue as unknown as UseFormSetValue<AddressInfo>
                }
                getValues={
                  getCertificateOwnerValue as unknown as UseFormGetValues<AddressInfo>
                }
                trigger={
                  triggerCertificateOwner as unknown as UseFormTrigger<AddressInfo>
                }
                isOwner
                occupationGroup={ownerOccupationGroup}
              />
              <VulnerableConsumerDeclaration
                control={certificateOwnerFormControl}
              />
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
        isCOMarried && {
          name: 'spouse',
          title: t('customerFactFind:personalDetails.menu.spouse'),
          content: (
            <ScrollViewContainer
              ref={spouseScrollRef}
              keyboardShouldPersistTaps="handled"
              enableResetScrollToCoords={false}>
              <PersonalParticipantDetails
                isFromSI={hasSpouseInsured}
                isMainInsured={isSpouseMainInsured}
                control={spouseDetailFormControl}
                setValue={setSpouseDetailValue}
                getValues={getSpouseDetailValue}
                disableGenderAndDob={hasSpouseInsured}
                disableMaritalStatus={true}
                resetField={resetSpouseDetailField}
              />
              <NationalityDetails
                isFromSI={hasSpouseInsured}
                isMainInsured={isSpouseMainInsured}
                control={spouseDetailFormControl}
                setValue={setSpouseDetailValue}
                clearErrors={clearErrorsSpouseDetail}
              />
              <OccupationDetails
                isMainInsured={getSpouseDetailValue('isMainInsured')}
                isFromSI={hasSpouseInsured}
                control={spouseDetailFormControl}
              />
              <ContactDetails
                control={spouseDetailFormControl}
                setValue={setSpouseDetailValue}
              />
              <AddressInformation
                isFromSI={hasSpouseInsured}
                ownerAddress={ownerAddress}
                control={
                  spouseDetailFormControl as unknown as Control<AddressInfo>
                }
                setValue={
                  setSpouseDetailValue as unknown as UseFormSetValue<AddressInfo>
                }
                getValues={
                  getSpouseDetailValue as unknown as UseFormGetValues<AddressInfo>
                }
                trigger={
                  triggerSpouseDetail as unknown as UseFormTrigger<AddressInfo>
                }
                occupationGroup={spouseOccupationGroup}
              />
              {!hasSpouseInsured && (
                <>
                  <Row justifyContent="center">
                    {isRemovingSection ? (
                      <LoadingIndicator size={sizes[4]} />
                    ) : (
                      <TouchableOpacity
                        style={{
                          alignItems: 'center',
                          flexDirection: 'row',
                        }}
                        hitSlop={ICON_HIT_SLOP}
                        onPress={() => {
                          showRemovingSectionModal();
                        }}>
                        <Icon.Delete
                          width={sizes[4]}
                          height={sizes[4]}
                          fill={colors.palette.fwdOrange[100]}
                        />
                        <LargeLabel
                          fontWeight="bold"
                          color={colors.primary}
                          style={{ marginLeft: space[1] }}>
                          {t('customerFactFind:personalDetails.remove.spouse')}
                        </LargeLabel>
                      </TouchableOpacity>
                    )}
                  </Row>
                  <BoxSpace />
                </>
              )}
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
        {
          name: 'childDependent',
          title: t('customerFactFind:personalDetails.menu.childDependent'),
          content: (
            <ScrollViewContainer
              ref={childScrollRef}
              keyboardShouldPersistTaps="handled"
              enableResetScrollToCoords={false}>
              <ChildDependentDetail
                isFromSI={hasChildInsured}
                control={childDependentFormControl}
                setValue={setChildDependentValue}
              />
              {!hasChildInsured && (
                <>
                  <Row justifyContent="center">
                    {isRemovingSection ? (
                      <LoadingIndicator size={sizes[4]} />
                    ) : (
                      <TouchableOpacity
                        style={{
                          alignItems: 'center',
                          flexDirection: 'row',
                        }}
                        hitSlop={ICON_HIT_SLOP}
                        onPress={() => {
                          showRemovingSectionModal();
                        }}>
                        <Icon.Delete
                          width={sizes[4]}
                          height={sizes[4]}
                          fill={colors.palette.fwdOrange[100]}
                        />
                        <LargeLabel
                          fontWeight="bold"
                          color={colors.primary}
                          style={{ marginLeft: space[1] }}>
                          {t(
                            'customerFactFind:personalDetails.remove.childDependent',
                          )}
                        </LargeLabel>
                      </TouchableOpacity>
                    )}
                  </Row>
                  <BoxSpace />
                </>
              )}
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
      ].filter(Boolean) as ItemSectionProps[],
    [
      t,
      certificateOwnerFormControl,
      setCertificateOwnerValue,
      hasSpouseInsured,
      resetPersonalDetailField,
      clearErrorsCertificateOwner,
      ownerAddress,
      getCertificateOwnerValue,
      triggerCertificateOwner,
      ownerOccupationGroup,
      isCOMarried,
      isSpouseMainInsured,
      spouseDetailFormControl,
      setSpouseDetailValue,
      resetSpouseDetailField,
      clearErrorsSpouseDetail,
      getSpouseDetailValue,
      triggerSpouseDetail,
      spouseOccupationGroup,
      isRemovingSection,
      sizes,
      colors.palette.fwdOrange,
      colors.primary,
      space,
      hasChildInsured,
      childDependentFormControl,
      setChildDependentValue,
      showRemovingSectionModal,
    ],
  );

  let isPrimaryDisabled = false;
  if (activePath === 'certificateOwner') {
    isPrimaryDisabled = !isPersonalDetailValid;
  } else if (activePath === 'spouse') {
    if (hasChild) {
      isPrimaryDisabled = !isSpouseDetailValid;
    } else {
      isPrimaryDisabled = !isPersonalDetailValid || !isSpouseDetailValid;
    }
  } else if (activePath === 'childDependent') {
    isPrimaryDisabled =
      !isPersonalDetailValid ||
      (hasSpouse && !isSpouseDetailValid) ||
      !isChildDetailValid;
  }

  return (
    <Container>
      <Sections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <DeleteConfirmationModal
        visible={isRemovingSectionModalVisible}
        onDismiss={hideRemovingSectionModal}
        onAgree={async () => {
          hideRemovingSectionModal();
          setIsRemovingSection(true);
          try {
            if (activePath === 'spouse') {
              if (
                caseId &&
                personalDetails.spouseDetail.id &&
                caseObj?.parties?.find(
                  p => p.id === personalDetails.spouseDetail.id,
                )
              ) {
                await deleteParty({
                  caseId,
                  partyId: personalDetails.spouseDetail.id,
                });
              }
              updateSpouseDetail(initialSpouseDetailFormData);
              resetSpouseDetailValue(initialSpouseDetailFormData);
              setActivePath('certificateOwner');
              setHasSpouse(false);
            } else if (activePath === 'childDependent') {
              if (caseId) {
                for (
                  let i = 0;
                  i < personalDetails.childDependentDetail.length;
                  i++
                ) {
                  const id = personalDetails.childDependentDetail[i].id;
                  if (id && caseObj?.parties?.find(p => p.id === id)) {
                    await deleteParty({ caseId, partyId: id });
                  }
                }
              }
              updateChildDependentDetail([initialChildDependentDetailFormData]);
              resetChildDependentValue({
                data: [initialChildDependentDetailFormData],
              });
              setHasChild(false);
              if (hasSpouse) {
                setActivePath('spouse');
              } else {
                setActivePath('certificateOwner');
              }
            }
          } finally {
            setIsRemovingSection(false);
          }
        }}
        removingSection={activePath as 'childDependent' | 'spouse'}
      />
      <Footer
        activeStep={2}
        onPrimaryPress={handleSubmit}
        isPrimaryDisabled={isPrimaryDisabled}
        isPrimaryLoading={isSaving}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnNextIncompleteField}
      />
      <ErrorDialog
        icon={<ErrorSVG />}
        message={t('customerFactFind:personalDetails.submit.failed')}
        visible={visible}
        onDismiss={() => {
          hide();
        }}
        onConfirm={async () => {
          hide();
          goNext();
        }}
      />
    </Container>
  );
};

export default PersonalDetails;

const Container = styled(Column)(({ theme: { colors } }) => ({
  backgroundColor: colors.surface,
  flex: 1,
}));

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    marginRight: space[6],
    paddingTop: space[6],
  }),
);

const DeleteConfirmationModal = ({
  removingSection,
  visible,
  onDismiss,
  onAgree,
}: {
  removingSection: 'childDependent' | 'spouse';
  visible: boolean;
  onDismiss: () => void;
  onAgree: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { space } = useTheme();
  return (
    <DialogPhone visible={visible}>
      <Box p={space[6]}>
        <H6 fontWeight="bold">
          {t('customerFactFind:personalDetails.removingSectionConfirmation')}
        </H6>
        <Box height={space[4]} />
        <H7 fontWeight="normal">
          {t(
            removingSection === 'childDependent'
              ? 'customerFactFind:personalDetails.removingSectionConfirmation.descForChildSection'
              : 'customerFactFind:personalDetails.removingSectionConfirmation.descForSpouseSection',
          )}
        </H7>
        <Row
          marginTop={space[6]}
          gap={space[4]}
          width={'100%'}
          justifyContent="center">
          <Button
            variant="secondary"
            text={t('customerFactFind:cancel')}
            style={{ flex: 1, maxWidth: 200 }}
            onPress={onDismiss}
            size="medium"
          />
          <Button
            onPress={onAgree}
            style={{ flex: 1, maxWidth: 200 }}
            variant="primary"
            text={t('customerFactFind:remove')}
            size="medium"
          />
        </Row>
      </Box>
    </DialogPhone>
  );
};
