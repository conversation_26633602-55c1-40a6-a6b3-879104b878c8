import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, Checkbox, Column, H6, Icon, Row } from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Country, OptionList, Postcode } from 'types/optionList';
import {
  MY_OPTION_LIST,
  MY_COUNTRY,
  OWNER_ADDRESS_OPTION,
  NEW_ADDRESS_OPTION,
  CORRESPONDENCE_ADDRESS_OPTION,
  NON_INCOME_OCC_GROUP,
  MAIN_INSURED_ADDRESS_OPTION,
} from 'constants/optionList';
import { TFuncKey } from 'i18next';
import { useAddressInformationOptions } from 'features/customerFactFind/hooks/useAddressInformationOptions';
import styled from '@emotion/native';
import { View } from 'react-native';
import React, { useCallback, useEffect, useLayoutEffect } from 'react';
import { useWatch } from 'react-hook-form';
import AutocompletePopup from '../../../../../components/AutocompletePopup';
import { MYAddressType } from 'features/eApp/validations/eAppCommonSchema';
import AddressLineField from 'components/AddressLineField';

export type AddressInfo = {
  correspondenceAddress?:
    | typeof OWNER_ADDRESS_OPTION
    | typeof MAIN_INSURED_ADDRESS_OPTION
    | typeof NEW_ADDRESS_OPTION;
  correspondenceAddressLine1?: string;
  correspondenceAddressLine2?: string;
  correspondenceAddressLine3?: string;
  correspondenceAddressLine4?: string;
  correspondencePostCode?: string;
  correspondenceCity?: string;
  correspondenceState?: string;
  correspondenceCountry?: string;
  residentialAddress?:
    | typeof CORRESPONDENCE_ADDRESS_OPTION
    | typeof NEW_ADDRESS_OPTION;
  residentialAddressLine1?: string;
  residentialAddressLine2?: string;
  residentialAddressLine3?: string;
  residentialAddressLine4?: string;
  residentialPostCode?: string;
  residentialCity?: string;
  residentialState?: string;
  residentialCountry?: string;
  businessAddress?:
    | typeof NEW_ADDRESS_OPTION
    | typeof CORRESPONDENCE_ADDRESS_OPTION
    | typeof OWNER_ADDRESS_OPTION
    | '';
  businessAddressLine1?: string;
  businessAddressLine2?: string;
  businessAddressLine3?: string;
  businessAddressLine4?: string;
  businessPostCode?: string;
  businessCity?: string;
  businessState?: string;
  businessCountry?: string;
  occupationGroup?: string;
};

interface Props {
  isFromSI?: boolean;
  isOwner?: boolean;
  addressTypes: MYAddressType[];
  ownerAddress?: AddressInfo;
  disabled?: boolean;
  occupationGroup?: string;
  control: Control<AddressInfo>;
  setValue: UseFormSetValue<AddressInfo>;
  getValues: UseFormGetValues<AddressInfo>;
  trigger: UseFormTrigger<AddressInfo>;
  shouldHighlight?: boolean;
  noAddressOption?: boolean;
  hiddenLegend?: boolean;
  isEntityPayor?: boolean;
  isPayor?: boolean;
}

export default function AddressInformationForm({
  isFromSI,
  isOwner,
  ownerAddress,
  addressTypes,
  disabled,
  occupationGroup,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight,
  noAddressOption,
  hiddenLegend,
  isEntityPayor,
  isPayor,
}: Props) {
  return (
    <>
      {addressTypes.map((type, idx, arr) => (
        <View key={type}>
          <AddressSection
            type={type}
            ownerAddress={ownerAddress}
            disabled={disabled}
            occupationGroup={occupationGroup}
            control={control}
            setValue={setValue}
            getValues={getValues}
            trigger={trigger}
            noAddressOption={
              (type === 'correspondence' && isOwner) || noAddressOption
            }
            isFromSI={isFromSI}
            shouldHighlight={shouldHighlight}
            hiddenLegend={hiddenLegend}
            isEntityPayor={isEntityPayor}
            isPayor={isPayor}
          />
          {idx !== arr.length - 1 && <Line />}
        </View>
      ))}
    </>
  );
}

const AddressSection = ({
  isFromSI,
  type,
  ownerAddress,
  noAddressOption,
  disabled,
  occupationGroup,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight,
  hiddenLegend,
  isEntityPayor,
  isPayor,
}: Omit<Props, 'addressTypes' | 'isOwner'> & {
  type: MYAddressType;
  noAddressOption?: boolean;
}) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['customerFactFind', 'eApp']);

  const { data: rawOptionList, isFetching: isLoadingOptionList } =
    useGetOptionList();
  const optionList = rawOptionList as OptionList<'my'> | undefined;

  const {
    postCodeList,
    stateList,
    cityList,
    isMY,
    onCountryChange,
    onPostCodeChange,
  } = useAddressInformationOptions(
    type,
    optionList,
    control,
    setValue,
    getValues,
    trigger,
  );

  let addressOptions = MY_OPTION_LIST.CORRESPONDENCE_ADDRESSES;
  if (type === 'residential') {
    addressOptions = MY_OPTION_LIST.RESIDENTIAL_ADDRESSES;
  } else if (type === 'business') {
    addressOptions = MY_OPTION_LIST.BUSINESS_ADDRESSES;
  }
  if (isPayor) {
    if (type === 'residential') {
      addressOptions = MY_OPTION_LIST.PAYOR_RESIDENTIAL_ADDRESSES;
    } else if (type === 'business') {
      addressOptions = MY_OPTION_LIST.PAYOR_BUSINESS_ADDRESSES;
    } else {
      addressOptions = MY_OPTION_LIST.PAYOR_CORRESPONDENCE_ADDRESSES;
    }
    if (isEntityPayor && type !== 'residential') {
      addressOptions = MY_OPTION_LIST.ENTITY_PAYOR_ADDRESSES;
    }
  }

  const selectedAddressOption = useWatch({
    name: `${type}Address`,
    control: control,
  });

  const isNewAddress = selectedAddressOption === NEW_ADDRESS_OPTION;

  const addressLine1 = ownerAddress?.[`${type}AddressLine1`];
  const addressLine2 = ownerAddress?.[`${type}AddressLine2`];
  const addressLine3 = ownerAddress?.[`${type}AddressLine3`];
  const postCode = ownerAddress?.[`${type}PostCode`];
  const city = ownerAddress?.[`${type}City`];
  const state = ownerAddress?.[`${type}State`];
  const country = ownerAddress?.[`${type}Country`];

  useEffect(() => {
    if (selectedAddressOption === OWNER_ADDRESS_OPTION) {
      // useWatch not working if setValue directly
      const timeout = setTimeout(() => {
        setValue(`${type}AddressLine1`, addressLine1 || '');
        setValue(`${type}AddressLine2`, addressLine2 || '');
        setValue(`${type}AddressLine3`, addressLine3 || '');
        setValue(`${type}PostCode`, postCode || '');
        setValue(`${type}City`, city || '');
        setValue(`${type}State`, state || '');
        setValue(`${type}Country`, country || '');
      });
      return () => clearTimeout(timeout);
    }
  }, [
    addressLine1,
    addressLine2,
    addressLine3,
    city,
    country,
    postCode,
    selectedAddressOption,
    setValue,
    state,
    type,
  ]);

  const isOptionalAddress =
    type === 'business' && occupationGroup === NON_INCOME_OCC_GROUP;

  return (
    <Column backgroundColor={colors.background} px={space[6]}>
      {!hiddenLegend && (
        <Row>
          <Icon.Location fill={colors.palette.black} />
          <H6 fontWeight="bold" style={{ marginLeft: space[1] }}>
            {t(`customerFactFind:personalDetails.title.${type}Address`)}
          </H6>
        </Row>
      )}
      {!noAddressOption && addressOptions.length > 1 ? (
        <Checkbox
          label={t(
            `eApp:certificate.${addressOptions[0].label}` as TFuncKey<['eApp']>,
          )}
          checked={selectedAddressOption === addressOptions[0].value}
          onChange={checked => {
            if (checked) {
              setValue(
                `${type}Address`,
                addressOptions[0].value as AddressInfo['correspondenceAddress'],
              );
            } else if (addressOptions.length > 1) {
              setValue(
                `${type}Address`,
                addressOptions[1].value as AddressInfo['correspondenceAddress'],
              );
              if (addressOptions[1].value === NEW_ADDRESS_OPTION) {
                setValue(`${type}AddressLine1`, '');
                setValue(`${type}AddressLine2`, '');
                setValue(`${type}AddressLine3`, '');
                setValue(`${type}PostCode`, '');
                setValue(`${type}City`, '');
                setValue(`${type}State`, '');
                setValue(`${type}Country`, MY_COUNTRY);
              }
            } else {
              setValue(`${type}Address`, '');
              setValue(`${type}AddressLine1`, '');
              setValue(`${type}AddressLine2`, '');
              setValue(`${type}AddressLine3`, '');
              setValue(`${type}PostCode`, '');
              setValue(`${type}City`, '');
              setValue(`${type}State`, '');
              setValue(`${type}Country`, MY_COUNTRY);
            }
          }}
          style={{ marginTop: 12 }}
        />
      ) : (
        <Box h={7} />
      )}
      {isNewAddress && (
        <>
          <Input
            control={control}
            as={AddressLineField}
            name={`${type}AddressLine1`}
            label={t('customerFactFind:personalDetails.form.addressLine1')}
            hint={t('customerFactFind:personalDetails.form.SubAddressLine1')}
            style={{ marginTop: 18 }}
            disabled={disabled}
            shouldHighlightOnUntouched={value =>
              Boolean(shouldHighlight && !value) && !isOptionalAddress
            }
          />
          <Input
            control={control}
            as={AddressLineField}
            name={`${type}AddressLine2`}
            label={t('customerFactFind:personalDetails.form.addressLine2')}
            hint={t('customerFactFind:personalDetails.form.SubAddressLine2')}
            style={{ marginTop: 27 }}
            disabled={disabled}
          />
          <Input
            control={control}
            as={AddressLineField}
            name={`${type}AddressLine3`}
            label={t('customerFactFind:personalDetails.form.addressLine3')}
            style={{ marginTop: 27 }}
            disabled={disabled}
          />
          {/* 1 */}
          <Row mt={27} gap={space[6]}>
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<Postcode<string, 'my'>, string>}
                name={`${type}PostCode`}
                label={t('customerFactFind:personalDetails.form.postCode')}
                data={postCodeList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={{ flex: 1 }}
                onChange={onPostCodeChange}
                searchable
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value) && !isOptionalAddress
                }
              />
            ) : (
              <Input
                control={control}
                as={AddressLineField}
                name={`${type}PostCode`}
                label={t('customerFactFind:personalDetails.form.postCode')}
                style={{ flex: 1 }}
                disabled={disabled}
                maxLength={10}
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value) && !isOptionalAddress
                }
              />
            )}
            {isMY ? (
              <Input
                control={control}
                as={AutocompletePopup<{ value: string; label: string }, string>}
                name={`${type}City`}
                label={t('customerFactFind:personalDetails.form.city')}
                data={cityList}
                disabled={isLoadingOptionList || disabled}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={{ flex: 1 }}
                searchable
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value) && !isOptionalAddress
                }
              />
            ) : (
              <Input
                control={control}
                as={AddressLineField}
                name={`${type}City`}
                label={t('customerFactFind:personalDetails.form.city')}
                style={{ flex: 1 }}
                disabled={disabled}
                maxLength={60}
                shouldHighlightOnUntouched={value =>
                  Boolean(shouldHighlight && !value) && !isOptionalAddress
                }
              />
            )}
          </Row>
          {/* 2 */}
          <Row mt={27} gap={space[6]}>
            <Input
              control={control}
              as={AutocompletePopup<{ value: string; label: string }, string>}
              name={`${type}State`}
              label={t('customerFactFind:personalDetails.form.state')}
              data={stateList}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={{ flex: 1 }}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value) && !isOptionalAddress
              }
            />
            <Input
              control={control}
              as={AutocompletePopup<Country, string>}
              name={`${type}Country`}
              label={t('customerFactFind:personalDetails.form.country')}
              data={optionList?.COUNTRY.options ?? []}
              disabled={isLoadingOptionList || disabled}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={{ flex: 1 }}
              onChange={onCountryChange}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value) && !isOptionalAddress
              }
            />
          </Row>
        </>
      )}
    </Column>
  );
};

const Line = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  height: 1,
  marginVertical: space[5],
  marginHorizontal: space[6],
}));
