import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import React from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import AddressInformationForm, { AddressInfo } from './AddressInformationForm';
import SectionContainer from '../../SectionContainer';

interface Props {
  isFromSI?: boolean;
  isOwner?: boolean;
  occupationGroup?: string;
  ownerAddress: AddressInfo;
  control: Control<AddressInfo>;
  setValue: UseFormSetValue<AddressInfo>;
  getValues: UseFormGetValues<AddressInfo>;
  trigger: UseFormTrigger<AddressInfo>;
}

const AddressInformation = (props: Props) => {
  const { t } = useTranslation(['customerFactFind']);
  const { space } = useTheme();

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.addressInfoTitle')}>
      <Box mt={space[5]} mb={space[6]}>
        <AddressInformationForm
          isFromSI={props.isFromSI}
          addressTypes={['correspondence', 'residential', 'business']}
          {...props}
        />
      </Box>
    </SectionContainer>
  );
};

export default AddressInformation;
