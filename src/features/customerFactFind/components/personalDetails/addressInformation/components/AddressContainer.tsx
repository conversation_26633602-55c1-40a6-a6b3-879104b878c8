import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { View } from 'react-native';

interface Props {
  title?: string;
  subTitle?: string;
  children?: React.ReactNode;
  lineBottom?: boolean;
}

const AddressContainer = (props: Props) => {
  const { title, subTitle, children, lineBottom } = props;
  const { colors, space } = useTheme();
  return (
    <Container>
      <Row>
        <Icon.Location fill={colors.palette.black} />
        <Typography.H6 fontWeight="bold" style={{ marginLeft: space[1] }}>
          {title}
        </Typography.H6>
      </Row>
      {subTitle && (
        <Box mt={space[5]}>
          <Typography.H8 fontWeight="bold" style={{ marginLeft: space[1] }}>
            {subTitle}
          </Typography.H8>
        </Box>
      )}
      {children}
      {lineBottom && <Line />}
    </Container>
  );
};

export default AddressContainer;

const Container = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const Line = styled(View)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  width: '100%',
  height: 1,
  marginTop: space[5],
}));
