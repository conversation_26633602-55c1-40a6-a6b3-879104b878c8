import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';

interface Props {
  title: string;
  children?: React.ReactNode;
  canRemove?: boolean;
  onRemove?: () => void;
  lineBottom?: boolean;
}

const ChildContainer = (props: Props) => {
  const {
    title,
    children,
    canRemove,
    onRemove,
    lineBottom,
  } = props;
  const { colors, space } = useTheme();
  const { t } = useTranslation(['customerFactFind']);
  return (
    <Container>
      <RowHeader>
        <Row>
          <Icon.Kid fill={colors.palette.black} />
          <Typography.H6 fontWeight="bold" style={{ marginLeft: space[1] }}>
            {title}
          </Typography.H6>
        </Row>
        {canRemove && (
          <Pressable onPress={onRemove}>
            <Icon.Delete fill={colors.palette.black} />
          </Pressable>
        )}
      </RowHeader>
      {children}
      {lineBottom && <Line />}
    </Container>
  );
};

export default ChildContainer;

const Container = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  marginTop: space[6],
}));

const RowHeader = styled(Row)(({ theme: { space } }) => ({
  gap: space[1],
  paddingHorizontal: space[6],
  justifyContent: 'space-between',
}));

const Line = styled(View)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  width: '100%',
  height: 1,
  margin: space[6],
  marginBottom: 0,
}));
