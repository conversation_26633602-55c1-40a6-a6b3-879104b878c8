import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Column,
  CurrencyTextField,
  Picker,
  Row,
  TextField,
} from 'cube-ui-components';
import { ChildDependentDetailFormSchemaType } from 'features/customerFactFind/validations/childDependentDetailSchema';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NumberSequence } from 'types';
import ChildContainer from './ChildContainer';
import { calculateAge } from 'utils/helper/calculateAge';
import Input from 'components/Input';
import { capitalizeFirstLetterOfEachWord } from 'utils';
import Autocomplete from 'components/Autocomplete';
import { Relationship } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import DatePickerCalendar from 'components/DatePickerCalendar';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ViewStyle } from 'react-native';
import { Control, useController, useWatch } from 'react-hook-form';
import { MaritalStatus } from 'types/person';
import { RelationshipValue } from 'features/proposal/types';
import NameField from 'components/NameField';

const { minDate, maxDate } = getDateOfBirthDropdownProps();

export default function ChildDependentForm({
  isFromSI,
  item,
  index,
  isLastChild,
  control,
  onChangeNameOfChild,
  onShowDeleteModal,
}: {
  isFromSI?: boolean;
  item: ChildDependentDetailFormSchemaType;
  index: number;
  isLastChild?: boolean;
  control: Control<{ data: ChildDependentDetailFormSchemaType[] }>;
  onChangeNameOfChild: (name: string, idx: number) => void;
  onShowDeleteModal: (
    item: ChildDependentDetailFormSchemaType,
    index: number,
  ) => void;
}) {
  const { t } = useTranslation(['customerFactFind', 'common']);
  const { space, colors } = useTheme();
  const [personalAge, setPersonalAge] = useState<string>('');

  const anbCheck = !personalAge || Number(personalAge) < 17;
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const fullName = useWatch({
    name: `data.${index}.fullName`,
    control: control,
  });

  const personalDob = useWatch({
    name: `data.${index}.dob`,
    control: control,
  });

  const {
    field: { onChange: onChangeMaritalStatus },
  } = useController({ name: `data.${index}.maritalStatus`, control });

  useEffect(() => {
    onChangeMaritalStatus(MaritalStatus.SINGLE);
  }, [onChangeMaritalStatus]);

  useEffect(() => {
    if (personalDob) {
      setPersonalAge(`${calculateAge(new Date(personalDob))}`);
    } else {
      setPersonalAge('');
    }
  }, [personalDob]);

  const relationships = useMemo(() => {
    return (
      (optionList?.RELATIONSHIP.options ?? []) as Relationship<string, 'my'>[]
    ).filter(
      r =>
        r.role.insured === 'True' &&
        r.isShow !== 'False' &&
        r.party.individual === 'True' && // TODO: handle entity logic in here
        r.group !== RelationshipValue.SPOUSE,
    );
  }, [optionList?.RELATIONSHIP.options]);

  return (
    <ChildContainer
      key={item?.id?.toString()}
      title={
        fullName
          ? t('customerFactFind:childInformation', { childName: fullName })
          : t('customerFactFind:nthChildInformation', {
              position: t(
                `common:position.${(index + 1) as NumberSequence<1, 5>}`,
              ),
            })
      }
      canRemove={index !== 0}
      onRemove={() => onShowDeleteModal(item, index)}
      lineBottom={!isLastChild}>
      <Column bgColor={colors.background} px={space[6]} gap={27} mt={30}>
        <RowBox>
          <Input
            control={control}
            as={NameField}
            name={`data.${index}.fullName`}
            label={t(
              'customerFactFind:personalDetails.form.nameOfChild.optional',
            )}
            style={{ flex: 1 }}
            disabled={isFromSI}
          />
          <Input
            control={control}
            as={Picker}
            name={`data.${index}.gender`}
            label={t('customerFactFind:gender')}
            items={optionList?.GENDER.options.map(o => ({
              value: o.value,
              text: o.label,
            }))}
            style={{ flex: 1 }}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Relationship, string>}
            name={`data.${index}.relationship`}
            label={t('customerFactFind:personalDetails.form.relationship')}
            data={relationships}
            disabled={isLoadingOptionList || isFromSI}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            style={{ flex: 1 }}
          />
          <Row gap={space[4]} flex={1}>
            <Input
              control={control}
              as={DatePickerCalendar}
              name={`data.${index}.dob`}
              label={t('customerFactFind:personalDetails.form.dateOfBirth')}
              minDate={minDate}
              maxDate={maxDate}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              hint={t('customerFactFind:personalDetails.form.dateOfBirth.hint')}
              style={{ flex: 4 }}
            />
            <TextField
              label={t('customerFactFind:personalDetails.form.age')}
              value={personalAge}
              disabled
              style={{ flex: 1.5 }}
              inputStyle={{ color: colors.placeholder } as ViewStyle}
            />
          </Row>
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={CurrencyTextField}
            name={`data.${index}.yearToSupport`}
            maxLength={2}
            style={{ flex: 1 }}
            label={t('customerFactFind:personalDetails.form.yearToSupport')}
          />
          <Input
            control={control}
            as={Autocomplete<{ value: string; label: string }, string>}
            name={`data.${index}.occupation`}
            label={t('customerFactFind:personalDetails.form.occupationChild')}
            disabled={anbCheck || isLoadingOptionList || isFromSI}
            data={optionList?.OCCUPATION.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1 }}
          />
        </RowBox>
      </Column>
    </ChildContainer>
  );
}

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
}));
