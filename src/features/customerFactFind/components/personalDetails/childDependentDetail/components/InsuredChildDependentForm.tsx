import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, CurrencyTextField, Row } from 'cube-ui-components';
import { ChildDependentDetailFormSchemaType } from 'features/customerFactFind/validations/childDependentDetailSchema';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { NumberSequence } from 'types';
import ChildContainer from './ChildContainer';
import { calculateAge } from 'utils/helper/calculateAge';
import Input from 'components/Input';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { Control, useWatch } from 'react-hook-form';
import { Gender } from 'types/person';
import ReadOnlyField from 'features/eApp/components/tablet/common/ReadOnlyField';

export default function InsuredChildDependentForm({
  item,
  index,
  control,
  isLastChild,
}: {
  item: ChildDependentDetailFormSchemaType;
  index: number;
  control: Control<{ data: ChildDependentDetailFormSchemaType[] }>;
  isLastChild?: boolean;
}) {
  const { t } = useTranslation(['customerFactFind', 'common']);
  const { space, colors } = useTheme();

  const { data: optionList } = useGetOptionList();

  const personalAge = item.dob
    ? `${calculateAge(new Date(item.dob))} ${t(
        'customerFactFind:personalDetails.form.shortYearOld',
      )}`
    : null;

  const fullName = useWatch({
    name: `data.${index}.fullName`,
    control: control,
  });

  const gender = useWatch({
    name: `data.${index}.gender`,
    control: control,
  });

  const relationship = useWatch({
    name: `data.${index}.relationship`,
    control: control,
  });

  const occupation = useWatch({
    name: `data.${index}.occupation`,
    control: control,
  });

  return (
    <ChildContainer
      key={item?.id?.toString()}
      title={
        fullName
          ? t('customerFactFind:childInformation', { childName: fullName })
          : t('customerFactFind:nthChildInformation', {
              position: t(
                `common:position.${(index + 1) as NumberSequence<1, 5>}`,
              ),
            })
      }
      lineBottom={!isLastChild}>
      <Column bgColor={colors.background} px={space[6]} gap={space[5]} mt={30}>
        <RowBox>
          <ReadOnlyField
            value={fullName}
            label={t('customerFactFind:personalDetails.form.nameOfChild')}
          />
          <ReadOnlyField
            value={
              optionList?.RELATIONSHIP.options.find(
                e => relationship == e.value,
              )?.label
            }
            label={t('customerFactFind:personalDetails.form.relationship')}
          />
        </RowBox>

        <RowBox>
          <ReadOnlyField
            value={
              item.dob ? `${dateFormatUtil(item.dob)} (${personalAge})` : ''
            }
            label={t('customerFactFind:personalDetails.form.dateOfBirth')}
          />
          <ReadOnlyField
            value={
              gender === Gender.MALE
                ? t('customerFactFind:male')
                : t('customerFactFind:female')
            }
            label={t('customerFactFind:gender')}
          />
        </RowBox>

        <RowBox>
          <ReadOnlyField
            value={
              optionList?.OCCUPATION.options.find(e => occupation == e.value)
                ?.label
            }
            label={t('customerFactFind:personalDetails.form.occupationChild')}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`data.${index}.yearToSupport`}
            maxLength={2}
            style={{ flex: 1, marginTop: 7 }}
            label={t('customerFactFind:personalDetails.form.yearToSupport')}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>
      </Column>
    </ChildContainer>
  );
}

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
}));
