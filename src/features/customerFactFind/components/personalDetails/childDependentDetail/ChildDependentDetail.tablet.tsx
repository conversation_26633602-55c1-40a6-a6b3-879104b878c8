import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Button, Icon, Box } from 'cube-ui-components';
import { ChildDependentDetailFormSchemaType } from 'features/customerFactFind/validations/childDependentDetailSchema';
import React, { useCallback, useRef, useState } from 'react';
import {
  Control,
  UseFormReset,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { DeleteModal } from '../components/DeleteModal';
import { initialChildDependentDetailFormData } from '../../../validations/childDependentDetailSchema';
import { NumberSequence } from 'types';
import ChildDependentForm from './components/ChildDependentForm';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import InsuredChildDependentForm from './components/InsuredChildDependentForm';
import { PartyRole } from 'types/party';
import { useDeleteParty } from 'hooks/useParty';
import SectionContainer from '../../SectionContainer';
interface Props {
  isFromSI?: boolean;
  control: Control<{ data: ChildDependentDetailFormSchemaType[] }>;
  setValue: UseFormSetValue<{ data: ChildDependentDetailFormSchemaType[] }>;
}

const ChildDependentDetail = (props: Props) => {
  const { isFromSI, control, setValue } = props;
  const data = useWatch({ name: 'data', control });
  const { space, colors } = useTheme();

  const { t } = useTranslation(['customerFactFind', 'common']);
  const [deleteModalVisible, setShowDeleteModal] = useState(false);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: deleteParty, isLoading: isDeletingParty } =
    useDeleteParty();
  const refItem = useRef<ChildDependentDetailFormSchemaType>();
  const refPosition = useRef<number>(0);

  const addChildHandler = () => {
    const newChild = {
      ...initialChildDependentDetailFormData,
    };
    const newData = [...data, newChild];
    setValue('data', newData);
  };

  const removeChildHandler = async () => {
    if (
      caseId &&
      refItem.current?.id &&
      caseObj?.parties?.find(p => p.id === refItem.current?.id)
    ) {
      await deleteParty({ caseId, partyId: refItem.current?.id });
      // onDelete(refItem.current?.id);
    }
    const newData = data.filter((_, curIdx) => curIdx !== refPosition.current);
    setValue('data', newData);
  };

  const onShowDeleteModal = (
    item: ChildDependentDetailFormSchemaType,
    index: number,
  ) => {
    refItem.current = item;
    refPosition.current = index;
    setShowDeleteModal(true);
  };

  const onCancelDelete = useCallback(() => {
    setShowDeleteModal(false);
  }, []);

  const onConfirmDelete = async () => {
    await removeChildHandler();
    setShowDeleteModal(false);
  };

  const renderItem = ({
    item,
    index,
  }: {
    item: ChildDependentDetailFormSchemaType;
    index: number;
  }) => {
    const isInsured = Boolean(
      caseObj?.parties?.find(
        p => p.id === item.id && p.roles.includes(PartyRole.INSURED),
      ),
    );
    if (isInsured) {
      return (
        <InsuredChildDependentForm
          key={index}
          item={item}
          index={index}
          control={control}
          isLastChild={index === data?.length - 1}
        />
      );
    }
    return (
      <ChildDependentForm
        key={index}
        isFromSI={index === 0 && isFromSI}
        item={item}
        index={index}
        control={control}
        isLastChild={index === data?.length - 1}
        onChangeNameOfChild={(name: string, idx) => {
          const newData = data.map((curItem, curIdx) => {
            if (curIdx === idx) {
              return {
                ...curItem,
                fullName: name,
              };
            }
            return curItem;
          });
          setValue('data', newData);
        }}
        onShowDeleteModal={onShowDeleteModal}
      />
    );
  };

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.menu.childDependent')}>
      {data?.map((item, index) => renderItem({ item, index }))}
      {data?.length && data.length < 5 ? (
        <ButtonAdd
          icon={<Icon.Plus fill={colors.primary} />}
          onPress={addChildHandler}
          text={t('customerFactFind:personalDetails.button.addChild')}
          mini
          size="small"
          variant={'secondary'}
        />
      ) : (
        <Box mb={space[6]} />
      )}
      <DeleteModal
        dialogVisible={deleteModalVisible}
        onConfirm={onConfirmDelete}
        onDeny={onCancelDelete}
        title={t('customerFactFind:titleDeleteDialog')}
        denyLabel={t('customerFactFind:cancel')}
        removeLabel={t('customerFactFind:remove')}
        subTitle={t('customerFactFind:contentDeleteDialog', {
          position: t(
            `common:position.${
              (refPosition.current + 1) as NumberSequence<1, 5>
            }`,
          )?.toLocaleLowerCase(),
        })}
        isDeleting={isDeletingParty}
      />
    </SectionContainer>
  );
};

export default ChildDependentDetail;

const ButtonAdd = styled(Button)(({ theme: { space } }) => ({
  marginHorizontal: space[6],
  marginVertical: space[6],
}));
