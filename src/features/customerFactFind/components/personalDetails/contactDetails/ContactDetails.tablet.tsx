import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Column, Row, TextField } from 'cube-ui-components';
import { CertificateOwnerDetailFormSchemaType } from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import React from 'react';
import { Control, UseFormSetValue } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CountryCode } from 'types/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import PhoneField from 'components/PhoneField';
import AutocompletePopup from '../../../../../components/AutocompletePopup';
import { phoneNumberStyle } from 'features/customerFactFind/styles/phoneNumberStyle';
import {
  getCountryCodeDisplayedLabel,
  getCountryCodeValue,
  getOptionListLabel,
} from 'constants/optionList';
import SectionContainer from '../../SectionContainer';

interface Props {
  isOwner?: boolean;
  control: Control<CertificateOwnerDetailFormSchemaType>;
  setValue: UseFormSetValue<CertificateOwnerDetailFormSchemaType>;
}

const ContactDetails = (props: Props) => {
  const { isOwner, control } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { space } = useTheme();
  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  return (
    <SectionContainer
      title={t('customerFactFind:personalDetails.contactTitle')}>
      <Content>
        {/* 1 */}
        <RowBox>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="mobileCountryCode"
              label={t('customerFactFind:personalDetails.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              disabled={isOwner}
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="mobileNumber"
              label={t('customerFactFind:personalDetails.form.mobileNumber')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              disabled={isOwner}
              inputStyle={phoneNumberStyle.input}
            />
          </Row>
          <Input
            control={control}
            as={TextField}
            name="email"
            label={t('customerFactFind:email')}
            style={{ flex: 1 }}
          />
        </RowBox>
        {/* 2 */}
        <RowBox>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="homeCountryCode"
              label={t('customerFactFind:personalDetails.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              disabled={isLoadingOptionList}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="homeNumber"
              label={t('customerFactFind:personalDetails.form.homePhone')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              inputStyle={phoneNumberStyle.input}
            />
          </Row>
          <Row flex={1}>
            <Input
              control={control}
              as={AutocompletePopup<CountryCode, string>}
              name="officeCountryCode"
              label={t('customerFactFind:personalDetails.form.countryCode')}
              data={optionList?.COUNTRY_CODE.options ?? []}
              disabled={isLoadingOptionList}
              getItemValue={getCountryCodeValue}
              getItemLabel={getOptionListLabel}
              getDisplayedLabel={getCountryCodeDisplayedLabel}
              style={{ marginRight: space[3], width: 120 }}
              searchable
              inputStyle={phoneNumberStyle.input}
            />
            <Input
              control={control}
              as={PhoneField}
              name="officeNumber"
              label={t('customerFactFind:personalDetails.form.officePhone')}
              keyboardType="number-pad"
              returnKeyType="done"
              style={{ flex: 4 }}
              inputStyle={phoneNumberStyle.input}
            />
          </Row>
        </RowBox>
      </Content>
    </SectionContainer>
  );
};

export default ContactDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  paddingTop: 34,
  gap: 27,
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
}));
