import PersonalDetailsTablet from '../../../../features/customerFactFind/components/personalDetails/PersonalDetails.tablet';
import PersonalDetailsPhone from '../../../../features/customerFactFind/components/personalDetails/PersonalDetails.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function PersonalDetails() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? <PersonalDetailsTablet /> : <PersonalDetailsPhone />;
}
