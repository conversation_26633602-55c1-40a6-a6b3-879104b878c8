import {
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Button,
  H6,
  H7,
  H8,
  Icon,
  LargeBody,
  Picker,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CONSENT_DATA } from 'features/customerFactFind/constants/consents';
import DialogTablet from 'components/Dialog.tablet';
import { useLanguageOptions } from 'hooks/useLanguageOptions';

export const CFFAgreementModal = ({
  visible,
  onDismiss,
  onAgree,
}: {
  visible: boolean;
  onDismiss: () => void;
  onAgree: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { colors, sizes, space } = useTheme();
  const languageOptions = useLanguageOptions();
  const [consentLang, setConsentLang] = useState('en');
  return (
    <DialogContainer visible={visible} isDefaultStyle>
      <ContentContainer>
        <Row justifyContent="space-between" mb={space[4]}>
          <H6 fontWeight="bold">
            {t(
              consentLang === 'en'
                ? 'customerFactFind:agreement.title.en'
                : 'customerFactFind:agreement.title.my',
            )}
          </H6>
          <Picker
            type="chip"
            items={languageOptions}
            value={consentLang}
            onChange={setConsentLang}
          />
        </Row>
        <Row alignItems="flex-end">
          <H7 fontWeight="bold">
            {t('customerFactFind:agreement.consent.title')}
          </H7>
          <Box ml={space[2]} mr={space[1]}>
            <Icon.Warning fill={colors.error} size={sizes[5]} />
          </Box>
          <H8 color={colors.palette.alertRed}>
            {t('customerFactFind:agreement.important')}
          </H8>
        </Row>
        <Content showsVerticalScrollIndicator={false}>
          {CONSENT_DATA.LANGUAGES[consentLang as 'en' | 'my'].content.map(
            (item, index) => {
              return (
                <Row gap={space[1]} mb={space[4]} width={'100%'} key={item}>
                  <LargeBody>{`${index + 1}.`}</LargeBody>
                  <ItemText>{item}</ItemText>
                </Row>
              );
            },
          )}
          <Box mt={sizes[6]}>
            <H7 fontWeight="bold">
              {CONSENT_DATA.LANGUAGES[consentLang as 'en' | 'my'].disclosure}
            </H7>
            <Box height={sizes[4]} />
            <LargeBody>
              {
                CONSENT_DATA.LANGUAGES[consentLang as 'en' | 'my']
                  .disclosureContent
              }
            </LargeBody>
          </Box>
        </Content>
        <Row
          gap={space[4]}
          marginTop={space[6]}
          width={'100%'}
          justifyContent="center">
          <ActionButton
            variant="secondary"
            size="medium"
            text={t('customerFactFind:cancel')}
            onPress={onDismiss}
          />
          <ActionButton
            size="medium"
            onPress={onAgree}
            variant="primary"
            text={t('customerFactFind:agree')}
          />
        </Row>
      </ContentContainer>
    </DialogContainer>
  );
};

const DialogContainer = styled(DialogTablet)(({ theme }) => {
  return {
    maxHeight: theme.space[144],
    marginLeft: theme.space[20],
    marginRight: theme.space[20],
    marginTop: theme.space[16],
    marginBottom: theme.space[16],
  };
});

const Content = styled(ScrollView)(({ theme: { sizes } }) => {
  const { height } = useWindowDimensions();
  return {
    marginTop: sizes[4],
    maxHeight: height * 0.45,
  };
});

const ContentContainer = styled(Box)(({ theme: { space } }) => {
  return {
    padding: space[6],
  };
});

const ActionButton = styled(Button)({
  flex: 1,
  maxWidth: 200,
});

const ItemText = styled(LargeBody)({
  flex: 1,
});
