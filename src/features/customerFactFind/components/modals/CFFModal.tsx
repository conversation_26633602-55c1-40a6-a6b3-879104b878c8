import useLatest from 'hooks/useLatest';
import { useEffect } from 'react';
import {
  StyleProp,
  StyleSheet, ViewStyle
} from 'react-native';
import Modal from '../common/modal/Modal';
import Portal from 'components/Portal/Portal';

interface Props {
  visible?: boolean;
  dismissable?: boolean;
  transparent?: boolean;
  backdropColor?: string;
  onShow?: () => void;
  onDismiss?: () => void;
  children?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  contentContainerStyle?: StyleProp<ViewStyle>;
}

const CFFModal = ({
  visible,
  dismissable,
  backdropColor,
  onShow,
  onDismiss,
  children,
  style,
  contentContainerStyle,
}: Props): JSX.Element | null => {
  const onShowRef = useLatest(onShow);
  useEffect(() => {
    if (visible) {
      onShowRef?.current?.();
    }
  }, [onShowRef, visible]);

  return (
    <Portal>
      <Modal
        dismissable={dismissable}
        onDismiss={onDismiss}
        visible={Boolean(visible)}
        backdropColor={backdropColor}
        style={[styles.modal, style]}
        contentContainerStyle={contentContainerStyle}>
        {children}
      </Modal>
    </Portal>
  );
};

export default CFFModal;

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    marginTop: 0,
  },
});
