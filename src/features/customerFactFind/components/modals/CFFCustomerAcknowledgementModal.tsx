import {
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Button,
  Checkbox,
  H6,
  Picker,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CUSTOMER_ACKNOWLEDGEMENT } from 'features/customerFactFind/constants/consents';

import TextMore from 'components/TextMore';
import DialogTablet from 'components/Dialog.tablet';
import { useLanguageOptions } from 'hooks/useLanguageOptions';

export const CFFCustomerAcknowledgement = ({
  visible,
  isSubmitting,
  onDismiss,
  onAgree,
}: {
  visible: boolean;
  isSubmitting: boolean;
  onDismiss: () => void;
  onAgree: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { sizes, space } = useTheme();
  const [consentLang, setConsentLang] = useState('en');
  const [consentsChecked, setConsentsChecked] = useState([false, false, false]);
  const isAcceptBtnEnabled = consentsChecked.every(item => item);
  const languageOptions = useLanguageOptions();

  return (
    <DialogContainer visible={visible}>
      <ContentContainer p={space[6]}>
        <Row justifyContent="space-between" alignItems="center">
          <H6 fontWeight="bold">
            {
              CUSTOMER_ACKNOWLEDGEMENT.LANGUAGES[consentLang as 'en' | 'my']
                .title
            }
          </H6>
          <Picker
            type="chip"
            items={languageOptions}
            value={consentLang}
            onChange={setConsentLang}
          />
        </Row>
        <Content showsVerticalScrollIndicator={false}>
          {CUSTOMER_ACKNOWLEDGEMENT.LANGUAGES[
            consentLang as 'en' | 'my'
          ].content.map((item, index) => {
            const isCkbConsent = typeof item === 'object';
            return (
              <Box
                mt={sizes[4]}
                borderRadius={18}
                key={index}
                mb={index >= 2 ? sizes[4] : sizes[3]}>
                {isCkbConsent && (
                  <Checkbox
                    value={consentsChecked[index]}
                    onChange={(isChecked: boolean) => {
                      setConsentsChecked(prevState => {
                        const newState = [...prevState];
                        newState[index] = isChecked;
                        return newState;
                      });
                    }}
                    label={item.body}
                    style={{
                      alignItems: 'flex-start',
                      flex: 1,
                      marginRight: space[2],
                    }}
                    labelStyle={{
                      marginTop: -2,
                      flex: 1,
                    }}
                  />
                )}
                {!isCkbConsent && (
                  <Box flex={1}>
                    <TextMore numLines={3} text={item} />
                  </Box>
                )}
              </Box>
            );
          })}
        </Content>
        <Row
          marginTop={sizes[6]}
          justifyContent="center"
          marginBottom={sizes[2]}>
          <Button
            variant="secondary"
            size="medium"
            disabled={isSubmitting}
            text={t('customerFactFind:cancel')}
            style={{ flex: 1, maxWidth: 200 }}
            onPress={onDismiss}
            gaParams={{
              screenName: "CustomerFactFind",
              screenClass: 'CFF flow',
              actionType: 'non_cta_button',
              buttonName: 'Cancel',
            }}
          />
          <Box width={sizes[4]} />
          <Button
            size="medium"
            onPress={onAgree}
            loading={isSubmitting}
            disabled={!isAcceptBtnEnabled}
            style={{ flex: 1, maxWidth: 200 }}
            variant="primary"
            text={t('customerFactFind:accept')}
            gaParams={{
              screenName: "CustomerFactFind",
              screenClass: 'CFF flow',
              actionType: 'non_cta_button',
              buttonName: 'Accept',
            }}
          />
        </Row>
      </ContentContainer>
    </DialogContainer>
  );
};

const DialogContainer = styled(DialogTablet)(({ theme }) => {
  return {
    maxHeight: theme.space[144],
    marginLeft: theme.space[20],
    marginRight: theme.space[20],
    marginTop: theme.space[16],
    marginBottom: theme.space[16],
  };
});
const Content = styled(ScrollView)(({ theme: { sizes } }) => {
  const { height } = useWindowDimensions();
  return {
    marginTop: sizes[4],
    maxHeight: height * 0.45,
  };
});
const TouchContainer = styled(TouchableOpacity)(
  ({ theme: { colors, sizes } }) => ({
    borderRadius: sizes[8] - 2,
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[50],
    padding: sizes[3],
    paddingTop: sizes[1],
    paddingBottom: sizes[1],
    marginEnd: sizes[1],
  }),
);
const ContentContainer = styled(Box)(({ theme: { space } }) => {
  return {
    padding: space[6],
  };
});
