import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H7, Icon, Box, Row } from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';

export interface MenuProps {
  list: Array<{ id: number; key: string; name: string }>;
  pickedIndex: number;
  getIndex: (index: number) => void;
}

const Menu = (props: MenuProps) => {
  const { pickedIndex, getIndex, list } = props;
  const { colors, sizes, space } = useTheme();
  const [selected, setSelected] = useState<number>(pickedIndex);

  useEffect(() => {
    setSelected(pickedIndex);
  }, [pickedIndex]);

  const onSelect = (index: number) => {
    setSelected(index);
    getIndex(index);
  };

  return (
    <Container>
      {list.map((item, index) => {
        const isSelected = selected === index;
        const selectedColorLabel = isSelected
          ? colors.primary
          : colors.palette.fwdDarkGreen[100];
        const selectedColorIcon = isSelected
          ? colors.primary
          : colors.palette.fwdGreyDark;
        return (
          <Pressable key={index} onPress={() => onSelect(index)}>
            <RowBox
              pt={index === 0 ? 0 : space[5]}
              pb={index === list.length - 1 ? 0 : space[5]}>
              <Box flex={1}>
                <H7
                  fontWeight={isSelected ? 'bold' : 'normal'}
                  color={selectedColorLabel}>
                  {item?.name}
                </H7>
              </Box>
              <Icon.ChevronRight
                width={sizes[4]}
                height={sizes[4]}
                fill={selectedColorIcon}
              />
            </RowBox>
            {index !== list?.length - 1 && <Line />}
          </Pressable>
        );
      })}
    </Container>
  );
};

export default Menu;

const Container = styled(Column)(
  ({ theme: { borderRadius, colors, space } }) => ({
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    padding: space[4],
  }),
);

const RowBox = styled(Row)(({ theme: { colors } }) => ({
  flexDirection: 'row',
  backgroundColor: colors.background,
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const Line = styled(View)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  width: '100%',
  height: 1,
}));
