import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogTablet from 'components/Dialog.tablet';
import { Box, Button, LargeBody, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface ErrorDialogProps {
  icon: JSX.Element | undefined;
  message: string;
  visible?: boolean;
  onDismiss?: () => void;
  onConfirm?: () => void;
}

export default function ErrorDialog({
  icon,
  message,
  visible,
  onDismiss,
  onConfirm,
}: ErrorDialogProps) {
  const { sizes } = useTheme();
  const { t } = useTranslation();

  return (
    <DialogTablet visible={visible}>
      <ModalDialog>
        <Row alignContent="center" alignItems="center" justifyContent="center">
          {icon}
        </Row>
        <Box h={sizes[4]} />
        <Message>{message}</Message>
        <Box h={sizes[12]} />
        <Row justifyContent="space-between">
          <Box w={sizes[7]} />
          <Button
            variant="secondary"
            text={t('back')}
            onPress={onDismiss}
            style={{ flex: 1 }}
            size="medium"
          />
          <Box w={sizes[4]} />
          <Button
            style={{ flex: 1 }}
            text={t('tryAgain')}
            onPress={onConfirm}
            size="medium"
          />
          <Box w={sizes[7]} />
        </Row>
      </ModalDialog>
    </DialogTablet>
  );
}

const ModalDialog = styled(Box)(({ theme: { space } }) => ({
  maxWidth: 572,
  padding: space[6],
}));

const Message = styled(LargeBody)(({ theme: { colors } }) => ({
  alignSelf: 'center',
  textAlign: 'center',
  color: colors.placeholder,
}));
