import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { H7, Row } from 'cube-ui-components';
import React, { ForwardedRef, forwardRef } from 'react';
import { View } from 'react-native';

interface IProps {
  title: string;
  children?: React.ReactNode;
  icon?: React.ReactNode;
}

const SectionContainer = forwardRef(
  (props: IProps, ref: ForwardedRef<View>) => {
    const { title, children, icon } = props;
    const { colors } = useTheme();
    return (
      <Wrapper ref={ref}>
        <TitleContainer>
          {icon && icon}
          <H7
            fontWeight="bold"
            color={colors.palette.white}
            style={{ flex: 1 }}>
            {title}
          </H7>
        </TitleContainer>
        {children}
      </Wrapper>
    );
  },
);

export default SectionContainer;

const Wrapper = styled.View(({ theme: { space, colors, borderRadius } }) => ({
  marginBottom: space[5],
  backgroundColor: colors.background,
  borderRadius: borderRadius.large,
  overflow: 'hidden',
}));

const TitleContainer = styled(Row)(
  ({ theme: { space, colors, borderRadius } }) => ({
    gap: space[1],
    alignItems: 'center',
    backgroundColor: colors.secondary,
    justifyContent: 'space-between',
    paddingHorizontal: space[6],
    paddingVertical: space[3],
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
  }),
);
