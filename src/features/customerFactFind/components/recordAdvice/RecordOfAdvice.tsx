import React, { useRef, useState } from 'react';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import {
  recordOfAdviceValidationSchema,
  RecordOfAdviceSchemaFormType,
  newRecordOfAdviceItem,
} from 'features/customerFactFind/validations/recordOfAdviceValidation';
import { useFieldArray, useForm } from 'react-hook-form';
import { RecommendSolutionItem } from './components/RecommendSolutionItem';
import { Footer } from '../footer/Footer';
import { AddRecommendBtn } from './components/AddRecommendBtn';
import {
  AdviceData,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import { shallow } from 'zustand/shallow';
import { DeleteConfirmationModal } from './components/DeleteConfirmationModal';
import { ActionTakenModal } from './components/ActionTakenModal';
import { ActionTakenModal as ActionTakenModalOld } from './components/ActionTakenModal.old';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import styled from '@emotion/native';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';

export const MAXIMUM_ADVICE_ITEMS_CREATED = 5;
const RecordOfAdvice = () => {
  const { sizes } = useTheme();
  const { activeStep, updateStep, updateAdvices, recordOfAdvices } =
    useCustomerFactFindStore(
      state => ({
        activeStep: state.activeStep,
        updateAdvices: state.updateAdvices,
        updateAdvice: state.updateAdvice,
        updateStep: state.updateStep,
        recordOfAdvices: state.recordOfAdvices,
      }),
      shallow,
    );

  const recordOfAdviceResolver = useYupResolver(recordOfAdviceValidationSchema);

  const {
    control,
    watch,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = useForm<RecordOfAdviceSchemaFormType>({
    mode: 'onBlur',
    defaultValues: recordOfAdvices,
    resolver: recordOfAdviceResolver,
    shouldUnregister: true,
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control,
      watch,
      schema: recordOfAdviceValidationSchema,
      scrollRef,
      scrollTo: ({ y }) => {
        scrollRef.current?.scrollToPosition(0, y || 0, true);
      },
    });

  const { fields, remove, append } = useFieldArray({
    control,
    name: 'advices',
  });

  const [deleteConfirmModalVisible, setDeleteConfirmModalVisible] =
    useState(false);
  const [actionTakenModalVisible, setActionTakenModalVisible] = useState(false);
  const [currentAdviceIndex, setCurrentAdviceIndex] = useState<number>(-1);

  const onNext = () => {
    setActionTakenModalVisible(true);
  };
  const onDelete = () => {
    onDismiss();
    remove(currentAdviceIndex);
    // fields.splice(currentAdviceIndex, 1);
    // setValue('advices', fields);
    // trigger();
  };
  const isReachMaximumAdviceItems =
    fields.length >= MAXIMUM_ADVICE_ITEMS_CREATED;
  const onAddMore = () => {
    if (fields.length >= MAXIMUM_ADVICE_ITEMS_CREATED) {
      return;
    }

    append({
      ...newRecordOfAdviceItem,
      index: fields.length + 1,
    });
    // fields.push({
    //   ...newRecordOfAdviceItem,
    //   index: fields.length + 1,
    // });
    // setValue('advices', fields);
    // trigger();
  };
  const onDismiss = () => setDeleteConfirmModalVisible(false);

  const isPrimaryDisabled = !isValid;

  return (
    <Box flex={1}>
      <ScrollViewContainer
        ref={scrollRef}
        enableResetScrollToCoords={false}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {fields.map((field, index) => (
          <RecommendSolutionItem
            key={field.id}
            control={control}
            index={index}
            activeStep={activeStep}
            onDelete={() => {
              setCurrentAdviceIndex(index);
              setDeleteConfirmModalVisible(true);
            }}
          />
        ))}

        <AddRecommendBtn
          onPress={onAddMore}
          disabled={isReachMaximumAdviceItems}
        />
        <Box height={sizes[30]} />
      </ScrollViewContainer>
      <Footer
        activeStep={4}
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        onPrimaryPress={onNext}
        isPrimaryDisabled={isPrimaryDisabled}
      />
      <DeleteConfirmationModal
        visible={deleteConfirmModalVisible}
        onAgree={onDelete}
        onDismiss={onDismiss}
      />
      <ActionTakenModal
        control={control}
        visible={actionTakenModalVisible}
        onCancel={() => setActionTakenModalVisible(false)}
        onConfirm={() => {
          handleSubmit(data => {
            updateAdvices(data as AdviceData);
          })();
          updateStep();
          setActionTakenModalVisible(false);
        }}
      />
    </Box>
  );
};

export default RecordOfAdvice;

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { colors, sizes } }) => ({
    flex: 1,
    backgroundColor: colors.surface,
    paddingTop: sizes[6],
    paddingLeft: sizes[8],
    paddingRight: sizes[8],
  }),
);
