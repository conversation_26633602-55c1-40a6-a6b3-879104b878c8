import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Row,
  Typography,
  LargeLabel,
  SmallLabel,
} from 'cube-ui-components';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';

const ParticipateButton = styled(Pressable)<{
  selected: boolean;
  disabled?: boolean;
}>(({ theme, selected, disabled }) => {
  return {
    flex: 1,
    backgroundColor: selected
      ? theme.colors.palette.fwdOrange[5]
      : disabled
      ? theme.colors.palette.fwdGrey[20]
      : theme.colors.palette.white,
    paddingVertical: theme.space[4],
    borderWidth: selected ? 2 : 1,
    borderColor: selected
      ? disabled
        ? theme.colors.palette.fwdOrange[20]
        : theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
    alignItems: 'center',
    justifyContent: 'center',
  };
});

interface Props {
  onChange?: (value: boolean) => void;
  value?: boolean;
  disabled?: boolean;
  error?: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

export default memo(function ({
  value,
  onChange: onChangeAction,
  disabled,
  error,
  onFocus,
  onBlur,
}: Props) {
  const theme = useTheme();
  const { t } = useTranslation(['customerFactFind']);

  const onChange = (value: boolean) => {
    onChangeAction?.(value);
    onFocus?.();
    onBlur?.();
  };

  return (
    <View style={{ flex: 1 }}>
      <Row>
        <ParticipateButton
          selected={value === true}
          style={{
            borderTopLeftRadius: theme.space[1],
            borderBottomLeftRadius: theme.space[1],
          }}
          disabled={disabled}
          onPress={() => onChange?.(true)}>
          <LargeLabel
            fontWeight={value === true ? 'bold' : 'normal'}
            color={
              value === true
                ? disabled
                  ? theme.colors.primaryVariant
                  : theme.colors.primary
                : disabled
                ? theme.colors.palette.fwdGrey[100]
                : theme.colors.secondary
            }>
            {t('customerFactFind:yes')}
          </LargeLabel>
        </ParticipateButton>
        <ParticipateButton
          disabled={disabled}
          selected={value === false}
          style={{
            borderTopRightRadius: theme.space[1],
            borderBottomRightRadius: theme.space[1],
          }}
          onPress={() => onChange?.(false)}>
          <LargeLabel
            fontWeight={value === false ? 'bold' : 'normal'}
            color={
              value === false
                ? disabled
                  ? theme.colors.primaryVariant2
                  : theme.colors.primary
                : disabled
                ? theme.colors.palette.fwdGrey[100]
                : theme.colors.secondary
            }>
            {t('customerFactFind:no')}
          </LargeLabel>
        </ParticipateButton>
        <Box
          position="absolute"
          top={-theme.space[2]}
          left={theme.space[3]}
          px={theme.space[1]}
          bgColor={theme.colors.background}>
          <Typography.SmallLabel color={theme.colors.secondary}>
            {t('customerFactFind:record.participate')}
          </Typography.SmallLabel>
        </Box>
      </Row>
      {Boolean(error) && (
        <SmallLabel
          color={theme.colors.error}
          style={{ marginTop: theme.space[1], marginLeft: theme.space[4] }}>
          {error}
        </SmallLabel>
      )}
    </View>
  );
});
