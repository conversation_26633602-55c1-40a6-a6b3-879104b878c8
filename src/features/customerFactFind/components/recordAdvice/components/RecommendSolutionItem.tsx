import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Body,
  Box,
  CurrencyTextField,
  H7,
  Icon,
  Row,
  TextField,
} from 'cube-ui-components';
import React, { memo, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Control, useController, useWatch } from 'react-hook-form';
import ParticipatePicker from './ParticipatePicker';
import { ProductPreview } from './ProductPreview';

import DecimalTextField from 'components/DecimalTextField';
import { duplicateMessage } from 'features/customerFactFind/constants/cffErrorMessages';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import {
  OTHER_REASON_FOR_RECOMMENDATION,
  RecordOfAdviceSchemaFormType,
} from 'features/customerFactFind/validations/recordOfAdviceValidation';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import { useProductListQuery } from 'features/productSelection/hooks/useProducts';
import { toQuotationParty } from 'features/proposal/untils/quotationUtils';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import _ from 'lodash';
import { TouchableOpacity } from 'react-native';
import { PaymentMode4CFF, ReasonForRecommendation } from 'types/optionList';
import { Party, PartyRole } from 'types/party';
import { Insured, Proposer } from 'types/quotation';
import Autocomplete from '../../../../../components/Autocomplete';
import { cffRoutes } from '../../header/CFFStepTabBar';

export const RecommendSolutionItem = memo(
  ({
    onDelete,
    index,
    activeStep,
    control,
  }: {
    control: Control<RecordOfAdviceSchemaFormType>;
    index: number;
    activeStep: number;
    onDelete: () => void;
  }) => {
    const { t } = useTranslation(['customerFactFind']);
    const { colors, sizes } = useTheme();
    const { data: optionList, isFetching: isLoadingOptionList } =
      useGetOptionList();
    const stepRecordOfAdvice = useMemo(() => {
      return cffRoutes.findIndex(e => e.key === 'recordOfAdvice') + 1;
    }, []);
    const {
      field: { value: productType, onChange: setProductType },
    } = useController({
      name: `advices.${index}.productType`,
      control,
    });
    const nameOfPersonCoverage = useWatch({
      name: `advices.${index}.nameOfPersonCoverage`,
      control: control,
    });
    const reason2 = useWatch({
      name: `advices.${index}.reason2`,
      control: control,
    });
    const reason3 = useWatch({
      name: `advices.${index}.reason3`,
      control: control,
    });
    const reason1 = useWatch({
      name: `advices.${index}.reason1`,
      control: control,
    });

    const reason2Validated = useMemo(() => {
      if (_.isEmpty(reason1) && _.isEmpty(reason2) && _.isEmpty(reason3)) {
        return true;
      }
      if (!_.isEmpty(reason3)) {
        return reason2 !== reason1 && reason2 !== reason3;
      }
      return reason2 !== reason1;
    }, [reason1, reason2, reason3]);

    const reason3Validated = useMemo(() => {
      if (_.isEmpty(reason1) && _.isEmpty(reason2) && _.isEmpty(reason3)) {
        return true;
      }
      if (!_.isEmpty(reason2)) {
        return reason3 !== reason1 && reason2 !== reason3;
      }
      return reason3 !== reason1;
    }, [reason1, reason2, reason3]);

    const {
      field: { onChange: onChangeNameOfPersonCoverage },
    } = useController({
      name: `advices.${index}.nameOfPersonCoverage`,
      control,
    });

    const {
      mutate: getProductList,
      data: productList,
      isLoading: isLoadingProductList,
    } = useProductListQuery();

    const shouldShowDetail =
      !_.isEmpty(nameOfPersonCoverage) && !_.isEmpty(productType);

    const { personalDetails, hasSpouse, hasChild } = useCustomerFactFindStore(
      state => ({
        personalDetails: state.personalDetails,
        hasSpouse: state.hasSpouse,
        hasChild: state.hasChild,
      }),
    );
    const caseId = useBoundStore(state => state.case.caseId);
    const { data: caseObj } = useGetCase(caseId);

    /**
     * Get insured list
     */
    const insuredList = useMemo(() => {
      return (
        caseObj?.parties
          ?.filter(
            p =>
              p.roles.includes(PartyRole.PROPOSER) ||
              p.roles.includes(PartyRole.INSURED) ||
              p.roles.includes(PartyRole.DEPENDENT),
          )
          .map(p => ({
            name: p.person.name.firstName ?? '',
            id: p.id ?? '',
          })) ?? []
      );
    }, [caseObj?.parties]);
    // TODO: potential bug cuz value found by name not id
    // currently no work around solution
    const selectedPersonCovered = insuredList.find(
      i => i.name === nameOfPersonCoverage,
    );

    /**
     * Get product payload
     * @param parties
     * @returns
     */
    const getProductListByParty = useCallback(
      (insured?: Party, proposer?: Party) => {
        const payload: {
          insureds: Insured[];
          proposers: Proposer[];
        } = {
          insureds: [],
          proposers: [],
        };
        if (insured) {
          if (!insured.id) return;

          const quotationParty: Insured = {
            ...toQuotationParty(
              caseObj?.agent?.id ?? '',
              insured,
              optionList?.RELATIONSHIP.options,
              optionList?.OCCUPATION?.options,
            ),
            id: insured.id,
          };
          payload.insureds.push(quotationParty);
        }
        if (proposer) {
          if (!proposer.id) return;

          const quotationParty: Proposer = {
            ...toQuotationParty(
              caseObj?.agent?.id ?? '',
              proposer,
              optionList?.RELATIONSHIP.options,
              optionList?.OCCUPATION?.options,
            ),
            id: proposer.id,
          };
          payload.proposers.push(quotationParty);
        }
        if (insured || proposer) {
          getProductList({
            insureds: payload.insureds,
            proposers: payload.proposers,
          });
          return;
        }
        getProductList({});
      },
      [
        caseObj?.agent?.id,
        getProductList,
        optionList?.OCCUPATION?.options,
        optionList?.RELATIONSHIP.options,
      ],
    );

    useEffect(() => {
      getProductList({});
    }, [getProductList]);

    const productTypeList = useMemo(() => {
      return productList
        ?.filter(product => product?.status === 'ACTIVE')
        ?.map(item => {
          return {
            name: getProductName(item?.productName),
          };
        });
    }, [productList]);

    const selectedProduct = useMemo(
      () =>
        productList?.find(p => getProductName(p.productName) === productType),
      [productList, productType],
    );

    const isOtherReasonVisible =
      reason1 === OTHER_REASON_FOR_RECOMMENDATION ||
      reason2 === OTHER_REASON_FOR_RECOMMENDATION ||
      reason3 === OTHER_REASON_FOR_RECOMMENDATION;
    const {
      field: { onChange: onChangeOtherReason, onBlur: onBlurOtherReason },
    } = useController({
      name: `advices.${index}.otherReason`,
      control,
    });
    useEffect(() => {
      if (!isOtherReasonVisible) {
        onChangeOtherReason('');
        onBlurOtherReason();
      }
    }, [isOtherReasonVisible, onBlurOtherReason, onChangeOtherReason]);

    const paymentFrequencies = useMemo(
      () =>
        optionList?.PAYMENTMODE4_CFF?.options
          ? [
              ...optionList.PAYMENTMODE4_CFF.options,
              {
                label: t('customerFactFind:singlePremium'),
                value: t('customerFactFind:singlePremium'),
              },
            ]
          : [],
      [optionList?.PAYMENTMODE4_CFF?.options, t],
    );

    return (
      <Row gap={sizes[5]} flex={1} marginBottom={sizes[4] + 2}>
        <ProductPreview
          isLazy={!shouldShowDetail}
          productName={getProductName(selectedProduct?.productName)}
          productThumbnail={selectedProduct?.productThumbnailUrl}
        />
        <Box flex={1}>
          <Box
            width={'100%'}
            minHeight={sizes[8]}
            backgroundColor={colors.secondary}
            borderTopLeftRadius={sizes[4]}
            borderTopRightRadius={sizes[4]}
            padding={sizes[3]}
            paddingLeft={sizes[6]}
            paddingRight={sizes[6]}>
            <Row justifyContent="space-between">
              <H7 fontWeight="bold" color={colors.background}>
                {t('customerFactFind:record.recommend.solution', {
                  number: index + 1,
                })}
              </H7>
              {index > 0 && (
                <TouchableOpacity onPress={onDelete}>
                  <Icon.Delete size={sizes[6]} fill={colors.background} />
                </TouchableOpacity>
              )}
            </Row>
          </Box>
          <Box
            flex={1}
            backgroundColor={colors.background}
            padding={sizes[6]}
            borderBottomEndRadius={sizes[4]}
            borderBottomStartRadius={sizes[4]}>
            <Row justifyContent="space-between">
              <Autocomplete<
                { name: string; id: string },
                { name: string; id: string }
              >
                label={t('customerFactFind:record.person.coverage')}
                style={{ flex: 1 }}
                data={insuredList}
                disabled={isLoadingOptionList}
                value={selectedPersonCovered}
                getItemValue={i => i}
                getItemLabel={i => i.name}
                onChange={item => {
                  if (!item) return;
                  onChangeNameOfPersonCoverage(item.name);
                  if (caseObj && caseObj?.parties) {
                    const insured = caseObj.parties.find(p => p.id === item.id);
                    const owner = caseObj.parties.find(
                      p => p.id === personalDetails.certificateOwnerDetail.id,
                    );
                    setProductType('');
                    getProductListByParty(insured, owner);
                  }
                }}
              />
              <Box width={sizes[6]} />
              <Input
                control={control}
                as={Autocomplete<{ name: string }, string>}
                data={productTypeList ?? []}
                disabled={
                  isLoadingProductList || activeStep !== stepRecordOfAdvice
                }
                getItemLabel={item => item.name}
                getItemValue={item => item.name}
                name={`advices.${index}.productType`}
                label={t('customerFactFind:record.product.type')}
                style={{ flex: 1 }}
              />
            </Row>
            {shouldShowDetail && (
              <Box>
                <Row justifyContent="space-between" marginTop={sizes[5]}>
                  <Input
                    control={control}
                    as={Autocomplete<PaymentMode4CFF, string>}
                    data={paymentFrequencies}
                    disabled={isLoadingOptionList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                    name={`advices.${index}.paymentFrequency`}
                    label={t('customerFactFind:record.payment.frequency')}
                    style={{ flex: 1 }}
                  />
                  <Box width={sizes[6]} />
                  <Input
                    disabled={false}
                    control={control}
                    as={DecimalTextField}
                    name={`advices.${index}.contribution`}
                    label={t('customerFactFind:record.contribution')}
                    returnKeyType="done"
                    style={{ flex: 1 }}
                  />
                  <Box width={sizes[6]} />
                  <Input
                    disabled={false}
                    control={control}
                    as={DecimalTextField}
                    name={`advices.${index}.sumCovered`}
                    label={t('customerFactFind:record.sum.covered')}
                    returnKeyType="done"
                    style={{ flex: 1 }}
                    keyboardType="decimal-pad"
                  />
                </Row>
                <Row justifyContent="space-between" marginTop={sizes[5]}>
                  <Input
                    disabled={false}
                    control={control}
                    as={CurrencyTextField}
                    name={`advices.${index}.term`}
                    label={t('customerFactFind:record.term')}
                    returnKeyType="done"
                    maxLength={2}
                    style={{ flex: 1 }}
                  />
                  <Box width={sizes[6]} />
                  <Input
                    disabled={false}
                    control={control}
                    as={TextField}
                    name={`advices.${index}.additionalBenefits`}
                    label={t('customerFactFind:record.additional')}
                    style={{ flex: 1 }}
                  />
                  <Box width={sizes[6]} />
                  <Input
                    as={ParticipatePicker}
                    control={control}
                    name={`advices.${index}.isParticipate`}
                    disabled={false}
                  />
                </Row>
                <Box height={sizes[5]} />
                <Body fontWeight="bold">
                  {t('customerFactFind:record.recommend')}
                </Body>
                <Box height={sizes[5]} />
                <Row justifyContent="space-between">
                  <Input
                    control={control}
                    as={Autocomplete<ReasonForRecommendation, string>}
                    data={optionList?.REASON_FOR_RECOMMENDATION?.options ?? []}
                    disabled={isLoadingOptionList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                    name={`advices.${index}.reason1`}
                    label={t('customerFactFind:record.reason1')}
                    style={{ flex: 1 }}
                    multiline
                    autoExpand
                  />
                  <Box width={sizes[6]} />
                  <Input
                    control={control}
                    as={Autocomplete<ReasonForRecommendation, string>}
                    data={optionList?.REASON_FOR_RECOMMENDATION?.options ?? []}
                    disabled={isLoadingOptionList}
                    getItemLabel={item => item.label}
                    getItemValue={item => item.value}
                    name={`advices.${index}.reason2`}
                    label={t('customerFactFind:record.reason2')}
                    style={{ flex: 1 }}
                    error={reason2Validated ? undefined : t(duplicateMessage)}
                    multiline
                    autoExpand
                  />
                  <Box width={sizes[6]} />
                  <Input
                    control={control}
                    as={Autocomplete<ReasonForRecommendation, string>}
                    data={optionList?.REASON_FOR_RECOMMENDATION?.options ?? []}
                    disabled={isLoadingOptionList}
                    getItemLabel={item => item.label}
                    error={reason3Validated ? undefined : t(duplicateMessage)}
                    getItemValue={item => item.value}
                    name={`advices.${index}.reason3`}
                    label={t('customerFactFind:record.reason3')}
                    multiline
                    autoExpand
                    style={{ flex: 1 }}
                  />
                </Row>
                {isOtherReasonVisible && (
                  <>
                    <Box height={sizes[7]} />
                    <Input
                      control={control}
                      as={TextField}
                      name={`advices.${index}.otherReason`}
                      label={t('customerFactFind:record.another.reason')}
                      returnKeyType="done"
                    />
                  </>
                )}
              </Box>
            )}
          </Box>
        </Box>
      </Row>
    );
  },
);
