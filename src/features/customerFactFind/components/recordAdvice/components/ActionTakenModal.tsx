import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import Input from 'components/Input';
import {
  Box,
  Button,
  H6,
  LargeBody,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import FormsIcon from 'features/customerFactFind/icons/FormsIcon';
import FormsWithCrossIcon from 'features/customerFactFind/icons/FormsWithCrossIcon';
import React, { useMemo } from 'react';
import { Control, useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Keyboard,
  KeyboardAvoidingView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import { RecordOfAdviceSchemaFormType } from '../../../validations/recordOfAdviceValidation';
import CFFModal from '../../modals/CFFModal';
import { useSafeAreaFrame } from 'react-native-safe-area-context';

export const ActionTakenModal = ({
  visible,
  onCancel,
  onConfirm,
  control,
}: {
  control: Control<RecordOfAdviceSchemaFormType>;
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { sizes, space, colors, borderRadius } = useTheme();
  const { height } = useSafeAreaFrame();
  const {
    field: { value: disclosure, onChange: onChangeDisclosure },
  } = useController({
    control,
    name: 'disclosure',
  });
  const {
    field: { value: preference, onChange: onChangepreference },
  } = useController({
    control,
    name: 'preference',
  });
  const {
    field: { value: disclosureReason },
  } = useController({
    control,
    name: 'disclosureReason',
  });

  const reasons = React.useMemo(() => {
    return [
      {
        label: t('customerFactFind:record.action.content1.reason1'),
        value: 'A',
      },
      {
        label: t('customerFactFind:record.action.content1.reason2'),
        value: 'B',
      },
      {
        label: t('customerFactFind:record.action.content1.reason3'),
        value: 'C',
      },
      {
        label: t('customerFactFind:record.action.content1.reason4'),
        value: 'D',
      },
      {
        label: t('customerFactFind:record.action.content1.reason5'),
        value: 'E',
      },
      {
        label: t('customerFactFind:record.action.content1.reason6'),
        value: 'F',
      },
      {
        label: t('customerFactFind:record.action.content1.reason7'),
        value: 'G',
      },
    ];
  }, [t]);

  const error =
    disclosureReason && disclosureReason.length >= 10
      ? ''
      : t('customerFactFind:validation.error.minLength10');

  const isValid = useMemo(() => {
    let isValid = Boolean(disclosure);
    if (disclosure === 'yes') {
      isValid &&= Boolean(preference);
      if (preference === 'G') {
        isValid &&= Boolean(disclosureReason && disclosureReason.length >= 10);
      }
    }
    return isValid;
  }, [disclosure, disclosureReason, preference]);

  return (
    <CFFModal visible={visible}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingViewContainer behavior="padding">
          <Box
            w={952}
            maxH={height - 58}
            backgroundColor={colors.background}
            p={space[12]}
            borderRadius={borderRadius.large}>
            <H6 fontWeight="bold">
              {t('customerFactFind:record.action.title')}
            </H6>
            <Box height={sizes[4]} />
            <Row gap={space[4]}>
              <SelectButton
                key={'no'}
                selected={disclosure === 'no'}
                onPress={() => onChangeDisclosure('no')}>
                <Row m={space[4]} gap={space[2]} flex={1}>
                  <FormsIcon size={sizes[12]} />
                  <Box gap={space[1]} flex={1}>
                    <H6 fontWeight="bold">
                      {t('customerFactFind:record.action.match')}
                    </H6>
                    <LargeBody>
                      {t('customerFactFind:record.action.content1')}
                    </LargeBody>
                  </Box>
                </Row>
              </SelectButton>
              <SelectButton
                key={'yes'}
                selected={disclosure === 'yes'}
                onPress={() => onChangeDisclosure('yes')}>
                <Row m={space[4]} gap={space[2]} flex={1}>
                  <FormsWithCrossIcon size={sizes[12]} />
                  <Box gap={space[1]} flex={1}>
                    <H6 fontWeight="bold">
                      {t('customerFactFind:record.action.missMatch')}
                    </H6>
                    <LargeBody>
                      {t('customerFactFind:record.action.content2')}
                    </LargeBody>
                  </Box>
                </Row>
              </SelectButton>
            </Row>
            {disclosure === 'yes' && (
              <Box mt={space[3]} gap={space[4]}>
                <LargeBody>
                  {t('customerFactFind:record.action.content1.reason.title')}
                </LargeBody>
                <RadioButtonGroup
                  value={preference}
                  onChange={onChangepreference}>
                  {reasons.map(e => (
                    <RadioButton
                      key={e.value}
                      value={e.value}
                      style={{ alignItems: 'flex-start' }}
                      label={e.label}
                      labelStyle={{ flex: 1 }}
                    />
                  ))}
                  {preference === 'G' && (
                    <Box ml={space[8]} mb={space[3]}>
                      <Input
                        disabled={false}
                        control={control}
                        as={TextField}
                        name="disclosureReason"
                        label={t(
                          'customerFactFind:record.action.content1.reason.other',
                        )}
                        returnKeyType="done"
                        error={error}
                      />
                    </Box>
                  )}
                </RadioButtonGroup>
              </Box>
            )}

            <Row
              marginTop={sizes[6]}
              alignItems="center"
              justifyContent="center">
              <Button
                variant="secondary"
                text={t('customerFactFind:cancel')}
                style={{ flex: 1, maxWidth: 200 }}
                onPress={onCancel}
                size="medium"
              />
              <Box width={sizes[4]} />
              <Button
                disabled={!isValid}
                onPress={onConfirm}
                style={{ flex: 1, maxWidth: 200 }}
                variant="primary"
                text={t('customerFactFind:confirm')}
                size="medium"
              />
            </Row>
          </Box>
        </KeyboardAvoidingViewContainer>
      </TouchableWithoutFeedback>
    </CFFModal>
  );
};

const SelectButton = styled(TouchableOpacity)<{
  selected?: boolean;
  highlight?: boolean;
}>(({ theme, selected, highlight }) => ({
  flex: 1,
  borderWidth: selected ? 2 : 1,
  borderRadius: theme.borderRadius['x-small'],
  borderColor: highlight
    ? theme.colors.primaryVariant
    : selected
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  backgroundColor: selected
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  minHeight: theme.space[32],
  shadowColor: highlight ? theme.colors.palette.fwdOrange[100] : undefined,
  shadowOffset: highlight
    ? {
        width: 0,
        height: 0,
      }
    : undefined,
  shadowOpacity: highlight ? 0.5 : undefined,
  shadowRadius: highlight ? 4 : undefined,
}));

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
}));
