import React, { useEffect, useState } from 'react';
import { Modal, StyleSheet } from 'react-native';
import styled from '@emotion/native';
import { View } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  H6,
  RadioButton,
  RadioButtonGroup,
  Row,
  TextField,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import Input from 'components/Input';
import { Control, useController, useWatch } from 'react-hook-form';
import { RecordOfAdviceSchemaFormType } from '../../../validations/recordOfAdviceValidation';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import { shallow } from 'zustand/shallow';
import DialogPhone from 'components/Dialog.phone';

export const ActionTakenModal = ({
  visible,
  onCancel,
  onConfirm,
  control,
}: {
  control: Control<RecordOfAdviceSchemaFormType>;
  visible: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { sizes, space } = useTheme();
  const {
    field: { value: disclosure, onChange: onChangeDisclosure },
  } = useController({
    control,
    name: 'disclosure',
  });
  const disclosureReason = useWatch({
    control,
    name: 'disclosureReason',
  });
  const { updateAdviceRecordDisclosureReason } = useCustomerFactFindStore(
    state => ({
      updateAdviceRecordDisclosureReason:
        state.updateAdviceRecordDisclosureReason,
    }),
    shallow,
  );
  useEffect(() => {
    if (disclosure === 'yes') {
      updateAdviceRecordDisclosureReason(disclosureReason ?? '');
    } else {
      updateAdviceRecordDisclosureReason('');
    }
  }, [disclosure, disclosureReason, updateAdviceRecordDisclosureReason]);
  return (
    <DialogContainer visible={visible}>
      <Box p={space[6]} w={714}>
        <H6 fontWeight="bold">
          {t('customerFactFind:record.action.title.old')}
        </H6>
        <Box height={sizes[4]} />
        <RadioButtonGroup value={disclosure} onChange={onChangeDisclosure}>
          <RadioButton
            value={'yes'}
            style={{ alignItems: 'flex-start' }}
            label={`${t('customerFactFind:record.action.content1.old')}`}
            labelStyle={{ flex: 1 }}
          />
          <Box height={sizes[4]} />
          {disclosure === 'yes' && (
            <Box ml={space[8]} mb={space[3]}>
              <Input
                disabled={false}
                control={control}
                as={TextField}
                name="disclosureReason"
                label={t('customerFactFind:record.another.reason')}
                returnKeyType="done"
              />
            </Box>
          )}
          <RadioButton
            style={{ alignItems: 'flex-start' }}
            value={'no'}
            label={`${t('customerFactFind:record.action.content2.old')}`}
            labelStyle={{ flex: 1 }}
          />
        </RadioButtonGroup>

        <Row marginTop={sizes[6]} alignItems="center" justifyContent="center">
          <Button
            variant="secondary"
            text={t('customerFactFind:cancel')}
            style={{ flex: 1, maxWidth: 200 }}
            onPress={onCancel}
            size="medium"
          />
          <Box width={sizes[4]} />
          <Button
            disabled={
              disclosure === '' ||
              (disclosure === 'yes' && disclosureReason === '')
            }
            onPress={onConfirm}
            style={{ flex: 1, maxWidth: 200 }}
            variant="primary"
            text={t('customerFactFind:confirm')}
            size="medium"
          />
        </Row>
      </Box>
    </DialogContainer>
  );
};

const DialogContainer = styled(DialogPhone)(() => ({
  minWidth: 762,
}));
