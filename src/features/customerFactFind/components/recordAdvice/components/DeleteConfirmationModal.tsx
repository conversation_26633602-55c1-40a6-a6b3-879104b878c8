import React from 'react';
import { useTheme } from '@emotion/react';
import { Box, Button, H6, H7, Row } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import DialogPhone from 'components/Dialog.phone';

export const DeleteConfirmationModal = ({
  visible,
  onDismiss,
  onAgree,
}: {
  visible: boolean;
  onDismiss: () => void;
  onAgree: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { sizes, space } = useTheme();
  return (
    <DialogPhone visible={visible}>
      <Box p={space[6]}>
        <H6 fontWeight="bold">
          {t('customerFactFind:record.delete.confirmation')}
        </H6>
        <Box height={sizes[4]} />
        <H7 fontWeight="normal">
          {t('customerFactFind:record.delete.confirmation.content')}
        </H7>
        <Row marginTop={sizes[6]} width={'100%'} justifyContent="center">
          <Button
            variant="secondary"
            text={t('customerFactFind:cancel')}
            style={{ flex: 1, maxWidth: 200 }}
            onPress={onDismiss}
            size="medium"
          />
          <Box width={sizes[4]} />
          <Button
            onPress={onAgree}
            style={{ flex: 1, maxWidth: 200 }}
            variant="primary"
            text={t('customerFactFind:remove')}
            size="medium"
          />
        </Row>
      </Box>
    </DialogPhone>
  );
};
