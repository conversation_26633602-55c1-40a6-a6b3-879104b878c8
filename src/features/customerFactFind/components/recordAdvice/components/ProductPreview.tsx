import React, { useMemo } from 'react';
import { Box, H6, H8, SmallBody } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import FWDIcon from 'features/customerFactFind/icons/FWDIcon';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { useProductImageQuery } from 'features/productSelection/hooks/useProducts';

export const ProductPreview = ({
  isLazy,
  productName,
  productThumbnail,
}: {
  isLazy?: boolean;
  productName?: string;
  productThumbnail?: string;
}) => {
  const { colors, sizes } = useTheme();
  const { t } = useTranslation(['customerFactFind']);

  const { data: imageUrl } = useProductImageQuery(productThumbnail);
  const thumbnailUri = useMemo(() => ({ uri: imageUrl }), [imageUrl]);

  if (isLazy) {
    return (
      <Box
        borderRadius={12}
        backgroundColor={colors.background}
        alignSelf="flex-start"
        maxWidth={257}
        width={257}
        p={sizes[4]}
        pb={sizes[6] + 3}>
        <Box
          backgroundColor={colors.palette.fwdGrey[100]}
          borderRadius={sizes[2]}
          marginBottom={sizes[4]}
          width={225}
          justifyContent="center"
          alignItems="center"
          height={75}>
          <FWDIcon />
        </Box>
        <Box
          width={'40%'}
          height={20}
          borderRadius={sizes[1] - 2}
          backgroundColor={colors.surface}
        />
        <Box
          borderRadius={sizes[1] - 2}
          width={'100%'}
          height={20}
          backgroundColor={colors.primaryVariant}
          marginTop={sizes[1]}
        />
        <Box
          mt={sizes[8]}
          borderRadius={sizes[1] - 2}
          width={'40%'}
          height={20}
          backgroundColor={colors.primaryVariant}
          marginTop={sizes[1]}
        />
      </Box>
    );
  }

  return (
    <Box
      borderRadius={12}
      backgroundColor={colors.background}
      alignSelf="flex-start"
        maxWidth={257}
        width={257}
      p={sizes[4]}
      pb={sizes[6] + 3}>
      <Thumbnail source={thumbnailUri} resizeMode="contain" />
      <SmallBody>{t('customerFactFind:record.productPreview.title')}</SmallBody>
      <H6 color={colors.primary} fontWeight="bold">
        {productName}
      </H6>
      <Box marginTop={sizes[8]} />
      <H8 color={colors.primary} fontWeight="bold">
        {t('customerFactFind:record.details')}
      </H8>
    </Box>
  );
};

const Thumbnail = styled.Image(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.small,
  marginBottom: theme.space[4],
  width: 225,
  height: 75,
}));
