import React from 'react';
import { Box, H7, Icon, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, ViewStyle } from 'react-native';
import styled from '@emotion/native';

export const AddRecommendBtn = ({
  onPress,
  style = {},
  disabled,
}: {
  disabled?: boolean;
  onPress: () => void;
  style?: ViewStyle;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { colors, sizes } = useTheme();
  return (
    <PressableContainer disabled={disabled} style={style} onPress={onPress}>
      <Box
        borderColor={colors.primary}
        borderWidth={2}
        maxWidth={209}
        backgroundColor={colors.background}
        p={sizes[2] + 2}
        borderRadius={sizes[1]}>
        <Row justifyContent="center" alignItems="center">
          <Icon.Add size={sizes[5]} fill={colors.primary} />
          <Box w={sizes[1]} />
          <H7 color={colors.primary} fontWeight="bold">
            {t('customerFactFind:add.recommendation')}
          </H7>
        </Row>
      </Box>
    </PressableContainer>
  );
};

const PressableContainer = styled.TouchableOpacity(({ disabled }) => ({
  opacity: disabled ? 0.5 : 1,
  marginLeft: 275,
}));
