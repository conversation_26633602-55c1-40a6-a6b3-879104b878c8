import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import PotentialAreas from './PotentialAreas';
import { DraxProvider, DraxScrollView } from 'react-native-drax';
import {
  initialPrioritizationList,
  initialPrioritiseList,
  PrioritizationKey,
} from 'features/customerFactFind/constants/prioritizations';
import CustomerChoice from './CustomerChoice';
import { Footer } from '../footer/Footer';
import {
  CustomerDisclosureOption,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import { shallow } from 'zustand/shallow';
import { ScrollView } from 'react-native';

const CustomerPreference = () => {
  const {
    updateCustomerPreference,
    customerPreference,
    resetStep,
    updateStep,
  } = useCustomerFactFindStore(
    state => ({
      updateCustomerPreference: state.updateCustomerPreference,
      customerPreference: state.customerPreference,
      resetStep: state.resetStep,
      updateStep: state.updateStep,
    }),
    shallow,
  );

  const { colors, space } = useTheme();
  const [prioritiseList, setPrioritiseList] = useState(initialPrioritiseList);

  const [prioritizationList, setPrioritizationList] = useState(
    initialPrioritizationList,
  );

  const [customerChoice, setCustomerChoice] = useState<
    CustomerDisclosureOption | undefined
  >(undefined);

  useEffect(() => {
    if (customerPreference.prioritization.length !== 6) return;
    setPrioritiseList([]);

    const prioritizationList = customerPreference.prioritization.map(
      (item, index) => ({
        idx: index,
        key: item.key,
        icon: initialPrioritiseList.find(e => e.key === item.key)?.icon,
        alreadyPlanned: item.alreadyPlanned,
        toDiscuss: item.toDiscuss,
      }),
    );

    setPrioritizationList(prioritizationList);

    setCustomerChoice(customerPreference.customerChoice);
  }, [customerPreference]);

  const dragCompleted = useMemo(() => {
    return !prioritiseList.length;
  }, [prioritiseList]);

  const onNextButtonPress = () => {
    updateCustomerPreference({
      prioritization: prioritizationList.map(e => ({
        key: e.key as PrioritizationKey,
        alreadyPlanned: e.alreadyPlanned,
        toDiscuss: e.toDiscuss,
      })),
      customerChoice: customerChoice,
    });
    updateStep();
  };

  const scrollViewRef = useRef<ScrollView>(null);
  const totalIncompleteRequiredFields = useMemo(() => {
    return prioritiseList.length + (customerChoice ? 0 : 1);
  }, [customerChoice, prioritiseList.length]);
  const focusOnIncompleteField = () => {
    if (prioritiseList.length > 0) {
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    } else {
      scrollViewRef.current?.scrollToEnd();
    }
  };

  return (
    <>
      <DraxProvider>
        <DraxScrollView
          ref={scrollViewRef}
          style={{ backgroundColor: colors.palette.fwdGrey['50'] }}
          key={Math.ceil(prioritiseList.length / 3)}
          showsVerticalScrollIndicator={false}>
          <Box px={space[8]} py={space[5]} flex={1}>
            <PotentialAreas
              dragCompleted={dragCompleted}
              prioritiseList={prioritiseList}
              setPrioritiseList={setPrioritiseList}
              prioritizationList={prioritizationList}
              setPrioritizationList={setPrioritizationList}
            />
            <Box h={space[6]} />
            <CustomerChoice
              customerChoice={customerChoice}
              setCustomerChoice={setCustomerChoice}
              resetStep={resetStep}
            />
          </Box>
        </DraxScrollView>
      </DraxProvider>
      <Footer
        activeStep={1}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnIncompleteField}
        isPrimaryDisabled={!customerChoice || prioritiseList.length !== 0}
        onPrimaryPress={onNextButtonPress}
      />
    </>
  );
};

export default CustomerPreference;
