import React, { useState } from 'react';
import { Body, Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { DraxView, DraxSnapbackTargetPreset } from 'react-native-drax';
import { PrioritizationKey } from '../../constants/prioritizations';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';
import { useTranslation } from 'react-i18next';

interface PriorityEmptyProps {
  priorityIdx: number;
  onReceiveFromPotentialAreas: ({
    idx,
    key,
    icon,
  }: {
    idx: number;
    key: PrioritizationKey;
    icon: React.ComponentType<SvgPictogramProps>;
  }) => void;
  onReceiveFromPriorityList: ({
    newIdx,
    oldIdx,
    key,
    icon,
    alreadyPlanned,
    toDiscuss,
  }: {
    newIdx: number;
    oldIdx: number;
    key: PrioritizationKey;
    icon: React.ComponentType<SvgPictogramProps>;
    alreadyPlanned: boolean;
    toDiscuss: boolean;
  }) => void;
}

const PriorityEmpty = ({
  priorityIdx,
  onReceiveFromPriorityList,
  onReceiveFromPotentialAreas,
}: PriorityEmptyProps) => {
  const { colors, space } = useTheme();
  const [mainReceptive, setMainReceptive] = useState(false);
  const { t } = useTranslation(['customerFactFind']);

  return (
    <Box flex={1}>
      <DraxView
        isParent={true}
        receptive={true}
        draggable={false}
        onReceiveDragEnter={() => {
          setMainReceptive(true);
        }}
        onReceiveDragExit={() => {
          setMainReceptive(false);
        }}
        onReceiveDragDrop={({ dragged: { payload } }) => {
          setMainReceptive(false);
          if (typeof payload.priorityIdx !== 'undefined') {
            onReceiveFromPriorityList({
              newIdx: priorityIdx,
              oldIdx: payload.priorityIdx,
              key: payload.prioritiseKey,
              icon: payload.icon,
              alreadyPlanned: payload.alreadyPlanned,
              toDiscuss: payload.toDiscuss,
            });
          } else {
            onReceiveFromPotentialAreas({
              idx: priorityIdx,
              key: payload.prioritiseKey,
              icon: payload.icon,
            });
          }
          return DraxSnapbackTargetPreset.None;
        }}>
        <Box
          backgroundColor={
            mainReceptive
              ? colors.palette.fwdGrey['20']
              : colors.palette.fwdOrange['5']
          }
          justifyContent="center"
          borderRadius={space[1]}
          borderWidth={1}
          borderColor={colors.palette.fwdGrey['100']}
          borderStyle={'dashed'}
          alignItems="center"
          height={space[14]}>
          <Body color={colors.palette.fwdGreyDark}>
            {t('customerFactFind:priorityEmpty.drop')}
          </Body>
        </Box>
      </DraxView>
    </Box>
  );
};

export default PriorityEmpty;
