import React, { useMemo } from 'react';
import {
  Box,
  LargeBody,
  RadioButtonGroup,
  RadioButton,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { CustomerDisclosureOption } from 'features/customerFactFind/utils/store/customerFactFindStore';

type ChoiceListProps = {
  value: CustomerDisclosureOption;
  label: string;
};

const CustomerChoice = ({
  customerChoice,
  setCustomerChoice,
  resetStep,
}: {
  customerChoice: CustomerDisclosureOption | undefined;
  setCustomerChoice: (value: CustomerDisclosureOption | undefined) => void;
  resetStep: () => void;
}) => {
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();

  const choiceList: ChoiceListProps[] = useMemo(
    () => [
      {
        value: CustomerDisclosureOption.FULL,
        label: t('customerFactFind:customerChoice.option1'),
      },
      {
        value: CustomerDisclosureOption.PARTIAL,
        label: t('customerFactFind:customerChoice.option2'),
      },
      {
        value: CustomerDisclosureOption.NONE,
        label: t('customerFactFind:customerChoice.option3'),
      },
    ],
    [t],
  );

  return (
    <Box backgroundColor={colors.palette.white} borderRadius={space[4]}>
      <Box
        bgColor={colors.secondary}
        px={space[6]}
        py={space[3]}
        borderTopLeftRadius={space[4]}
        borderTopRightRadius={space[4]}>
        <LargeBody fontWeight="bold" color={colors.palette.white}>
          {t('customerFactFind:customerChoice.title')}
        </LargeBody>
      </Box>
      <Box m={space[6]} mt={space[3]}>
        <RadioButtonGroup value={customerChoice}>
          {choiceList.map((item, index) => (
            <ChoiceItem
              key={index}
              value={item.value}
              label={item.label}
              onSelect={() => {
                if (customerChoice === item.value) return;
                setCustomerChoice(item.value);
                resetStep();
              }}
            />
          ))}
        </RadioButtonGroup>
      </Box>
    </Box>
  );
};

export default CustomerChoice;

const ChoiceItem = styled(RadioButton)(({ theme: { space } }) => {
  return {
    alignItems: 'flex-start',
    marginTop: space[3],
  };
});
