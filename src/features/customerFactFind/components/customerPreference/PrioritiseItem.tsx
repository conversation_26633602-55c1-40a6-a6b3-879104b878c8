import React, { createElement } from 'react';
import { Box, LargeBody, Switch, Row } from 'cube-ui-components';
import Svg, { Path } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import { DraxView } from 'react-native-drax';
import {
  initialPrioritiseListProps,
  initialPrioritizationListProps,
} from 'features/customerFactFind/constants/prioritizations';
import { TFuncKey } from 'i18next';
import styled from '@emotion/native';
import Tooltip from '../../../../components/Tooltip';

const PrioritiseItem = ({
  priorityIdx,
  dragCompleted,
  prioritise,
  onUpdateSwitchData,
}: {
  dragCompleted: boolean;
  priorityIdx?: number;
  prioritise: initialPrioritizationListProps | initialPrioritiseListProps;
  onUpdateSwitchData?: ({
    key,
    idx,
    value,
  }: {
    key: 'alreadyPlanned' | 'toDiscuss';
    idx: number;
    value: boolean;
  }) => void;
}) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['customerFactFind']);

  return (
    <Box flex={1}>
      <PrioritiseDraxView
        receptive={false}
        draggable={!dragCompleted}
        longPressDelay={100}
        payload={{
          prioritiseKey: prioritise.key,
          priorityIdx,
          icon: prioritise.icon,
          alreadyPlanned: (prioritise as initialPrioritizationListProps)
            .alreadyPlanned,
          toDiscuss: (prioritise as initialPrioritizationListProps).toDiscuss,
        }}>
        <Box
          borderRadius={space[2]}
          borderWidth={1}
          height={space[14]}
          justifyContent="center"
          backgroundColor={colors.palette.white}
          flex={1}
          borderColor={colors.palette.fwdGrey['100']}>
          <Row px={space[3]} alignItems="center" justifyContent="space-between">
            <Row alignItems="center">
              <IconReorder />
              <Box w={space[3]} />
              {prioritise.icon &&
                createElement(prioritise.icon, { size: sizes[10] })}
              <Box w={space[1]} />
              <Row flex={typeof priorityIdx === 'undefined' ? 1 : 0}>
                <LargeBody fontWeight="bold">
                  {t(
                    `customerFactFind:customerPreference.${prioritise.key}` as TFuncKey<
                      ['customerFactFind']
                    >,
                  )}
                </LargeBody>
                <Box w={space[1]} />
                <Tooltip
                  content={t(
                    `customerFactFind:customerPreference.${prioritise.key}.tooltip` as TFuncKey<
                      ['customerFactFind']
                    >,
                  )}
                />
              </Row>
              <Box w={space[2]} />
            </Row>
            {typeof priorityIdx !== 'undefined' ? (
              <Row alignItems="center">
                <PrioritiseSwitch
                  label={
                    (prioritise as initialPrioritizationListProps)
                      .alreadyPlanned
                      ? t('customerFactFind:yes')
                      : t('customerFactFind:no')
                  }
                  checked={
                    (prioritise as initialPrioritizationListProps)
                      .alreadyPlanned
                  }
                  onChange={value => {
                    onUpdateSwitchData &&
                      onUpdateSwitchData({
                        key: 'alreadyPlanned',
                        idx: priorityIdx,
                        value,
                      });
                  }}
                />

                <PrioritiseSwitch
                  label={
                    (prioritise as initialPrioritizationListProps).toDiscuss
                      ? t('customerFactFind:yes')
                      : t('customerFactFind:no')
                  }
                  checked={
                    (prioritise as initialPrioritizationListProps).toDiscuss
                  }
                  onChange={value => {
                    onUpdateSwitchData &&
                      onUpdateSwitchData({
                        key: 'toDiscuss',
                        idx: priorityIdx,
                        value,
                      });
                  }}
                />
              </Row>
            ) : (
              <></>
            )}
          </Row>
        </Box>
      </PrioritiseDraxView>
    </Box>
  );
};

export default PrioritiseItem;

const PrioritiseSwitch = styled(Switch)(({ theme: { space } }) => {
  return {
    width: space[40],
  };
});

const PrioritiseDraxView = styled(DraxView)(({ theme: { space } }) => {
  return {
    height: space[14],
  };
});

const IconReorder = ({ fill = '#B3B6B8' }: { fill?: string }): JSX.Element => {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8 6C6.8955 6 6 5.1045 6 4C6 2.8955 6.8955 2 8 2C9.1045 2 10 2.8955 10 4C10 5.1045 9.1045 6 8 6ZM8 14C6.8955 14 6 13.1045 6 12C6 10.8955 6.8955 10 8 10C9.1045 10 10 10.8955 10 12C10 13.1045 9.1045 14 8 14ZM8 22C6.8955 22 6 21.1045 6 20C6 18.8955 6.8955 18 8 18C9.1045 18 10 18.8955 10 20C10 21.1045 9.1045 22 8 22Z"
        fill={fill}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 6C14.8955 6 14 5.1045 14 4C14 2.8955 14.8955 2 16 2C17.1045 2 18 2.8955 18 4C18 5.1045 17.1045 6 16 6ZM16 14C14.8955 14 14 13.1045 14 12C14 10.8955 14.8955 10 16 10C17.1045 10 18 10.8955 18 12C18 13.1045 17.1045 14 16 14ZM16 22C14.8955 22 14 21.1045 14 20C14 18.8955 14.8955 18 16 18C17.1045 18 18 18.8955 18 20C18 21.1045 17.1045 22 16 22Z"
        fill={fill}
      />
    </Svg>
  );
};
