import React, { Fragment, useCallback, useMemo } from 'react';
import { Box, LargeBody, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import PrioritiseItem from './PrioritiseItem';
import PriorityEmpty from './PriorityEmpty';
import {
  initialPrioritizationListProps,
  initialPrioritiseListProps,
  slotPrioritiseRow,
  PrioritizationKey,
} from 'features/customerFactFind/constants/prioritizations';
import styled from '@emotion/native';
import { DraxList } from 'react-native-drax';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';

interface PotentialAreasProps {
  dragCompleted: boolean;
  prioritiseList: initialPrioritiseListProps[];
  setPrioritiseList: (list: initialPrioritiseListProps[]) => void;
  prioritizationList: initialPrioritizationListProps[];
  setPrioritizationList: (list: initialPrioritizationListProps[]) => void;
}

const PotentialAreas = ({
  dragCompleted,
  prioritiseList,
  setPrioritiseList,
  prioritizationList,
  setPrioritizationList,
}: PotentialAreasProps) => {
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();

  const onReceiveFromPotentialAreas = ({
    idx,
    key,
    icon,
  }: {
    idx: number;
    key: PrioritizationKey;
    icon: React.ComponentType<SvgPictogramProps>;
  }) => {
    const newPrioritiseList = prioritiseList.filter(e => e.key !== key);
    setPrioritiseList(newPrioritiseList);

    const newPrioritizationList: initialPrioritizationListProps[] =
      prioritizationList.reduce(
        (newArray: initialPrioritizationListProps[], item, index) => {
          const prioritization: initialPrioritizationListProps = { ...item };
          if (index === idx) {
            prioritization.key = key;
            prioritization.icon = icon;
          }
          newArray.push(prioritization);
          return newArray;
        },
        [],
      );

    setPrioritizationList(newPrioritizationList);
  };

  const onReceiveFromPriorityList = ({
    newIdx,
    oldIdx,
    key,
    icon,
    alreadyPlanned,
    toDiscuss,
  }: {
    newIdx: number;
    oldIdx: number;
    key: PrioritizationKey;
    icon: React.ComponentType<SvgPictogramProps>;
    alreadyPlanned: boolean;
    toDiscuss: boolean;
  }) => {
    const newPrioritizationList: initialPrioritizationListProps[] =
      prioritizationList.reduce(
        (newArray: initialPrioritizationListProps[], item, index) => {
          const prioritization: initialPrioritizationListProps = { ...item };
          if (index === newIdx) {
            prioritization.key = key;
            prioritization.icon = icon;
            prioritization.alreadyPlanned = alreadyPlanned;
            prioritization.toDiscuss = toDiscuss;
          }
          if (index === oldIdx) {
            prioritization.key = undefined;
            prioritization.icon = undefined;
            prioritization.alreadyPlanned = false;
            prioritization.toDiscuss = false;
          }
          newArray.push(prioritization);
          return newArray;
        },
        [],
      );

    setPrioritizationList(newPrioritizationList);
  };

  const onUpdateSwitchData = ({
    key,
    idx,
    value,
  }: {
    key: 'alreadyPlanned' | 'toDiscuss';
    idx: number | undefined;
    value: boolean;
  }) => {
    const newPrioritizationList: initialPrioritizationListProps[] =
      prioritizationList.reduce(
        (newArray: initialPrioritizationListProps[], item, index) => {
          const prioritization: initialPrioritizationListProps = { ...item };
          if (index === idx) {
            prioritization[key] = value;
          }
          newArray.push(prioritization);
          return newArray;
        },
        [],
      );

    setPrioritizationList(newPrioritizationList);
  };

  const renderDraxListContent = useCallback(
    ({
      item,
      index,
      isHover = false,
    }: {
      item: initialPrioritizationListProps;
      index: number;
      isHover?: boolean;
    }) => {
      return (
        <Box
          key={item.key}
          flexDirection="row"
          mt={space[2]}
          alignItems="center">
          <Box ml={space[6]} w={space[12]}>
            <LargeBody fontWeight="bold">{isHover ? '' : index + 1}</LargeBody>
          </Box>
          <PrioritiseItem
            priorityIdx={index}
            prioritise={item}
            dragCompleted={dragCompleted}
            onUpdateSwitchData={onUpdateSwitchData}
          />
        </Box>
      );
    },
    [prioritizationList, dragCompleted],
  );

  const isDragAndDrop = useMemo(() => {
    return prioritiseList.length !== 6;
  }, [prioritiseList]);

  const prioritiseListDisplay1 = useMemo(() => {
    return [...prioritiseList].slice(0, 3);
  }, [prioritiseList]);

  const prioritiseListDisplay2 = useMemo(() => {
    return [...prioritiseList].slice(3);
  }, [prioritiseList]);

  return (
    <Box backgroundColor={colors.palette.white} borderRadius={space[4]}>
      <Box
        bgColor={colors.secondary}
        px={space[6]}
        py={space[3]}
        borderTopLeftRadius={space[4]}
        borderTopRightRadius={space[4]}>
        <LargeBody fontWeight="bold" color={colors.palette.white}>
          {t('customerFactFind:potentialAreas.title')}
        </LargeBody>
      </Box>
      <Box m={space[6]}>
        <LargeBody>{t('customerFactFind:potentialAreas.dragDrop')}</LargeBody>
        {!dragCompleted ? (
          <Box mt={space[3]}>
            <Box flexDirection="row">
              {slotPrioritiseRow.map((slot, index) => {
                const prioritise = prioritiseListDisplay1[slot];
                return (
                  <Fragment key={index}>
                    {prioritise?.key ? (
                      <PrioritiseItem
                        prioritise={prioritise}
                        dragCompleted={dragCompleted}
                      />
                    ) : (
                      <Box flex={1} />
                    )}
                    {index !== 2 && <Box w={space[2]} />}
                  </Fragment>
                );
              })}
            </Box>
            {prioritiseListDisplay2 && prioritiseListDisplay2.length ? (
              <Box flexDirection="row" mt={space[2]}>
                {slotPrioritiseRow.map((slot, index) => {
                  const prioritise = prioritiseListDisplay2[slot];
                  return (
                    <Fragment key={index}>
                      {prioritise?.key ? (
                        <PrioritiseItem
                          prioritise={prioritise}
                          dragCompleted={dragCompleted}
                        />
                      ) : (
                        <Box flex={1} />
                      )}
                      {index !== 2 && <Box w={space[2]} />}
                    </Fragment>
                  );
                })}
              </Box>
            ) : (
              <></>
            )}
          </Box>
        ) : (
          <></>
        )}
        <Box mt={space[6]}>
          <Row justifyContent="space-between">
            <LargeBody fontWeight="bold">
              {t('customerFactFind:potentialAreas.priority')}
            </LargeBody>
            {isDragAndDrop ? (
              <Row>
                <TitleTable fontWeight="bold">
                  {t('customerFactFind:potentialAreas.alreadyPlanned')}
                </TitleTable>
                <TitleTable fontWeight="bold">
                  {t('customerFactFind:potentialAreas.toDiscuss')}
                </TitleTable>
              </Row>
            ) : (
              <></>
            )}
          </Row>
          {!dragCompleted ? (
            <>
              {prioritizationList.map((prioritise, index) => (
                <Box
                  key={index}
                  flexDirection="row"
                  mt={space[2]}
                  alignItems="center">
                  <Box ml={space[6]} w={space[12]}>
                    <LargeBody fontWeight="bold">
                      {prioritise.idx + 1}
                    </LargeBody>
                  </Box>
                  {prioritise.key ? (
                    <PrioritiseItem
                      priorityIdx={index}
                      prioritise={prioritise}
                      dragCompleted={dragCompleted}
                      onUpdateSwitchData={onUpdateSwitchData}
                    />
                  ) : (
                    <PriorityEmpty
                      priorityIdx={prioritise.idx}
                      onReceiveFromPotentialAreas={onReceiveFromPotentialAreas}
                      onReceiveFromPriorityList={onReceiveFromPriorityList}
                    />
                  )}
                </Box>
              ))}
            </>
          ) : (
            <>
              <Box h={space[2]} />
              <DraxList
                key={Math.random()} // TODO: not sure why???
                data={prioritizationList}
                renderItemContent={renderDraxListContent}
                renderItemHoverContent={({ item, index }) =>
                  renderDraxListContent({ item, index, isHover: true })
                }
                onItemReorder={({ fromIndex, toIndex }) => {
                  const newData = prioritizationList.slice();
                  newData.splice(toIndex, 0, newData.splice(fromIndex, 1)[0]);
                  setPrioritizationList(newData);
                }}
                keyExtractor={(item, index) => index.toString()}
                scrollEnabled={false}
                longPressDelay={200}
              />
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default PotentialAreas;

const TitleTable = styled(LargeBody)(({ theme: { space } }) => {
  return {
    marginRight: space[10],
  };
});
