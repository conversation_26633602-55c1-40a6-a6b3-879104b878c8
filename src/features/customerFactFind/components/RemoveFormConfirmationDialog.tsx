import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { Box, Button, H6, LargeBody, Row } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';

interface RemoveFormConfirmationProps {
  visible?: boolean;
  onDismiss?: () => void;
  onConfirm?: () => void;
  isLoading?: boolean;
}

export default function RemoveFormConfirmationDialog({
  visible,
  onDismiss,
  onConfirm,
  isLoading,
}: RemoveFormConfirmationProps) {
  const { colors } = useTheme();
  const { sizes } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <DialogPhone visible={visible} style={isTabletMode && { minWidth: sizes[95] }}>
      <Wrapper>
        <H6 fontWeight="bold">
          {t('eApp:other.nominationDetails.remove.title')}
        </H6>
        <Box h={sizes[4]} />
        <LargeBody color={colors.secondary}>
          {t('eApp:other.nominationDetails.remove.description')}
        </LargeBody>
        <Box h={sizes[6]} />
        <Row justifyContent="center">
          <Button
            variant="secondary"
            text={t('eApp:cancel')}
            onPress={onDismiss}
            size="medium"
            style={{ flex: 1 }}
            disabled={isLoading}
          />
          <Box w={sizes[4]} />
          <Button
            style={{ flex: 1 }}
            text={t('eApp:remove')}
            onPress={onConfirm}
            size="medium"
            loading={isLoading}
          />
        </Row>
      </Wrapper>
    </DialogPhone>
  );
}

const Wrapper = styled(Box)(({ theme: { space } }) => ({
  padding: space[6],
}));
