import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Tooltip from 'components/Tooltip';
import { H6, Row, SmallLabel } from 'cube-ui-components';
import React from 'react';

interface ItemListingHeaderProps {
  title: string;
  label: string;
  icon: React.ComponentType<{ size?: number }>;
  tooltip: string;
}

const ItemListingHeader = (props: ItemListingHeaderProps) => {
  const { title, label, icon, tooltip } = props;
  const { sizes } = useTheme();
  return (
    <Wrapper>
      {React.createElement(icon, { size: sizes[10] })}
      <H6 fontWeight="bold">{title}</H6>
      <Tooltip content={tooltip} />
      <SubLabel>{label}</SubLabel>
    </Wrapper>
  );
};

export default ItemListingHeader;

const Wrapper = styled(Row)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  gap: space[2],
  alignItems: 'center',
}));

const SubLabel = styled(SmallLabel)(
  ({ theme: { space, colors, borderRadius } }) => ({
    borderColor: colors.palette.fwdYellow[100],
    borderRadius: borderRadius['x-small'],
    borderWidth: 1,
    backgroundColor: colors.palette.fwdYellow[20],
    padding: space[1],
    fontWeight: 'bold',
    includeFontPadding: false,
    marginLeft: space[2],
  }),
);
