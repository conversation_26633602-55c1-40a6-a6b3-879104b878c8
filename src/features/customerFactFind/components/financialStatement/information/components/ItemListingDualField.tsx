import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  CurrencyTextField,
  LargeBody,
  Row,
  TextField,
} from 'cube-ui-components';
import React from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';

interface ItemListingDualFieldProps<T extends FieldValues> {
  title: string;
  firstLabel: string;
  secondLabel: string;
  firstName: Path<T>;
  secondName: Path<T>;
  control: Control<T>;
  isAmount?: boolean;
  isYear?: boolean;
}

const ItemListingDualField = <T extends FieldValues>(
  props: ItemListingDualFieldProps<T>,
) => {
  const {
    title,
    firstLabel,
    secondLabel,
    firstName,
    secondName,
    isAmount,
    isYear,
    control,
  } = props;
  const { colors } = useTheme();
  return (
    <Wrapper backgroundColor={colors.background} alignItems="center">
      <Box flex={1}>
        <LargeBody>{title}</LargeBody>
      </Box>

      <Box flex={1}>
        {isAmount ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={firstName}
            label={firstLabel}
          />
        ) : isYear ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={firstName}
            label={firstLabel}
            maxLength={2}
          />
        ) : (
          <Input
            control={control}
            as={TextField}
            name={firstName}
            label={firstLabel}
          />
        )}
      </Box>

      <Box flex={1}>
        {isAmount ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={secondName}
            label={secondLabel}
          />
        ) : isYear ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={secondName}
            label={secondLabel}
            maxLength={2}
          />
        ) : (
          <Input
            control={control}
            as={TextField}
            name={secondName}
            label={secondLabel}
          />
        )}
      </Box>
    </Wrapper>
  );
};

export default ItemListingDualField;

const Wrapper = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: 22,
  backgroundColor: colors.background,
  gap: space[3],
  alignItems: 'center',
}));
