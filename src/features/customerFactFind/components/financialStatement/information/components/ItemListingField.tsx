import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  CurrencyTextField,
  LargeBody,
  Row,
  Switch,
  TextField,
} from 'cube-ui-components';
import Autocomplete from 'components/Autocomplete';
import React from 'react';
import { Control, FieldValues, Path, useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

interface ItemListingFieldProps<T extends FieldValues> {
  title: string;
  label: string;
  name: Path<T>;
  isYesNo?: boolean;
  isAmountInput?: boolean;
  isYearInput?: boolean;
  isDropdown?: boolean;
  data?: { label: string; value: string }[];
  control: Control<T>;
  isDisabled?: boolean;
}

const ItemListingField = <T extends FieldValues>(
  props: ItemListingFieldProps<T>,
) => {
  const {
    title,
    label,
    control,
    name,
    isYesNo = false,
    isAmountInput = false,
    isYearInput = false,
    isDropdown = false,
    data,
    isDisabled = false,
  } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors } = useTheme();
  const {
    field: { value: checked },
  } = useController({
    control,
    name,
  });

  return (
    <Wrapper backgroundColor={colors.background} alignItems="center">
      <Box flex={2}>
        <LargeBody>{title}</LargeBody>
      </Box>

      <Box flex={1.5}>
        {isYesNo ? (
          <Input
            as={SwitchContainer}
            control={control}
            name={name}
            label={
              checked
                ? t('customerFactFind:financial.yes')
                : t('customerFactFind:financial.no')
            }
          />
        ) : isAmountInput ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={name}
            label={label}
            keyboardType="number-pad"
          />
        ) : isDropdown ? (
          <Input
            control={control}
            as={Autocomplete<{ label: string; value: string }, string>}
            name={name}
            label={label}
            data={data ?? []}
            getItemLabel={i => i.label}
            getItemValue={i => i.value}
          />
        ) : isYearInput ? (
          <Input
            control={control}
            as={CurrencyTextField}
            name={name}
            maxLength={2}
            label={label}
            keyboardType="number-pad"
          />
        ) : (
          <Input
            control={control}
            as={TextField}
            name={name}
            label={label}
            disabled={isDisabled}
          />
        )}
      </Box>
    </Wrapper>
  );
};

export default ItemListingField;

const Wrapper = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: 22,
  backgroundColor: colors.background,
  gap: space[6],
}));

const SwitchContainer = styled(Switch)(({ theme: { space } }) => ({
  marginLeft: space[3],
  padding: 0,
}));
