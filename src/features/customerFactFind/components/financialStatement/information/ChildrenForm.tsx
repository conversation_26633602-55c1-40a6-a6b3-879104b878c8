import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, H7, Icon, Row } from 'cube-ui-components';
import { Kid } from 'cube-ui-components/dist/cjs/icons';
import React from 'react';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import ItemListingField from './components/ItemListingField';
import { FinancialChildEducationFormSchemaType } from '../../../validations/financialStatementValidationSchema';
import { NumberSequence } from 'types';

interface ChildrenFormProps {
  index: number;
  control: Control<FinancialChildEducationFormSchemaType>;
  onDelete?: () => void;
}

const ChildrenForm = (props: ChildrenFormProps) => {
  const { control, index, onDelete } = props;
  const { t } = useTranslation(['customerFactFind', 'common']);
  const { colors, space, sizes } = useTheme();

  return (
    <Wrapper>
      <Row gap={space[2]}>
        <Kid size={sizes[6]} fill={colors.onSurface} />
        <H7 fontWeight="bold">
          {t('customerFactFind:nthChildInformation', {
            position: t(
              `common:position.${(index + 1) as NumberSequence<1, 5>}`,
            ),
          })}
        </H7>
        <Box flex={1} />

        {index > 0 && onDelete && (
          <TouchableOpacity onPress={onDelete}>
            <Icon.Delete fill={colors.palette.black} />
          </TouchableOpacity>
        )}
      </Row>

      <ItemListingField
        title={t('customerFactFind:financial.childEducation.1')}
        label={t('customerFactFind:financial.name')}
        name={`informations.${index}.name`}
        control={control}
      />
      <ItemListingField
        title={t('customerFactFind:financial.childEducation.2')}
        label={t('customerFactFind:financial.age')}
        name={`informations.${index}.age`}
        control={control}
        isYearInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.childEducation.3')}
        label={t('customerFactFind:financial.years')}
        name={`informations.${index}.yearsToTertiaryEducation`}
        control={control}
        isYearInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.childEducation.4')}
        label={t('customerFactFind:financial.amount')}
        name={`informations.${index}.existingFund`}
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.childEducation.5')}
        label={t('customerFactFind:financial.amount')}
        name={`informations.${index}.additionalAmount`}
        control={control}
        isAmountInput
      />
    </Wrapper>
  );
};

export default ChildrenForm;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  marginTop: space[6],
  backgroundColor: colors.background,
}));
