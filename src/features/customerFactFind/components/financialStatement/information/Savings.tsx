import styled from '@emotion/native';
import {
  Column,
  Row,
  SmallBody,
  SmallLabel,
  H6,
  Box,
} from 'cube-ui-components';
import IconSavings from 'cube-ui-components/dist/cjs/icons/pictograms/Savings';
import { FinancialSavingsFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemListingField from './components/ItemListingField';
import ItemListingHeader from './components/ItemListingHeader';
import {
  areNumbers,
  round,
} from 'features/customerFactFind/utils/numberUtil';
import { formatCurrency } from 'utils';

interface SavingsProps {
  control: Control<FinancialSavingsFormSchemaType>;
  priorityIndex: number;
}

const Savings = (props: SavingsProps) => {
  const { control, priorityIndex } = props;
  const { t } = useTranslation(['customerFactFind']);

  const { expected, meetYears } = useWatch({
    control,
  });

  const {
    field: { onChange: onChangeAdditionalAmount },
  } = useController({
    control,
    name: 'additionalAmount',
  });

  const amountToBeAllocated = Math.max(
    0,
    round(Number(expected) / Number(meetYears) / 12),
  );
  useEffect(() => {
    onChangeAdditionalAmount(amountToBeAllocated);
  }, [amountToBeAllocated, onChangeAdditionalAmount]);

  const isAmountToBeAllocatedValid =
    areNumbers(expected, meetYears) && isFinite(amountToBeAllocated);

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.savings')}
        tooltip={t('customerFactFind:customerPreference.Savings.tooltip')}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={IconSavings}
      />
      <ItemListingField
        title={t('customerFactFind:financial.savings.1')}
        label={t('customerFactFind:financial.amount')}
        name="expected"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.savings.2')}
        label={t('customerFactFind:financial.meetYears')}
        name="meetYears"
        control={control}
        isYearInput
      />
      <AdditionalContainer alignItems="center">
        <Box flex={1}>
          <SmallLabel fontWeight="medium">
            {t('customerFactFind:financial.savings.amountNeeded')}
          </SmallLabel>
        </Box>

        <CurrencyContainer>
          {`${t('customerFactFind:financial.rm')} `}
        </CurrencyContainer>
        <H6 fontWeight="bold">
          {isAmountToBeAllocatedValid
            ? formatCurrency(amountToBeAllocated)
            : t('customerFactFind:financial.amountPlaceHolder')}
        </H6>
      </AdditionalContainer>
    </Wrapper>
  );
};

export default Savings;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));

const AdditionalContainer = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[3],
  borderRadius: space[2],
  backgroundColor: colors.primaryVariant3,
  padding: space[3],
}));

const CurrencyContainer = styled(SmallBody)(({ theme: { space } }) => ({
  marginVertical: space[1],
  fontWeight: 'bold',
  alignSelf: 'flex-end',
  textAlignVertical: 'top',
}));
