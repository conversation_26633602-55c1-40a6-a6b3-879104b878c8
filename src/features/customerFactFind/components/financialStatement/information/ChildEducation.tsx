import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Button, Column, Icon } from 'cube-ui-components';
import { PeopleKnowledge } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { FinancialChildEducationFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useCallback, useRef, useState } from 'react';
import { Control, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import ChildrenForm from './ChildrenForm';
import ItemListingHeader from './components/ItemListingHeader';
import { DeleteModal } from '../../personalDetails/components/DeleteModal';
import { NumberSequence } from 'types';

interface ChildEducationProps {
  control: Control<FinancialChildEducationFormSchemaType>;
  priorityIndex: number;
}

const MAX_CHILDREN = 5;

const ChildEducation = (props: ChildEducationProps) => {
  const { control, priorityIndex } = props;
  const { t } = useTranslation(['customerFactFind', 'common']);
  const { space } = useTheme();

  const { fields, append, remove } = useFieldArray({
    name: 'informations',
    control,
  });

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const refPosition = useRef<number>(0);

  const onShowDeleteModal = (index: number) => {
    refPosition.current = index;
    setShowDeleteModal(true);
  };

  const onCancelDelModal = useCallback(() => {
    setShowDeleteModal(false);
  }, []);

  const onConfirmDelModal = () => {
    remove(refPosition.current);
    setShowDeleteModal(false);
  };

  const renderItems = () => {
    return fields.map((_, index) => (
      <View key={index}>
        <ChildrenForm
          control={control}
          index={index}
          onDelete={() => onShowDeleteModal(index)}
        />
        {index < fields.length - 1 && <Divider />}
      </View>
    ));
  };

  const addChildren = useCallback(() => {
    if (fields.length < MAX_CHILDREN) {
      append({
        name: '',
        age: '',
        yearsToTertiaryEducation: '',
        existingFund: '',
        additionalAmount: '',
      });
    }
  }, [append, fields]);

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.childEducation')}
        tooltip={t(
          'customerFactFind:customerPreference.ChildEducation.tooltip',
        )}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={PeopleKnowledge}
      />

      {renderItems()}

      {fields.length < 5 && (
        <Button
          text={t('customerFactFind:financial.addChildEducation')}
          icon={Icon.Plus}
          variant="secondary"
          compact={true}
          style={{ alignSelf: 'baseline', marginTop: space[6] }}
          onPress={addChildren}
          disabled={fields.length >= MAX_CHILDREN}
        />
      )}

      {fields.length > 1 && (
        <DeleteModal
          dialogVisible={showDeleteModal}
          onConfirm={onConfirmDelModal}
          onDeny={onCancelDelModal}
          title={t('customerFactFind:titleDeleteDialog')}
          denyLabel={t('customerFactFind:cancel')}
          removeLabel={t('customerFactFind:remove')}
          subTitle={t('customerFactFind:contentDeleteDialog', {
            position: t(
              `common:position.${
                (refPosition.current + 1) as NumberSequence<1, 5>
              }`,
            )?.toLocaleLowerCase(),
          })}
        />
      )}
    </Wrapper>
  );
};

export default ChildEducation;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
    marginTop: theme.space[6],
  };
});
