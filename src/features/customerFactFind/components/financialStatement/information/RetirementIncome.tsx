import styled from '@emotion/native';
import {
  Box,
  Column,
  H6,
  Row,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { Family2 } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { FinancialRetirementFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemListingField from './components/ItemListingField';
import ItemListingHeader from './components/ItemListingHeader';
import {
  areNumbers,
  round,
} from 'features/customerFactFind/utils/numberUtil';
import { formatCurrency } from 'utils';

interface RetirementIncomeProps {
  control: Control<FinancialRetirementFormSchemaType>;
  priorityIndex: number;
}

const RetirementIncome = (props: RetirementIncomeProps) => {
  const { control, priorityIndex } = props;
  const { t } = useTranslation(['customerFactFind']);

  const [
    annualIncome,
    existingRetirementIncome,
    hasOtherIncome,
    replacedYears,
  ] = useWatch({
    control,
    name: [
      'annualIncome',
      'existingRetirementIncome',
      'hasOtherIncome',
      'replacedYears',
    ],
  });

  const {
    field: { onChange: onChangeAdditionalAmount },
  } = useController({
    control,
    name: 'additionalAmount',
  });

  const additionalAmount = Math.max(
    0,
    round(
      Number(replacedYears) * Number(annualIncome) -
        Number(existingRetirementIncome),
    ),
  );
  useEffect(() => {
    onChangeAdditionalAmount(additionalAmount);
  }, [additionalAmount, onChangeAdditionalAmount]);

  const isAdditionalAmountValid = areNumbers(
    replacedYears,
    annualIncome,
    existingRetirementIncome,
  );

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.retirementIncome')}
        tooltip={t(
          'customerFactFind:customerPreference.RetirementIncome.tooltip',
        )}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={Family2}
      />
      <ItemListingField
        title={t('customerFactFind:financial.retirementIncome.1')}
        label={t('customerFactFind:financial.retirementYears')}
        name="retirementYears"
        control={control}
        isYearInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.retirementIncome.2')}
        label={t('customerFactFind:financial.amount')}
        name="annualIncome"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.retirementIncome.3')}
        label={t('customerFactFind:financial.replacedYears')}
        name="replacedYears"
        control={control}
        isYearInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.retirementIncome.4')}
        label={t('customerFactFind:financial.amount')}
        name="existingRetirementIncome"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.retirementIncome.5')}
        label={t('customerFactFind:financial.pleaseSpecify')}
        name="hasOtherIncome"
        control={control}
        isYesNo
      />
      {hasOtherIncome && (
        <ItemListingField
          title={t('customerFactFind:financial.retirementIncome.5.1')}
          label={t('customerFactFind:financial.amount')}
          name="otherIncome"
          control={control}
          isAmountInput
        />
      )}
      <AdditionalContainer alignItems="center">
        <Box flex={1}>
          <SmallLabel fontWeight="medium">
            {t(
              'customerFactFind:financial.retirementIncome.additionalAmountRequired',
            )}
          </SmallLabel>
        </Box>

        <CurrencyContainer>
          {`${t('customerFactFind:financial.rm')} `}
        </CurrencyContainer>
        <H6 fontWeight="bold">
          {isAdditionalAmountValid
            ? formatCurrency(additionalAmount)
            : t('customerFactFind:financial.amountPlaceHolder')}
        </H6>
      </AdditionalContainer>
    </Wrapper>
  );
};

export default RetirementIncome;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));

const AdditionalContainer = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[3],
  borderRadius: space[2],
  backgroundColor: colors.primaryVariant3,
  padding: space[3],
}));

const CurrencyContainer = styled(SmallBody)(({ theme: { space } }) => ({
  marginVertical: space[1],
  fontWeight: 'bold',
  alignSelf: 'flex-end',
  textAlignVertical: 'top',
}));
