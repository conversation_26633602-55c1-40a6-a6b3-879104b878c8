import styled from '@emotion/native';
import {
  Body,
  Box,
  Column,
  H6,
  LargeLabel,
  Row,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { Medicine } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { FinancialMedicalPlanningFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, useController, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemListingDualField from './components/ItemListingDualField';
import ItemListingHeader from './components/ItemListingHeader';
import { useTheme } from '@emotion/react';
import { areNumbers, round } from 'features/customerFactFind/utils/numberUtil';
import { formatCurrency } from 'utils';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';

interface MedicalPlanningProps {
  control: Control<FinancialMedicalPlanningFormSchemaType>;
  priorityIndex: number;
}

const MedicalPlanning = (props: MedicalPlanningProps) => {
  const { control, priorityIndex } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();
  const quotation = useSelectedQuotation();

  const {
    neededAmountForMedicalPlanning,
    neededAmountForCriticalIllness,
    existingProtectionForMedicalPlanning,
    existingProtectionForCriticalIllness,
    existingProtectionForCurrentEmployerForMedicalPlanning,
    existingProtectionForCurrentEmployerForCriticalIllness,
  } = useWatch({
    control,
  });

  const {
    field: { onChange: onChangeAdditionalAmountForMedicalPlanning },
  } = useController({
    control,
    name: 'additionalAmountForMedicalPlanning',
  });

  const additionalAmountMedicalPlanning = Math.max(
    0,
    round(
      Number(neededAmountForMedicalPlanning) -
        Number(existingProtectionForMedicalPlanning) -
        Number(existingProtectionForCurrentEmployerForMedicalPlanning),
    ),
  );
  useEffect(() => {
    onChangeAdditionalAmountForMedicalPlanning(additionalAmountMedicalPlanning);
  }, [
    additionalAmountMedicalPlanning,
    onChangeAdditionalAmountForMedicalPlanning,
  ]);

  const {
    field: { onChange: onChangeAdditionalAmountForCriticalIllness },
  } = useController({
    control,
    name: 'additionalAmountForCriticalIllness',
  });
  const additionalAmountCriticalIllness = Math.max(
    0,
    round(
      Number(neededAmountForCriticalIllness) -
        Number(existingProtectionForCriticalIllness) -
        Number(existingProtectionForCurrentEmployerForCriticalIllness),
    ),
  );
  useEffect(() => {
    onChangeAdditionalAmountForCriticalIllness(additionalAmountCriticalIllness);
  }, [
    additionalAmountCriticalIllness,
    onChangeAdditionalAmountForCriticalIllness,
  ]);

  const isAdditionalAmountMedicalPlanningValid = areNumbers(
    neededAmountForMedicalPlanning,
    existingProtectionForMedicalPlanning,
  );

  const isAdditionalAmountCriticalIllnessValid = areNumbers(
    neededAmountForCriticalIllness,
    existingProtectionForCriticalIllness,
  );

  const {
    field: { onChange: onChangeHasMHIT },
  } = useController({ name: 'hasMHIT', control });

  useEffect(() => {
    onChangeHasMHIT(Boolean(quotation?.containMHITProduct));
  }, [onChangeHasMHIT, quotation?.containMHITProduct]);

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.medicalPlanning')}
        tooltip={t(
          'customerFactFind:customerPreference.MedicalPlanning.tooltip',
        )}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={Medicine}
      />
      {quotation?.containMHITProduct && (
        <MHITHint color={colors.secondaryVariant}>
          {t('customerFactFind:financial.medicalPlanning.MHITHint')}
        </MHITHint>
      )}
      <TableHeader>
        <Box flex={1} />
        <Box flex={1}>
          <LargeLabel style={{ marginLeft: space[4] }} fontWeight="medium">
            {t('customerFactFind:financial.medicalPlanning.medicalPlanning')}
          </LargeLabel>
        </Box>
        <Box flex={1}>
          <LargeLabel style={{ marginLeft: space[4] }} fontWeight="medium">
            {t('customerFactFind:financial.medicalPlanning.criticalIllness')}
          </LargeLabel>
        </Box>
      </TableHeader>
      <ItemListingDualField
        title={t('customerFactFind:financial.medicalPlanning.1')}
        firstLabel={t('customerFactFind:financial.amount')}
        secondLabel={t('customerFactFind:financial.amount')}
        firstName="neededAmountForMedicalPlanning"
        secondName="neededAmountForCriticalIllness"
        control={control}
        isAmount={true}
      />
      <ItemListingDualField
        title={t('customerFactFind:financial.medicalPlanning.2')}
        firstLabel={t('customerFactFind:financial.amount')}
        secondLabel={t('customerFactFind:financial.amount')}
        firstName="existingProtectionForMedicalPlanning"
        secondName="existingProtectionForCriticalIllness"
        control={control}
        isAmount={true}
      />
      <ItemListingDualField
        title={t('customerFactFind:financial.medicalPlanning.3')}
        firstLabel={t('customerFactFind:financial.yearsOld')}
        secondLabel={t('customerFactFind:financial.yearsOld')}
        firstName="expiryAgeForMedicalPlanning"
        secondName="expiryAgeForCriticalIllness"
        control={control}
        isYear
      />
      {quotation?.containMHITProduct && (
        <ItemListingDualField
          title={t('customerFactFind:financial.medicalPlanning.4')}
          firstLabel={t('customerFactFind:financial.amount')}
          secondLabel={t('customerFactFind:financial.amount')}
          firstName="existingProtectionForCurrentEmployerForMedicalPlanning"
          secondName="existingProtectionForCurrentEmployerForCriticalIllness"
          control={control}
          isAmount={true}
        />
      )}
      <AdditionalContainer alignItems="center">
        <Box flex={1}>
          <SmallLabel fontWeight="medium">
            {t('customerFactFind:financial.medicalPlanning.amountNeeded')}
          </SmallLabel>
        </Box>

        <Row flex={1}>
          <CurrencyContainer>
            {`${t('customerFactFind:financial.rm')} `}
          </CurrencyContainer>
          <H6 fontWeight="bold">
            {isAdditionalAmountMedicalPlanningValid
              ? formatCurrency(additionalAmountMedicalPlanning)
              : t('customerFactFind:financial.amountPlaceHolder')}
          </H6>
        </Row>
        <Row flex={1} ml={space[3]}>
          <CurrencyContainer>
            {`${t('customerFactFind:financial.rm')} `}
          </CurrencyContainer>
          <H6 fontWeight="bold">
            {isAdditionalAmountCriticalIllnessValid
              ? formatCurrency(additionalAmountCriticalIllness)
              : t('customerFactFind:financial.amountPlaceHolder')}
          </H6>
        </Row>
      </AdditionalContainer>
    </Wrapper>
  );
};

export default MedicalPlanning;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));

const TableHeader = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[3],
  backgroundColor: colors.background,
  gap: space[3],
}));

const AdditionalContainer = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[3],
  borderRadius: space[2],
  backgroundColor: colors.primaryVariant3,
  padding: space[3],
  gap: space[3],
}));

const CurrencyContainer = styled(SmallBody)(({ theme: { space } }) => ({
  marginVertical: space[1],
  fontWeight: 'bold',
  alignSelf: 'flex-end',
  textAlignVertical: 'top',
}));

const MHITHint = styled(Body)(({ theme: { space } }) => {
  return {
    marginTop: space[3],
  };
});
