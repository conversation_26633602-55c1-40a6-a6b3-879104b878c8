import styled from '@emotion/native';
import { Column } from 'cube-ui-components';
import { MoneyInvestment } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { FinancialInvestmentsFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useMemo } from 'react';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemListingField from './components/ItemListingField';
import ItemListingHeader from './components/ItemListingHeader';

interface InvestmentsProps {
  control: Control<FinancialInvestmentsFormSchemaType>;
  priorityIndex: number;
}

const Investments = (props: InvestmentsProps) => {
  const { control, priorityIndex } = props;
  const { t } = useTranslation(['customerFactFind']);

  const investmentDuration = useMemo(
    () => [
      {
        label: t('customerFactFind:financial.investmentDuration.less5'),
        value: 'less5',
      },
      {
        label: t('customerFactFind:financial.investmentDuration.more5'),
        value: 'more5',
      },
    ],
    [t],
  );

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.investments')}
        tooltip={t('customerFactFind:customerPreference.Investments.tooltip')}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={MoneyInvestment}
      />
      <ItemListingField
        title={t('customerFactFind:financial.investments.1')}
        label={t('customerFactFind:financial.amount')}
        name="initialAmount"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.investments.2')}
        label={t('customerFactFind:financial.amount')}
        name="regularAmount"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.investments.3')}
        label={t('customerFactFind:financial.investmentDuration')}
        name="duration"
        control={control}
        isDropdown
        data={investmentDuration}
      />
      <ItemListingField
        title={t('customerFactFind:financial.investments.4')}
        label={t('customerFactFind:financial.amount')}
        name="expectedMonthly"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.investments.5')}
        label={t('customerFactFind:financial.payoutPeriod')}
        name="payoutPeriod"
        control={control}
        isYearInput
      />
    </Wrapper>
  );
};

export default Investments;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));
