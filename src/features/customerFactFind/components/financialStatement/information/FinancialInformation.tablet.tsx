import styled from '@emotion/native';
import { Box, Column, Row } from 'cube-ui-components';
import React, {
  ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import StateSectionHeader from '../../StateSectionHeader';
import ChildEducation from './ChildEducation';
import IncomeProtection from './IncomeProtection';
import Investments from './Investments';
import MedicalPlanning from './MedicalPlanning';
import RetirementIncome from './RetirementIncome';
import Savings from './Savings';
import { Control } from 'react-hook-form';
import {
  FinancialIncomeProtectionFormSchemaType,
  FinancialRetirementFormSchemaType,
  FinancialChildEducationFormSchemaType,
  FinancialSavingsFormSchemaType,
  FinancialInvestmentsFormSchemaType,
  FinancialMedicalPlanningFormSchemaType,
} from 'features/customerFactFind/validations/financialStatementValidationSchema';
import { useTheme } from '@emotion/react';
import {
  PrioritizationItem,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import { PrioritizationKey } from 'features/customerFactFind/constants/prioritizations';
import { TFuncKey } from 'i18next';
import SectionContainer from '../../SectionContainer';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';

interface FinancialInformationProps {
  financialIncomeProtectionFormControl: Control<FinancialIncomeProtectionFormSchemaType>;
  financialRetirementFormControl: Control<FinancialRetirementFormSchemaType>;
  financialChildEducationFormControl: Control<FinancialChildEducationFormSchemaType>;
  financialSavingsFormControl: Control<FinancialSavingsFormSchemaType>;
  financialInvestmentsFormControl: Control<FinancialInvestmentsFormSchemaType>;
  financialMedicalPlanningFormControl: Control<FinancialMedicalPlanningFormSchemaType>;
}

export enum InformationStep {
  INCOME_PROTECTION = 0,
  RETIREMENT_INCOME = 1,
  CHILD_EDUCATION = 2,
  SAVINGS = 3,
  INVESTMENTS = 4,
  MEDICAL_PLANNING = 5,
}

const defaultPrioritization: Array<PrioritizationItem> = [
  {
    key: 'IncomeProtection',
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    key: 'RetirementIncome',
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    key: 'ChildEducation',
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    key: 'Savings',
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    key: 'Investments',
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    key: 'MedicalPlanning',
    alreadyPlanned: false,
    toDiscuss: false,
  },
];

export interface FinancialInformationRef {
  setType: (type: PrioritizationKey) => void;
}

const FinancialInformation = forwardRef(
  (
    props: FinancialInformationProps,
    ref: ForwardedRef<FinancialInformationRef>,
  ) => {
    const {
      financialIncomeProtectionFormControl,
      financialRetirementFormControl,
      financialChildEducationFormControl,
      financialSavingsFormControl,
      financialInvestmentsFormControl,
      financialMedicalPlanningFormControl,
    } = props;
    const { t } = useTranslation(['customerFactFind']);
    const { space } = useTheme();
    const quotation = useSelectedQuotation();

    const storePrioritization = useCustomerFactFindStore(
      state => state.customerPreference.prioritization,
    );

    const prioritization = useMemo(() => {
      if (!storePrioritization || storePrioritization.length === 0) {
        return defaultPrioritization;
      }
      return storePrioritization;
    }, [storePrioritization]);

    const prioritizationRef = useRef<PrioritizationItem[]>(prioritization);

    const [type, setType] = useState<PrioritizationKey>(
      prioritization[0]?.key ?? undefined,
    );

    useImperativeHandle(ref, () => ({
      setType,
    }));

    //Update first PrioritizationKey from customer preference instead of using mock item PrioritizationKey
    useEffect(() => {
      if (prioritization && prioritization[0]?.key) {
        if (
          prioritization !== defaultPrioritization &&
          prioritizationRef.current === defaultPrioritization
        ) {
          setType(prioritization[0].key);
        }
        prioritizationRef.current = prioritization;
      }
    }, [prioritization]);

    const isIncomeProtectionValid =
      financialIncomeProtectionFormControl._formState.isValid;
    const isRetirementIncomeValid =
      financialRetirementFormControl._formState.isValid;
    const isChildEducationValid =
      financialChildEducationFormControl._formState.isValid;
    const isSavingsValid = financialSavingsFormControl._formState.isValid;
    const isInvestmentsValid =
      financialInvestmentsFormControl._formState.isValid;
    const isMedicalPlanningValid =
      financialMedicalPlanningFormControl._formState.isValid;

    const renderForm = () => {
      const priorityItem =
        prioritization.find(item => item.key === type) || prioritization[0];
      const priorityIndex = prioritization.indexOf(priorityItem);

      switch (type) {
        case 'IncomeProtection':
          return (
            <IncomeProtection
              control={financialIncomeProtectionFormControl}
              priorityIndex={priorityIndex}
            />
          );
        case 'RetirementIncome':
          return (
            <RetirementIncome
              control={financialRetirementFormControl}
              priorityIndex={priorityIndex}
            />
          );
        case 'ChildEducation':
          return (
            <ChildEducation
              control={financialChildEducationFormControl}
              priorityIndex={priorityIndex}
            />
          );
        case 'Savings':
          return (
            <Savings
              control={financialSavingsFormControl}
              priorityIndex={priorityIndex}
            />
          );
        case 'Investments':
          return (
            <Investments
              control={financialInvestmentsFormControl}
              priorityIndex={priorityIndex}
            />
          );
        case 'MedicalPlanning':
          return (
            <MedicalPlanning
              control={financialMedicalPlanningFormControl}
              priorityIndex={priorityIndex}
            />
          );
        default:
          return null;
      }
    };

    const checkValid = (type: PrioritizationKey) => {
      switch (type) {
        case 'IncomeProtection':
          return isIncomeProtectionValid;
        case 'RetirementIncome':
          return isRetirementIncomeValid;
        case 'ChildEducation':
          return isChildEducationValid;
        case 'Savings':
          return isSavingsValid;
        case 'Investments':
          return isInvestmentsValid;
        case 'MedicalPlanning':
          return isMedicalPlanningValid;
        default:
          return false;
      }
    };

    return (
      <SectionContainer
        title={t('customerFactFind:financial.information.title')}>
        <Content>
          <Column flex={2} p={space[4]}>
            {prioritization.map((item, index) => {
              return (
                <Box key={item.key}>
                  {index > 0 && <Divider />}
                  <StateSectionHeader
                    title={t(
                      `customerFactFind:financial.${item.key
                        .charAt(0)
                        .toLowerCase()}${item.key.substring(1)}` as TFuncKey,
                    )}
                    onPress={() => setType(item.key)}
                    done={checkValid(item.key)}
                    selected={type === item.key}
                    alerted={
                      quotation?.containMHITProduct &&
                      item.key === 'MedicalPlanning'
                    }
                    style={
                      index === 0
                        ? { paddingBottom: space[5] }
                        : index === prioritization.length - 1
                        ? { paddingTop: space[5] }
                        : { paddingVertical: space[5] }
                    }
                  />
                </Box>
              );
            })}
          </Column>
          <VerticalDivider />
          <Column flex={5}>{renderForm()}</Column>
        </Content>
      </SectionContainer>
    );
  },
);

export default FinancialInformation;

const Content = styled(Row)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  gap: space[4],
  paddingHorizontal: space[4],
  paddingVertical: space[5],
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    marginHorizontal: theme.space[4],
    height: 1,
    backgroundColor: theme.colors.surface,
  };
});

export const VerticalDivider = styled(View)(({ theme }) => {
  return {
    width: 1,
    backgroundColor: theme.colors.surface,
  };
});
