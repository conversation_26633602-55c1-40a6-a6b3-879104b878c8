import styled from '@emotion/native';
import {
  Box,
  Column,
  H6,
  Row,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { MoneyCapital } from 'cube-ui-components/dist/cjs/icons/pictograms';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ItemListingField from './components/ItemListingField';
import ItemListingHeader from './components/ItemListingHeader';
import { FinancialIncomeProtectionFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import { Control, useController, useWatch } from 'react-hook-form';
import {
  areNumbers,
  round,
} from 'features/customerFactFind/utils/numberUtil';
import { formatCurrency } from 'utils';

interface IncomeProtectionProps {
  control: Control<FinancialIncomeProtectionFormSchemaType>;
  priorityIndex: number;
}

const IncomeProtection = (props: IncomeProtectionProps) => {
  const { control, priorityIndex } = props;

  const { annualIncome, replacedYears, existingLifeInsurance } = useWatch({
    control,
  });

  const {
    field: { onChange: onChangeAdditionalAmount },
  } = useController({
    control,
    name: 'additionalAmount',
  });

  const additionalAmount = Math.max(
    0,
    round(
      Number(annualIncome) * Number(replacedYears) -
        Number(existingLifeInsurance),
    ),
  );
  useEffect(() => {
    onChangeAdditionalAmount(additionalAmount);
  }, [additionalAmount, onChangeAdditionalAmount]);

  const isAdditionalAmountValid = areNumbers(
    annualIncome,
    replacedYears,
    existingLifeInsurance,
  );

  const { t } = useTranslation(['customerFactFind']);

  return (
    <Wrapper>
      <ItemListingHeader
        title={t('customerFactFind:financial.incomeProtection')}
        tooltip={t(
          'customerFactFind:customerPreference.IncomeProtection.tooltip',
        )}
        label={t('customerFactFind:financial.customerPriority', {
          priorityNumber: priorityIndex + 1,
        })}
        icon={MoneyCapital}
      />
      <ItemListingField
        title={t('customerFactFind:financial.incomeProtection.1')}
        label={t('customerFactFind:financial.annualIncome')}
        name="annualIncome"
        control={control}
        isAmountInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.incomeProtection.2')}
        label={t('customerFactFind:financial.replacedYears')}
        name="replacedYears"
        control={control}
        isYearInput
      />
      <ItemListingField
        title={t('customerFactFind:financial.incomeProtection.3')}
        label={t('customerFactFind:financial.amount')}
        name="existingLifeInsurance"
        control={control}
        isAmountInput
      />
      <AdditionalContainer alignItems="center">
        <Box flex={1}>
          <SmallLabel fontWeight="medium">
            {t(
              'customerFactFind:financial.incomeProtection.additionalAmountRequired',
            )}
          </SmallLabel>
        </Box>

        <CurrencyContainer>
          {`${t('customerFactFind:financial.rm')} `}
        </CurrencyContainer>
        <H6 fontWeight="bold">
          {isAdditionalAmountValid
            ? formatCurrency(additionalAmount)
            : t('customerFactFind:financial.amountPlaceHolder')}
        </H6>
      </AdditionalContainer>
    </Wrapper>
  );
};

export default IncomeProtection;

const Wrapper = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingBottom: space[5],
}));

const AdditionalContainer = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[3],
  borderRadius: space[2],
  backgroundColor: colors.primaryVariant3,
  padding: space[3],
}));

const CurrencyContainer = styled(SmallBody)(({ theme: { space } }) => ({
  marginVertical: space[1],
  fontWeight: 'bold',
  alignSelf: 'flex-end',
  textAlignVertical: 'top',
}));
