import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useCFFValidationResolver } from 'features/customerFactFind/hooks/useCFFValidationResolver';
import {
  CustomerDisclosureOption,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import {
  FinancialChildEducationFormSchemaType,
  FinancialIncomeProtectionFormSchemaType,
  FinancialInvestmentPreferenceFormSchemaType,
  FinancialInvestmentsFormSchemaType,
  FinancialMedicalPlanningFormSchemaType,
  FinancialPlanningFormSchemaType,
  FinancialRetirementFormSchemaType,
  FinancialSavingsFormSchemaType,
  FinancialStatementAndAnalysisFormSchemaType,
  financialChildEducationFormValidationSchema,
  financialIncomeProtectionFormValidationSchema,
  financialInvestmentPreferenceFormValidationSchema,
  financialInvestmentsFormValidationSchema,
  financialMedicalPlanningFormValidationSchema,
  financialPlanningFormValidationSchema,
  financialRetirementFormValidationSchema,
  financialSavingsFormValidationSchema,
  financialStatementAndAnalysisFormValidationSchema,
} from 'features/customerFactFind/validations/financialStatementValidationSchema';
import { useForm } from 'react-hook-form';
import { shallow } from 'zustand/shallow';
import { Footer } from '../footer/Footer';
import FinancialPlanning from './financialPlanning/FinancialPlanning.tablet';
import FinancialStatementAndAnalysis from './financialStatementAndAnalysis/FinancialStatementAndAnalysis.tablet';
import FinancialInformation, {
  FinancialInformationRef,
} from './information/FinancialInformation.tablet';
import InvestmentPreference from './investmentPreference/InvestmentPreference.tablet';
import InvestmentPreferenceOld from './investmentPreference/InvestmentPreference.tablet.old';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useMemo, useRef, useState } from 'react';
import { useIncompleteFields } from 'features/eApp/hooks/useIncompleteFields';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import RiskConfirmation from './riskConfirmation/RiskConfirmation';
import useToggle from 'hooks/useToggle';

const FinancialStatement = () => {
  const { space } = useTheme();
  const quotation = useSelectedQuotation();
  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const financialInformationRef = useRef<FinancialInformationRef>(null);
  const [riskConfirmationVisible, showRiskConfirmation, hideRiskConfirmation] =
    useToggle();
  const [riskConfirmationType, setRiskConfirmationType] = useState<
    'High' | 'Low'
  >('High');

  const {
    financialStatement,
    saveFinancialInvestmentPreference,
    saveFinancialStatementAndAnalysis,
    saveFinancialIncomeProtection,
    saveFinancialRetirement,
    saveFinancialChildEducation,
    saveFinancialSavings,
    saveFinancialInvestments,
    saveFinancialMedicalPlanning,
    saveFinancialPlanning,
    customerChoice,
    prioritization,
    updateStep,
  } = useCustomerFactFindStore(
    state => ({
      financialStatement: state.financialStatement,
      saveFinancialInvestmentPreference:
        state.saveFinancialInvestmentPreference,
      saveFinancialStatementAndAnalysis:
        state.saveFinancialStatementAndAnalysis,
      saveFinancialIncomeProtection: state.saveFinancialIncomeProtection,
      saveFinancialRetirement: state.saveFinancialRetirement,
      saveFinancialChildEducation: state.saveFinancialChildEducation,
      saveFinancialSavings: state.saveFinancialSavings,
      saveFinancialInvestments: state.saveFinancialInvestments,
      saveFinancialMedicalPlanning: state.saveFinancialMedicalPlanning,
      saveFinancialPlanning: state.saveFinancialPlanning,
      customerChoice: state.customerPreference.customerChoice,
      prioritization: state.customerPreference.prioritization,
      updateStep: state.updateStep,
    }),
    shallow,
  );

  const {
    getValues: getInvestmentPreferenceValues,
    control: financialInvestmentPreferenceFormControl,
    watch: watchFinancialInvestmentPreferenceForm,
    formState: { isValid: isInvestmentPreferenceValid },
  } = useForm<FinancialInvestmentPreferenceFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.investmentPreference,
    resolver: useCFFValidationResolver(
      financialInvestmentPreferenceFormValidationSchema,
    ),
  });
  const investmentPreferenceStatus = useIncompleteFields({
    control: financialInvestmentPreferenceFormControl,
    watch: watchFinancialInvestmentPreferenceForm,
    schema: financialInvestmentPreferenceFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getStatementAndAnalysisValues,
    control: financialStatementAndAnalysisFormControl,
    watch: watchFinancialStatementAndAnalysisForm,
    formState: { isValid: isStatementAndAnalysisValid },
  } = useForm<FinancialStatementAndAnalysisFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.statementAndAnalysis,
    resolver: useYupResolver(financialStatementAndAnalysisFormValidationSchema),
  });
  const statementAndAnalysisStatus = useIncompleteFields({
    control: financialStatementAndAnalysisFormControl,
    watch: watchFinancialStatementAndAnalysisForm,
    schema: financialStatementAndAnalysisFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getIncomeProtectionValues,
    control: financialIncomeProtectionFormControl,
    watch: watchFinancialIncomeProtectionForm,
    formState: { isValid: isIncomeProtectionValid },
  } = useForm<FinancialIncomeProtectionFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.incomeProtection,
    resolver: useCFFValidationResolver(
      financialIncomeProtectionFormValidationSchema,
    ),
  });
  const incomeProtectionStatus = useIncompleteFields({
    control: financialIncomeProtectionFormControl,
    watch: watchFinancialIncomeProtectionForm,
    schema: financialIncomeProtectionFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getRetirementValues,
    control: financialRetirementFormControl,
    watch: watchFinancialRetirementForm,
    formState: { isValid: isRetirementValid },
  } = useForm<FinancialRetirementFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.retirementIncome,
    resolver: useCFFValidationResolver(financialRetirementFormValidationSchema),
  });
  const retirementStatus = useIncompleteFields({
    control: financialRetirementFormControl,
    watch: watchFinancialRetirementForm,
    schema: financialRetirementFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getChildEducationValues,
    control: financialChildEducationFormControl,
    watch: watchFinancialChildEducationForm,
    formState: { isValid: isChildEducationValid },
  } = useForm<FinancialChildEducationFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.childEducation,
    resolver: useYupResolver(financialChildEducationFormValidationSchema),
  });
  const childEducationStatus = useIncompleteFields({
    control: financialChildEducationFormControl,
    watch: watchFinancialChildEducationForm,
    schema: financialChildEducationFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getSavingsValues,
    control: financialSavingsFormControl,
    watch: watchFinancialSavingsForm,
    formState: { isValid: isSavingsValid },
  } = useForm<FinancialSavingsFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.savings,
    resolver: useCFFValidationResolver(financialSavingsFormValidationSchema),
  });
  const savingsStatus = useIncompleteFields({
    control: financialSavingsFormControl,
    watch: watchFinancialSavingsForm,
    schema: financialSavingsFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getMedicalPlanningValues,
    control: financialMedicalPlanningFormControl,
    watch: watchFinancialMedicalPlanningForm,
    formState: { isValid: isMedicalPlanningValid },
  } = useForm<FinancialMedicalPlanningFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.medicalPlanning,
    resolver: useCFFValidationResolver(
      financialMedicalPlanningFormValidationSchema,
    ),
  });
  const medicalPlanningStatus = useIncompleteFields({
    control: financialMedicalPlanningFormControl,
    watch: watchFinancialMedicalPlanningForm,
    schema: financialMedicalPlanningFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getInvestmentValues,
    control: financialInvestmentsFormControl,
    watch: watchFinancialInvestmentsForm,
    formState: { isValid: isInvestmentsValid },
  } = useForm<FinancialInvestmentsFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.investments,
    resolver: useCFFValidationResolver(
      financialInvestmentsFormValidationSchema,
    ),
  });
  const investmentsStatus = useIncompleteFields({
    control: financialInvestmentsFormControl,
    watch: watchFinancialInvestmentsForm,
    schema: financialInvestmentsFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const {
    getValues: getPlanningValues,
    control: financialPlanningFormControl,
    watch: watchFinancialPlanningForm,
    formState: { isValid: isPlanningValid },
  } = useForm<FinancialPlanningFormSchemaType>({
    mode: 'onBlur',
    defaultValues: financialStatement.financialPlanning,
    resolver: useCFFValidationResolver(financialPlanningFormValidationSchema),
  });
  const planningStatus = useIncompleteFields({
    control: financialPlanningFormControl,
    watch: watchFinancialPlanningForm,
    schema: financialPlanningFormValidationSchema,
    scrollRef,
    scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
  });

  const hasChildInsured = useCustomerFactFindStore(state => state.hasChild);

  const isVUL = useMemo(
    () => quotation?.plans?.some(e => e?.productLine == 'UL'),
    [quotation?.plans],
  );

  let isFinish = false;
  if (customerChoice === CustomerDisclosureOption.NONE) {
    isFinish = isVUL ? isInvestmentPreferenceValid : true;
  }
  const isMiddleSectionValid =
    isStatementAndAnalysisValid &&
    isIncomeProtectionValid &&
    isRetirementValid &&
    (!hasChildInsured || isChildEducationValid) &&
    isSavingsValid &&
    isInvestmentsValid &&
    isMedicalPlanningValid;

  if (customerChoice === CustomerDisclosureOption.FULL) {
    isFinish =
      isInvestmentPreferenceValid && isMiddleSectionValid && isPlanningValid;
  }
  if (customerChoice === CustomerDisclosureOption.PARTIAL) {
    isFinish =
      (isVUL ? isInvestmentPreferenceValid : true) &&
      (isInvestmentPreferenceValid || isMiddleSectionValid || isPlanningValid);
  }

  if (quotation?.containMHITProduct) {
    isFinish &&= Boolean(isMedicalPlanningValid);
  }

  const { focusOnIncompleteField, totalIncompleteRequiredFields } =
    useMemo(() => {
      if (customerChoice === CustomerDisclosureOption.FULL) {
        return {
          totalIncompleteRequiredFields:
            (isVUL
              ? investmentPreferenceStatus.totalIncompleteRequiredFields
              : 0) +
            statementAndAnalysisStatus.totalIncompleteRequiredFields +
            incomeProtectionStatus.totalIncompleteRequiredFields +
            retirementStatus.totalIncompleteRequiredFields +
            childEducationStatus.totalIncompleteRequiredFields +
            savingsStatus.totalIncompleteRequiredFields +
            medicalPlanningStatus.totalIncompleteRequiredFields +
            investmentsStatus.totalIncompleteRequiredFields +
            planningStatus.totalIncompleteRequiredFields,
          focusOnIncompleteField: () => {
            if (
              isVUL &&
              investmentPreferenceStatus.totalIncompleteRequiredFields > 0
            ) {
              investmentPreferenceStatus.focusOnNextIncompleteField();
            } else if (
              statementAndAnalysisStatus.totalIncompleteRequiredFields > 0
            ) {
              statementAndAnalysisStatus.focusOnNextIncompleteField();
            } else if (
              incomeProtectionStatus.totalIncompleteRequiredFields > 0 ||
              retirementStatus.totalIncompleteRequiredFields > 0 ||
              childEducationStatus.totalIncompleteRequiredFields > 0 ||
              savingsStatus.totalIncompleteRequiredFields > 0 ||
              medicalPlanningStatus.totalIncompleteRequiredFields > 0 ||
              investmentsStatus.totalIncompleteRequiredFields > 0
            ) {
              let done = false;
              for (let i = 0; i < prioritization.length; i++) {
                if (done) continue;
                const area = prioritization[i];
                switch (area.key) {
                  case 'IncomeProtection':
                    if (
                      incomeProtectionStatus.totalIncompleteRequiredFields > 0
                    ) {
                      done = true;
                      financialInformationRef.current?.setType(
                        'IncomeProtection',
                      );
                      setTimeout(
                        incomeProtectionStatus.focusOnNextIncompleteField,
                        500,
                      );
                    }
                    break;
                  case 'RetirementIncome':
                    if (retirementStatus.totalIncompleteRequiredFields > 0) {
                      done = true;
                      financialInformationRef.current?.setType(
                        'RetirementIncome',
                      );
                      setTimeout(
                        retirementStatus.focusOnNextIncompleteField,
                        500,
                      );
                    }
                    break;
                  case 'ChildEducation':
                    if (
                      childEducationStatus.totalIncompleteRequiredFields > 0
                    ) {
                      done = true;
                      financialInformationRef.current?.setType(
                        'ChildEducation',
                      );
                      setTimeout(
                        childEducationStatus.focusOnNextIncompleteField,
                        500,
                      );
                    }
                    break;
                  case 'Savings':
                    if (savingsStatus.totalIncompleteRequiredFields > 0) {
                      done = true;
                      financialInformationRef.current?.setType('Savings');
                      setTimeout(savingsStatus.focusOnNextIncompleteField, 500);
                    }
                    break;
                  case 'Investments':
                    if (investmentsStatus.totalIncompleteRequiredFields > 0) {
                      done = true;
                      financialInformationRef.current?.setType('Investments');
                      setTimeout(
                        investmentsStatus.focusOnNextIncompleteField,
                        500,
                      );
                    }
                    break;
                  case 'MedicalPlanning':
                    if (
                      medicalPlanningStatus.totalIncompleteRequiredFields > 0
                    ) {
                      done = true;
                      financialInformationRef.current?.setType(
                        'MedicalPlanning',
                      );
                      setTimeout(
                        medicalPlanningStatus.focusOnNextIncompleteField,
                        500,
                      );
                    }
                    break;
                }
              }
            } else if (planningStatus.totalIncompleteRequiredFields > 0) {
              planningStatus.focusOnNextIncompleteField();
            }
          },
        };
      } else if (quotation?.containMHITProduct) {
        return {
          totalIncompleteRequiredFields:
            (isVUL
              ? investmentPreferenceStatus.totalIncompleteRequiredFields
              : 0) + medicalPlanningStatus.totalIncompleteRequiredFields,
          focusOnIncompleteField: () => {
            if (
              isVUL &&
              investmentPreferenceStatus.totalIncompleteRequiredFields > 0
            ) {
              investmentPreferenceStatus.focusOnNextIncompleteField();
            } else {
              financialInformationRef.current?.setType('MedicalPlanning');
              setTimeout(medicalPlanningStatus.focusOnNextIncompleteField, 500);
            }
          },
        };
      }
      return {
        totalIncompleteRequiredFields: isVUL
          ? investmentPreferenceStatus.totalIncompleteRequiredFields
          : 0,
        focusOnIncompleteField: () => {
          if (
            isVUL &&
            investmentPreferenceStatus.totalIncompleteRequiredFields > 0
          ) {
            investmentPreferenceStatus.focusOnNextIncompleteField();
          }
        },
      };
    }, [
      customerChoice,
      quotation?.containMHITProduct,
      isVUL,
      investmentPreferenceStatus,
      statementAndAnalysisStatus,
      incomeProtectionStatus.totalIncompleteRequiredFields,
      incomeProtectionStatus.focusOnNextIncompleteField,
      retirementStatus.totalIncompleteRequiredFields,
      retirementStatus.focusOnNextIncompleteField,
      childEducationStatus.totalIncompleteRequiredFields,
      childEducationStatus.focusOnNextIncompleteField,
      savingsStatus.totalIncompleteRequiredFields,
      savingsStatus.focusOnNextIncompleteField,
      medicalPlanningStatus.totalIncompleteRequiredFields,
      medicalPlanningStatus.focusOnNextIncompleteField,
      investmentsStatus.totalIncompleteRequiredFields,
      investmentsStatus.focusOnNextIncompleteField,
      planningStatus,
      prioritization,
    ]);

  const saveData = () => {
    saveFinancialInvestmentPreference(getInvestmentPreferenceValues());
    saveFinancialStatementAndAnalysis(getStatementAndAnalysisValues());
    saveFinancialIncomeProtection(getIncomeProtectionValues());
    saveFinancialRetirement(getRetirementValues());
    saveFinancialChildEducation(getChildEducationValues());
    saveFinancialSavings(getSavingsValues());
    saveFinancialInvestments(getInvestmentValues());
    saveFinancialMedicalPlanning(getMedicalPlanningValues());
    saveFinancialPlanning(getPlanningValues());
  };

  const onNext = () => {
    // check for investment risk confirmation
    const investmentPreference = getInvestmentPreferenceValues().risk;
    const funds = quotation?.funds;
    const FUND_GROUP_A = 'BOND';
    const FUND_GROUP_B = 'MANAGED';
    const FUND_GROUP_C = 'EQUITY';
    let shouldConfirmRisk = false;
    let riskConfirmationType: 'High' | 'Low' | '' = '';
    if (isVUL && funds && funds.fundAllocation && funds.fundAllocation.length > 0) {
      const selectedFunds = funds.fundAllocation.filter(
        fund => fund.allocation > 0,
      );
      let isSingleFundType = true;
      const firstFundType = selectedFunds[0].fundType;
      selectedFunds.forEach(fund => {
        if (firstFundType !== fund.fundType) {
          isSingleFundType = false;
        }
      });
      if (isSingleFundType) {
        const fundType = selectedFunds[0].fundType;
        switch (fundType) {
          case FUND_GROUP_A:
            if (investmentPreference >= 2) {
              shouldConfirmRisk = true;
              riskConfirmationType = 'Low';
            }
            break;
          case FUND_GROUP_B:
            if (investmentPreference <= 1) {
              shouldConfirmRisk = true;
              riskConfirmationType = 'High';
            } else if (investmentPreference >= 3) {
              shouldConfirmRisk = true;
              riskConfirmationType = 'Low';
            }
            break;
          case FUND_GROUP_C:
            if (investmentPreference <= 2) {
              shouldConfirmRisk = true;
              riskConfirmationType = 'High';
            }
            break;
        }
      } else {
        const groupAPercentage = selectedFunds.reduce(
          (total, fund) =>
            total + (fund.fundType === FUND_GROUP_A ? fund.allocation || 0 : 0),
          0,
        );
        if (groupAPercentage === 0) {
          // only B & C
          if (investmentPreference <= 1) {
            shouldConfirmRisk = true;
            riskConfirmationType = 'High';
          }
        } else {
          if (groupAPercentage > 50 && investmentPreference >= 2) {
            shouldConfirmRisk = true;
            riskConfirmationType = 'Low';
          } else if (groupAPercentage <= 50 && investmentPreference <= 1) {
            shouldConfirmRisk = true;
            riskConfirmationType = 'High';
          }
        }
      }
    }
    if (shouldConfirmRisk && riskConfirmationType) {
      setRiskConfirmationType(riskConfirmationType);
      showRiskConfirmation();
    } else {
      saveData();
      updateStep();
    }
  };

  return (
    <Container>
      <ScrollViewContainer
        ref={scrollRef}
        contentContainerStyle={{ paddingBottom: space[5] }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        <InvestmentPreference
          shouldHighlight={
            isVUL && customerChoice !== CustomerDisclosureOption.FULL
          }
          control={financialInvestmentPreferenceFormControl}
        />
        <FinancialStatementAndAnalysis
          control={financialStatementAndAnalysisFormControl}
        />
        <FinancialInformation
          ref={financialInformationRef}
          financialIncomeProtectionFormControl={
            financialIncomeProtectionFormControl
          }
          financialRetirementFormControl={financialRetirementFormControl}
          financialChildEducationFormControl={
            financialChildEducationFormControl
          }
          financialSavingsFormControl={financialSavingsFormControl}
          financialInvestmentsFormControl={financialInvestmentsFormControl}
          financialMedicalPlanningFormControl={
            financialMedicalPlanningFormControl
          }
        />
        <FinancialPlanning control={financialPlanningFormControl} />
      </ScrollViewContainer>
      <Footer
        activeStep={3}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnIncompleteField}
        onPrimaryPress={onNext}
        isPrimaryDisabled={!isFinish}
      />
    </Container>
  );
};

export default FinancialStatement;

const Container = styled.View(({ theme: { colors } }) => ({
  backgroundColor: colors.surface,
  flex: 1,
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    paddingTop: space[5],
    paddingHorizontal: space[8],
  }),
);
