import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H7 } from 'cube-ui-components';
import SliderIndicator from 'features/customerFactFind/components/SliderIndicator';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import { FinancialInvestmentPreferenceFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, get, useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import RiskInstruction from './RiskInstruction';
import ProgressStepIndicator, {
  StepStatus,
} from './progressStepIndicator/ProgressStepIndicator';
import SectionContainer from '../../SectionContainer';

interface Props {
  control: Control<FinancialInvestmentPreferenceFormSchemaType>;
}

export default function InvestmentPreference({ control }: Props) {
  const { t } = useTranslation(['customerFactFind']);

  const { colors } = useTheme();

  const { risk } = useCustomerFactFindStore(
    state => ({
      risk: state.financialStatement.investmentPreference.risk,
    }),
    shallow,
  );

  const {
    field: { value: position, onChange: setPosition },
  } = useController({ name: 'risk', control });

  useEffect(() => {
    setPosition(risk);
  }, [setPosition, risk]);

  const labels = React.useMemo(() => {
    return [
      t('customerFactFind:financial.investment.conservative'),
      t('customerFactFind:financial.investment.moderatelyConservative'),
      t('customerFactFind:financial.investment.balanced'),
      t('customerFactFind:financial.investment.moderatelyAggressive'),
      t('customerFactFind:financial.investment.aggressive'),
    ];
  }, [t]);

  const renderStepIndicator = ({
    stepStatus,
  }: {
    position: number;
    stepStatus: StepStatus;
  }) => {
    if (stepStatus == StepStatus.CURRENT) {
      return <SliderIndicator />;
    }
  };

  const renderLabel = ({
    stepStatus,
    label,
  }: {
    stepStatus: StepStatus;
    label: string;
  }) => {
    return (
      <H7
        color={
          stepStatus !== StepStatus.UNFINISHED
            ? colors.primary
            : colors.palette.black
        }>
        {label}
      </H7>
    );
  };

  const onRef = <T,>(e: T | null) => {
    const field = get(control._fields, 'risk');

    if (field && e) {
      const elm = e as unknown as HTMLFormElement;
      field._f.ref = {
        focus: () => elm.focus?.(),
        select: () => elm.select?.(),
        setCustomValidity: (message: string) =>
          elm.setCustomValidity?.(message),
        reportValidity: () => elm.reportValidity?.(),
        elm,
        toggleHighlight: () => null,
      };
    }
  };

  return (
    <SectionContainer
      ref={onRef}
      title={t('customerFactFind:financial.investment.title')}>
      <Content>
        <H7>{t('customerFactFind:financial.investment.desc')}</H7>
        <ProgressStepIndicator
          currentPosition={position}
          onPress={position => {
            setPosition(position);
          }}
          renderStepIndicator={renderStepIndicator}
          labels={labels}
          renderLabel={renderLabel}
        />
        <RiskContainer>
          <RiskInstruction />
        </RiskContainer>
      </Content>
    </SectionContainer>
  );
}

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  gap: space[4],
}));

const RiskContainer = styled(Column)(({ theme: { space, colors } }) => ({
  paddingHorizontal: space[9],
}));
