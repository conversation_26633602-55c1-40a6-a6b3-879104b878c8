import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, H7, LargeBody, Row } from 'cube-ui-components';
import {
  LevelAverage,
  LevelAverageToHigh,
  LevelHigh,
  LevelLow,
  LevelLowToMedium,
} from 'cube-ui-components/dist/cjs/icons/pictograms';
import { LinearGradient } from 'expo-linear-gradient';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import { FinancialInvestmentPreferenceFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, get, useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { shallow } from 'zustand/shallow';
import SectionContainer from '../../SectionContainer';
import RiskInstruction from './RiskInstruction';

interface Props {
  shouldHighlight: boolean | undefined;
  control: Control<FinancialInvestmentPreferenceFormSchemaType>;
}

export default function InvestmentPreference({
  control,
  shouldHighlight,
}: Props) {
  const { t } = useTranslation(['customerFactFind']);

  const { colors, space, sizes } = useTheme();

  const {
    field: { value: position, onChange: setPosition },
  } = useController({ name: 'risk', control });

  const steps = React.useMemo(() => {
    return [
      {
        label: t('customerFactFind:financial.investment.conservative'),
        icon: <LevelLow size={sizes[14]} />,
      },
      {
        label: t(
          'customerFactFind:financial.investment.moderatelyConservative',
        ),
        icon: <LevelLowToMedium size={sizes[14]} />,
      },
      {
        label: t('customerFactFind:financial.investment.balanced'),
        icon: <LevelAverage size={sizes[14]} />,
      },
      {
        label: t('customerFactFind:financial.investment.moderatelyAggressive'),
        icon: <LevelAverageToHigh size={sizes[14]} />,
      },
      {
        label: t('customerFactFind:financial.investment.aggressive'),
        icon: <LevelHigh size={sizes[14]} />,
      },
    ];
  }, [sizes, t]);

  const onRef = <T,>(e: T | null) => {
    const field = get(control._fields, 'risk');

    if (field && e) {
      const elm = e as unknown as HTMLFormElement;
      field._f.ref = {
        focus: () => elm.focus?.(),
        select: () => elm.select?.(),
        setCustomValidity: (message: string) =>
          elm.setCustomValidity?.(message),
        reportValidity: () => elm.reportValidity?.(),
        elm,
        toggleHighlight: () => null,
      };
    }
  };

  return (
    <SectionContainer
      ref={onRef}
      title={t('customerFactFind:financial.investment.title')}>
      <Content>
        <LargeBody>{t('customerFactFind:financial.investment.desc')}</LargeBody>
        <Row gap={space[4]}>
          {steps.map((step, i) => (
            <StepButton
              key={i}
              selected={position === i}
              highlight={shouldHighlight && position < 0}
              onPress={() => {
                setPosition(i);
              }}>
              <>
                {step.icon}
                <H7>{step.label}</H7>
              </>
            </StepButton>
          ))}
        </Row>
        <RiskContainer
          start={{ x: 0, y: 1 }}
          end={{ x: 1, y: 1 }}
          colors={[
            colors.palette.fwdLightGreen[20],
            colors.palette.fwdAlternativeOrange[20],
          ]}>
          <RiskInstruction />
        </RiskContainer>
      </Content>
    </SectionContainer>
  );
}

const StepButton = styled(TouchableOpacity)<{
  selected?: boolean;
  highlight?: boolean;
}>(({ theme, selected, highlight }) => ({
  flex: 1,
  alignItems: 'center',
  borderWidth: selected ? 2 : 1,
  borderRadius: theme.borderRadius['x-small'],
  borderColor: highlight
    ? theme.colors.primaryVariant
    : selected
    ? theme.colors.primary
    : theme.colors.palette.fwdGrey[100],
  backgroundColor: selected
    ? theme.colors.primaryVariant3
    : theme.colors.background,
  padding: theme.space[4],
  minHeight: theme.space[32],
  shadowColor: highlight ? theme.colors.palette.fwdOrange[100] : undefined,
  shadowOffset: highlight
    ? {
        width: 0,
        height: 0,
      }
    : undefined,
  shadowOpacity: highlight ? 0.5 : undefined,
  shadowRadius: highlight ? 4 : undefined,
}));

const Content = styled(Box)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[6],
  gap: space[3],
}));

const RiskContainer = styled(LinearGradient)(
  ({ theme: { space, colors } }) => ({
    paddingVertical: space[1],
    paddingHorizontal: space[4],
  }),
);
