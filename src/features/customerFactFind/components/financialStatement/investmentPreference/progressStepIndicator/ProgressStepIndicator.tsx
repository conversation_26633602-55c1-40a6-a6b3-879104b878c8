import React from 'react';
import { TouchableWithoutFeedback, ViewStyle } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export enum StepStatus {
  CURRENT = 'current',
  FINISHED = 'finished',
  UNFINISHED = 'unfinished',
}

interface ProgressStepIndicatorProps {
  currentPosition: number;
  stepCount?: number;
  direction?: 'horizontal' | 'vertical';
  labels?: string[];

  onPress?(step: number): void;

  renderStepIndicator(args: {
    position: number;
    stepStatus: string;
  }): React.ReactNode;

  renderLabel(args: { stepStatus: string; label: string }): React.ReactNode;
}

const STEP_INDICATION_SIZE = 16;
const CURRENT_STEP_INDICATION_SIZE = 56;
const SEPARATOR_STOKE_WIDTH = 12;
const STEP_STROKE_WIDTH = 2;

const ProgressStepIndicator = ({
  currentPosition,
  stepCount = 5,
  direction = 'horizontal',
  labels = [],
  onPress,
  renderStepIndicator: renderCustomStepIndicator,
  renderLabel,
}: ProgressStepIndicatorProps) => {
  const { sizes, colors } = useTheme();
  const [width, setWidth] = React.useState<number>(0);
  const [height, setHeight] = React.useState<number>(0);
  const [progressBarSize, setProgressBarSize] = React.useState<number>(0);

  const progressValue = useSharedValue(0);

  const progressStyles = useAnimatedStyle(() => {
    return {
      width: withTiming(progressValue.value, {
        duration: 200,
        easing: Easing.linear,
      }),
    };
  }, [progressValue.value]);

  const defaultStepStyles: ViewStyle = React.useMemo(() => {
    return {
      overflow: 'hidden',
      borderWidth: STEP_STROKE_WIDTH,
      height: STEP_INDICATION_SIZE,
      width: STEP_INDICATION_SIZE,
      borderRadius: STEP_INDICATION_SIZE / 2,
    };
  }, []);

  const stepPressed = (position: number) => {
    if (onPress) {
      onPress(position);
    }
  };

  const onCurrentPositionChanged = React.useCallback(
    (position: number) => {
      if (position < 0) {
        progressValue.value = 0;
        return;
      }
      if (position > stepCount - 1) {
        position = stepCount - 1;
      }
      const animateToPosition = (progressBarSize / (stepCount - 1)) * position;
      progressValue.value = animateToPosition;
    },
    [progressBarSize, progressValue, stepCount],
  );

  React.useEffect(() => {
    onCurrentPositionChanged(currentPosition);
  }, [currentPosition, onCurrentPositionChanged, progressBarSize]);

  const renderProgressBarBackground = () => {
    let progressBarBackgroundStyle: ViewStyle = {
      backgroundColor: colors.palette.fwdGreyDark,
      position: 'absolute',
    };
    if (direction === 'vertical') {
      progressBarBackgroundStyle = {
        ...progressBarBackgroundStyle,
        left: (width - SEPARATOR_STOKE_WIDTH) / 2,
        top: height / (2 * stepCount),
        bottom: height / (2 * stepCount),
        width: SEPARATOR_STOKE_WIDTH,
      };
    } else {
      progressBarBackgroundStyle = {
        ...progressBarBackgroundStyle,
        top: (height - SEPARATOR_STOKE_WIDTH) / 2,
        left: width / (2 * stepCount),
        right: width / (2 * stepCount),
        height: SEPARATOR_STOKE_WIDTH,
      };
    }
    return (
      <Box
        onLayout={event => {
          if (direction === 'vertical') {
            setProgressBarSize(event.nativeEvent.layout.height);
          } else {
            setProgressBarSize(event.nativeEvent.layout.width);
          }
        }}
        style={progressBarBackgroundStyle}
      />
    );
  };

  const renderProgressBar = () => {
    let progressBarStyle: ViewStyle = {
      backgroundColor: colors.primary,
      position: 'absolute',
    };
    if (direction === 'vertical') {
      progressBarStyle = {
        ...progressBarStyle,
        left: (width - SEPARATOR_STOKE_WIDTH) / 2,
        top: height / (2 * stepCount),
        bottom: height / (2 * stepCount),
        width: SEPARATOR_STOKE_WIDTH,
      };
    } else {
      progressBarStyle = {
        ...progressBarStyle,
        top: (height - SEPARATOR_STOKE_WIDTH) / 2,
        left: width / (2 * stepCount),
        right: width / (2 * stepCount),
        height: SEPARATOR_STOKE_WIDTH,
      };
    }
    return <Animated.View style={[progressBarStyle, progressStyles]} />;
  };

  const renderStepIndicator = () => {
    const steps = [];
    for (let position = 0; position < stepCount; position++) {
      steps.push(
        <TouchableWithoutFeedback
          key={position}
          onPress={() => stepPressed(position)}>
          <Box
            flex={1}
            flexDirection={direction === 'vertical' ? 'column' : 'row'}
            alignItems="center"
            justifyContent="center">
            {renderStep(position)}
          </Box>
        </TouchableWithoutFeedback>,
      );
    }
    return (
      <Box
        onLayout={event => {
          setWidth(event.nativeEvent.layout.width);
          setHeight(event.nativeEvent.layout.height);
        }}
        flexDirection={direction === 'vertical' ? 'column' : 'row'}
        alignItems="center"
        justifyContent="space-around"
        bgColor={'transparent'}
        height={
          direction === 'vertical' ? undefined : CURRENT_STEP_INDICATION_SIZE
        }
        width={
          direction === 'vertical' ? CURRENT_STEP_INDICATION_SIZE : undefined
        }>
        {steps}
      </Box>
    );
  };

  const renderStepLabels = () => {
    if (!labels || labels.length === 0) {
      return;
    }
    const labelViews = labels.map((label, index) => {
      return (
        <TouchableWithoutFeedback
          key={index}
          onPress={() => stepPressed(index)}>
          <Box flex={1} alignItems="center" justifyContent="center">
            {renderLabel?.({
              stepStatus: getStepStatus(index),
              label,
            })}
          </Box>
        </TouchableWithoutFeedback>
      );
    });

    return (
      <Box
        justifyContent="space-around"
        flexDirection={direction === 'vertical' ? 'column' : 'row'}
        padding={sizes[1]}>
        {labelViews}
      </Box>
    );
  };

  const renderStep = (position: number) => {
    let stepStyle: ViewStyle = {};
    switch (getStepStatus(position)) {
      case StepStatus.CURRENT: {
        stepStyle = {
          ...defaultStepStyles,
          backgroundColor: colors.primary,
          borderColor: colors.background,
          borderRadius: CURRENT_STEP_INDICATION_SIZE / 2,
          height: CURRENT_STEP_INDICATION_SIZE,
          width: CURRENT_STEP_INDICATION_SIZE,
        };
        break;
      }
      case StepStatus.FINISHED: {
        stepStyle = {
          ...defaultStepStyles,
          backgroundColor: colors.primary,
          borderColor: colors.background,
        };
        break;
      }

      case StepStatus.UNFINISHED: {
        stepStyle = {
          ...defaultStepStyles,
          backgroundColor: colors.palette.fwdGreyDarkest,
          borderColor: colors.background,
        };
        break;
      }
      default:
    }

    return (
      <Box
        key={'progress-indicator'}
        style={stepStyle}
        alignItems="center"
        justifyContent="center"
        zIndex={2}>
        {renderCustomStepIndicator?.({
          position,
          stepStatus: getStepStatus(position),
        })}
      </Box>
    );
  };

  const getStepStatus = (stepPosition: number) => {
    if (stepPosition === currentPosition) {
      return StepStatus.CURRENT;
    } else if (stepPosition < currentPosition) {
      return StepStatus.FINISHED;
    } else {
      return StepStatus.UNFINISHED;
    }
  };

  return (
    <Box
      bgColor={'transparent'}
      flexDirection={direction === 'vertical' ? 'row' : 'column'}
      flex={1}>
      {width !== 0 && (
        <React.Fragment>
          {renderProgressBarBackground()}
          {renderProgressBar()}
        </React.Fragment>
      )}
      {renderStepIndicator()}
      {labels && renderStepLabels()}
    </Box>
  );
};

export default React.memo(ProgressStepIndicator);
