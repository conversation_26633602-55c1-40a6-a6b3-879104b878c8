import styled from '@emotion/native';
import { LargeLabel, Row } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';

const RiskInstruction = () => {
  const { t } = useTranslation(['customerFactFind']);

  return (
    <Wrapper>
      <LargeLabel fontWeight="medium">
        {t('customerFactFind:financial.investment.lowRisk')}
      </LargeLabel>
      <LargeLabel fontWeight="medium">
        {t('customerFactFind:financial.investment.highRisk')}
      </LargeLabel>
    </Wrapper>
  );
};

export default RiskInstruction;

const Wrapper = styled(Row)(() => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
}));
