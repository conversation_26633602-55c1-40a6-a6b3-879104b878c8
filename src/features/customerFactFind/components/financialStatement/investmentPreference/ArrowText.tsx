import React from 'react';
import { useTheme } from '@emotion/react';
import { H7, Row } from 'cube-ui-components';
import styled from '@emotion/native';

interface ArrowTextProps {
  direction?: 'left' | 'right';
  title: string;
}

const ArrowText = ({ title, direction = 'right' }: ArrowTextProps) => {
  const { colors } = useTheme();

  const renderText = React.useCallback(() => {
    return (
      <TextContainer>
        <H7 color={colors.placeholder}>{title}</H7>
      </TextContainer>
    );
  }, [colors.placeholder, title]);

  const renderContent = React.useCallback(() => {
    if (direction == 'right') {
      return (
        <>
          {renderText()}
          <RightTriangle />
        </>
      );
    } else {
      return (
        <>
          <LeftTriangle />
          {renderText()}
        </>
      );
    }
  }, [direction, renderText]);

  return <Wrapper>{renderContent()}</Wrapper>;
};

export default React.memo(ArrowText);

const Wrapper = styled(Row)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const TextContainer = styled(Row)(
  ({ theme: { space, colors } }) => ({
    gap: space[1],
    alignItems: 'center',
    backgroundColor: colors.surface,
    alignContent: 'center',
    justifyContent: 'center',
    paddingHorizontal: space[4],
    paddingVertical: space[2],
  }),
);

const RightTriangle = styled(Row)(
  ({ theme: { space, colors } }) => ({
    backgroundColor: colors.palette.whiteTransparent,
    borderStyle: 'solid',
    borderLeftWidth: space[8],
    borderRightWidth: space[8],
    borderBottomWidth: space[11],
    borderLeftColor: colors.palette.whiteTransparent,
    borderRightColor: colors.palette.whiteTransparent,
    borderBottomColor: colors.surface,
    transform: [{ rotate: '90deg' }],
    marginLeft: -space[3],
  }),
);

const LeftTriangle = styled(Row)(
  ({ theme: { space, colors } }) => ({
    backgroundColor: colors.palette.whiteTransparent,
    borderStyle: 'solid',
    borderLeftWidth: space[8],
    borderRightWidth: space[8],
    borderBottomWidth: space[11],
    borderLeftColor: colors.palette.whiteTransparent,
    borderRightColor: colors.palette.whiteTransparent,
    borderBottomColor: colors.surface,
    transform: [{ rotate: '270deg' }],
    marginRight: -space[3],
  }),
);
