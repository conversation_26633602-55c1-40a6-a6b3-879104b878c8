import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { Box, CurrencyTextField, LargeLabel, Row } from 'cube-ui-components';
import React from 'react';
import { Control, FieldValues, Path } from 'react-hook-form';

interface ItemListingTripleFieldProps<T extends FieldValues> {
  title: string;
  firstLabel: string;
  secondLabel: string;
  thirdLabel: string;
  firstName: Path<T>;
  secondName: Path<T>;
  thirdName: Path<T>;
  control: Control<T>;
}

const ItemListingTripleField = <T extends FieldValues>(
  props: ItemListingTripleFieldProps<T>,
) => {
  const {
    title,
    firstLabel,
    secondLabel,
    thirdLabel,
    firstName,
    secondName,
    thirdName,
    control,
  } = props;
  const { colors } = useTheme();
  return (
    <Wrapper backgroundColor={colors.background} alignItems="center">
      <Box flex={1.5}>
        <LargeLabel fontWeight="medium">{title}</LargeLabel>
      </Box>

      <Box flex={1}>
        <Input
          control={control}
          as={CurrencyTextField}
          name={firstName}
          label={firstLabel}
        />
      </Box>

      <Box flex={1}>
        <Input
          control={control}
          as={CurrencyTextField}
          name={secondName}
          label={secondLabel}
        />
      </Box>

      <Box flex={1}>
        <Input
          control={control}
          as={CurrencyTextField}
          name={thirdName}
          label={thirdLabel}
        />
      </Box>
    </Wrapper>
  );
};

export default ItemListingTripleField;

const Wrapper = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: 22,
  marginHorizontal: space[6],
  backgroundColor: colors.background,
  gap: space[3],
}));
