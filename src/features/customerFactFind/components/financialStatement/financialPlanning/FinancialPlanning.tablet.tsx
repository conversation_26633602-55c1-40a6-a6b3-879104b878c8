import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Column,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { FinancialPlanningFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useEffect } from 'react';
import { Control, useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ItemListingTripleField from './components/ItemListingTripleField';
import { useCustomerFactFindStore } from 'features/customerFactFind/utils/store/customerFactFindStore';
import SectionContainer from '../../SectionContainer';

interface FinancialPlanningProps {
  control: Control<FinancialPlanningFormSchemaType>;
}

const FinancialPlanning = (props: FinancialPlanningProps) => {
  const { control } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();

  const {
    field: { onChange: setHasSpouse },
  } = useController({
    control,
    name: 'hasSpouse',
  });

  const hasSpouse = useCustomerFactFindStore(
    state => state.hasSpouse,
  );

  useEffect(() => {
    setHasSpouse(hasSpouse ? 'yes' : 'no');
  }, [hasSpouse, setHasSpouse]);

  return (
    <SectionContainer title={t('customerFactFind:financial.planning.title')}>
      <Content>
        <LargeBody>
          {t('customerFactFind:financial.planning.description')}
        </LargeBody>
      </Content>
      <TableHeader>
        <Box flex={1.5}></Box>
        <Box flex={1}>
          <LargeLabel style={{ marginLeft: space[6] }} fontWeight="medium">
            {t('customerFactFind:financial.monthly')}
          </LargeLabel>
        </Box>
        <Box flex={1}>
          <LargeLabel style={{ marginLeft: space[4] }} fontWeight="medium">
            {t('customerFactFind:financial.yearly')}
          </LargeLabel>
        </Box>
        <Box flex={1}>
          <LargeLabel style={{ marginLeft: space[3] }} fontWeight="medium">
            {t('customerFactFind:financial.lumpSum')}
          </LargeLabel>
        </Box>
      </TableHeader>
      <ItemListingTripleField
        title={t('customerFactFind:financial.proposedParticipant')}
        firstLabel={t('customerFactFind:financial.amount')}
        secondLabel={t('customerFactFind:financial.amount')}
        thirdLabel={t('customerFactFind:financial.amount')}
        firstName="proposedParticipantMonthly"
        secondName="proposedParticipantYearly"
        thirdName="proposedParticipantLumpSum"
        control={control}
      />
      <ItemListingTripleField
        title={t('customerFactFind:financial.spouse')}
        firstLabel={t('customerFactFind:financial.amount')}
        secondLabel={t('customerFactFind:financial.amount')}
        thirdLabel={t('customerFactFind:financial.amount')}
        firstName="spouseMonthly"
        secondName="spouseYearly"
        thirdName="spouseLumpSum"
        control={control}
      />
      <Body
        style={{
          color: colors.secondaryVariant,
          marginHorizontal: space[6],
          marginTop: space[4],
          marginBottom: space[5],
        }}>
        {t('customerFactFind:financial.planning.note')}
      </Body>
    </SectionContainer>
  );
};

export default FinancialPlanning;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[4],
  marginTop: space[5],
}));

const TableHeader = styled(Row)(({ theme: { space, colors } }) => ({
  marginTop: space[4],
  marginBottom: 18,
  backgroundColor: colors.background,
}));
