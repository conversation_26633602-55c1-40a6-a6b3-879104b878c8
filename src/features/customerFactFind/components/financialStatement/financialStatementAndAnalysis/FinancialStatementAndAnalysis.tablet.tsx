import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Button,
  Column,
  H6,
  Icon,
  LargeLabel,
  Row,
  Switch,
} from 'cube-ui-components';
import { FinancialStatementAndAnalysisFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import React, { useCallback, useState } from 'react';
import { Control, useFieldArray, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import RemoveFormConfirmationDialog from '../../RemoveFormConfirmationDialog';
import CertificateForm from './components/CertificateForm';
import SectionContainer from '../../SectionContainer';

interface FinancialStatementAndAnalysisProps {
  control: Control<FinancialStatementAndAnalysisFormSchemaType>;
}
const MAX_CERTIFICATE = 5;

const FinancialStatementAndAnalysis = (
  props: FinancialStatementAndAnalysisProps,
) => {
  const { control } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors, space } = useTheme();
  const [indexToRemove, setIndexToRemove] = useState<number>(0);

  const hasCertificate = useWatch({
    control,
    name: 'hasCertificate',
  });
  const [removeConfirmationVisible, setRemoveConfirmationVisible] =
    useState<boolean>(false);

  const showRemoveConfirmationDialog = useCallback(
    (index: number) => {
      setIndexToRemove(index);
      setRemoveConfirmationVisible(true);
    },
    [setRemoveConfirmationVisible, setIndexToRemove],
  );

  const { fields, append, remove } = useFieldArray({
    name: 'certificates',
    control,
  });

  const renderCertificates = useCallback(() => {
    return fields.map((_field, index) => (
      <View key={index}>
        <CertificateForm
          control={control}
          index={index}
          onDelete={
            index > 0 ? () => showRemoveConfirmationDialog(index) : undefined
          }
        />
        {index < fields.length - 1 && <Divider />}
      </View>
    ));
  }, [control, fields, showRemoveConfirmationDialog]);

  const addCertificate = useCallback(() => {
    if (fields.length < MAX_CERTIFICATE) {
      append({
        policyHolders: '',
        lifeAssured: '',
        productType: '',
        insurer: '',
        deathBenefit: '',
        disabilityBenefit: '',
        criticalIllnessBenefit: '',
        otherBenefit: '',
        frequency: '',
        contribution: '',
        maturityDate: null,
      });
    }
  }, [append, fields.length]);

  return (
    <>
      <SectionContainer title={t('customerFactFind:financial.certificate')}>
        <Content>
          <H6 fontWeight="bold">
            {t('customerFactFind:financial.certificate')}
          </H6>
          <Row alignItems="center" marginTop={space[3]}>
            <DescriptionLabel>
              {t('customerFactFind:financial.certificate.description')}
            </DescriptionLabel>

            <Input
              as={SwitchContainer}
              control={control}
              name="hasCertificate"
              label={
                hasCertificate
                  ? t('customerFactFind:financial.yes')
                  : t('customerFactFind:financial.no')
              }
              labelStyle={{
                color: colors.secondary,
              }}
              onChange={value => {
                if (value && fields.length === 0) {
                  append({
                    policyHolders: '',
                    lifeAssured: '',
                    productType: '',
                    insurer: '',
                    deathBenefit: '',
                    disabilityBenefit: '',
                    criticalIllnessBenefit: '',
                    otherBenefit: '',
                    frequency: '',
                    contribution: '',
                    maturityDate: null,
                  });
                }
              }}
            />
          </Row>
          {hasCertificate && renderCertificates()}
          {hasCertificate && fields.length < MAX_CERTIFICATE && (
            <AddButton
              text={t('customerFactFind:financial.certificate.addCertificate', {
                count: fields.length,
                maxCount: MAX_CERTIFICATE,
              })}
              icon={Icon.Plus}
              variant="secondary"
              compact={true}
              onPress={addCertificate}
            />
          )}
        </Content>
      </SectionContainer>

      <RemoveFormConfirmationDialog
        visible={removeConfirmationVisible}
        onDismiss={() => setRemoveConfirmationVisible(false)}
        onConfirm={async () => {
          setRemoveConfirmationVisible(false);
          remove(indexToRemove);
        }}
      />
    </>
  );
};

export default FinancialStatementAndAnalysis;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
  marginVertical: space[6],
}));

const AddButton = styled(Button)(() => ({
  alignSelf: 'baseline',
}));

const DescriptionLabel = styled(LargeLabel)(({ theme: { colors } }) => ({
  color: colors.secondary,
  alignSelf: 'center',
}));

const SwitchContainer = styled(Switch)(({ theme: { space } }) => ({
  marginLeft: space[3],
  padding: 0,
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.surface,
  };
});
