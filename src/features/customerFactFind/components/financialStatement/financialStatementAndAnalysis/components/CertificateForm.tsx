import React, { useEffect, useMemo, useState } from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Column,
  CurrencyTextField,
  H7,
  Icon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import Autocomplete from 'components/Autocomplete';
import DatePickerCalendar from 'components/DatePickerCalendar';
import { FinancialStatementAndAnalysisFormSchemaType } from 'features/customerFactFind/validations/financialStatementValidationSchema';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';
import { TFuncKey } from 'i18next';
import { MY_OPTION_LIST } from 'constants/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { InsuserName, PaymentMode4CFF, PlanType } from 'types/optionList';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { PartyRole } from 'types/party';

interface CertificateFormProps {
  index: number;
  onDelete?: () => void;
  control: Control<FinancialStatementAndAnalysisFormSchemaType>;
}

const CertificateForm = (props: CertificateFormProps) => {
  const { index, control, onDelete } = props;
  const { t } = useTranslation(['customerFactFind']);
  const { colors } = useTheme();

  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList();

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const ownerList = useMemo(() => {
    return (
      caseObj?.parties
        ?.filter(
          p =>
            p.roles.includes(PartyRole.PROPOSER) ||
            p.roles.includes(PartyRole.INSURED) ||
            p.roles.includes(PartyRole.DEPENDENT),
        )
        .map(p => ({
          name: p.person.name.firstName ?? '',
        })) ?? []
    );
  }, [caseObj?.parties]);

  const [disableFocus, setDisableFocus] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setDisableFocus(false);
    }, 100);
  }, []);

  return (
    <Wrapper>
      <Row alignItems="center">
        <TitleContainer>
          {t('customerFactFind:financial.certificate.withIndex', {
            index: index + 1,
          })}
        </TitleContainer>
        {onDelete && (
          <TouchableOpacity onPress={onDelete}>
            <Icon.Delete fill={colors.palette.black} />
          </TouchableOpacity>
        )}
      </Row>

      <Content>
        {/* Col 1 */}
        <ColumnContainer>
          <Input
            control={control}
            as={Autocomplete<{ name: string }, string>}
            name={`certificates.${index}.policyHolders`}
            label={t('customerFactFind:financial.certificate.policyHolders')}
            disabled={disableFocus}
            data={ownerList}
            getItemValue={item => item.name}
            getItemLabel={item => item.name}
          />
          <Input
            control={control}
            as={Autocomplete<InsuserName, string>}
            name={`certificates.${index}.insurer`}
            label={t('customerFactFind:financial.certificate.insurer')}
            data={optionList?.INSUSER_NAME.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`certificates.${index}.criticalIllnessBenefit`}
            label={t(
              'customerFactFind:financial.certificate.criticalIllnessBenefit',
            )}
          />
          <Input
            control={control}
            as={Autocomplete<PaymentMode4CFF, string>}
            name={`certificates.${index}.frequency`}
            label={t('customerFactFind:financial.certificate.frequency')}
            data={optionList?.PAYMENTMODE4_CFF.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
          />
        </ColumnContainer>
        {/* Col 2 */}
        <ColumnContainer>
          <Input
            control={control}
            as={TextField}
            name={`certificates.${index}.lifeAssured`}
            label={t('customerFactFind:financial.certificate.lifeAssured')}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`certificates.${index}.deathBenefit`}
            label={t('customerFactFind:financial.certificate.deathBenefit')}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`certificates.${index}.otherBenefit`}
            label={t('customerFactFind:financial.certificate.otherBenefit')}
          />
          <Input
            control={control}
            as={DatePickerCalendar}
            name={`certificates.${index}.maturityDate`}
            label={t('customerFactFind:financial.certificate.maturityDate')}
            hint={t('customerFactFind:personalDetails.form.dateOfBirth.hint')}
            formatDate={val => (val ? dateFormatUtil(val) : '')}
          />
        </ColumnContainer>
        {/* Col 3 */}
        <ColumnContainer>
          <Input
            control={control}
            as={Autocomplete<PlanType, string>}
            name={`certificates.${index}.productType`}
            label={t('customerFactFind:financial.certificate.productType')}
            data={optionList?.PLAN_TYPE.options ?? []}
            disabled={isLoadingOptionList}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`certificates.${index}.disabilityBenefit`}
            label={t(
              'customerFactFind:financial.certificate.disabilityBenefit',
            )}
          />
          <Input
            control={control}
            as={CurrencyTextField}
            name={`certificates.${index}.contribution`}
            label={t('customerFactFind:financial.certificate.contribution')}
          />
        </ColumnContainer>
      </Content>
    </Wrapper>
  );
};

export default CertificateForm;

const Wrapper = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    marginTop: space[6],
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    paddingBottom: space[6],
  }),
);

const Content = styled(Row)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  gap: space[5],
  marginTop: space[6],
}));

const ColumnContainer = styled(Column)(({ theme: { space } }) => ({
  gap: space[5],
  flex: 1,
}));

const TitleContainer = styled(H7)(() => ({
  fontWeight: 'bold',
  flex: 1,
}));

const DateFormatContainer = styled(Box)(({ theme: { space } }) => ({
  marginLeft: space[4],
}));
