import * as React from 'react';
import Svg, { SvgProps, G, Path, Defs, ClipPath } from 'react-native-svg';
const FWDIcon = (props: SvgProps) => (
  <Svg width={57} height={20} fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#E87722"
        d="M45.705 9.582a.74.74 0 0 1 .196.504c0 .189-.07.37-.196.505l-3.34 3.488a.681.681 0 0 1-.483.21.621.621 0 0 1-.262-.055.714.714 0 0 1-.419-.658V6.597c0-.29.166-.548.42-.66a.667.667 0 0 1 .746.155l3.338 3.49Z"
      />
      <Path
        fill="#183028"
        d="M24.49.505a2.88 2.88 0 0 0-.854 1.204c-.174.464-.366 1.138-.591 2.028l-.177.697-2.17 9-2.53-11.512c-.215-1.1-.8-1.78-2.248-1.78H.46C-.25.142-.276.933-.278.94v18.657s.012.396.375.396h1.436c1.032 0 2.35-.701 2.35-2.634V11.673h5.415c2.064 0 2.567-1.553 2.567-2.437v-.328c0-.313-.1-.504-.46-.504H3.883V3.698h10.635l3.209 13.023c.097.39.66 2.45.66 2.45.216.724.192.828.776.828h1.3c1.598 0 2.149-1.037 2.432-1.768.141-.397.242-.572.47-1.467l2.83-11.226 2.992 11.17c.097.39.62 2.459.62 2.459.216.721.194.83.778.83h1.299c1.598 0 2.15-1.038 2.434-1.774.138-.396.278-.776.506-1.669L37.765 3.71h9.109c.783 0 1.414.072 1.982.253.734.233 1.388.812 1.932 1.721.528.89.83 2.45.844 4.386 0 2.183-.621 4.121-1.772 5.168a2.737 2.737 0 0 1-.913.572 4.059 4.059 0 0 1-1.049.265c-.36.04-.833.116-1.46.121h-.008l-1.987.004H41.59l-.013-.004c-.619 0-.637.378-.637.54v.782c0 1.137.89 2.443 2.678 2.443h.061l3.468-.002a14.38 14.38 0 0 0 2.383-.205 7.484 7.484 0 0 0 2.01-.66 6.938 6.938 0 0 0 1.73-1.226 8.568 8.568 0 0 0 1.628-2.229c.418-.83.726-1.754.92-2.772.182-.96.274-1.645.274-2.785v-.025c-.03-3.69-1.066-6.315-3.128-8.147a6.31 6.31 0 0 0-2.661-1.47c-.91-.24-1.987-.36-3.215-.36H36.05c-.605.007-1.512.336-1.862 1.838l-2.394 11.44-2.545-8.968v.007l-.17-.66c-.246-.957-.444-1.642-.591-2.057-.149-.415-.41-.796-.793-1.151C27.319.175 26.784 0 26.098 0c-.68 0-1.214.168-1.608.505Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h56.369v20H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default FWDIcon;
