import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Circle } from 'react-native-svg';

interface Props extends SvgIconProps {
  size?: number;
}

const SelectIcon = (props: Props) => (
  <Svg
    width={props.size || 20}
    height={props.size || 20}
    viewBox="0 0 24 24"
    fill="none"
    {...props}>
    <Circle
      cx={12}
      cy={12}
      r={11}
      fill="#F8F9F9"
      stroke={props.fill || '#E87722'}
      strokeWidth={2}
    />
  </Svg>
);
export default SelectIcon;
