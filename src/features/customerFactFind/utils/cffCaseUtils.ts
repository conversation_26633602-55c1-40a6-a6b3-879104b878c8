import { Party, PartyRole, PartyType } from 'types/party';
import { CertificateOwnerDetailFormSchemaType } from '../validations/certificateOwnerDetailsSchema';
import { Gender, MaritalStatus, Religion, SmokingHabit } from 'types/person';
import { format, parse } from 'date-fns';
import { SpouseDetailFormSchemaType } from '../validations/spouseDetailsSchema';
import { ChildDependentDetailFormSchemaType } from '../validations/childDependentDetailSchema';
import { calculateAge } from 'utils/helper/calculateAge';
import {
  CustomerDisclosureOption,
  CustomerFactFindState,
  InvestmentPreferenceReasonOption,
} from './store/customerFactFindStore';
import { CFF } from 'types/case';
import {
  NEW_ADDRESS_OPTION,
  MY_COUNTRY,
  OWNER_ADDRESS_OPTION,
  CORRESPONDENCE_ADDRESS_OPTION,
  MY_MOBILE_CODE,
  getOriginalCountryCode,
} from 'constants/optionList';
import { PrioritizationKey } from '../constants/prioritizations';
import { Occupation, OptionList } from 'types/optionList';

export const parseParty = (party: Party, optionList: OptionList) => {
  const defaultId = party.person.registrations?.find(r => r.type === 'DEFAULT');
  const additionalId = party.person.registrations?.find(
    r => r.type === 'ADDITIONAL',
  );
  const mobileContact = party.contacts.phones.find(i => i.type === 'MOBILE');
  const homeContact = party.contacts.phones.find(i => i.type === 'HOME');
  const officeContact = party.contacts.phones.find(i => i.type === 'WORK');
  const correspondenceAddress = party.addresses?.find(
    a => a.addressType === 'MAIN',
  );
  const residentialAddress = party.addresses?.find(
    a => a.addressType === 'HOME',
  );
  const businessAddress = party.addresses?.find(a => a.addressType === 'WORK');

  return {
    id: party.id || '',
    isMainInsured: party.isMainInsured,
    relationship: party.relationship || '',
    title: party.person.name.title || '',
    smokingHabit: party.person.isSmoker
      ? SmokingHabit.SMOKER
      : SmokingHabit.NONSMOKER,
    maritalStatus: party.person.maritalStatus || '',
    dob: party.person?.dateOfBirth?.date
      ? parse(party.person.dateOfBirth.date, 'yyyy-MM-dd', new Date())
      : null,
    primaryIdType: defaultId?.idType || '',
    identificationNumber: defaultId?.id || '',
    additionalIDType: additionalId?.idType || '',
    additionalIdentification: additionalId?.id || '',
    source: party.sourceLeadId || '',
    fullName: party.person.name.firstName || '',
    gender: party.person.gender,
    ethnicity: party.person.ethnicityCode || '',
    religion: party.person.religion || '',
    nationality: party.person.nationality || '',
    countryOfBirth: party.person.countryOfBirth || '',
    stateOfBirth: party.person.stateOfBirth || '',
    cityOfBirth: party.person.cityOfBirth || '',
    cityName: party.person.cityNameOfBirth || '',
    occupation: party.person.occupation?.natureOfSubWork || '',
    occupationDescription: party.person.occupation?.occupationDescription || '',
    nameOfBusiness: party.person.occupation?.nameOfEmployer || '',
    exactDuties: party.person.occupation?.duties || '',
    natureOfWork: party.person.occupation?.natureOfBusiness || '',
    annualIncome: party.person.occupation?.incomeRange || '',
    annualIncomeAmount:
      typeof party.person.occupation?.income === 'number'
        ? String(party.person.occupation.income)
        : '',
    email: party.contacts.email || '',
    mobileCountryCode:
      getOriginalCountryCode(mobileContact?.countryCode, optionList) ||
      MY_MOBILE_CODE,
    mobileNumber: mobileContact?.number || '',
    homeCountryCode:
      getOriginalCountryCode(homeContact?.countryCode, optionList) ||
      MY_MOBILE_CODE,
    homeNumber: homeContact?.number || '',
    officeCountryCode:
      getOriginalCountryCode(officeContact?.countryCode, optionList) ||
      MY_MOBILE_CODE,
    officeNumber: officeContact?.number || '',
    correspondenceAddress:
      // keep this to refactor address option logic
      // old value: 'certificate'
      // new value: 'owner'
      (correspondenceAddress?.businessAddressOpt === 'certificate'
        ? OWNER_ADDRESS_OPTION
        : (correspondenceAddress?.businessAddressOpt as CertificateOwnerDetailFormSchemaType['correspondenceAddress'])) ||
      (party.roles.includes(PartyRole.PROPOSER)
        ? NEW_ADDRESS_OPTION
        : OWNER_ADDRESS_OPTION),
    correspondenceAddressLine1: correspondenceAddress?.street || '',
    correspondenceAddressLine2: correspondenceAddress?.subDistrict || '',
    correspondenceAddressLine3: correspondenceAddress?.district || '',
    correspondencePostCode: correspondenceAddress?.zipCode || '',
    correspondenceCity: correspondenceAddress?.city || '',
    correspondenceState: correspondenceAddress?.province || '',
    correspondenceCountry: correspondenceAddress?.countryCode || MY_COUNTRY,
    residentialAddress:
      (residentialAddress?.businessAddressOpt as CertificateOwnerDetailFormSchemaType['residentialAddress']) ||
      CORRESPONDENCE_ADDRESS_OPTION,
    residentialAddressLine1: residentialAddress?.street || '',
    residentialAddressLine2: residentialAddress?.subDistrict || '',
    residentialAddressLine3: residentialAddress?.district || '',
    residentialPostCode: residentialAddress?.zipCode || '',
    residentialCity: residentialAddress?.city || '',
    residentialState: residentialAddress?.province || '',
    residentialCountry: residentialAddress?.countryCode || MY_COUNTRY,
    businessAddress:
      (businessAddress?.businessAddressOpt as CertificateOwnerDetailFormSchemaType['businessAddress']) ||
      '',
    businessAddressLine1: businessAddress?.street || '',
    businessAddressLine2: businessAddress?.subDistrict || '',
    businessAddressLine3: businessAddress?.district || '',
    businessPostCode: businessAddress?.zipCode || '',
    businessCity: businessAddress?.city || '',
    businessState: businessAddress?.province || '',
    businessCountry: businessAddress?.countryCode || MY_COUNTRY,
    yearToSupport: String(party.dependent?.yearToSupport || ''),
  } as
    | CertificateOwnerDetailFormSchemaType
    | SpouseDetailFormSchemaType
    | ChildDependentDetailFormSchemaType;
};

export const toParty = (
  info:
    | CertificateOwnerDetailFormSchemaType
    | SpouseDetailFormSchemaType
    | ChildDependentDetailFormSchemaType,
  role: PartyRole,
  optionList: OptionList,
): Party => {
  return {
    id: info.id || '',
    roles: [role],
    clientType: PartyType.INDIVIDUAL, // TODO: need to put into form
    relationship: info.relationship || '',
    person: {
      name: {
        title: 'title' in info ? info.title : '',
        firstName: info.fullName || '',
        middleName: '',
        lastName: '',
        extensionName: '',
      },
      registrations: [
        {
          type: 'DEFAULT',
          idType: info.primaryIdType || '',
          id: info.identificationNumber || '',
        },
        {
          type: 'ADDITIONAL',
          idType: info.additionalIDType || '',
          id: info.additionalIdentification || '',
        },
      ],
      gender: (info.gender || Gender.MALE) as Gender,
      dateOfBirth: {
        date: info.dob ? format(info.dob as Date, 'yyyy-MM-dd') : '',
      },
      age: info.dob ? calculateAge(info.dob) : 0,
      maritalStatus:
        'maritalStatus' in info
          ? (info.maritalStatus as MaritalStatus)
          : undefined,
      nationality: info.nationality,
      ethnicityCode: 'ethnicity' in info ? info.ethnicity : undefined,
      religion: 'religion' in info ? (info.religion as Religion) : undefined,
      countryOfBirth: info.countryOfBirth,
      stateOfBirth: 'stateOfBirth' in info ? info.stateOfBirth : '',
      cityOfBirth: 'cityOfBirth' in info ? info.cityOfBirth : '',
      cityNameOfBirth: 'cityName' in info ? info.cityName : '',
      occupation: {
        nameOfEmployer: 'nameOfBusiness' in info ? info.nameOfBusiness : '',
        natureOfBusiness: 'natureOfWork' in info ? info.natureOfWork : '',
        natureOfWork:
          (optionList.OCCUPATION.options as Occupation<string, 'my'>[]).find(
            o => o.value === info.occupation,
          )?.occupationClass.value || '',
        natureOfSubWork: info.occupation,
        occupationDescription:
          'occupationDescription' in info ? info.occupationDescription : '',
        duties: 'exactDuties' in info ? info.exactDuties : '',
        incomeRange: 'annualIncome' in info ? info.annualIncome : '',
        income:
          info.annualIncomeAmount !== undefined &&
          info.annualIncomeAmount !== null &&
          info.annualIncomeAmount !== ''
            ? Number(info.annualIncomeAmount)
            : undefined,
      },
      isSmoker:
        'smokingHabit' in info
          ? info.smokingHabit === SmokingHabit.SMOKER
          : undefined,
    },
    sourceLeadId: 'source' in info ? info.source || '' : '',
    contacts: {
      email: 'email' in info ? info.email : '',
      phones: [
        'mobileCountryCode' in info
          ? {
              type: 'MOBILE',
              countryCode: info.mobileCountryCode || '',
              number: info.mobileNumber || '',
            }
          : null,
        'homeCountryCode' in info
          ? {
              type: 'HOME',
              countryCode: info.homeCountryCode || '',
              number: info.homeNumber || '',
            }
          : null,
        'officeCountryCode' in info
          ? {
              type: 'WORK',
              countryCode: info.officeCountryCode || '',
              number: info.officeNumber || '',
            }
          : null,
      ].filter(Boolean) as Party['contacts']['phones'],
    },
    addresses: [
      {
        addressType: 'MAIN',
        street:
          'correspondenceAddressLine1' in info
            ? info.correspondenceAddressLine1
            : '',
        subDistrict:
          'correspondenceAddressLine2' in info
            ? info.correspondenceAddressLine2
            : '',
        district:
          'correspondenceAddressLine3' in info
            ? info.correspondenceAddressLine3
            : '',
        zipCode:
          'correspondencePostCode' in info ? info.correspondencePostCode : '',
        city: 'correspondenceCity' in info ? info.correspondenceCity : '',
        province: 'correspondenceState' in info ? info.correspondenceState : '',
        countryCode:
          'correspondenceCountry' in info ? info.correspondenceCountry : '',
        businessAddressOpt:
          'correspondenceAddress' in info ? info.correspondenceAddress : '',
      },
      'residentialAddress' in info
        ? info.residentialAddress === CORRESPONDENCE_ADDRESS_OPTION
          ? {
              addressType: 'HOME',
              street:
                'correspondenceAddressLine1' in info
                  ? info.correspondenceAddressLine1
                  : '',
              subDistrict:
                'correspondenceAddressLine2' in info
                  ? info.correspondenceAddressLine2
                  : '',
              district:
                'correspondenceAddressLine3' in info
                  ? info.correspondenceAddressLine3
                  : '',
              zipCode:
                'correspondencePostCode' in info
                  ? info.correspondencePostCode
                  : '',
              city: 'correspondenceCity' in info ? info.correspondenceCity : '',
              province:
                'correspondenceState' in info ? info.correspondenceState : '',
              countryCode:
                'correspondenceCountry' in info
                  ? info.correspondenceCountry
                  : '',
              businessAddressOpt: CORRESPONDENCE_ADDRESS_OPTION,
            }
          : {
              addressType: 'HOME',
              street:
                'residentialAddressLine1' in info
                  ? info.residentialAddressLine1
                  : '',
              subDistrict:
                'residentialAddressLine2' in info
                  ? info.residentialAddressLine2
                  : '',
              district:
                'residentialAddressLine3' in info
                  ? info.residentialAddressLine3
                  : '',
              zipCode:
                'residentialPostCode' in info ? info.residentialPostCode : '',
              city: 'residentialCity' in info ? info.residentialCity : '',
              province: 'residentialState' in info ? info.residentialState : '',
              countryCode:
                'residentialCountry' in info ? info.residentialCountry : '',
              businessAddressOpt: NEW_ADDRESS_OPTION,
            }
        : null,
      'businessAddress' in info
        ? {
            addressType: 'WORK',
            street:
              'businessAddressLine1' in info ? info.businessAddressLine1 : '',
            subDistrict:
              'businessAddressLine2' in info ? info.businessAddressLine2 : '',
            district:
              'businessAddressLine3' in info ? info.businessAddressLine3 : '',
            zipCode: 'businessPostCode' in info ? info.businessPostCode : '',
            city: 'businessCity' in info ? info.businessCity : '',
            province: 'businessState' in info ? info.businessState : '',
            countryCode: 'businessCountry' in info ? info.businessCountry : '',
            businessAddressOpt: info.businessAddress,
          }
        : null,
    ].filter(Boolean) as Party['addresses'],
    ...('yearToSupport' in info && {
      dependent: {
        yearToSupport: Number(info.yearToSupport),
      },
    }),
  };
};

const financialStatementPreferenceList = [
  'Conservative',
  'Moderate Conservative',
  'Balance',
  'Moderate Aggressive',
  'Aggressive',
];

export const toCFF = (
  data: Pick<
    CustomerFactFindState,
    'customerPreference' | 'recordOfAdvices' | 'financialStatement'
  >,
  agentId: string,
  proposalNum: string,
  acknowledge: boolean,
): CFF => {
  return {
    agentId,
    stageStatus: {},
    stage: '',
    vulnerable: {},
    proposalNum,
    acknowledge: acknowledge,
    pdfToken: '',
    clientPreference: {
      agentId,
      disclosure:
        data.customerPreference.customerChoice || CustomerDisclosureOption.FULL,
      purposeOfInsuranceList: data.customerPreference.prioritization.map(
        (area, idx) => ({
          agentId,
          type: area.key,
          planned: area.alreadyPlanned,
          review: area.toDiscuss,
          priority: idx + 1,
        }),
      ),
    },
    financialStatement: {
      agentId,
      preference:
        financialStatementPreferenceList[
          data.financialStatement.investmentPreference.risk
        ],
      haveExistingCertificate: Boolean(
        data.financialStatement.statementAndAnalysis.hasCertificate,
      ),
      existingCertificateList: data.financialStatement.statementAndAnalysis
        .hasCertificate
        ? data.financialStatement.statementAndAnalysis.certificates?.map(
            cert => ({
              agentId,
              policyHolderName: cert.policyHolders,
              lifeAssuredName: cert.lifeAssured,
              planType: cert.productType,
              insurerName: cert.insurer,
              benefits: [
                cert.deathBenefit,
                cert.disabilityBenefit,
                cert.criticalIllnessBenefit,
                cert.otherBenefit,
              ].map(i => convertNonEmptyNumericValue(i)),
              contributions: convertNonEmptyNumericValue(cert.contribution),
              paymentFrequency: cert.frequency,
              maturityDate: cert.maturityDate?.toISOString(),
            }),
          ) || []
        : [],
      incomeProtection: {
        field2: convertNonEmptyNumericValue(
          data.financialStatement.incomeProtection.annualIncome,
        ),
        field3: convertNonEmptyNumericValue(
          data.financialStatement.incomeProtection.replacedYears,
        ),
        field4: convertNonEmptyNumericValue(
          data.financialStatement.incomeProtection.existingLifeInsurance,
        ),
        field5: convertNonEmptyNumericValue(
          data.financialStatement.incomeProtection.additionalAmount,
        ),
      },
      retirementIncome: {
        field1: data.financialStatement.retirementIncome.hasOtherIncome
          ? data.financialStatement.retirementIncome.otherIncome
          : '',
        field2: convertNonEmptyNumericValue(
          data.financialStatement.retirementIncome.retirementYears,
        ),
        field3: convertNonEmptyNumericValue(
          data.financialStatement.retirementIncome.annualIncome,
        ),
        field4: convertNonEmptyNumericValue(
          data.financialStatement.retirementIncome.replacedYears,
        ),
        field5: convertNonEmptyNumericValue(
          data.financialStatement.retirementIncome.existingRetirementIncome,
        ),
        field6: convertNonEmptyNumericValue(
          data.financialStatement.retirementIncome.additionalAmount,
        ),
      },
      childEducationList:
        data.financialStatement.childEducation.informations?.map(info => ({
          field1: info.name || '',
          field2: convertNonEmptyNumericValue(info.age),
          field3: convertNonEmptyNumericValue(info.yearsToTertiaryEducation),
          field4: convertNonEmptyNumericValue(info.existingFund),
          field5: convertNonEmptyNumericValue(info.additionalAmount),
        })) || [],
      savings: {
        field2: convertNonEmptyNumericValue(
          data.financialStatement.savings.expected,
        ),
        field3: convertNonEmptyNumericValue(
          data.financialStatement.savings.meetYears,
        ),
        field4: convertNonEmptyNumericValue(
          data.financialStatement.savings.additionalAmount,
        ),
      },
      investments: {
        field1: data.financialStatement.investments.duration,
        field2: convertNonEmptyNumericValue(
          data.financialStatement.investments.initialAmount,
        ),
        field3: convertNonEmptyNumericValue(
          data.financialStatement.investments.regularAmount,
        ),
        field4: convertNonEmptyNumericValue(
          data.financialStatement.investments.expectedMonthly,
        ),
        field5: convertNonEmptyNumericValue(
          data.financialStatement.investments.payoutPeriod,
        ),
      },
      medicalPlanningList: [
        {
          field2: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .neededAmountForMedicalPlanning,
          ),
          field3: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .existingProtectionForMedicalPlanning,
          ),
          field4: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning.expiryAgeForMedicalPlanning,
          ),
          field5: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .additionalAmountForMedicalPlanning,
          ),
          field6: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .existingProtectionForCurrentEmployerForMedicalPlanning,
          ),
        },
        {
          field2: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .neededAmountForCriticalIllness,
          ),
          field3: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .existingProtectionForCriticalIllness,
          ),
          field4: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning.expiryAgeForCriticalIllness,
          ),
          field5: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .additionalAmountForCriticalIllness,
          ),
          field6: convertNonEmptyNumericValue(
            data.financialStatement.medicalPlanning
              .existingProtectionForCurrentEmployerForCriticalIllness,
          ),
        },
      ],
      financialPlanningList: [
        {
          field2: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning
              .proposedParticipantMonthly,
          ),
          field3: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning.proposedParticipantYearly,
          ),
          field4: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning
              .proposedParticipantLumpSum,
          ),
        },
        {
          field2: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning.spouseMonthly,
          ),
          field3: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning.spouseYearly,
          ),
          field4: convertNonEmptyNumericValue(
            data.financialStatement.financialPlanning.spouseLumpSum,
          ),
        },
      ],
    },
    adviceRecord: {
      agentId,
      recommendationList: data.recordOfAdvices.advices.map(record => ({
        agentId,
        nameofPerson: record.nameOfPersonCoverage,
        planName: record.productType || '',
        paymentFrequency: record.paymentFrequency,
        sumAssured: convertNonEmptyNumericValue(record.sumCovered),
        contribution: convertNonEmptyNumericValue(record.contribution),
        term: convertNonEmptyNumericValue(record.term),
        participation: record.isParticipate,
        additionalBenefits: record.additionalBenefits || '',
        reasonForRecommendationList: [
          {
            agentId,
            reason: record.reason1 || '',
            otherSpecify: record.reason1 === 'O' ? record.otherReason : '',
          },
          record.reason2 && {
            agentId,
            reason: record.reason2 || '',
            otherSpecify: record.reason2 === 'O' ? record.otherReason : '',
          },
          record.reason3 && {
            agentId,
            reason: record.reason3 || '',
            otherSpecify: record.reason3 === 'O' ? record.otherReason : '',
          },
        ].filter(
          Boolean,
        ) as CFF['adviceRecord']['recommendationList'][0]['reasonForRecommendationList'],
      })),
      disclosure: data.recordOfAdvices.disclosure
        ? data.recordOfAdvices.disclosure === 'yes'
        : null,
      preference: data.recordOfAdvices.preference,
      comments: data.recordOfAdvices.disclosureReason,
    },
  };
};

export const parseCFF = (
  data: CFF,
): Pick<
  CustomerFactFindState,
  'customerPreference' | 'recordOfAdvices' | 'financialStatement'
> => {
  return {
    customerPreference: {
      prioritization:
        data.clientPreference?.purposeOfInsuranceList?.map(area => ({
          key: area.type as PrioritizationKey,
          alreadyPlanned: area.planned,
          toDiscuss: area.review,
        })) || [],
      customerChoice: data.clientPreference?.disclosure as
        | CustomerDisclosureOption
        | undefined,
    },
    recordOfAdvices: {
      disclosure:
        typeof data.adviceRecord?.disclosure === 'boolean'
          ? data.adviceRecord.disclosure
            ? 'yes'
            : 'no'
          : '',
      disclosureReason: data.adviceRecord?.comments || '',
      preference: data.adviceRecord?.preference,
      advices:
        data.adviceRecord?.recommendationList?.map((record, index) => ({
          index: index,
          nameOfPersonCoverage: record.nameofPerson,
          productType: record.planName,
          paymentFrequency: record.paymentFrequency,
          contribution: record.contribution?.toString() || '',
          sumCovered: record.sumAssured?.toString() || '',
          term: record.term?.toString() || '',
          additionalBenefits: record.additionalBenefits,
          isParticipate: record.participation,
          reason1: record.reasonForRecommendationList?.[0]?.reason,
          reason2: record.reasonForRecommendationList?.[1]?.reason,
          reason3: record.reasonForRecommendationList?.[2]?.reason,
          otherReason: record.reasonForRecommendationList?.[0]?.otherSpecify,
        })) || [],
    },
    financialStatement: {
      investmentPreference: {
        risk: financialStatementPreferenceList.findIndex(
          e => e === data.financialStatement?.preference,
        ),
      },
      statementAndAnalysis: {
        hasCertificate: data.financialStatement?.haveExistingCertificate,
        certificates:
          data.financialStatement?.existingCertificateList?.map(cert => ({
            policyHolders: cert.policyHolderName,
            lifeAssured: cert.lifeAssuredName,
            productType: cert.planType,
            insurer: cert.insurerName,
            deathBenefit: cert.benefits?.[0]?.toString() || '',
            disabilityBenefit: cert.benefits?.[1]?.toString() || '',
            criticalIllnessBenefit: cert.benefits?.[2]?.toString() || '',
            otherBenefit: cert.benefits?.[3]?.toString() || '',
            frequency: cert.paymentFrequency,
            contribution: cert.contributions?.toString() || '',
            maturityDate: cert.maturityDate
              ? new Date(cert.maturityDate)
              : null,
          })) || [],
      },
      incomeProtection: {
        annualIncome:
          data.financialStatement?.incomeProtection?.field2?.toString() || '',
        replacedYears:
          data.financialStatement?.incomeProtection?.field3?.toString() || '',
        existingLifeInsurance:
          data.financialStatement?.incomeProtection?.field4?.toString() || '',
        additionalAmount:
          data.financialStatement?.incomeProtection?.field5?.toString() || '',
      },
      retirementIncome: {
        hasOtherIncome: data.financialStatement?.retirementIncome?.field1
          ? true
          : false,
        otherIncome:
          data.financialStatement?.retirementIncome?.field1?.toString(),
        retirementYears:
          data.financialStatement?.retirementIncome?.field2?.toString() ?? '',
        annualIncome:
          data.financialStatement?.retirementIncome?.field3?.toString() ?? '',
        replacedYears:
          data.financialStatement?.retirementIncome?.field4?.toString() ?? '',
        existingRetirementIncome:
          data.financialStatement?.retirementIncome?.field5?.toString() ?? '',
        additionalAmount:
          data.financialStatement?.retirementIncome?.field6?.toString(),
      },
      childEducation: {
        informations:
          data.financialStatement?.childEducationList?.map(info => ({
            name: info.field1 || '',
            age: info.field2?.toString() || '',
            yearsToTertiaryEducation: info.field3?.toString() || '',
            existingFund: info.field4?.toString() || '',
            additionalAmount: info.field5?.toString() || '',
          })) || [],
      },
      savings: {
        expected: data.financialStatement?.savings?.field2?.toString() ?? '',
        meetYears: data.financialStatement?.savings?.field3?.toString() ?? '',
        additionalAmount:
          data.financialStatement?.savings?.field4?.toString() ?? '',
      },
      investments: {
        duration:
          data?.financialStatement?.investments?.field1?.toString() ?? '',
        initialAmount:
          data.financialStatement?.investments?.field2?.toString() ?? '',
        regularAmount:
          data.financialStatement?.investments?.field3?.toString() ?? '',
        expectedMonthly:
          data.financialStatement?.investments?.field4?.toString() ?? '',
        payoutPeriod:
          data.financialStatement?.investments?.field5?.toString() ?? '',
      },
      medicalPlanning: {
        neededAmountForMedicalPlanning:
          data.financialStatement?.medicalPlanningList?.[0]?.field2?.toString() ??
          '',
        existingProtectionForMedicalPlanning:
          data.financialStatement?.medicalPlanningList?.[0]?.field3?.toString() ??
          '',
        expiryAgeForMedicalPlanning:
          data.financialStatement?.medicalPlanningList?.[0]?.field4?.toString() ??
          '',
        existingProtectionForCurrentEmployerForMedicalPlanning:
          data.financialStatement?.medicalPlanningList?.[0]?.field6?.toString() ??
          '',
        additionalAmountForMedicalPlanning:
          data.financialStatement?.medicalPlanningList?.[0]?.field5?.toString() ??
          '',
        neededAmountForCriticalIllness:
          data.financialStatement?.medicalPlanningList?.[1]?.field2?.toString() ??
          '',
        existingProtectionForCriticalIllness:
          data.financialStatement?.medicalPlanningList?.[1]?.field3?.toString() ??
          '',
        expiryAgeForCriticalIllness:
          data.financialStatement?.medicalPlanningList?.[1]?.field4?.toString() ??
          '',
        existingProtectionForCurrentEmployerForCriticalIllness:
          data.financialStatement?.medicalPlanningList?.[1]?.field6?.toString() ??
          '',
        additionalAmountForCriticalIllness:
          data.financialStatement?.medicalPlanningList?.[1]?.field5?.toString() ??
          '',
      },
      financialPlanning: {
        proposedParticipantMonthly:
          data.financialStatement?.financialPlanningList?.[0]?.field2?.toString() ??
          '',
        proposedParticipantYearly:
          data.financialStatement?.financialPlanningList?.[0]?.field3?.toString() ??
          '',
        proposedParticipantLumpSum:
          data.financialStatement?.financialPlanningList?.[0]?.field4?.toString() ??
          '',
        spouseMonthly:
          data.financialStatement?.financialPlanningList?.[1]?.field2?.toString() ??
          '',
        spouseYearly:
          data.financialStatement?.financialPlanningList?.[1]?.field3?.toString() ??
          '',
        spouseLumpSum:
          data.financialStatement?.financialPlanningList?.[1]?.field4?.toString() ??
          '',
      },
      investmentPreferenceReason: data?.financialStatement?.investmentPreference
        ? {
            option: data.financialStatement
              .investmentPreference as InvestmentPreferenceReasonOption,
            differentiateOthers:
              data.financialStatement.differentiateOthers || '',
            comments: data.financialStatement.comments || '',
          }
        : undefined,
    },
  };
};

const convertNonEmptyNumericValue = (value: string | undefined) => {
  return value === '' || value === undefined ? null : Number(value);
};
