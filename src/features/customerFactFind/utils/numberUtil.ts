import { country } from 'utils/context';

export const isNumber = (value: string | undefined): boolean => {
  return (
    value !== undefined &&
    String(value).trim() !== '' &&
    !Number.isNaN(Number(value))
  );
};

export const areNumbers = (...values: (string | undefined)[]): boolean => {
  return values.every(isNumber);
};

const TO_FIXED_BY_COUNTRY =
  {
    //define toFixed by country
    // e.g. : 'my': 1,
  }[country as string] || 1;

export const round = (number: number, toFixed = TO_FIXED_BY_COUNTRY) => {
  const multiplier = 10 ** (toFixed - 1);
  return Math.round(number * multiplier) / multiplier;
};
