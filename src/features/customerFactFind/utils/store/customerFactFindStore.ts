import { PrioritizationKey } from 'features/customerFactFind/constants/prioritizations';
import {
  ChildDependentDetailFormSchemaType,
  initialChildDependentDetailFormData,
} from 'features/customerFactFind/validations/childDependentDetailSchema';
import {
  FinancialChildEducationFormSchemaType,
  FinancialIncomeProtectionFormSchemaType,
  FinancialInvestmentPreferenceFormSchemaType,
  FinancialInvestmentsFormSchemaType,
  FinancialMedicalPlanningFormSchemaType,
  FinancialPlanningFormSchemaType,
  FinancialRetirementFormSchemaType,
  FinancialSavingsFormSchemaType,
  FinancialStatementAndAnalysisFormSchemaType,
  initialFinancialChildEducationFormData,
  initialFinancialIncomeProtectionFormData,
  initialFinancialInvestmentPreferenceFormData,
  initialFinancialInvestmentsFormData,
  initialFinancialMedicalPlanningFormData,
  initialFinancialPlanningFormData,
  initialFinancialRetirementFormData,
  initialFinancialSavingsFormData,
  initialFinancialStatementAndAnalysisFormData,
} from 'features/customerFactFind/validations/financialStatementValidationSchema';
import {
  CertificateOwnerDetailFormSchemaType,
  initialCertificateOwnerDetailFormData,
} from 'features/customerFactFind/validations/certificateOwnerDetailsSchema';
import {
  SpouseDetailFormSchemaType,
  initialSpouseDetailFormData,
} from 'features/customerFactFind/validations/spouseDetailsSchema';
import { initialRecordAdvicesFormData } from 'features/customerFactFind/validations/recordOfAdviceValidation';
import { cloneDeep } from 'lodash';
import { StateCreator, create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export type RecordOfAdviceItem = {
  index: number;
  nameOfPersonCoverage: string;
  productType: string | undefined;
  paymentFrequency: string;
  contribution: string | undefined;
  sumCovered: string | undefined;
  term: string | undefined;
  additionalBenefits: string | undefined;
  isParticipate: boolean;
  reason1: string;
  reason2: string;
  reason3: string;
  otherReason?: string;
  createdAt?: number;
  specificReason?: string;
  isCompleted?: boolean;
};

export type PrioritizationItem = {
  key: PrioritizationKey;
  alreadyPlanned: boolean;
  toDiscuss: boolean;
};

export enum CustomerDisclosureOption {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  NONE = 'NONE',
}

export type CustomerPreference = {
  prioritization: PrioritizationItem[];
  customerChoice?: CustomerDisclosureOption;
};

export type AdviceData = {
  disclosure?: string;
  disclosureReason?: string;
  preference?: string;
  advices: RecordOfAdviceItem[];
};

export interface CustomerFactFindState {
  activeStep: number;
  processingStep: number;
  customerPreference: CustomerPreference;
  recordOfAdvices: AdviceData;
  financialStatement: {
    investmentPreference: FinancialInvestmentPreferenceFormSchemaType;
    statementAndAnalysis: FinancialStatementAndAnalysisFormSchemaType;
    incomeProtection: FinancialIncomeProtectionFormSchemaType;
    retirementIncome: FinancialRetirementFormSchemaType;
    childEducation: FinancialChildEducationFormSchemaType;
    savings: FinancialSavingsFormSchemaType;
    investments: FinancialInvestmentsFormSchemaType;
    medicalPlanning: FinancialMedicalPlanningFormSchemaType;
    financialPlanning: FinancialPlanningFormSchemaType;
  };
  hasSpouse: boolean;
  hasChild: boolean;
  personalDetails: {
    certificateOwnerDetail: CertificateOwnerDetailFormSchemaType;
    spouseDetail: SpouseDetailFormSchemaType;
    childDependentDetail: ChildDependentDetailFormSchemaType[];
  };
}
export interface CustomerFactFindStore extends CustomerFactFindState {
  resetStep: () => void;
  updateStep: (step?: number) => void;
  setActiveStep: (step: number) => void;
  setProcessingStep: (step: number) => void;
  updateCustomerPreference: (customerPreference: CustomerPreference) => void;
  updateRecordOfAdvices: (recordOfAdvices: AdviceData) => void;
  updateFinancialStatement: (
    financialStatement: CustomerFactFindState['financialStatement'],
  ) => void;
  addNewAdvice: () => void;
  deleteAdvice: (advice: RecordOfAdviceItem) => void;
  updateAdvice: (advice: RecordOfAdviceItem) => void;
  updateAdvices: (advices: AdviceData) => void;
  updateAdviceRecordDisclosureReason: (reason: string) => void;
  saveFinancialInvestmentPreference: (
    investmentPreference: FinancialInvestmentPreferenceFormSchemaType,
  ) => void;
  saveFinancialStatementAndAnalysis: (
    statementAndAnalysis: FinancialStatementAndAnalysisFormSchemaType,
  ) => void;
  saveFinancialIncomeProtection: (
    incomeProtection: FinancialIncomeProtectionFormSchemaType,
  ) => void;
  saveFinancialRetirement: (
    retirementIncome: FinancialRetirementFormSchemaType,
  ) => void;
  saveFinancialChildEducation: (
    childEducation: FinancialChildEducationFormSchemaType,
  ) => void;
  saveFinancialSavings: (savings: FinancialSavingsFormSchemaType) => void;
  saveFinancialInvestments: (
    investments: FinancialInvestmentsFormSchemaType,
  ) => void;
  saveFinancialMedicalPlanning: (
    medicalPlanning: FinancialMedicalPlanningFormSchemaType,
  ) => void;
  saveFinancialPlanning: (
    financialPlanning: FinancialPlanningFormSchemaType,
  ) => void;
  updateCertificateOwnerDetail: (
    certificateOwnerDetail: CertificateOwnerDetailFormSchemaType,
  ) => void;
  updateSpouseDetail: (spouseDetail: SpouseDetailFormSchemaType) => void;
  updateChildDependentDetail: (
    childDependentDetail: ChildDependentDetailFormSchemaType[],
  ) => void;
  setHasSpouse: (hasSpouse: boolean) => void;
  setHasChild: (hasChild: boolean) => void;
  setCertificateOwnerId: (id: string) => void;
  setSpouseId: (id: string) => void;
  setChildId: (index: number, id: string) => void;
  resetCFFStore: () => void;
}

const createCustomerFactFindStore: StateCreator<CustomerFactFindStore> = (
  set,
  get,
) => ({
  ...initialState,
  resetStep: () => {
    set(() => ({
      processingStep: 1,
      activeStep: 1,
    }));
  },
  updateStep: (step?: number) => {
    if (step) {
      set(() => ({
        processingStep: Math.max(get().processingStep, step),
        activeStep: step,
      }));
    } else {
      const activeStep = get().activeStep;
      const processingStep = get().processingStep;

      set(() => ({
        processingStep: Math.max(processingStep, activeStep + 1),
        activeStep: activeStep + 1,
      }));
    }
  },
  setActiveStep: (step: number) => set({ activeStep: step }),
  setProcessingStep: (step: number) => set({ processingStep: step }),
  updateCustomerPreference: (customerPreference: CustomerPreference) => {
    set(() => ({
      customerPreference: customerPreference,
    }));
  },
  updateRecordOfAdvices: (recordOfAdvices: AdviceData) => {
    set(() => ({
      recordOfAdvices: recordOfAdvices,
    }));
  },
  updateFinancialStatement: (
    financialStatement: CustomerFactFindState['financialStatement'],
  ) => {
    set(() => ({
      financialStatement: financialStatement,
    }));
  },
  addNewAdvice: () => {
    const createdAt = new Date().getTime();
    set(() => ({
      recordOfAdvices: {
        ...get().recordOfAdvices,
        advices: [
          ...get().recordOfAdvices.advices,
          {
            index: get().recordOfAdvices.advices.length + 1,
            nameOfPersonCoverage: '',
            productType: '',
            paymentFrequency: '',
            contribution: '',
            sumCovered: '',
            term: '',
            additionalBenefits: '',
            isParticipate: false,
            reason1: '',
            reason2: '',
            reason3: '',
            otherReason: '',
            createdAt,
            isCompleted: false,
          },
        ],
      },
    }));
  },
  deleteAdvice: (advice: RecordOfAdviceItem) =>
    set(() => ({
      recordOfAdvices: {
        ...get().recordOfAdvices,
        advices: [
          ...get().recordOfAdvices.advices.filter(
            item => item.createdAt !== advice.createdAt,
          ),
        ],
      },
    })),
  updateAdvice: (advice: RecordOfAdviceItem) => {
    set(() => ({
      recordOfAdvices: {
        ...get().recordOfAdvices,
        advices: [
          ...get().recordOfAdvices.advices.map(item => {
            return item.createdAt === advice.createdAt
              ? { ...item, ...advice }
              : item;
          }),
        ],
      },
    }));
  },
  updateAdvices: (records: AdviceData) => {
    set(() => ({
      recordOfAdvices: {
        ...get().recordOfAdvices,
        ...records,
        advices: cloneDeep(records.advices),
      },
    }));
  },
  updateAdviceRecordDisclosureReason: (reason: string) => {
    set(() => ({
      recordOfAdvices: {
        ...get().recordOfAdvices,
        otherReason: reason,
      },
    }));
  },
  saveFinancialInvestmentPreference: (
    investmentPreference: FinancialInvestmentPreferenceFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        investmentPreference: cloneDeep(investmentPreference),
      },
    }));
  },
  saveFinancialStatementAndAnalysis: (
    statementAndAnalysis: FinancialStatementAndAnalysisFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        statementAndAnalysis: cloneDeep(statementAndAnalysis),
      },
    }));
  },
  saveFinancialIncomeProtection: (
    incomeProtection: FinancialIncomeProtectionFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        incomeProtection: cloneDeep(incomeProtection),
      },
    }));
  },
  saveFinancialRetirement: (
    retirementIncome: FinancialRetirementFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        retirementIncome: cloneDeep(retirementIncome),
      },
    }));
  },
  saveFinancialChildEducation: (
    childEducation: FinancialChildEducationFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        childEducation: cloneDeep(childEducation),
      },
    }));
  },
  saveFinancialSavings: (savings: FinancialSavingsFormSchemaType) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        savings: cloneDeep(savings),
      },
    }));
  },
  saveFinancialInvestments: (
    investments: FinancialInvestmentsFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        investments: cloneDeep(investments),
      },
    }));
  },
  saveFinancialMedicalPlanning: (
    medicalPlanning: FinancialMedicalPlanningFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        medicalPlanning: cloneDeep(medicalPlanning),
      },
    }));
  },
  saveFinancialPlanning: (
    financialPlanning: FinancialPlanningFormSchemaType,
  ) => {
    set(() => ({
      financialStatement: {
        ...get().financialStatement,
        financialPlanning: cloneDeep(financialPlanning),
      },
    }));
  },
  updateCertificateOwnerDetail: (
    certificateOwnerDetail: CertificateOwnerDetailFormSchemaType,
  ) =>
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        certificateOwnerDetail,
      },
    })),
  updateSpouseDetail: (spouseDetail: SpouseDetailFormSchemaType) =>
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        spouseDetail: spouseDetail,
      },
    })),
  updateChildDependentDetail: (
    childDependentDetail: ChildDependentDetailFormSchemaType[],
  ) =>
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        childDependentDetail: childDependentDetail,
      },
    })),
  setHasSpouse: (hasSpouse: boolean) => {
    set({ hasSpouse: hasSpouse });
  },
  setHasChild: (hasChild: boolean) => {
    set({ hasChild: hasChild });
  },
  setCertificateOwnerId: (id: string) => {
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        certificateOwnerDetail: {
          ...get().personalDetails.certificateOwnerDetail,
          id,
        },
      },
    }));
  },
  setSpouseId: (id: string) => {
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        spouseDetail: {
          ...get().personalDetails.spouseDetail,
          id,
        },
      },
    }));
  },
  setChildId: (index: number, id: string) => {
    set(() => ({
      personalDetails: {
        ...get().personalDetails,
        childDependentDetail: get().personalDetails.childDependentDetail.map(
          (child, childIdx) => {
            if (childIdx === index) {
              return {
                ...child,
                id,
              };
            }
            return child;
          },
        ),
      },
    }));
  },
  resetCFFStore: () => set(() => initialState),
});

const initialState: CustomerFactFindState = {
  activeStep: 1,
  processingStep: 1,
  customerPreference: {
    prioritization: [],
    customerChoice: undefined,
  },
  recordOfAdvices: initialRecordAdvicesFormData,
  financialStatement: {
    investmentPreference: initialFinancialInvestmentPreferenceFormData,
    statementAndAnalysis: initialFinancialStatementAndAnalysisFormData,
    incomeProtection: initialFinancialIncomeProtectionFormData,
    retirementIncome: initialFinancialRetirementFormData,
    childEducation: initialFinancialChildEducationFormData,
    savings: initialFinancialSavingsFormData,
    investments: initialFinancialInvestmentsFormData,
    medicalPlanning: initialFinancialMedicalPlanningFormData,
    financialPlanning: initialFinancialPlanningFormData,
  },
  personalDetails: {
    certificateOwnerDetail: initialCertificateOwnerDetailFormData,
    spouseDetail: initialSpouseDetailFormData,
    childDependentDetail: [initialChildDependentDetailFormData],
  },
  hasSpouse: false,
  hasChild: false,
};

export const useCustomerFactFindStore = create(
  immer(devtools(createCustomerFactFindStore)),
);
