import { NavigationProp, useNavigation } from '@react-navigation/native';
import { ApplicationProgress } from 'api/caseApi';
import { useGetApplicationProgressManually } from 'hooks/useApplicationProgress';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGenerateDeviceId } from 'hooks/useGenerateDeviceId';
import { useGeneratePolicyNumber } from 'hooks/useGeneratePolicyNumber';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useGetOptionListManually } from 'hooks/useGetOptionList';
import { useGetQuotationManually } from 'hooks/useGetQuotation';
import useLatest from 'hooks/useLatest';
import { useCallback, useEffect, useState } from 'react';
import { RootStackParamList } from 'types';
import { OptionList, Relationship } from 'types/optionList';
import { PartyRole } from 'types/party';
import {
  getDeviceId,
  increaseApplicationCount,
  saveDeviceId,
} from 'utils/storage/device';
import { cffRoutes } from '../components/header/CFFStepTabBar';
import { parseCFF, parseParty } from '../utils/cffCaseUtils';
import { useCustomerFactFindStore } from '../utils/store/customerFactFindStore';
import { CertificateOwnerDetailFormSchemaType } from '../validations/certificateOwnerDetailsSchema';
import { ChildDependentDetailFormSchemaType } from '../validations/childDependentDetailSchema';
import { SpouseDetailFormSchemaType } from '../validations/spouseDetailsSchema';

import { getProductName } from 'features/eApp/utils/eAppFormat';
import { getPremByPaymentMode } from 'features/eApp/utils/planUtils';
import { RelationshipValue } from 'features/proposal/types';
import { useAlert } from 'hooks/useAlert';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useTranslation } from 'react-i18next';
import { Case } from 'types/case';
import { PaymentMode } from 'types/proposal';
import { Quotation, RiderCode } from 'types/quotation';
import { initialRecordAdvicesFormData } from '../validations/recordOfAdviceValidation';

export const useAutoPopulateCFF = (onFreshStart?: () => void) => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const onFreshStartRef = useLatest(onFreshStart);
  const {
    updateCertificateOwnerDetail,
    setHasSpouse,
    updateSpouseDetail,
    setHasChild,
    updateChildDependentDetail,
    updateCustomerPreference,
    updateRecordOfAdvices,
    updateFinancialStatement,
    setActiveStep,
    setProcessingStep,
    recordOfAdvices,
  } = useCustomerFactFindStore(state => ({
    updateCertificateOwnerDetail: state.updateCertificateOwnerDetail,
    setHasSpouse: state.setHasSpouse,
    updateSpouseDetail: state.updateSpouseDetail,
    setHasChild: state.setHasChild,
    updateChildDependentDetail: state.updateChildDependentDetail,
    updateCustomerPreference: state.updateCustomerPreference,
    updateRecordOfAdvices: state.updateRecordOfAdvices,
    updateFinancialStatement: state.updateFinancialStatement,
    setActiveStep: state.setActiveStep,
    setProcessingStep: state.setProcessingStep,
    recordOfAdvices: state.recordOfAdvices,
  }));
  const channel = useGetCubeChannel();
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const caseId = useBoundStore(state => state.case.caseId);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { mutateAsync: getCase } = useGetCaseManually();
  const { mutateAsync: getQuotation } = useGetQuotationManually();
  const { mutateAsync: getProgress } = useGetApplicationProgressManually();
  const { mutateAsync: generatePolicyNumber } = useGeneratePolicyNumber();
  const { mutateAsync: createApplication } = useCreateApplication();
  const { mutateAsync: getOptionList } = useGetOptionListManually();
  const { mutateAsync: generateDeviceId } = useGenerateDeviceId();
  const { t } = useTranslation(['common', 'customerFactFind']);

  const [isLoading, setIsLoading] = useState(true);

  const { alertError } = useAlert();

  const populateRecordOfAdvice = useCallback(
    async ({
      selectedQuotation,
      caseObj,
      optionList,
    }: {
      selectedQuotation: Quotation;
      caseObj: Case;
      optionList: OptionList;
    }) => {
      if (selectedQuotation && caseObj) {
        const { plans } = selectedQuotation;
        if (plans && plans.length > 0) {
          const basePlan = plans?.[0];

          const adviceItem = { ...initialRecordAdvicesFormData.advices?.[0] };

          const fullName =
            caseObj.parties?.find((p: { roles: string | PartyRole[] }) =>
              p.roles.includes(PartyRole.INSURED),
            )?.person?.name?.firstName ?? '';

          adviceItem.nameOfPersonCoverage = fullName;

          adviceItem.productType = getProductName(basePlan.productName);

          adviceItem.paymentFrequency =
            optionList?.PAYMENTMODE4_CFF?.options?.find(
              (item: { label: string }) =>
                item?.label?.split?.('')?.[0] === basePlan?.paymentMode,
            )?.label ?? '';
          if (basePlan?.paymentMode === PaymentMode.SINGLE) {
            adviceItem.paymentFrequency = t('customerFactFind:singlePremium');
          }

          adviceItem.term = String(
            basePlan?.maxPolicyTerm ?? basePlan?.policyTerm,
          );

          adviceItem.sumCovered = String(basePlan?.sumAssured);

          adviceItem.contribution = String(
            getPremByPaymentMode(selectedQuotation),
          );
          adviceItem.isParticipate = true;

          const fields = Object.assign(
            [],
            initialRecordAdvicesFormData.advices,
          );
          if (fields) {
            fields.splice(0, 1, adviceItem);

            updateRecordOfAdvices({
              advices: fields,
              disclosure: '',
              disclosureReason: '',
              preference: '',
            });
          } else {
            updateRecordOfAdvices(fields);
          }
        }
      }
    },
    [t, updateRecordOfAdvices],
  );

  const prePopulateCFF = useCallback(async () => {
    setAppLoading();
    setIsLoading(true);
    try {
      if (!caseId) throw new Error('case id not found');
      if (!agentId) throw new Error('agent id not found');
      const optionList = await getOptionList();
      const caseObj = await getCase(caseId);
      const selectedQuotation = await getQuotation({
        caseId,
        quotationId: caseObj?.quotations?.[0].id || '',
      });
      const policyOwner = caseObj.parties?.find(p =>
        p.roles.includes(PartyRole.PROPOSER),
      );

      // Generate policy number
      let policyNum = '';
      let applicationNum = '';
      if (!caseObj.application?.policyNum) {
        const mobileContact = policyOwner?.contacts.phones.find(
          phone => phone.type === 'MOBILE',
        );
        const phoneNo = mobileContact
          ? `${mobileContact.countryCode}${mobileContact.number}`
          : '';

        let retryCount = 0;
        while (retryCount < 3) {
          try {
            policyNum = await generatePolicyNumber({
              phoneNo,
              productType: selectedQuotation.plans[0].planCode,
            });
            break;
          } catch {
            retryCount++;
          }
        }
        if (retryCount === 3 && !policyNum) {
          throw new Error('policy number was not generated');
        }
        let deviceId = await getDeviceId();
        if (deviceId === null) {
          deviceId = await generateDeviceId();
          await saveDeviceId(deviceId);
        }
        const count = await increaseApplicationCount();
        const replaceRegexp = RegExp(`[0-9]{${count.length}}$`);
        applicationNum = `${deviceId}0000`.replace(replaceRegexp, count);
        await createApplication({
          caseId,
          data: {
            agent: {
              agentCode: agentId,
            },
            policyId: policyNum,
            policyNum,
            applicationNum,
          },
        });
      }

      if (!policyOwner) throw new Error('policy owner not found');
      const addRider = selectedQuotation.plans.find(
        plan => plan?.pid === RiderCode.ADIA,
      );
      const hasAddRider = addRider !== null && addRider !== undefined;
      const addRiderAnnualIncome = hasAddRider
        ? optionList.INCOME_RANGE?.options?.find(
            e => e.annualIncomeValue === addRider?.annualIncome,
          )?.value
        : undefined;
      const ownerInfo = parseParty(
        policyOwner,
        optionList,
      ) as CertificateOwnerDetailFormSchemaType;
      updateCertificateOwnerDetail({
        ...ownerInfo,
        annualIncome: policyOwner.isMainInsured
          ? addRiderAnnualIncome ?? ownerInfo.annualIncome
          : ownerInfo.annualIncome,
        isVulnerable: caseObj.cff?.vulnerable?.vulnerableCustomerTag || 'N',
        vulnerableLevel:
          caseObj.cff?.vulnerable?.vulnerableCustomerOption || '',
      });
      const childList: ChildDependentDetailFormSchemaType[] = [];
      const insureds = caseObj.parties?.filter(
        p =>
          !p.roles.includes(PartyRole.PROPOSER) &&
          p.roles.includes(PartyRole.INSURED),
      );

      if (insureds) {
        insureds.forEach(insured => {
          const relationshipGroup = (
            optionList.RELATIONSHIP.options as Relationship<string, 'my'>[]
          ).find(i => i.value === insured?.relationship)?.group;
          if (relationshipGroup === RelationshipValue.SPOUSE) {
            setHasSpouse(true);
            const insuredInfo = parseParty(
              insured,
              optionList,
            ) as SpouseDetailFormSchemaType;
            updateSpouseDetail({
              ...insuredInfo,
              annualIncome: insured.isMainInsured
                ? addRiderAnnualIncome ?? insuredInfo.annualIncome
                : insuredInfo.annualIncome,
            });
          } else {
            const childInfo = parseParty(
              insured,
              optionList,
            ) as ChildDependentDetailFormSchemaType;
            childList.push({
              ...childInfo,
              annualIncome: insured.isMainInsured
                ? addRiderAnnualIncome ?? childInfo.annualIncome
                : childInfo.annualIncome,
            });
          }
        });
      }
      const dependents =
        caseObj.parties?.filter(p => p.roles.includes(PartyRole.DEPENDENT)) ||
        [];
      dependents.forEach(dependent => {
        const relationshipGroup = (
          optionList.RELATIONSHIP.options as Relationship<string, 'my'>[]
        ).find(i => i.value === dependent?.relationship)?.group;
        if (relationshipGroup === RelationshipValue.SPOUSE) {
          setHasSpouse(true);
          updateSpouseDetail(
            parseParty(dependent, optionList) as SpouseDetailFormSchemaType,
          );
        } else {
          childList.push(
            parseParty(
              dependent,
              optionList,
            ) as ChildDependentDetailFormSchemaType,
          );
        }
      });
      if (childList.length > 0) {
        setHasChild(true);
        updateChildDependentDetail(childList);
      }

      if (caseObj.cff) {
        const { customerPreference, recordOfAdvices, financialStatement } =
          parseCFF(caseObj.cff);
        // Come from current eApp
        if (recordOfAdvices?.advices?.length > 0) {
          updateRecordOfAdvices(recordOfAdvices);
        } else {
          // Come from CFF/SI
          await populateRecordOfAdvice({
            selectedQuotation,
            caseObj,
            optionList,
          });
        }

        updateCustomerPreference(customerPreference);
        updateFinancialStatement(financialStatement);
      } else {
        // Incase no RoA, still need to fill all data
        await populateRecordOfAdvice({
          selectedQuotation,
          caseObj,
          optionList,
        });
      }
      try {
        const status = await getProgress(caseId);
        if (status) {
          const keys = Object.keys(status);
          let maxProcessingStep = 1;
          let activeStep = 1;
          keys.forEach(key => {
            if (
              status[key] === ApplicationProgress.COMPLETED ||
              status[key] === ApplicationProgress.IN_PROGRESS
            ) {
              const step =
                cffRoutes.findIndex(route => key === `cff/${route.key}`) + 1;
              maxProcessingStep = Math.max(maxProcessingStep, step);
              if (status[key] === ApplicationProgress.IN_PROGRESS) {
                activeStep = Math.max(activeStep, step);
              }
            }
          });
          setActiveStep(activeStep);
          setProcessingStep(maxProcessingStep);
          if (maxProcessingStep === 1) {
            onFreshStartRef.current?.();
          }
        } else {
          onFreshStartRef.current?.();
        }
      } catch {
        onFreshStartRef.current?.();
      }
    } catch (e) {
      console.log(e);
      alertError(t('customerFactFind:failedToStartCFF'));
      navigation.navigate('Main');
    } finally {
      setIsLoading(false);
      setAppIdle();
    }
  }, [
    setAppLoading,
    caseId,
    agentId,
    getOptionList,
    getCase,
    getQuotation,
    updateCertificateOwnerDetail,
    createApplication,
    generatePolicyNumber,
    generateDeviceId,
    setHasSpouse,
    updateSpouseDetail,
    setHasChild,
    updateChildDependentDetail,
    updateCustomerPreference,
    updateFinancialStatement,
    updateRecordOfAdvices,
    populateRecordOfAdvice,
    getProgress,
    setActiveStep,
    setProcessingStep,
    onFreshStartRef,
    alertError,
    t,
    navigation,
    setAppIdle,
  ]);

  useEffect(() => {
    if (caseId) {
      prePopulateCFF();
    }
  }, [caseId, prePopulateCFF]);
  
  return { isLoading };
};
