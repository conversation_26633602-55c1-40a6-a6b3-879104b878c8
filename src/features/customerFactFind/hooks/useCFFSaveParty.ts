import { OWNER_ADDRESS_OPTION } from 'constants/optionList';
import { toParty } from 'features/customerFactFind/utils/cffCaseUtils';
import {
  CustomerFactFindState,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import { RelationshipValue } from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateCFF } from 'hooks/useCreateCFF';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import { useCallback, useState } from 'react';
import { CHANNELS } from 'types/channel';
import { Relationship } from 'types/optionList';
import { PartyRole } from 'types/party';
import { Gender } from 'types/person';

type PartySavedCallback = (
  role: 'owner' | 'spouse' | 'child',
  index: number,
  id: string,
) => void;

export const useCFFSaveParty = () => {
  const caseId = useBoundStore(state => state.case.caseId);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { data: caseObj } = useGetCase(caseId);
  const { data: optionList } = useGetOptionList();
  const { saveParty } = useSaveParty();
  const [isSaving, setIsSaving] = useState(false);
  const { mutateAsync: createCFF } = useCreateCFF();

  const onCFFSaveParty = useCallback(
    async (
      onPartySaved: PartySavedCallback = () => null,
      {
        personalDetails,
      }: {
        personalDetails?: CustomerFactFindState['personalDetails'];
      } = {},
    ) => {
      if (!optionList) return;
      const cffStore = useCustomerFactFindStore.getState();
      const { hasSpouse, hasChild } = cffStore;
      const ownerDetails = personalDetails
        ? personalDetails.certificateOwnerDetail
        : cffStore.personalDetails.certificateOwnerDetail;
      const spouseDetails = personalDetails
        ? personalDetails.spouseDetail
        : cffStore.personalDetails.spouseDetail;
      const childDependentDetails = personalDetails
        ? personalDetails.childDependentDetail
        : cffStore.personalDetails.childDependentDetail;
      try {
        setIsSaving(true);
        if (caseId && agentId) {
          const ownerPartyData = toParty(
            ownerDetails,
            PartyRole.PROPOSER,
            optionList,
          );
          const ownerId = await saveParty(ownerPartyData, {
            overridingRoles: false,
            preventCreatingParty: true,
          });
          if (ownerId) {
            onPartySaved('owner', 0, ownerId);
          }
          if (hasSpouse) {
            const spouseRelationship = (
              (optionList?.RELATIONSHIP.options ?? []) as Relationship<
                string,
                'my'
              >[]
            ).filter(
              r =>
                r.role.insured === 'True' &&
                r.party.individual === 'True' && // TODO: handle entity logic in here
                r.group === RelationshipValue.SPOUSE &&
                r.gender.female ===
                  (ownerDetails.gender === Gender.MALE ? 'True' : 'False'),
            )?.[0]?.value;
            const spouseInsured = caseObj?.parties?.find(
              p =>
                p.id === spouseDetails.id &&
                p.roles.includes(PartyRole.INSURED),
            );
            let spouseId: string | undefined = '';
            if (spouseDetails.correspondenceAddress === OWNER_ADDRESS_OPTION) {
              spouseDetails.correspondenceAddressLine1 =
                ownerDetails.correspondenceAddressLine1;
              spouseDetails.correspondenceAddressLine2 =
                ownerDetails.correspondenceAddressLine2;
              spouseDetails.correspondenceAddressLine3 =
                ownerDetails.correspondenceAddressLine3;
              spouseDetails.correspondencePostCode =
                ownerDetails.correspondencePostCode;
              spouseDetails.correspondenceCity =
                ownerDetails.correspondenceCity;
              spouseDetails.correspondenceState =
                ownerDetails.correspondenceState;
              spouseDetails.correspondenceCountry =
                ownerDetails.correspondenceCountry;
            }
            if (spouseInsured) {
              spouseId = await saveParty(
                {
                  ...toParty(spouseDetails, PartyRole.INSURED, optionList),
                  relationship: spouseRelationship,
                },
                {
                  overridingRoles: false,
                  preventCreatingParty: true,
                },
              );
            } else {
              spouseId = await saveParty(
                {
                  ...toParty(spouseDetails, PartyRole.DEPENDENT, optionList),
                  relationship: spouseRelationship,
                },
                {
                  overridingRoles: true,
                },
              );
            }
            if (spouseId) {
              onPartySaved('spouse', 0, spouseId);
            }
          }
          if (hasChild) {
            for (let i = 0; i < childDependentDetails.length; i++) {
              const childData = childDependentDetails[i];
              const isChildInsured = caseObj?.parties?.find(
                p =>
                  p.id === childData.id && p.roles.includes(PartyRole.INSURED),
              );
              let childId: string | undefined = '';
              if (isChildInsured) {
                const childMobile = ownerPartyData.contacts.phones.find(
                  tel => tel.type === 'MOBILE',
                );
                childId = await saveParty(
                  {
                    ...toParty(childData, PartyRole.INSURED, optionList),
                    // addresses: ownerPartyData.addresses,
                    contacts: {
                      ...ownerPartyData.contacts,
                      phones: childMobile ? [childMobile] : [],
                    },
                  },
                  {
                    overridingRoles: false,
                    preventCreatingParty: true,
                  },
                );
              } else {
                childId = await saveParty(
                  toParty(childData, PartyRole.DEPENDENT, optionList),
                  {
                    overridingRoles: true,
                  },
                );
              }
              if (childId) {
                onPartySaved('child', i, childId);
              }
            }
          }
        }
        if (caseId && agentId) {
          await createCFF({
            caseId,
            cff: {
              agentId,
              pdfToken: '',
              acknowledge: caseObj?.cff?.acknowledge || false,
              stage: '',
              proposalNum: caseObj?.application?.policyNum || '',
              ...caseObj?.cff,
              vulnerable: {
                vulnerableCustomerTag: ownerDetails.isVulnerable,
                vulnerableCustomerOption: ownerDetails.vulnerableLevel,
              },
            },
          });
        }
      } finally {
        setIsSaving(false);
      }
    },
    [
      optionList,
      caseId,
      agentId,
      saveParty,
      caseObj?.parties,
      caseObj?.cff,
      caseObj?.application?.policyNum,
      createCFF,
    ],
  );

  return {
    isSaving,
    onCFFSaveParty,
  };
};
