import { MY_COUNTRY, NEW_NRIC, PASSPORT } from 'constants/optionList';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useEffect, useState } from 'react';

export const useUpdatePrimaryIdType = (
  onChangePrimaryIdType: (...event: any[]) => void,
  nationality?: string,
) => {
  const { data: optionList } = useGetOptionList<'my'>();
  const [isDisabledOcr, setDisabledOcr] = useState(false);

  useEffect(() => {
    if (nationality === MY_COUNTRY) {
      onChangePrimaryIdType(
        optionList?.ID_TYPE.options.find(e => e?.value === NEW_NRIC)?.value ??
          '',
      );
      setDisabledOcr(false);
    } else if (nationality !== '' && nationality !== null && nationality !== undefined) {
      onChangePrimaryIdType(
        optionList?.ID_TYPE.options.find(e => e?.value === PASSPORT)?.value ??
          '',
      );
      setDisabledOcr(true);
    }
  }, [nationality]);

  return {
    isDisabledOcr,
  };
};
