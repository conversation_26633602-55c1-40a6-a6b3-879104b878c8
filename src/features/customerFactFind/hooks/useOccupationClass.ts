import { useGetOptionList } from 'hooks/useGetOptionList';
import { useMemo } from 'react';
import { Occupation } from 'types/optionList';

export const useOccupationClass = (occupationValue?: string) => {
  const { data: optionList } = useGetOptionList();
  const selectedOccupation = useMemo(
    () =>
      (optionList?.OCCUPATION.options as Occupation<string, 'my'>[])?.find(
        e => e.value === occupationValue,
      ),
    [occupationValue, optionList?.OCCUPATION.options],
  );

  return {
    occupationClass: selectedOccupation?.occupationClass,
    occupationDescription: selectedOccupation?.occupationDesc,
    occupationGroup: selectedOccupation?.occupationGroupCode,
  };
};
