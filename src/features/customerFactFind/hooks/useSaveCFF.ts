import { ApplicationProgress } from 'api/caseApi';
import { toCFF } from 'features/customerFactFind/utils/cffCaseUtils';
import {
  CustomerFactFindState,
  useCustomerFactFindStore,
} from 'features/customerFactFind/utils/store/customerFactFindStore';
import { useSaveApplicationProgress } from 'hooks/useApplicationProgress';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateCFF } from 'hooks/useCreateCFF';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CHANNELS } from 'types/channel';
import { cffRoutes } from '../components/header/CFFStepTabBar';

export const useSaveCFF = () => {
  const caseId = useBoundStore(state => state.case.caseId);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { data: caseObj } = useGetCase(caseId);
  const { data: optionList } = useGetOptionList();
  const { mutateAsync: createCFF } = useCreateCFF();
  const [isSaving, setIsSaving] = useState(false);
  const { saveApplicationProgress } = useSaveApplicationProgress();

  const onSaveCFF = useCallback(
    async (
      {
        isAcknowledge,
        personalDetails,
      }: {
        isAcknowledge?: boolean;
        personalDetails?: CustomerFactFindState['personalDetails'];
      } = { isAcknowledge: false },
    ) => {
      if (!optionList) return;
      const cffStore = useCustomerFactFindStore.getState();
      const { processingStep, activeStep } = cffStore;
      const ownerDetails = personalDetails
        ? personalDetails.certificateOwnerDetail
        : cffStore.personalDetails.certificateOwnerDetail;
      try {
        setIsSaving(true);
        if (caseId && agentId) {
          const status: { [key: string]: ApplicationProgress } = {};
          for (let i = 1; i <= processingStep; i++) {
            const key = cffRoutes[i - 1]?.key;
            if (key) {
              status[`cff/${key}`] = ApplicationProgress.COMPLETED;
              if (activeStep === i) {
                status[`cff/${key}`] = ApplicationProgress.IN_PROGRESS;
              }
            }
          }
          await saveApplicationProgress(status);
          await createCFF({
            caseId,
            cff: {
              ...toCFF(
                cffStore,
                agentId,
                caseObj?.application?.policyNum || '',
                isAcknowledge || caseObj?.cff?.acknowledge || false,
              ),
              vulnerable: {
                vulnerableCustomerTag: ownerDetails.isVulnerable,
                vulnerableCustomerOption: ownerDetails.vulnerableLevel,
              },
            },
          });
        }
      } catch {
        throw new Error('Failed to save CFF');
      } finally {
        setIsSaving(false);
      }
    },
    [
      optionList,
      caseId,
      agentId,
      createCFF,
      caseObj?.cff,
      caseObj?.application?.policyNum,
      saveApplicationProgress,
    ],
  );

  return {
    isSaving,
    onSaveCFF,
  };
};
