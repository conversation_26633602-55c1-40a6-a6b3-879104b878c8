import { useCallback, useMemo } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import { City, OptionList, Postcode, State } from 'types/optionList';
import { AddressInfo } from '../components/personalDetails/addressInformation/AddressInformationForm';
import { MY_COUNTRY } from 'constants/optionList';
import { MYAddressType } from 'features/eApp/validations/eAppCommonSchema';
import uniqBy from 'lodash/uniqBy';

export const useAddressInformationOptions = (
  type: MYAddressType,
  optionList: OptionList<'my'> | undefined,
  control: Control<AddressInfo>,
  setValue: UseFormSetValue<AddressInfo>,
  getValues: UseFormGetValues<AddressInfo>,
  trigger: UseFormTrigger<AddressInfo>,
): {
  postCodeList: Array<Postcode<string, 'my'>>;
  stateList: Array<State<string>>;
  cityList: Array<City<string>>;
  isMY: boolean;
  onCountryChange: (value: string | undefined) => void;
  onPostCodeChange: (value: string | undefined) => void;
} => {
  const country = useWatch({
    name: `${type}Country`,
    control: control,
  });

  const postCode = useWatch({
    name: `${type}PostCode`,
    control: control,
  });

  const isMY =
    country === MY_COUNTRY || country === undefined || country === '';

  const getDefaultStateAndCityFromPostCode = useCallback(
    (
      postCode: string | undefined,
    ): [State<string> | undefined, City<string> | undefined] => {
      const postCodeOption = postCode
        ? (optionList?.POSTCODE.options ?? []).find(
            i => String(i.value) === postCode,
          )
        : undefined;
      const cityOption = postCodeOption?.city
        ? (optionList?.CITY.options ?? []).find(
            i => i.value === postCodeOption?.city,
          )
        : undefined;
      const stateOption = postCodeOption?.state
        ? (optionList?.STATE.options ?? []).find(
            i => i.label === postCodeOption?.state,
          )
        : undefined;
      return [stateOption, cityOption];
    },
    [optionList],
  );

  const onCountryChange = (value: string | undefined) => {
    const isMY = value === MY_COUNTRY || value === undefined || value === '';
    if (isMY) {
      const [stateOption, cityOption] =
        getDefaultStateAndCityFromPostCode(postCode);
      if (cityOption) {
        setValue(`${type}City`, cityOption.value);
        trigger(`${type}City`);
      }
      if (stateOption) {
        setValue(`${type}State`, stateOption.value);
        trigger(`${type}State`);
      }
    } else {
      setValue(`${type}PostCode`, '');
      setValue(`${type}City`, '');
      setValue(`${type}State`, '');
      trigger(`${type}PostCode`);
      trigger(`${type}City`);
    }
  };

  const onPostCodeChange = (value: string | undefined) => {
    const country = getValues(`${type}Country`);
    const isMY =
      country === MY_COUNTRY || country === undefined || country === '';
    if (isMY) {
      const [stateOption, cityOption] =
        getDefaultStateAndCityFromPostCode(value);
      if (cityOption) {
        setValue(`${type}City`, cityOption.value);
        trigger(`${type}City`);
      }
      if (stateOption) {
        setValue(`${type}State`, stateOption.value);
        trigger(`${type}State`);
      }
      setValue(`${type}Country`, MY_COUNTRY);
    }
  };

  const postCodeList = useMemo(() => {
    return uniqBy(optionList?.POSTCODE.options, 'value');
  }, [optionList?.POSTCODE.options]);

  const stateList = useMemo(() => {
    if (isMY) {
      const postCodeOptions = postCode
        ? (optionList?.POSTCODE.options ?? [])
            .filter(i => String(i.value) === postCode)
            .map(i => i.state)
        : [];
      return (optionList?.STATE.options ?? []).filter(i =>
        postCodeOptions.includes(i.label),
      );
    } else {
      const normalizedCountry = country || MY_COUNTRY;
      const countryOption = (optionList?.COUNTRY.options ?? []).find(
        i => i.value === normalizedCountry,
      );
      const stateList = countryOption?.lookupKey
        ? (optionList?.STATE.options ?? []).filter(
            i => i.key === countryOption.lookupKey,
          )
        : [];
      return stateList;
    }
  }, [country, optionList, isMY, postCode]);

  const cityList = useMemo(() => {
    if (isMY) {
      const postCodeOptions = postCode
        ? (optionList?.POSTCODE.options ?? [])
            .filter(i => String(i.value) === postCode)
            .map(i => i.city)
        : [];
      return (optionList?.CITY.options ?? []).filter(
        i =>
          postCodeOptions.includes(i.value) &&
          Boolean(stateList.find(s => s.lookupKey === i.key)),
      );
    } else {
      return [];
    }
  }, [
    isMY,
    postCode,
    optionList?.POSTCODE.options,
    optionList?.CITY.options,
    stateList,
  ]);

  return {
    postCodeList,
    stateList,
    cityList,
    isMY,
    onCountryChange,
    onPostCodeChange,
  };
};
