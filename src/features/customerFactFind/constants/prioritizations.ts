import { PictogramIcon } from 'cube-ui-components';
import { SvgPictogramProps } from 'cube-ui-components/dist/cjs/icons/pictograms/SvgPictogramProps';

export type PrioritizationKey =
  | 'IncomeProtection'
  | 'RetirementIncome'
  | 'ChildEducation'
  | 'Savings'
  | 'Investments'
  | 'MedicalPlanning';

// export const cffPrioritizationMap: Record<
//   PrioritizationProps,
//   PrioritizationKey
// > = {
//   protection: 'IncomeProtection',
//   retirement: 'retirement',
//   education: 'education',
//   savings: 'savings',
//   investment: 'investment',
//   medical: 'medical',
// };

export const slotPrioritiseRow = [0, 1, 2];

export interface initialPrioritiseListProps {
  key: PrioritizationKey;
  icon: React.ComponentType<SvgPictogramProps>;
}

export const initialPrioritiseList: initialPrioritiseListProps[] = [
  {
    key: 'IncomeProtection',
    icon: PictogramIcon.MoneyCapital,
  },
  {
    key: 'RetirementIncome',
    icon: PictogramIcon.Family2,
  },
  {
    key: 'ChildEducation',
    icon: PictogramIcon.PeopleKnowledge,
  },
  {
    key: 'Savings',
    icon: PictogramIcon.Savings,
  },
  {
    key: 'Investments',
    icon: PictogramIcon.MoneyInvestment,
  },
  {
    key: 'MedicalPlanning',
    icon: PictogramIcon.Medicine,
  },
];

export const prioritizationKeyToIcon: Record<
PrioritizationKey,
  React.ComponentType<SvgPictogramProps>
> = {
  IncomeProtection: PictogramIcon.MoneyCapital,
  RetirementIncome: PictogramIcon.Family2,
  ChildEducation: PictogramIcon.PeopleKnowledge,
  Savings: PictogramIcon.Savings,
  Investments: PictogramIcon.MoneyInvestment,
  MedicalPlanning: PictogramIcon.Medicine,
};

export interface initialPrioritizationListProps {
  idx: number;
  icon: undefined | React.ComponentType<SvgPictogramProps>;
  key: PrioritizationKey | undefined;
  alreadyPlanned: boolean;
  toDiscuss: boolean;
}

export const initialPrioritizationList: initialPrioritizationListProps[] = [
  {
    idx: 0,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    idx: 1,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    idx: 2,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    idx: 3,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    idx: 4,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
  {
    idx: 5,
    key: undefined,
    icon: undefined,
    alreadyPlanned: false,
    toDiscuss: false,
  },
];
