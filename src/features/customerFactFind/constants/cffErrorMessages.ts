import { TFunc<PERSON><PERSON> } from 'i18next';

export const requiredMessage: TFun<PERSON><PERSON><PERSON><['customerFactFind']> =
  'customerFactFind:validation.error.required';
export const duplicateMessage: TFunc<PERSON>ey<['customerFactFind']> =
  'customerFactFind:validation.error.duplicate';
export const invalidAnnualIncomeAmount: TFunc<PERSON>ey<['customerFactFind']> =
  'customerFactFind:validation.error.invalidAnnualIncomeAmount';
export const minLength10Message: TFunc<PERSON>ey<['customerFactFind']> =
  'customerFactFind:validation.error.minLength10';
