import styled from '@emotion/native';
import { useNavigation, useRoute } from '@react-navigation/native';
import PersonalDetails from 'features/customerFactFind/components/personalDetails/PersonalDetails.tablet';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SceneMap, TabView } from 'react-native-tab-view';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import ConfirmationOfAdvice from './components/confirmationOfAdvice/ConfirmationOfAdvice';
import CustomerPreference from './components/customerPreference/CustomerPreference';
import FinancialStatement from './components/financialStatement/FinancialStatement.tablet';
import { CFFStepTabBar, CustomerFactFindHeader } from './components/header';
import { cffRoutes } from './components/header/CFFStepTabBar';
import { CFFAgreementModal } from './components/modals/CFFAgreeementModal';
import RecordOfAdvice from './components/recordAdvice/RecordOfAdvice';
import { useAutoPopulateCFF } from './hooks/useAutoPopulateCFF';
import { useCustomerFactFindStore } from './utils/store/customerFactFindStore';

const renderScene = SceneMap({
  customerPreference: CustomerPreference,
  personalDetails: PersonalDetails,
  financialStatement: FinancialStatement,
  recordOfAdvice: RecordOfAdvice,
  confirmationOfAdvice: ConfirmationOfAdvice,
});

export default function CustomerFactFind() {
  const [consentVisible, setConsentVisible] = useState(false);
  const navigation = useNavigation();
  const { name: screenName } = useRoute();

  const onDismiss = () => {
    setConsentVisible(false);
    GATracking.logButtonPress({
      screenName,
      screenClass: 'CFF flow',
      actionType: 'non_cta_button',
      buttonName: 'Confirm',
    });
    navigation.goBack();
  };
  const onAgree = () => setConsentVisible(false);
  const { activeStep, processingStep, updateStep, resetCFFStore } =
    useCustomerFactFindStore(
      state => ({
        activeStep: state.activeStep,
        processingStep: state.processingStep,
        updateStep: state.updateStep,
        resetCFFStore: state.resetCFFStore,
      }),
      shallow,
    );

  const navigationState = useMemo(() => {
    return {
      index: activeStep - 1,
      routes: cffRoutes,
    };
  }, [activeStep]);

  useEffect(() => {
    return () => {
      resetCFFStore();
    };
  }, [resetCFFStore]);

  const { isLoading } = useAutoPopulateCFF(() => {
    setConsentVisible(true);
  });

  if (isLoading) return null;

  return (
    <Container edges={['top']}>
      <CustomerFactFindHeader />
      <CFFStepTabBar
        activeStep={activeStep}
        processingStep={processingStep}
        updateStep={updateStep}
      />
      <TabView
        swipeEnabled={false}
        navigationState={navigationState}
        onIndexChange={updateStep}
        renderScene={renderScene}
        lazy
        renderTabBar={() => <Fragment />}
      />
      <CFFAgreementModal
        visible={consentVisible}
        onDismiss={onDismiss}
        onAgree={onAgree}
      />
    </Container>
  );
}

const Container = styled(SafeAreaView)(({ theme }) => {
  return {
    flex: 1,
    backgroundColor: theme.colors.background,
  };
});
