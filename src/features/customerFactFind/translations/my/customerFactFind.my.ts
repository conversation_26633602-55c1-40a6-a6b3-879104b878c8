export default {
  title: 'Customer Fact Find (CFF)',
  failedToStartCFF: 'Failed to start CFF',
  failedToSave: 'Failed to save data',
  failedToGeneratePdf: 'Failed to generate PDF',
  'saveModal.title': 'Are you sure to quit?',
  'saveModal.desc':
    'Do you want to save before exit the Customer Fact Find (CFF)?',
  'saveModal.dontSave': `Don't save`,
  'saveModal.save': 'Save',
  saved: 'Saved',
  //Financial statement
  'financial.annualIncome': 'Annual Income (RM)',
  'financial.amount': 'Amount (RM)',
  'financial.proposedParticipant': 'Proposed participant',
  'financial.spouse': 'Spouse',
  'financial.monthly': 'Monthly',
  'financial.yearly': 'Yearly',
  'financial.lumpSum': 'Lump Sum',
  'financial.replacedYears': 'Years to be replaced',
  'financial.retirementYears': 'Years to retirement',
  'financial.meetYears': 'Years to meet',
  'financial.rm': 'RM',
  'financial.pleaseSpecify': 'Please specify',
  'financial.name': 'Name',
  'financial.age': 'Age',
  'financial.years': 'Years',
  'financial.yearsOld': 'Years old',
  'financial.investmentDuration': 'Investment duration (years)',
  'financial.investmentDuration.less5': '< 5 years',
  'financial.investmentDuration.more5': '5 years and above',
  'financial.payoutPeriod': 'Payout period (years)',
  'financial.yes': 'Yes',
  'financial.no': 'No',
  'financial.remove': 'Remove',
  'financial.cancel': 'Cancel',
  'financial.amountPlaceHolder': '- -',

  'financial.investment.title': 'Investment preference',
  'financial.investment.desc':
    'Please indicate your investment risk preference.',
  'financial.investment.conservative': 'Conservative',
  'financial.investment.moderatelyConservative': 'Moderate conservative',
  'financial.investment.balanced': 'Balanced',
  'financial.investment.moderatelyAggressive': 'Moderate aggressive',
  'financial.investment.aggressive': 'Aggressive',
  'financial.investment.lowRisk': 'Low risk/Low potential return',
  'financial.investment.highRisk': 'High risk/ High potential return',
  'financial.statementAndAnalysis.title': 'Financial Statement & Analysis',
  'financial.investment.confirmation.title': 'Fund selection validation',
  'financial.investment.confirmation.desc':
    'Your fund selection is either not in-line with the customer’s risk appetite or not optimised. Could you provide the reason of your selection?',
  'financial.investment.confirmation.high':
    'Prefer higher risk for potential better returns',
  'financial.investment.confirmation.low':
    'Prefer lower risk as customer has other higher risk investment elsewhere',
  'financial.investment.confirmation.others': 'Others',
  'financial.investment.confirmation.others.reason':
    'Please specify the reason',
  'financial.investment.confirmation.others.reason.error':
    'Minimum 10 characters',
  'financial.certificate': 'Existing certificate',
  'financial.certificate.withIndex': 'Existing certificate {{index}}',
  'financial.certificate.addCertificate':
    'Add existing insurance policy/takaful certificate ({{count}}/ {{maxCount}})',
  'financial.certificate.description':
    'Do you have any existing insurance policies/ Takaful plans?',
  'financial.certificate.policyHolders': 'Policy holders/Certificate owners',
  'financial.certificate.lifeAssured': 'Life assured/Person covered',
  'financial.certificate.productType': 'Product type',
  'financial.certificate.insurer': 'Insurer/Takaful operator',
  'financial.certificate.deathBenefit': 'Death benefit (RM)',
  'financial.certificate.disabilityBenefit': 'Disability benefit (RM)',
  'financial.certificate.criticalIllnessBenefit':
    'Critical illness benefit (RM)',
  'financial.certificate.otherBenefit': 'Other benefit (RM) (optional)',
  'financial.certificate.contribution': 'Contribution (RM)',
  'financial.certificate.frequency': 'Frequency',
  'financial.certificate.maturityDate': 'Maturity date',
  'financial.certificate.removeConfirm.title': 'Are you sure to remove?',
  'financial.certificate.removeConfirm.description':
    'Filled information under the existing certificate will be removed.',
  'financial.information.title': 'Financial details',
  'financial.incomeProtection': 'Income protection',
  'financial.incomeProtection.1':
    '1. Annual Income needed in the event of death or disability',
  'financial.incomeProtection.2':
    '2. Years needed for this annual income to be replace',
  'financial.incomeProtection.3': '3. Existing Life Insurance / Takaful plan',
  'financial.incomeProtection.4': '4. Additional amount of protection required',
  'financial.incomeProtection.additionalAmountRequired':
    'Additional amount of protection required',
  'financial.retirementIncome': 'Retirement income',
  'financial.retirementIncome.1': '1. No. of years to retirement',
  'financial.retirementIncome.2': '2. Annual Income needed during retirement',
  'financial.retirementIncome.3':
    '3. Years needed for this annual income to be replace',
  'financial.retirementIncome.4': '4. Existing retirement income',
  'financial.retirementIncome.5':
    '5. Do you have other sources of income that you expect to rely on during retirement?',
  'financial.retirementIncome.5.1':
    '5.1. Other sources of income to reply on during retirement',
  'financial.retirementIncome.additionalAmountRequired':
    'Additional amount of retirement required',
  'financial.childEducation': 'Child education',
  'financial.childEducation.first': 'First child’s information',
  'financial.childEducation.second': 'Second child’s information',
  'financial.childEducation.1': '1. Name of child (optional)',
  'financial.childEducation.2': '2. Age',
  'financial.childEducation.3': '3. Number of year to tertiary education',
  'financial.childEducation.4': '4. Existing child education fund',
  'financial.childEducation.5':
    '5. Additional amount needed for tertiary education',
  'financial.childEducation.amountNeeded':
    'Additional amount needed for tertiary education',
  'financial.addChildEducation': "Add child's education",
  'financial.savings': 'Savings',
  'financial.savings.1': '1. Expected savings needed',
  'financial.savings.2': '2. Years needed to meet the expected savings',
  'financial.savings.amountNeeded':
    'Amount to be allocated for saving per month',
  'financial.investments': 'Investments',
  'financial.investments.1': '1. Initial amount you intend to invest',
  'financial.investments.2': '2. Regular amount you intend to invest',
  'financial.investments.3': '3. Investment duration',
  'financial.investments.4': '4. Expected monthly pay-out',
  'financial.investments.5': '5. Payout period',
  'financial.customerPriority': 'Customer priority {{priorityNumber}}',
  'financial.medicalPlanning': 'Medical/Healthcare planning',
  'financial.medicalPlanning.1':
    '1. Amount needed in the event of hospitalization/ diagnosis of critical illness',
  'financial.medicalPlanning.2': '2. Existing protection available',
  'financial.medicalPlanning.3': '3. Protection Expiry Age',
  'financial.medicalPlanning.4':
    '4. Existing protection available with current employer',
  'financial.medicalPlanning.amountNeeded':
    'Additional amount required for medical planning',
  'financial.medicalPlanning.medicalPlanning': 'Medial planning',
  'financial.medicalPlanning.criticalIllness': 'Critical illness',
  'financial.medicalPlanning.MHITHint':
    'Following your medical & health selections in the benefits illustration, please answer these questions.',
  'financial.planning.title': 'Financial Planning',
  'financial.planning.description':
    'How much do you and other (adult) wish to allocate to meet your financial needs?',
  'financial.planning.note':
    'Note: Amount available for Takaful (recommended 5% of gross income)',
  home: 'Home',
  yes: 'Yes',
  no: 'No',
  'customerPreference.title': 'Customer preference',
  'personalDetails.title': 'Personal details',
  'financialStatement.title': 'Financial statement',
  'recordOfAdvice.title': 'Record of advice',
  'confirmationOfAdvice.title': 'Confirmation of advice',
  'customerPreference.IncomeProtection': 'Income Protection',
  'customerPreference.IncomeProtection.tooltip':
    'Protecting you and your family against loss of income in the event of death, emergency and yourself against disability and critical illness',
  'customerPreference.RetirementIncome': 'Retirement Income',
  'customerPreference.RetirementIncome.tooltip':
    'Financial planning for old age/retirement',
  'customerPreference.ChildEducation': "Child's Education",
  'customerPreference.ChildEducation.tooltip':
    'Financial planning for children’s education',
  'customerPreference.Savings': 'Savings',
  'customerPreference.Savings.tooltip': 'Regular savings for the future',
  'customerPreference.Investments': 'Investments',
  'customerPreference.Investments.tooltip': 'Regular/lump sum investment',
  'customerPreference.MedicalPlanning': 'Medical / Healthcare Planning',
  'customerPreference.MedicalPlanning.tooltip': 'Health and medical plans',
  'customerChoice.title': 'Customer’s choice',
  'customerChoice.option1':
    'I wish to disclose ALL information requested for in this form',
  'customerChoice.option2':
    'I wish to disclose PARTIAL information requested for in the form (To complete "Potential Area for Discussion" and any other sections except the section on "Record of Advice")',
  'customerChoice.option3':
    'I wish to receive product information only and DO NOT WISH to disclose any information requested for in this Form. I fully understand that in choosing to not provide sufficient information, the takaful agent may not be able to give the best suited advice.',
  'potentialAreas.title': 'Potential areas for discussions',
  'potentialAreas.dragDrop': 'Drag and drop prioritise your preference',
  'potentialAreas.priority': 'Priority',
  'potentialAreas.alreadyPlanned': 'Already planned',
  'potentialAreas.toDiscuss': 'To discuss / review',
  'priorityEmpty.drop': 'Drop your preference here for prioritization',
  'agreement.title.en': 'Agreement to start Customer Fact Find',
  'agreement.title.my': 'Agreement to start Pencarian Fakta Pelanggan',
  'agreement.consent.title': 'Consents',
  'agreement.important': 'Important notice',
  'agreement.1':
    'Your takaful intermediary must have sufficient information before making a suitable recommendation. The information that you provide will be basis on which advice will be given.',
  'agreement.2':
    'If you choose not to provide all relevant information requested, your takaful intermediary may not be able to provide you suitable advice and as a result, you may risk making a financial commitment to a family takaful plan inappropriate to your needs.',
  'agreement.3':
    'Your takaful intermediary is required to preserve the confidentiality of information disclosed by you and restrict the use of such information only for the purpose of recommending family takaful products.',
  'agreement.4':
    'You must ensure that important information regarding the plan is disclosed to you and that you understand the information disclosed. Where there is ambiguity, you should seek an explanation from the takaful intermediary or the takaful operator.',
  'agreement.5':
    'Prior to making a decision to participate in any family takaful plan, you must satisfy yourself that the plan best meets your takaful need and resources.',
  'agreement.disclosure.title': 'Disclosure Of Takaful Intermediary’s Status',
  'agreement.disclosure.content':
    'I am takaful intermediary who represents FWD Takaful and can advise you on the individual Family Takaful products of FWD Takaful. I receive remuneration from FWD Takaful for providing advice on / promoting of their takaful products.',
  'btn.english': 'English',
  'btn.takaful': 'Bahasa',
  cancel: 'Cancel',
  agree: 'I agree',
  more: 'More',
  close: 'Close',
  'record.title': 'Suggest at least 2 suitable plans to your customer',
  'record.recommend.solution': 'Recommended solution - Priority {{number}}',
  'record.person.coverage': 'Name of Person Covered',
  'record.product.type': 'Product type',
  'record.payment.frequency': 'Frequency of payment',
  'record.contribution': 'Contribution (RM)',
  'record.sum.covered': 'Sum covered (RM)',
  'record.term': 'Coverage term (years)',
  'record.additional': 'Additional benefits (optional)',
  'record.participate': 'Participation',
  'record.recommend': 'Reason for recommending (can select more than 1 reason)',
  'record.reason1': 'Reason 1',
  'record.reason2': 'Reason 2 (optional)',
  'record.reason3': 'Reason 3 (optional)',
  'record.another.reason': 'Please specify the other reason',
  'record.details': 'Details',
  'record.productPreview.title': 'Life Base Plan + Riders',
  next: 'Next',
  save: 'Save',
  remove: 'Remove',
  accept: 'Accept',
  'add.recommendation': 'Add recommendation',
  'record.delete.confirmation': 'Are you sure to remove?',
  'record.delete.confirmation.content':
    'Filled information under the recommendation will be removed.',
  'record.action.title.old':
    'Action taken if different from recommendations and the reasons',
  'record.action.content1.old':
    'Customer is aware that the product recommended is not aligned with his/her takaful needs and resources.',
  'record.action.title': 'Did our recommendations align with customer needs?',
  'record.action.content1':
    "The recommended plan aligned with the customer's Takaful needs.",
  'record.action.match': 'Yes, they did align',
  'record.action.missMatch': "No, they didn't align",
  'record.action.content1.reason.title': 'Please select one:',
  'record.action.content1.reason.other': 'Please specify the reason ',
  'record.action.content1.reason1':
    'Prefers a Takaful plan with returns if surrendered or reaches maturity',
  'record.action.content1.reason2':
    'Prefers to have some form of investment in a Takaful certificate',
  'record.action.content1.reason3': 'Recommended plan is too expensive',
  'record.action.content1.reason4':
    'Prefers a shorter contribution term commitment',
  'record.action.content1.reason5':
    'Prefers higher coverage amount and /or longer coverage period plan with lower payment',
  'record.action.content1.reason6':
    'Other plan features are more relevant to customer',
  'record.action.content1.reason7': 'Other reasons',
  'record.action.content2':
    'Customer is aware that the product recommended is not aligned with his/her Takaful needs and resources.',
  'record.action.content2.old':
    'Not applicable as the product recommended to customer is aligned with his/her takaful needs and resources.',
  confirm: 'Confirm',
  gender: 'Gender',
  open: 'Open',
  male: 'Male',
  female: 'Female',
  email: 'Email',
  smokingHabit: 'Smoking habit',
  smoker: 'Smoker',
  nonSmoker: 'Non-Smoker',
  address: 'Address',
  titleDeleteDialog: 'Are you sure to remove?',
  first: 'First',
  second: 'Second',
  third: 'Third',
  fourth: 'Fourth',
  fifth: 'Fifth',
  nthChildInformation: '{{position}} child’s information',
  childInformation: '{{childName}}’s information',
  contentDeleteDialog:
    'Filled information under the {{position}} child will be removed.',
  'personalDetails.participantTitle':
    'Personal details (Proposed participants)',
  'personalDetails.menu.personal': 'Personal participants’s details',
  'personalDetails.menu.spouse': 'Spouse’s details',
  'personalDetails.menu.childDependent': 'Child’s details',
  'personalDetails.remove.spouse': 'Remove spouse’s details',
  'personalDetails.remove.childDependent': 'Remove child’s details',
  'personalDetails.title.correspondenceAddress': 'Correspondence address',
  'personalDetails.title.residentialAddress': 'Residential address',
  'personalDetails.title.businessAddress': 'Business address',
  'personalDetails.button.addChild': 'Add child',
  'personalDetails.form.title': 'Title',
  'personalDetails.form.maritalStatus': 'Marital status',
  'personalDetails.form.dateOfBirth': 'Date of birth',
  'personalDetails.form.dateOfBirth.hint': 'DD/MM/YYYY',
  'personalDetails.form.age': 'Age',
  'personalDetails.form.primaryIdType': 'ID Type',
  'personalDetails.form.additionalIDType': 'Additional ID Type (optional)',
  'personalDetails.form.additionalIDType.required': 'Additional ID Type',
  'personalDetails.form.source': 'Lead source',
  'personalDetails.form.fullName': 'Full name',
  'personalDetails.form.race': 'Race',
  'personalDetails.form.religion': 'Religion',
  'personalDetails.form.identificationNumber': 'Identification number',
  'personalDetails.form.additionalIdentification':
    'Additional identification number (optional)',
  'personalDetails.form.additionalIdentification.required':
    'Additional identification number',
  'personalDetails.nationalityTitle': 'Nationality details',
  'personalDetails.form.nationality': 'Nationality',
  'personalDetails.form.countryOfBirth': 'Place of birth - Country',
  'personalDetails.form.stateOfBirth': 'Place of birth - State',
  'personalDetails.form.cityOfBirth': 'Place of birth - City',
  'personalDetails.form.cityName': 'City name',
  'personalDetails.occupationTitle': 'Occupation details',
  'personalDetails.form.occupation': 'Occupation',
  'personalDetails.form.nameOfBusiness': 'Name of business/Employer',
  'personalDetails.form.exactDuties': 'Exact duties',
  'personalDetails.form.annualIncomeAmount': 'Annual income amount (RM)',
  'personalDetails.form.natureOfWork': 'Nature of work/Business',
  'personalDetails.form.annualIncome': 'Annual income',
  'personalDetails.form.annualIncomeOptional': 'Annual income (optional)',
  'personalDetails.form.reminder': 'Ensure income declared matches amount in Bank Suitability Assessment Report (SAR)',
  'personalDetails.contactTitle': 'Contact details',
  'personalDetails.form.countryCode': 'Code',
  'personalDetails.form.mobileNumber': 'Mobile number',
  'personalDetails.form.homePhone': 'Home phone number (optional)',
  'personalDetails.form.faxNumber': 'Fax number (optional)',
  'personalDetails.form.officePhone': 'Office phone number (optional)',
  'personalDetails.addressInfoTitle': 'Address Information',
  'personalDetails.form.addressLine1': 'Address line 1 ',
  'personalDetails.form.SubAddressLine1':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'personalDetails.form.addressLine2': 'Address line 2 (optional)',
  'personalDetails.form.SubAddressLine2': 'Street no./Street Name',
  'personalDetails.form.addressLine3': 'Address line 3 (optional)',
  'personalDetails.form.postCode': 'Postcode',
  'personalDetails.form.city': 'City',
  'personalDetails.form.state': 'State',
  'personalDetails.form.country': 'Country',
  'personalDetails.form.correspondenceAddress': 'Correspondence address',
  'personalDetails.form.correspondenceAddress.optional':
    'Correspondence address (optional)',
  'personalDetails.form.residentialAddress': 'Residential address',
  'personalDetails.form.residentialAddress.optional':
    'Residential address (optional)',
  'personalDetails.form.businessAddress': 'Business address',
  'personalDetails.form.businessAddress.optional':
    'Business address (optional)',
  'personalDetails.form.nameOfChild': 'Name of child / dependent',
  'personalDetails.form.nameOfChild.optional':
    'Name of child / dependent (optional)',
  'personalDetails.form.shortYearOld': 'y.o.',
  'personalDetails.form.relationship': 'Relationship',
  'personalDetails.form.yearToSupport': 'Year to support',
  'personalDetails.form.occupationChild': 'Occupation',
  'personalDetails.correspondenceAddress.certificate':
    'Same as Certificate Owner',
  'personalDetails.correspondenceAddress.new': 'New Address',
  'personalDetails.residentialAddress.correspondence':
    'Same as Correspondence Address',
  'personalDetails.residentialAddress.new': 'New Address',
  'personalDetails.payor.correspondenceAddress.new': 'New Address Details',
  'personalDetails.payor.residentialAddress.new': 'New Address Details',
  'personalDetails.payor.businessAddress.new': 'New Address Details',
  'personalDetails.removingSectionConfirmation': 'Are you sure to remove?',
  'personalDetails.removingSectionConfirmation.descForChildSection':
    'Filled information under the child section cannot be recovered.',
  'personalDetails.removingSectionConfirmation.descForSpouseSection':
    'Filled information under the spouse section cannot be recovered.',
  'personalDetails.submit.failed':
    'Failed to save data.\nPlease try again by clicking on Next button and your data should be saved successfully.',
  'confirmationOfAdvice.recommendations': 'Recommendations',
  'confirmationOfAdvice.confirmationStatement':
    'Confirm of advice given to **{{ownerName}}** (Name of Customer) by **{{agentName}}** (Name of Intermediary)',
  'confirmationOfAdvice.prioritization':
    'Based on the information provided in the Customer Fact Find Form, if applicable, we have concluded the prioritised financial goals of the named customer are as follows:',
  'confirmationOfAdvice.priorityOrderAndFinancialGoals':
    'Customer priority order & financial goals',
  'confirmationOfAdvice.priorityOrderAndFinancialGoals.note':
    'Note: Ranking of 1-6; where “1” means most important & “6” means least important',
  'confirmationOfAdvice.priorityOrderAndFinancialGoals.priority':
    'Priority {{position}}',
  'confirmationOfAdvice.recommendations.description':
    'In order to meet these goals, the following products have been recommended for {{ownerName}} (Name of Customer) to participate',
  'confirmationOfAdvice.recommendations.order': 'Recommendation {{position}}',
  'confirmationOfAdvice.recommendations.personCoverage':
    'Name of Person Covered',
  'confirmationOfAdvice.recommendations.productType': 'Product type',
  'confirmationOfAdvice.recommendations.paymentFrequency':
    'Frequency of payment',
  'confirmationOfAdvice.recommendations.contribution': 'Contribution',
  'confirmationOfAdvice.recommendations.sumCovered': 'Sum covered',
  'confirmationOfAdvice.recommendations.paymentTerm': 'Coverage term',
  'confirmationOfAdvice.recommendations.paymentTerm.years':
    '{{paymentTerm}} year(s)',
  'confirmationOfAdvice.recommendations.additionalBenefits':
    'Additional benefits',
  'confirmationOfAdvice.recommendations.participation': 'Participation',
  'confirmationOfAdvice.recommendations.NA': 'N/A',
  previewDocument: 'Preview documents',
  startApplication: 'Start application',
  'customerfactfind.annual': 'Annual',
  'customerfactfind.semi': 'Semi-Annual',
  'customerfactfind.quarterly': 'Quarterly',
  'customerfactfind.monthly': 'Monthly',
  singlePremium: 'Single',
  'customerfactfind.investLink': 'Investment Link',
  'customerfactfind.takafulFutureSelectPlus': 'Takaful Future Select Plus',
  'customerfactfind.takafulFutureEducation': 'Takaful Future Education',
  'customerfactfind.takafulFutureDefender': 'Takaful Future Defender',
  'customerfactfind.lifeSelectSingle': 'Takaful LifeSelect Single',
  'customerfactfind.takafulFutureSecure': 'Takaful FutureSecure',
  'customerfactfind.lifestyleprotectorplus': 'Takaful Lifestyle Protector Plus',
  'customerfactfind.lifestyleprotectorpluspremier':
    'Takaful Lifestyle Protector Plus (Premier)',
  'customerfactfind.incomeFirst': 'FWD Income First',
  'customerfactfind.familyFirst': 'FWD Family First',
  'customerfactfind.ciFirst': 'FWD CI First',
  'customerfactfind.investFirst': 'FWD Invest First',
  'customerfactfind.investFirstPlus': 'FWD Invest First Plus',
  'customerfactfind.futureFirst': 'FWD Future First',
  'customerfactfind.lifeFirst': 'FWD Life First',
  'existingcertificate.frequencypayment.monthly': 'Monthly',
  'existingcertificate.frequencypayment.annual': 'Annual',
  'existingcertificate.frequencypayment.quarterly': 'Quarterly',
  'existingcertificate.frequencypayment.semiannual': 'Semi-annual',
  'existingcertificate.typeofplans.accident': 'Accident',
  'existingcertificate.typeofplans.criticalillness': 'Critical illness',
  'existingcertificate.typeofplans.health': 'Health',
  'existingcertificate.typeofplans.investmentlinked': 'Investment linked',
  'existingcertificate.typeofplans.term': 'Term',
  'idTypeCd.nricNew': 'New NRIC',
  'idTypeCd.passport': 'Passport',
  'idTypeCd.nricOld': 'Old NRIC',
  'idTypeCd.birthCertificate': 'Birth Certificate',
  'maritalStatusCode.divorced': 'Divorced',
  'maritalStatusCode.married': 'Married',
  'maritalStatusCode.single': 'Single',
  'maritalStatusCode.widowed': 'Widowed',
  'leadSourceCode.referredByCompany': 'Referred by company',
  'leadSourceCode.selfSource': 'Self source',
  'labelSourceCode.recommendedByAnotherCompany':
    'Recommended by another company',
  'labelSourceCode.referredByBusinessBanking':
    'Referred by Retail Business Banking',
  'ethnicityCd.chinese': 'Chinese',
  'ethnicityCd.indian': 'Indian',
  'ethnicityCd.malay': 'Malay',
  'ethnicityCd.others': 'Others',
  'religionCd.buddhist': 'Buddhist',
  'religionCd.christianity': 'Christianity',
  'religionCd.hinduism': 'Hinduism',
  'religionCd.islam': 'Islam',
  'religionCd.others': 'Others',
  'customerfactfind.childrenEducationTitle': "Children's Education",
  'customerfactfind.saving': 'Savings',
  'customerfactfind.other': 'Others (to specify)',
  'occupationDetails.occupationClass': 'Occupation class',
  'occupationDetails.occupationDescription': 'Occupation description',
  'validation.error.required': 'This field is required',
  'validation.error.duplicate': 'The reason can not be same',
  'validation.error.invalidAnnualIncomeAmount':
    'Value should be greater than 200,000',
  'validation.error.minLength10': 'Minimum length is 10',
  'title.cik': 'Cik',
  'title.datin': 'Datin',
  'title.datinPaduka': 'Datin Paduka',
  'title.dato': 'Dato',
  'title.datoSri': 'Dato Sri',
  'title.datuk': 'Datuk',
  'title.datukSri': 'Datuk Sri',
  'title.encik': 'Encik',
  'title.hajah': 'Hajah',
  'title.haji': 'Haji',
  'title.mr': 'Mr.',
  'title.mrs': 'Mrs.',
  'title.ms': 'Ms',
  'title.puan': 'Puan',
  'title.tanSri': 'Tan Sri',
  'pdf.cff.title': 'Please review CFF Document',
  'pdf.cff.fileName': 'CFF Document',
  incompleteFields_other: '{{count}} incomplete fields',
  incompleteFields_one: '{{count}} incomplete field',
  goToTheField: 'Go to the field',
  'vulnerableConsumerDeclaration.title': 'Vulnerable Consumer Declaration',
  'vulnerableConsumerDeclaration.isCustomerVulnerable':
    'Is your customer a Vulnerable Consumer?',
  'vulnerableConsumerDeclaration.vulnerableQuestion':
    'How would you describe your customer?',
};
