import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { G, Path } from 'react-native-svg';

export default function ErrorSVG(props: SvgIconProps) {
  return (
    <Svg width={72} height={105} viewBox="0 0 72 105" {...props}>
      <G clip-Path="url(#clip0_30065_268145)">
        <Path
          d="M26.0471 86.5234H45.9236C48.4082 86.5234 50.425 88.5449 50.425 91.035V95.3122H21.5457V91.035C21.5457 88.5449 23.5625 86.5234 26.0471 86.5234Z"
          fill="#E87722"
        />
        <Path
          d="M13.9459 95.3125H58.0542C60.5388 95.3125 62.5556 97.3339 62.5556 99.824V104.101H9.44446V99.824C9.44446 97.3339 11.4613 95.3125 13.9459 95.3125Z"
          fill="#F3BB91"
        />
        <Path
          d="M39.9314 46.6523H32.0393V86.5239H39.9314V46.6523Z"
          fill="#F3BB91"
        />
        <Path
          d="M1.25991 52.8344L28.4439 4.52571C31.747 -1.36274 40.2237 -1.36274 43.5267 4.52571L70.74 52.8344C73.9845 58.6057 69.8338 65.7832 63.1986 65.7832H8.80129C2.16605 65.7832 -1.98463 58.635 1.25991 52.8344Z"
          fill="#E87722"
        />
        <Path
          d="M13.215 51.6617L33.53 15.5986C34.6115 13.6651 37.3591 13.6651 38.4406 15.5986L58.7556 51.6617C59.8079 53.5366 58.4633 55.8803 56.3003 55.8803H15.6996C13.5366 55.8803 12.1627 53.5366 13.2443 51.6617H13.215Z"
          fill="white"
        />
        <Path
          d="M35.9854 44.925C34.6116 44.925 33.4716 43.9582 33.4132 42.6985L32.5947 26.2928C32.507 24.5351 34.0562 23.0703 35.9854 23.0703C37.9146 23.0703 39.4638 24.5351 39.3761 26.2928L38.5577 42.6985C38.4992 43.9289 37.3592 44.925 35.9854 44.925Z"
          fill="#183028"
        />
        <Path
          d="M36.0146 52.7185C37.6451 52.7185 38.9669 51.3544 38.9669 49.6718C38.9669 47.9891 37.6451 46.625 36.0146 46.625C34.3841 46.625 33.0624 47.9891 33.0624 49.6718C33.0624 51.3544 34.3841 52.7185 36.0146 52.7185Z"
          fill="#183028"
        />
      </G>
    </Svg>
  );
}
