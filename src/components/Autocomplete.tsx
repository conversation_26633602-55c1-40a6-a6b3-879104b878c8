import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  Keyboard,
  Pressable,
  ViewProps,
  LayoutChangeEvent,
  useWindowDimensions,
  StyleProp,
  ViewStyle,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { find } from 'lodash';
import {
  Box,
  Row,
  TextField,
  Icon,
  LargeLabel,
  Text,
  TextFieldRef,
  TextFieldProps,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import useLatest from 'hooks/useLatest';
import CFFModal from '../features/customerFactFind/components/modals/CFFModal';
import { TextStyle } from 'react-native';
import { t } from 'i18next';

export interface AutocompleteProps<T, V> extends ViewProps {
  label?: string;
  disabled?: boolean;
  placeholder?: string;
  hint?: string;
  error?: string;
  isError?: boolean;
  value?: V;
  defaultValue?: V;
  emptyLabel?: string;
  onChange?: (value: V | null) => void;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  getItemDisabled?: (item: T) => boolean;
  getDisplayedLabel?: (item: T) => string;
  keyExtractor?: (item: T) => string;
  searchable?: boolean;
  multiline?: boolean;
  autoExpand?: boolean;
  numberOfLines?: number;
  onFocus?: () => void;
  onBlur?: () => void;
  modalStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  renderInput?: (
    props: Partial<Omit<TextFieldProps, 'value'> & { value?: string }>,
  ) => React.ReactElement<
    Partial<Omit<TextFieldProps, 'value'> & { value?: string }>
  >;
  highlight?: boolean;
  required?: boolean;
}

export type AutocompleteRef = TextFieldRef;

const AutocompleteInner = <T, V>(
  {
    label,
    disabled,
    hint,
    placeholder,
    error,
    isError,
    value,
    defaultValue,
    emptyLabel,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    getItemDisabled,
    getDisplayedLabel,
    keyExtractor,
    searchable,
    multiline,
    autoExpand,
    numberOfLines,
    inputStyle,
    onFocus,
    onBlur,
    modalStyle,
    onLayout: onLayoutProp,
    renderInput,
    highlight,
    required,
    ...viewProps
  }: AutocompleteProps<T, V>,
  ref: React.ForwardedRef<AutocompleteRef>,
) => {
  const { space, colors, elevation, borderRadius } = useTheme();
  const dropdownRef = useRef<View>(null);
  const inputRef = useRef<TextFieldRef>(null);
  const flatListRef = useRef<FlatList>(null);
  const getItemLabelRef = useLatest(getItemLabel);
  const getItemValueRef = useLatest(getItemValue);
  const getItemDisabledRef = useLatest(getItemDisabled);
  const getDisplayedLabelRef = useLatest(getDisplayedLabel);
  const [dropdownSize, setDropdownSize] = useState({ width: 0, height: 0 });
  const [inputSize, setInputSize] = useState({ width: 0, height: 0 });
  const [dropDownVisible, setDropDownVisible] = useState(false);
  const [position, setPosition] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);

  const openDropdown = () => {
    dropdownRef.current?.measure((x, y, w, h, px, py) => {
      setPosition({ x: px, y: py, width: w, height: h });
      inputRef.current?.focus();
      setSearchString('');
      onFocus?.();
      setDropDownVisible(true);
    });
  };

  const closeDropdown = () => {
    setDropDownVisible(false);
  };

  const selectedItem = useMemo(() => {
    return find(data, item => getItemValueRef.current(item) === value);
  }, [data, getItemValueRef, value]);

  const [searchString, setSearchString] = useState<string | undefined>(() => {
    return selectedItem
      ? (getItemLabelRef.current(selectedItem) as string) || ''
      : '';
  });

  const selectedLabel = useMemo(() => {
    const label = selectedItem
      ? getDisplayedLabelRef.current
        ? getDisplayedLabelRef.current(selectedItem)
        : (getItemLabelRef.current(selectedItem) as string)
      : emptyLabel || '';
    return label || '';
  }, [getDisplayedLabelRef, getItemLabelRef, selectedItem, emptyLabel]);

  const onClear = useCallback(() => {
    setSearchString('');
  }, []);

  const filterData = useMemo(() => {
    if (!searchString || !searchable) {
      return data;
    }
    const searchLowerCase = searchString.toLowerCase();
    return data.filter(
      item =>
        getItemLabelRef.current(item) &&
        (getItemLabelRef.current(item) as string)
          ?.toLowerCase?.()
          ?.indexOf?.(searchLowerCase) >= 0,
    );
  }, [data, getItemLabelRef, searchString, searchable]);

  useEffect(() => {
    if (dropDownVisible) {
      if (selectedItem && data) {
        const selectedIndex = data.findIndex(
          item => getItemValue(item) === getItemValue(selectedItem),
        );
        if (selectedIndex && selectedIndex > 0) {
          setTimeout(() => {
            flatListRef.current?.scrollToIndex({
              index: selectedIndex,
              viewPosition: 0.1,
            });
          }, 0);
        }
      }
    }
  }, [dropDownVisible, selectedItem, data, getItemValue]);

  const onBackdropPress = useCallback(() => {
    Keyboard.dismiss();
    closeDropdown();
    onBlur?.();
  }, [onBlur]);

  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      onLayoutProp?.(e);
    },
    [onLayoutProp],
  );

  const { height: screenHeight, width: screenWidth } = useWindowDimensions();
  const hasReachedBottom = position
    ? position.height + position.y + dropdownSize.height > screenHeight
    : false;

  const hasReachedRight = position
    ? position.x + dropdownSize.width > screenWidth
    : false;

  const renderDropdown = () => {
    return (
      <>
        <CFFModal
          visible={dropDownVisible}
          backdropColor="transparent"
          contentContainerStyle={styles.flex1}>
          <Backdrop onPress={onBackdropPress}>
            {position !== null && (
              <Animated.View
                style={[
                  {
                    position: 'absolute',
                    top: position.y,
                    left: position.x,
                    width: position.width,
                  },
                ]}>
                <TextFieldContainer
                  onLayout={e => setInputSize(e.nativeEvent.layout)}>
                  {renderInput ? (
                    renderInput({
                      error,
                      value: selectedLabel,
                    })
                  ) : (
                    <TextField
                      ref={inputRef}
                      label={label}
                      value={searchable ? searchString : selectedLabel}
                      onChangeText={text => {
                        if (searchable) {
                          setSearchString(text);
                        }
                      }}
                      autoFocus={Boolean(searchable)}
                      editable={Boolean(searchable)}
                      right={
                        <RightActionContainer>
                          {searchable && (
                            <>
                              <Box w={space[4]} />
                              <TouchableOpacity
                                onPress={onClear}
                                hitSlop={ICON_HIT_SLOP}>
                                <Icon.CloseCircle />
                              </TouchableOpacity>
                            </>
                          )}
                          <Box w={space[4]} />
                          <Icon.Dropdown />
                        </RightActionContainer>
                      }
                      multiline={multiline}
                      autoExpand={autoExpand}
                      numberOfLines={numberOfLines}
                      inputStyle={inputStyle}
                      highlight={highlight}
                    />
                  )}
                </TextFieldContainer>
                <DropdownContainer
                  onLayout={e => {
                    setDropdownSize(e.nativeEvent.layout);
                  }}
                  style={[
                    dropDownVisible && elevation[3],
                    {
                      transform: [
                        {
                          translateY: hasReachedBottom
                            ? -(inputSize.height + dropdownSize.height)
                            : 0,
                        },
                        {
                          translateX: hasReachedRight
                            ? -(dropdownSize.width - inputSize.width)
                            : 0,
                        },
                      ],
                    },
                    hasReachedBottom && {
                      borderTopRightRadius: borderRadius.small,
                      borderTopLeftRadius: borderRadius.small,
                      borderBottomRightRadius: 0,
                      borderBottomLeftRadius: 0,
                    },
                    modalStyle,
                  ]}>
                  {filterData.length === 0 ? (
                    <Row px={space[4]} py={space[2]}>
                      <Box flex={1}>
                        <LargeLabel>{t('autoComplete.noData')}</LargeLabel>
                      </Box>
                    </Row>
                  ) : (
                    <ItemList
                      ref={flatListRef}
                      data={filterData}
                      keyboardShouldPersistTaps="handled"
                      getItemLayout={(item, index) => ({
                        length: 36,
                        offset: 36 * index,
                        index,
                      })}
                      renderItem={props => {
                        const item = props.item as T;
                        const itemLabel = getItemLabelRef.current(item);
                        const itemValue = getItemValueRef.current(item);
                        const selected = selectedItem
                          ? itemValue === getItemValueRef.current(selectedItem)
                          : false;

                        const disabled = getItemDisabledRef?.current?.(item);
                        const quoteMeta =
                          searchString?.replace(
                            /[-/\\^$*+?.()|[\]{}]/g,
                            '\\$&',
                          ) ?? '';
                        const regex = new RegExp(quoteMeta, 'gi');
                        const matchParts = searchString
                          ? (itemLabel as string).match(regex) || []
                          : [];
                        const parts =
                          matchParts.length > 0
                            ? (itemLabel as string).split(matchParts[0])
                            : [itemLabel];

                        return (
                          <Pressable
                            disabled={!!disabled}
                            onPress={() => {
                              if (selected && !required) {
                                if (defaultValue !== undefined) {
                                  onChange?.(defaultValue);
                                } else {
                                  if (typeof itemValue === 'string') {
                                    onChange?.('' as V);
                                  } else {
                                    onChange?.(null);
                                  }
                                }
                              } else {
                                closeDropdown();
                                onChange?.(itemValue);
                              }
                              setTimeout(() => {
                                Keyboard.dismiss();
                                onBlur?.();
                              }, 100);
                            }}>
                            <Row
                              alignItems="center"
                              px={space[4]}
                              py={space[2]}
                              style={{ opacity: disabled ? 0.5 : 1 }}>
                              <Box flex={1}>
                                {searchString ? (
                                  <LargeLabel>
                                    {parts.map((p, i) => {
                                      return (
                                        <Text key={i}>
                                          <Text>{p as string}</Text>
                                          {parts.length > 1 &&
                                            i < parts.length - 1 && (
                                              <Text
                                                color={
                                                  i === 0
                                                    ? colors.primary
                                                    : undefined
                                                }>
                                                {matchParts[0]}
                                              </Text>
                                            )}
                                        </Text>
                                      );
                                    })}
                                  </LargeLabel>
                                ) : (
                                  <LargeLabel>{itemLabel as string}</LargeLabel>
                                )}
                              </Box>
                              {selected && (
                                <Box pl={space[4]}>
                                  <Icon.Tick />
                                </Box>
                              )}
                            </Row>
                          </Pressable>
                        );
                      }}
                      keyExtractor={item =>
                        (keyExtractor
                          ? keyExtractor(item as T)
                          : getItemValueRef.current(item as T)) as string
                      }
                    />
                  )}
                </DropdownContainer>
              </Animated.View>
            )}
          </Backdrop>
        </CFFModal>
      </>
    );
  };

  return (
    <>
      <View
        ref={dropdownRef}
        collapsable={false}
        onLayout={onLayout}
        {...viewProps}>
        {renderInput ? (
          renderInput({
            // @ts-expect-error 'ref' does not exist
            ref: ref,
            label: label,
            value: selectedLabel,
            disabled: disabled,
            right: disabled ? null : <Icon.Dropdown />,
            hint: hint,
            placeholder: placeholder,
            error: error,
            isError: isError,
            showSoftInputOnFocus: false,
            multiline: multiline,
            autoExpand: autoExpand,
            numberOfLines: numberOfLines,
            inputStyle: inputStyle,
            highlight: highlight,
            onFocus: () => {
              Keyboard.dismiss();
              openDropdown();
            },
            onBlur: Keyboard.dismiss,
          })
        ) : (
          <TextField
            ref={ref}
            label={label}
            value={selectedLabel}
            disabled={disabled}
            right={disabled ? null : <Icon.Dropdown />}
            hint={hint}
            placeholder={placeholder}
            error={error}
            isError={isError}
            showSoftInputOnFocus={false}
            multiline={multiline}
            autoExpand={autoExpand}
            numberOfLines={numberOfLines}
            inputStyle={inputStyle}
            highlight={highlight}
            onFocus={() => {
              Keyboard.dismiss();
              openDropdown();
            }}
            onBlur={Keyboard.dismiss}
          />
        )}
        <Pressable
          disabled={disabled}
          onPress={openDropdown}
          style={StyleSheet.absoluteFill}
        />
      </View>
      {renderDropdown()}
    </>
  );
};

const Autocomplete = forwardRef(AutocompleteInner) as <T, V>(
  props: AutocompleteProps<T, V> & {
    ref?: React.ForwardedRef<AutocompleteRef>;
  },
) => ReturnType<typeof AutocompleteInner>;

export default Autocomplete;

const Backdrop = styled.Pressable(() => ({
  flex: 1,
}));

const TextFieldContainer = styled.View(() => ({
  zIndex: 10,
}));

const RightActionContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  height: '100%',
  alignItems: 'center',
}));

const DropdownContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  paddingVertical: theme.space[2],
  borderBottomRightRadius: theme.borderRadius.small,
  borderBottomLeftRadius: theme.borderRadius.small,
}));

const ItemList = styled(FlatList)(({ theme }) => ({
  flexGrow: 0,
  maxHeight: theme.sizes[46],
}));

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
});
