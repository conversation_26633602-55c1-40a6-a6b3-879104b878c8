import React, {
  useRef,
  useState,
  useMemo,
  useEffect,
  useCallback,
  memo,
} from 'react';
import {
  LayoutChangeEvent,
  ScrollViewProps,
  TouchableOpacity,
  StyleSheet,
  View,
} from 'react-native';
import {
  TextField,
  Icon,
  Button,
  RadioButton,
  H6,
  Box,
  Row,
  Body,
  Checkbox,
} from 'cube-ui-components';
import styled from '@emotion/native';
import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetScrollView,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import {
  HANDLE_HEIGHT,
  useBottomSheet,
} from 'features/eApp/hooks/useBottomSheet';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { useTheme } from '@emotion/react';
import { runOnJS } from 'react-native-reanimated';
import BottomSheetFooterSpace from './BottomSheetFooterSpace';
import useLatest from 'hooks/useLatest';
import { FlashL<PERSON>, ListRenderItem } from '@shopify/flash-list';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { BottomSheetDefaultFooterProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetFooter/types';
import useDebounceFn from 'features/policy/hooks/useDebounceFn';
import useKeyboardHeight from 'hooks/useKeyboardHeight';
import { ICON_HIT_SLOP } from 'constants/hitSlop';

export type SearchableDropdownPanelProps<T, V> = {
  title?: string;
  actionLabel?: string;
  listTitle?: string;
  data?: T[];
  searchable?: boolean;
  searchLabel?: string;
  onSearchTriggered?: () => void;
  onQuery?: (text: string) => void;
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  onDismiss?: () => void;
  renderItem?: <V>(props: ItemProps<V> & { item: T }) => JSX.Element;
  emptyMessage?: string | JSX.Element;
  snapMode?: 'fixed' | 'dynamic';
  estimatedItemSize?: number;
  loading?: boolean;
  onClear?: () => void;
  getItemDisabled?: (item: T) => boolean;
  keyExtractor?: (item: T) => string;
} & (
  | {
      searchMode: 'manual-trigger';
      hideListWhenEmpty?: boolean;
    }
  | {
      searchMode?: 'auto' | 'manual';
    }
) &
  (
    | {
        type: 'single';
        value?: V;
        onDone?: (value: V) => void;
      }
    | {
        type: 'multiple';
        value?: V[];
        onDone?: (value: V[]) => void;
      }
  );

export default function SearchableDropdownPanel<T, V>({
  title,
  actionLabel = 'Done',
  listTitle,
  data,
  searchable,
  searchLabel = 'Search',
  onSearchTriggered,
  onQuery: onQueryAction,
  getItemLabel,
  getItemValue,
  onDismiss: onDismissAction,
  type,
  onDone,
  renderItem: customRenderItem,
  value,
  emptyMessage = 'Adjust keyword to get better result or try another search.',
  snapMode = 'fixed',
  estimatedItemSize = 67,
  loading,
  onClear: onClearAction,
  getItemDisabled,
  keyExtractor,
  ...rest
}: SearchableDropdownPanelProps<T, V>) {
  const { searchMode, hideListWhenEmpty, autoTriggering } =
    rest.searchMode === 'manual-trigger'
      ? {
          searchMode: rest.searchMode,
          hideListWhenEmpty: rest.hideListWhenEmpty,
          autoTriggering: false,
        }
      : {
          searchMode: rest.searchMode ?? 'auto',
          hideListWhenEmpty: false,
          autoTriggering: true,
        };
  // if auto triggering = true, result is rendered by default
  // else check search triggered flag
  const [searchTriggered, setSearchTriggered] = useState(autoTriggering);
  const shouldRenderResult = autoTriggering || searchTriggered;

  const flatListRef = useRef<FlashList<T>>(null);
  const [query, setQuery] = useState('');
  const getItemLabelRef = useLatest(getItemLabel);
  const getItemValueRef = useLatest(getItemValue);
  const onDoneRef = useLatest(onDone);
  const [checkedValue, setCheckedValue] = useState(value);

  const getItemDisabledRef = useLatest(getItemDisabled);

  useEffect(() => {
    if (!Array.isArray(checkedValue) && checkedValue && data) {
      const selectedIndex = data.findIndex?.(
        item => getItemValueRef.current(item) === checkedValue,
      );
      if (selectedIndex && selectedIndex > 0) {
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index: selectedIndex,
            viewPosition: 0.1,
            animated: false,
          });
        }, 0);
      }
    }
  }, [data, checkedValue, getItemValueRef]);

  useEffect(() => {
    setCheckedValue(checkedValue);
  }, [setCheckedValue, checkedValue]);

  const onDismiss = () => {
    if (onDismissAction) {
      runOnJS(onDismissAction)();
    }
  };

  const { space, colors } = useTheme();
  const { height } = useSafeAreaFrame();
  const bottomSheetProps = useBottomSheet();

  const [footerHeight, setFooterHeight] = useState(0);
  const keyboardHeight = useKeyboardHeight();
  const [excludeListContentHeight, setExcludeListContentHeight] = useState(0);

  const onQueryActionRef = useLatest(onQueryAction);
  const onQuery = useCallback(
    (text: string) => {
      if (/manual/.test(searchMode)) {
        onQueryActionRef.current?.(text);
      }
      setQuery(text);
    },
    [onQueryActionRef, searchMode],
  );

  const onClear = useCallback(() => {
    onQuery('');
    setCheckedValue(undefined);
    onClearAction?.();
  }, [onQuery, onClearAction]);

  const filteredData = useMemo(() => {
    if (/manual/.test(searchMode)) {
      return data;
    } else {
      return data?.filter(item => {
        return String(getItemLabelRef.current(item))
          .toLowerCase()
          .includes(query.trim().toLowerCase());
      });
    }
  }, [searchMode, data, getItemLabelRef, query]);

  const hasQuery = useMemo(() => !!query.trim(), [query]);

  const shouldHideList =
    hideListWhenEmpty && !loading && filteredData?.length === 0;
  const shouldRenderOuterEmptyMessage = shouldRenderResult && shouldHideList;
  const shouldRenderList = shouldRenderResult && !shouldHideList;

  const snapPoints = useMemo(
    () =>
      snapMode === 'dynamic'
        ? [
            shouldRenderList
              ? height - space[22]
              : keyboardHeight +
                footerHeight +
                excludeListContentHeight +
                HANDLE_HEIGHT,
          ]
        : ['50%', height - space[22]],
    [
      snapMode,
      shouldRenderList,
      height,
      space,
      keyboardHeight,
      footerHeight,
      excludeListContentHeight,
    ],
  );

  const onFooterLayout = useCallback((e: LayoutChangeEvent) => {
    setFooterHeight(e.nativeEvent.layout.height);
  }, []);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <Footer
          {...props}
          style={
            isNarrowScreen
              ? styles.smallContentContainerStyle
              : styles.contentContainerStyle
          }
          actionLabel={actionLabel}
          disabled={shouldRenderList ? !checkedValue : !hasQuery}
          loading={loading}
          onLayout={onFooterLayout}
          onDone={() => {
            if (shouldRenderList) {
              if (checkedValue) {
                if (type === 'single') {
                  (onDoneRef.current as (v: V) => void)?.(checkedValue as V);
                } else {
                  (onDoneRef.current as (v: V[]) => void)?.(
                    checkedValue as V[],
                  );
                }
                bottomSheetProps.bottomSheetRef.current?.close();
              }
            } else {
              setSearchTriggered(true);
              onSearchTriggered?.();
            }
          }}
        />
      );
    },
    [
      isNarrowScreen,
      actionLabel,
      shouldRenderList,
      checkedValue,
      hasQuery,
      loading,
      onFooterLayout,
      type,
      bottomSheetProps.bottomSheetRef,
      onDoneRef,
      onSearchTriggered,
    ],
  );

  const onSelectItem = useCallback(
    (newValue: V) => {
      setCheckedValue(value => {
        if (type === 'multiple') {
          const _value = Array.isArray(value)
            ? [...value]
            : value === undefined
            ? []
            : [value];
          if (_value.find(v => v === newValue)) {
            return _value.filter(v => v !== newValue);
          } else {
            return _value.concat(newValue);
          }
        } else {
          return newValue;
        }
      });
    },
    [type],
  );

  const renderItem = useCallback<ListRenderItem<T>>(
    props => {
      const { item } = props;

      const disabled = getItemDisabledRef?.current?.(item);
      return (
        <Item<V>
          type={type}
          label={getItemLabelRef.current(item)}
          value={getItemValueRef.current(item)}
          selected={getSelected(getItemValueRef.current(item), checkedValue)}
          onSelect={onSelectItem}
          disabled={disabled}
        />
      );
    },
    [
      checkedValue,
      getItemLabelRef,
      getItemValueRef,
      onSelectItem,
      type,
      getItemDisabledRef,
    ],
  );

  const extraData = useMemo(
    () =>
      Array.isArray(checkedValue)
        ? filteredData?.filter(item =>
            checkedValue.includes(getItemValueRef.current(item)),
          )
        : filteredData?.find(
            item => checkedValue === getItemValueRef.current(item),
          )
        ? [
            filteredData?.find(
              item => checkedValue === getItemValueRef.current(item),
            ),
          ]
        : [],
    [checkedValue, filteredData, getItemValueRef],
  );

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    setExcludeListContentHeight(e.nativeEvent.layout.height);
  }, []);

  return (
    <BottomSheetModal
      {...bottomSheetProps}
      style={[bottomSheetProps.style, styles.contentContainerStyle]}
      onChange={() => {
        if (!Array.isArray(value) && value && data) {
          const selectedIndex = data.findIndex?.(
            item => getItemValueRef.current(item) === value,
          );
          if (selectedIndex && selectedIndex > 0) {
            flatListRef.current?.scrollToIndex({
              index: selectedIndex,
              animated: false,
            });
          }
        }
      }}
      onDismiss={onDismiss}
      snapPoints={snapPoints}
      index={snapMode === 'dynamic' ? 0 : 1}
      footerComponent={renderFooter}>
      <Box onLayout={onLayout} px={isNarrowScreen ? space[1] : space[2]}>
        {Boolean(title) && <Label fontWeight="bold">{title}</Label>}
        {searchable && (
          <SearchInput
            editable={autoTriggering || !searchTriggered}
            searchLabel={searchLabel}
            query={query}
            onQuery={onQuery}
            onClear={() => {
              if (!autoTriggering) {
                setSearchTriggered(false);
              }
              onClear();
            }}
          />
        )}
        {shouldRenderOuterEmptyMessage &&
          (typeof emptyMessage === 'string' ? (
            <EmptySearchResult message={emptyMessage} />
          ) : (
            emptyMessage
          ))}
      </Box>
      {shouldRenderList && (
        <>
          {Boolean(listTitle) && (
            <Box pb={space[3]} px={space[2]}>
              <Body color={colors.placeholder}>{listTitle}</Body>
            </Box>
          )}
          <FlashList
            ref={flatListRef}
            contentContainerStyle={
              isNarrowScreen
                ? styles.smallContentContainerStyle
                : styles.contentContainerStyle
            }
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="on-drag"
            data={filteredData}
            extraData={extraData}
            keyExtractor={item =>
              keyExtractor
                ? keyExtractor(item as T)
                : String(getItemValueRef.current(item))
            }
            // getItemLayout={getItemLayout}
            estimatedItemSize={estimatedItemSize}
            renderItem={data => {
              if (customRenderItem) {
                return customRenderItem({
                  type,
                  item: data.item,
                  label: getItemLabelRef.current(data.item),
                  value: getItemValueRef.current(data.item),
                  selected: getSelected(
                    getItemValueRef.current(data.item),
                    checkedValue,
                  ),
                  onSelect: onSelectItem,
                  searchText: query,
                });
              } else {
                return renderItem(data);
              }
            }}
            ItemSeparatorComponent={Separator}
            ListEmptyComponent={
              !loading && emptyMessage ? (
                typeof emptyMessage === 'string' ? (
                  <EmptySearchResult message={emptyMessage} />
                ) : (
                  emptyMessage
                )
              ) : null
            }
            ListFooterComponent={BottomSheetFooterSpace}
            renderScrollComponent={
              BottomSheetScrollView as React.ComponentType<ScrollViewProps>
            }
          />
        </>
      )}
    </BottomSheetModal>
  );
}

export const SearchInput = ({
  searchLabel,
  query: initialQuery,
  editable,
  onQuery,
  onClear,
}: {
  searchLabel?: string;
  query?: string;
  editable?: boolean;
  onQuery?: (text: string) => void;
  onClear?: () => void;
}) => {
  const [query, setQuery] = useState(initialQuery || '');
  const { space, colors, sizes } = useTheme();
  const { shouldHandleKeyboardEvents } = useBottomSheetInternal();

  const onChangeText = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const latestOnQuery = useLatest(onQuery);

  useDebounceFn(
    () => {
      latestOnQuery.current?.(query);
    },
    500,
    [query],
  );

  const onClearFn = useCallback(() => {
    setQuery('');
    onClear?.();
  }, [onClear]);

  return (
    <TextField
      style={{ marginTop: 10, marginBottom: space[4] }}
      label={searchLabel}
      left={<Icon.Search fill={colors.placeholder} />}
      value={query}
      onChangeText={onChangeText}
      onFocus={() => {
        shouldHandleKeyboardEvents.value = true;
      }}
      onBlur={() => {
        shouldHandleKeyboardEvents.value = false;
      }}
      editable={editable}
      focusable={editable}
      right={
        query ? (
          <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={onClearFn}>
            <Icon.CloseCircle fill={colors.secondary} size={sizes[5]} />
          </TouchableOpacity>
        ) : null
      }
    />
  );
};

export const EmptySearchResult = memo(({ message }: { message?: string }) => {
  const theme = useTheme();
  return (
    <Row
      padding={theme.space[2]}
      borderRadius={theme.borderRadius['x-small']}
      backgroundColor={theme.colors.palette.alertRedLight}>
      <Icon.Warning fill={theme.colors.error} />
      <Box w={theme.space[1]} />
      <Body style={{ flex: 1 }} color={theme.colors.error}>
        {message}
      </Body>
    </Row>
  );
});

const Label = styled(H6)(({ theme: { space } }) => ({
  marginVertical: space[4],
}));

const ItemContainer = styled.View(() => ({
  paddingVertical: 10,
}));

export const Separator = styled.View(({ theme: { space, colors } }) => ({
  marginVertical: space[3],
  height: 1,
  borderBottomWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
}));

export const Footer = memo(
  ({
    actionLabel,
    disabled,
    loading,
    onDone,
    onLayout,
    ...props
  }: {
    actionLabel: string;
    disabled: boolean;
    loading?: boolean;
    onDone: () => void;
    onLayout?: ((event: LayoutChangeEvent) => void) | undefined;
  } & BottomSheetDefaultFooterProps) => {
    const { bottom: bottomInset } = useSafeAreaInsets();
    const { space, colors } = useTheme();
    const { isWideScreen } = useWindowAdaptationHelpers();

    return (
      <BottomSheetFooter {...props}>
        <View
          onLayout={e => {
            onLayout?.(e);
          }}
          style={[
            {
              paddingTop: space[4],
              paddingBottom: space[4] + bottomInset,
              backgroundColor: colors.background,
              flexDirection: 'row',
              justifyContent: 'center',
            },
          ]}>
          <Button
            text={actionLabel}
            disabled={disabled}
            onPress={onDone}
            style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
            loading={loading}
          />
        </View>
      </BottomSheetFooter>
    );
  },
);

type ItemProps<V> = {
  type: 'single' | 'multiple';
  value: V;
  label: string;
  selected: boolean;
  onSelect: (value: V) => void;
  searchText?: string;
  disabled?: boolean;
};
const Item = React.memo(
  <V,>({ type, value, label, selected, onSelect, disabled }: ItemProps<V>) => {
    return (
      <ItemContainer>
        {type === 'single' ? (
          <RadioButton
            labelStyle={{ flex: 1 }}
            value={String(value)}
            label={label}
            selected={selected}
            onSelect={() => onSelect(value)}
            disabled={disabled}
          />
        ) : (
          <Checkbox
            labelStyle={{ flex: 1 }}
            label={label}
            checked={selected}
            onChange={() => onSelect(value)}
            disabled={disabled}
          />
        )}
      </ItemContainer>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

export const getSelected = <V,>(value: V, selectedValue: V | V[]) => {
  return selectedValue
    ? Array.isArray(selectedValue)
      ? selectedValue.some(curVal => curVal === value)
      : value === selectedValue
    : false;
};

const styles = StyleSheet.create({
  contentContainerStyle: {
    paddingHorizontal: 8,
  },
  smallContentContainerStyle: {
    paddingHorizontal: 4,
  },
});
