import { StyleSheet, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { Portal } from '@gorhom/portal';
import useBoundStore from 'hooks/useBoundStore';
import { LOADING_STATUS } from 'types';
import styled from '@emotion/native';
import { loadingAnimation } from 'assets/images';
import { useTheme } from '@emotion/react';
import { Image } from 'expo-image';

const LOADING_BUFFER = 900;

export default function AppLoadingIndicator() {
  const isLoading =
    useBoundStore(state => state.loadingStatus) === LOADING_STATUS.LOADING;

  const [bufferedIsLoading, setBufferedIsLoading] = useState(isLoading);

  useEffect(() => {
    if (isLoading && !bufferedIsLoading) {
      setBufferedIsLoading(true);
    }

    if (!isLoading && bufferedIsLoading) {
      const timeout = setTimeout(
        () => setBufferedIsLoading(false),
        LOADING_BUFFER,
      );
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [isLoading]);

  const { sizes } = useTheme();
  return (
    <>
      {isLoading || bufferedIsLoading ? (
        <Portal>
          <AbsoluteView>
            <Background />
            <LoadingContainer>
              <Image
                style={{ width: sizes[50], height: sizes[50] }}
                contentFit="contain"
                source={loadingAnimation}
              />
            </LoadingContainer>
          </AbsoluteView>
        </Portal>
      ) : null}
    </>
  );
}
const AbsoluteView = styled(View)(() => ({
  ...StyleSheet.absoluteFillObject,
  zIndex: 9999,
}));
const Background = styled(View)(({ theme: { colors } }) => ({
  backgroundColor: colors.background,
  height: '100%',
  width: '100%',
  opacity: 0.5,
  justifyContent: 'center',
  alignItems: 'center',
}));
const LoadingContainer = styled(View)({
  ...StyleSheet.absoluteFillObject,
  justifyContent: 'center',
  alignItems: 'center',
});
