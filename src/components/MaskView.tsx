import * as React from 'react';
import { UIManager } from 'react-native';
import RNCMaskedView, {
  MaskedViewProps as BaseMaskViewProps,
} from '@react-native-masked-view/masked-view';

type Props = BaseMaskViewProps & {
  children: React.ReactElement;
};

const isMaskedViewAvailable =
  UIManager.getViewManagerConfig('RNCMaskedView') != null;

export default function MaskedView({ children, ...rest }: Props) {
  if (isMaskedViewAvailable) {
    return (
      <RNCMaskedView maskElement={rest.maskElement}>{children}</RNCMaskedView>
    );
  }

  return children;
}
