import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Icon,
  LoadingIndicator,
  Row,
  TextField,
  TextFieldRef,
  Typography,
} from 'cube-ui-components';
import React, { Dispatch, SetStateAction, useRef, useState } from 'react';
import { Pressable, useWindowDimensions } from 'react-native';

import ErrorMessage from 'features/home/<USER>/CreateSaleIllustrationModal/tablet/ErrorMessage';
import { useTranslation } from 'react-i18next';

import CustomModal from 'components/Modal/CustomModal';
import debounce from 'lodash/debounce';
import { Lead } from 'types';
import SearchLeadResults from './SearchLeadResults.tablet';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';

type SearchLeadModalProps = {
  isVisible: boolean;
  leads: Lead[];
  searchText: string;
  setSearchText: Dispatch<SetStateAction<string>>;
  isFetching: boolean;
  removeLeadQuery: () => void;
  onClose: () => void;
  onConfirm: (leadId: string) => void;
  isEntityLead?: boolean;
};

export default function SearchLeadModal({
  isVisible,
  isEntityLead,
  leads,
  searchText,
  setSearchText,
  isFetching,
  removeLeadQuery,
  onClose,
  onConfirm,
}: SearchLeadModalProps) {
  const { space, colors, borderRadius } = useTheme();

  const searchInputRef = useRef<TextFieldRef>(null);

  const [selectedLeadId, setSelectedLeadId] = useState<string>();

  const leadResults = leads.filter(lead => !!lead);

  const handleOnSearchTextChange = (text: string) => {
    setSearchText(text.trim());
  };

  const clearSearchInput = () => {
    setSearchText('');
    setSelectedLeadId('');
    searchInputRef?.current?.clear();
    removeLeadQuery();
  };

  const handleOnClose = () => {
    clearSearchInput();
    setSelectedLeadId('');
    onClose();
  };

  const debouncedSearch = debounce(handleOnSearchTextChange, 300);

  const { t } = useTranslation(['common']);

  const rightInputIcon = () => {
    if (isFetching) {
      return <LoadingIndicator />;
    }

    if (searchText.length > 0) {
      return (
        <Pressable onPress={clearSearchInput}>
          <Icon.CloseCircle fill={colors.secondary} />
        </Pressable>
      );
    }
  };

  const handleOnConfirm = () => {
    if (selectedLeadId) {
      onConfirm(selectedLeadId);
      handleOnClose();
    }
  };

  const { width, height } = useWindowDimensions();

  return (
    <CustomModal
      isVisible={isVisible}
      onBackButtonPress={handleOnClose}
      onBackdropPress={handleOnClose}
      style={{
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      <Box
        backgroundColor={colors.background}
        width={width * 0.7}
        padding={space[12]}
        maxHeight={height - space[14]}
        borderRadius={borderRadius.large}>
        <Box>
          <Typography.H6 fontWeight="bold">
            {isEntityLead
              ? t('common:searchExistingLeadModal.existingCompanyTitle')
              : t('common:searchExistingLeadModal.existingLeadTitle')}
          </Typography.H6>
        </Box>

        <Row gap={space[5]} marginTop={space[6]}>
          <TextField
            autoFocus
            ref={searchInputRef}
            style={{
              flex: 1,
            }}
            label={
              isEntityLead
                ? t('common:searchExistingLeadModal.entityLeadPlaceholder')
                : t('common:searchExistingLeadModal.individualLeadPlaceholder')
            }
            left={<Icon.Search fill={colors.placeholder} />}
            right={rightInputIcon}
            onChangeText={debouncedSearch}
          />
        </Row>

        {leadResults.length === 0 && searchText.length !== 0 && !isFetching && (
          <Box mt={space[5]}>
            <ErrorMessage
              error={
                isEntityLead
                  ? t('common:searchExistingLeadModal.emptyEntity')
                  : t('common:searchExistingLeadModal.emptyIndividual')
              }
            />
          </Box>
        )}

        {leadResults.length > 0 && (
          <>
            <Box mt={space[6]} />
            <SearchLeadResults
              searchText={searchText}
              foundLeads={leadResults}
              onSelectLead={setSelectedLeadId}
              selectedLeadId={selectedLeadId}
              isEntityLead={isEntityLead}
            />
          </>
        )}

        <Row gap={space[4]} paddingTop={space[12]} justifyContent="center">
          <Button
            size="medium"
            text={t('common:cancel')}
            variant="secondary"
            onPress={handleOnClose}
            style={{ width: '30%' }}
          />
          <Button
            size="medium"
            text={t('common:confirm')}
            variant="primary"
            onPress={handleOnConfirm}
            disabled={!selectedLeadId}
            style={{ width: '30%' }}
          />
        </Row>
      </Box>
    </CustomModal>
  );
}
