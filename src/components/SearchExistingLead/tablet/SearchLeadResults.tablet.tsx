import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, RadioButton, Row, Typography } from 'cube-ui-components';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { Lead } from 'types';
import { EntityInsured } from 'types/entityInsured';
import { formatPhoneNumber } from 'utils';
import { getFullName } from 'features/lead/utils';
import { FlatList } from 'react-native';
import reactStringReplace from 'react-string-replace';

export function ColumnText({
  children,
  highlightText,
  variant = 'Label',
  textColor: textC,
  highlightColor: highlightC,
}: {
  highlightText?: string;
  children?: string;
  variant?: keyof typeof Typography;
  textColor?: string;
  highlightColor?: string;
}) {
  const { colors } = useTheme();
  const textColor = textC ?? colors.palette.black;
  const highlightColor = highlightC ?? colors.primary;

  const Label = Typography[variant];

  return (
    <Label color={textColor}>
      {reactStringReplace(children, highlightText, match => (
        <Label fontWeight="bold" color={highlightColor}>
          {match}
        </Label>
      ))}
    </Label>
  );
}

export type SearchLeadResultsProps = {
  searchText: string;
  foundLeads: Lead[] | EntityInsured[];
  selectedLeadId?: string;
  onSelectLead: (leadId: string) => void;
  isEntityLead?: boolean;
  isInsured?: boolean;
  maxHeightRatio?: number;
};

export default function SearchLeadResults({
  searchText,
  foundLeads,
  onSelectLead,
  selectedLeadId,
  isEntityLead,
  isInsured,
}: SearchLeadResultsProps) {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['common', 'leadProfile']);
  let nameLabel = t('common:search.leadName');
  if (isEntityLead) {
    nameLabel = t('common:search.companyName');
  } else if (isInsured) {
    nameLabel = t('leadProfile:leadProfile.insuredName');
  }

  const renderResultItem = (lead: Lead) => {
    return (
      <TouchableOpacity
        style={{
          marginBottom: space[3],
          paddingBottom: space[3],
          borderBottomWidth: 1,
          borderBottomColor: colors.palette.fwdGrey[100],
          flexDirection: 'row',
        }}
        onPress={() => onSelectLead(lead.id)}>
        <FirstColumn>
          <RadioButton
            value={lead.id}
            selected={selectedLeadId === lead.id}
            onSelect={() => onSelectLead(lead.id)}
          />
        </FirstColumn>
        <Column flex={1}>
          <ColumnText highlightText={searchText}>
            {isEntityLead
              ? lead?.companyName
              : getFullName({
                  firstName: lead.firstName,
                  lastName: lead.lastName,
                  middleName: lead.middleName,
                })}
          </ColumnText>
        </Column>
        <Column flex={1}>
          <ColumnText highlightText={searchText}>
            {formatPhoneNumber(lead?.mobilePhoneNumber)}
          </ColumnText>
        </Column>
        <Column flex={1}>
          <ColumnText highlightText={searchText}>{lead?.email}</ColumnText>
        </Column>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <Typography.H7 fontWeight="bold">
        {t('common:searchResults', { count: foundLeads.length })}
      </Typography.H7>

      <Box marginTop={space[3]}>
        <Row marginBottom={space[3]}>
          <FirstColumn></FirstColumn>
          <Column flex={1}>
            <TableTitle>{nameLabel}</TableTitle>
          </Column>
          <Column flex={1}>
            <TableTitle>{t('common:search.mobile')}</TableTitle>
          </Column>
          <Column flex={1}>
            <TableTitle>{t('common:search.email')}</TableTitle>
          </Column>
        </Row>
      </Box>
      <FlatList
        data={foundLeads}
        keyExtractor={item => item.id}
        renderItem={({ item }) => renderResultItem(item as Lead)}
      />
    </>
  );
}

const TableTitle = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDark,
}));

const FirstColumn = styled(Column)(() => ({
  minWidth: 50,
}));
