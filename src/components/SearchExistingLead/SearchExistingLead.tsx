import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { useSearchLead } from 'hooks/useGetLeads';
import { useCallback, useMemo, useState } from 'react';
import SearchExistingLeadTablet from './tablet/SearchExistingLead.tablet';
import SearchExistingLeadPhone from './phone/SearchExistingLead.phone';
import { Lead } from 'types';

interface Props {
  isVisible: boolean;
  isEntityLead?: boolean;
  onClose: () => void;
  onDone?: (customer: Lead) => void;
}

const SearchExistingLead = function SearchExistingLead({
  isVisible,
  isEntityLead,
  onClose,
  onDone,
}: Props) {
  const [searchText, setSearchText] = useState('');

  const {
    data: searchResult,
    isFetching,
    remove: removeLeadQuery,
  } = useSearchLead({
    queryText: searchText,
    enabled: Boolean(searchText.length >= 3),
    params: {
      isIndividual: !isEntityLead,
    },
  });

  const leads = useMemo(() => {
    return searchResult?.data || [];
  }, [searchResult?.data]);

  const handleOnConfirm = useCallback(
    (id: string) => {
      const lead = leads.find(i => i.id === id);
      if (lead) {
        onDone && onDone(lead);
      }
    },
    [leads, onDone],
  );

  return (
    <DeviceBasedRendering
      tablet={
        <SearchExistingLeadTablet
          isVisible={isVisible}
          isEntityLead={isEntityLead}
          leads={leads}
          searchText={searchText}
          setSearchText={setSearchText}
          isFetching={isFetching}
          removeLeadQuery={removeLeadQuery}
          onClose={onClose}
          onConfirm={handleOnConfirm}
        />
      }
      phone={
        <SearchExistingLeadPhone
          isVisible={isVisible}
          isEntityLead={isEntityLead}
          leads={leads}
          setSearchText={setSearchText}
          isFetching={isFetching}
          removeLeadQuery={removeLeadQuery}
          onClose={onClose}
          onConfirm={handleOnConfirm}
        />
      }
    />
  );
};
export default SearchExistingLead;
