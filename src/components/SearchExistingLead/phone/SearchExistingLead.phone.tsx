import SearchableDropdownPanel from 'components/SearchableDropdownPanel';
import { getFullName } from 'features/lead/utils';
import React, { Dispatch, SetStateAction, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Lead } from 'types';
import SearchLeadResults from './SearchLeadResults.phone';

type SearchLeadModalProps = {
  isVisible: boolean;
  leads: Lead[];
  setSearchText: Dispatch<SetStateAction<string>>;
  isFetching: boolean;
  removeLeadQuery: () => void;
  onClose: () => void;
  onConfirm: (leadId: string) => void;
  isEntityLead?: boolean;
};

export default function SearchExistingLead({
  isVisible,
  isEntityLead,
  leads,
  setSearchText,
  isFetching,
  removeLeadQuery,
  onClose,
  onConfirm,
}: SearchLeadModalProps) {
  const { t } = useTranslation(['common']);
  const [shouldShowList, setShowList] = useState(false);
  const getItemValue = useCallback((item: Lead) => item.id, []);
  const getItemLabel = useCallback(
    (item: Lead) =>
      getFullName({
        firstName: item.firstName,
        lastName: item.lastName,
        middleName: item.middleName,
      }),
    [],
  );
  const onClear = useCallback(() => {
    removeLeadQuery();
    setShowList(false);
  }, [removeLeadQuery]);

  const onStartSearching = useCallback(() => {
    setShowList(true);
  }, []);

  const onDismiss = useCallback(() => {
    setShowList(false);
    onClose();
  }, [onClose]);

  return (
    <>
      {isVisible && (
        <SearchableDropdownPanel<Lead, string>
          title={
            isEntityLead
              ? t('common:searchExistingLeadModal.existingCompanyTitle')
              : t('common:searchExistingLeadModal.existingLeadTitle')
          }
          actionLabel={
            shouldShowList && leads.length > 0
              ? t('common:confirm')
              : t('common:search')
          }
          listTitle={
            leads.length > 0
              ? t('common:searchResults', {
                  count: leads.length,
                })
              : ''
          }
          data={leads}
          searchable={true}
          searchMode="manual-trigger"
          hideListWhenEmpty
          searchLabel={
            isEntityLead
              ? t('common:searchExistingLeadModal.entityLeadPlaceholder')
              : t('common:searchExistingLeadModal.individualLeadPlaceholder')
          }
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          onDismiss={onDismiss}
          type="single"
          onDone={onConfirm}
          renderItem={props => <SearchLeadResults {...props} />}
          onQuery={setSearchText}
          onSearchTriggered={onStartSearching}
          emptyMessage={
            isEntityLead
              ? t('common:searchExistingLeadModal.emptyEntity')
              : t('common:searchExistingLeadModal.emptyIndividual')
          }
          snapMode="dynamic"
          estimatedItemSize={74}
          loading={isFetching}
          onClear={onClear}
        />
      )}
    </>
  );
}
