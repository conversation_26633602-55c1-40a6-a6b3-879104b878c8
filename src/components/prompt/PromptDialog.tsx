import React, { <PERSON>ps<PERSON><PERSON><PERSON><PERSON>dren, ReactNode, useCallback } from 'react';
import {
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  Button,
  Column,
  H6,
  Icon,
  LargeBody,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import Modal from 'react-native-modal';
import { moduleConfigs } from 'utils/config/module';
import { country } from 'utils/context';
import { PromptActions } from './PromptContext';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export interface PromptDialogProps {
  visible?: boolean;
  title?: string | undefined;
  description: string | React.ReactNode;
  actions: ReactNode;
  hide: () => void;
  onClose?: () => void;
  closable?: boolean;
  dialogStyle?: StyleProp<ViewStyle>;
  /**
   * If true, the dialog will be displayed in a modal
   * default is false
   */
  useModal?: boolean;
}

export default function PromptDialog({
  visible,
  title,
  description,
  actions,
  hide,
  onClose,
  closable = false,
  dialogStyle,
  useModal = false,
}: PromptDialogProps) {
  const debugPolicyNo = moduleConfigs[country].eAppConfig.debugPolicyNo;
  const { space, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();
  const { caseObj } = useGetActiveCase();
  const policyNo = caseObj?.application?.policyNum;

  const DialogContainer = useCallback(
    ({ children }: PropsWithChildren) => {
      if (useModal) {
        return (
          <Modal
            isVisible={visible}
            backdropOpacity={0.5}
            style={StyleSheet.flatten([
              {
                zIndex: 10,
                margin: 0,
              },
              dialogStyle,
            ])}>
            <ModalContent>{children}</ModalContent>
          </Modal>
        );
      }

      return (
        <DialogPhone visible={visible} style={dialogStyle}>
          {children}
        </DialogPhone>
      );
    },
    [useModal, space, isTabletMode, dialogStyle, visible],
  );

  const handleCloseAction = () => {
    onClose && onClose();
    hide();
  };

  return (
    <DialogContainer>
      <Box pb={space[isTabletMode ? 6 : 0]}>
        <Box px={space[isTabletMode ? 6 : 0]} pt={space[isTabletMode ? 6 : 0]}>
          {debugPolicyNo && Boolean(policyNo) && (
            <DebugView>
              <SmallLabel color={colors.placeholder}>
                Ref no. {policyNo}
              </SmallLabel>
              <Box h={space[2]} />
            </DebugView>
          )}
          <Row gap={space[1]}>
            <H6
              fontWeight="bold"
              style={{
                flex: 1,
              }}>
              {title}
            </H6>

            {closable && (
              <TouchableOpacity
                hitSlop={ICON_HIT_SLOP}
                onPress={handleCloseAction}>
                <Icon.Close size={24} fill={colors.secondary} />
              </TouchableOpacity>
            )}
          </Row>
          {!!title && <Box h={space[4]} />}
          <LargeBody>{description}</LargeBody>
          <Box h={space[6]} />
          {actions}
        </Box>
      </Box>
    </DialogContainer>
  );
}

export const getAlertActions: PromptActions<{ accept: string }> = (
  resolve,
  label,
) => {
  return (
    <Button
      text={label?.accept}
      variant="primary"
      onPress={() => resolve(true)}
    />
  );
};

export const getConfirmationActions: PromptActions<{
  dismiss: string;
  accept: string;
}> = (resolve, config) => {
  const { space } = useTheme();

  return (
    <Row gap={space[4]}>
      <Button
        style={{ flex: 1, maxWidth: 200 }}
        text={config?.dismiss}
        variant="secondary"
        size="medium"
        onPress={() => resolve(false)}
      />
      <Button
        style={{ flex: 1, maxWidth: 200 }}
        text={config?.accept}
        variant="primary"
        size="medium"
        onPress={() => resolve(true)}
      />
    </Row>
  );
};

/**
 * This function returns a column of action buttons
 * @param resolve - The function to call when the user accepts or dismisses the prompt
 * @param config - The configuration object containing the labels for the accept and dismiss buttons
 * @returns A column of action buttons
 */
export const getColumnActionButtons: PromptActions<{
  dismiss: string;
  accept: string;
}> = (resolve, config) => {
  const { space, typography } = useTheme();

  return (
    <Column gap={space[2]}>
      <Button
        text={config?.accept}
        variant="primary"
        size="medium"
        onPress={() => resolve(true)}
      />
      <Button
        text={config?.dismiss}
        variant="text"
        size="medium"
        textStyle={{
          fontWeight: 700,
          fontSize: typography.h7.size,
          lineHeight: typography.h7.lineHeight,
        }}
        onPress={() => resolve(false)}
      />
    </Column>
  );
};

export const getStyledAlertActions: PromptActions<{ accept: string }> = (
  resolve,
  label,
) => {
  return (
    <Row justifyContent="center">
      <Button
        style={{ flex: 1, maxWidth: 200 }}
        text={label?.accept}
        variant="primary"
        onPress={() => resolve(true)}
      />
    </Row>
  );
};

const DebugView = styled(Box)(({ theme: { space } }) => ({
  position: 'absolute',
  top: 0,
  left: space[6],
}));

const ModalContent = styled.View(
  ({ theme: { colors, space, borderRadius } }) => {
    const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();
    const { isTabletMode } = useLayoutAdoptionCheck();
    return {
      backgroundColor: colors.background,
      borderRadius: borderRadius.large,
      padding: space[isNarrowScreen ? 5 : 6],
      alignSelf: isWideScreen ? 'auto' : 'stretch',
      maxWidth: isWideScreen ? 460 : undefined,
      minWidth: isTabletMode ? 380 : undefined,
      marginHorizontal: space[isNarrowScreen ? 3 : 4],
    };
  },
);
