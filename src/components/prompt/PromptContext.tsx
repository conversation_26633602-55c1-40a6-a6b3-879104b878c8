import React, {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useState,
} from 'react';
import PromptDialog, { PromptDialogProps } from './PromptDialog';
import useToggle from 'hooks/useToggle';

type Value = string | boolean;

export type { Value as PromptValue };

export type PromptActions<Config = unknown> = (
  resolve: (value: Value) => void,
  config?: Config,
) => ReactNode;

interface DialogProps<Config = unknown>
  extends Omit<PromptDialogProps, 'actions' | 'visible' | 'hide'> {
  actions: PromptActions<Config>;
  config?: Config;
}

type Prompt = <Config = unknown>(props: DialogProps<Config>) => Promise<Value>;

export type { Prompt };

export interface PromptState {
  prompt: Prompt;
}

export const PromptContext = createContext<PromptState>({
  prompt: () => {
    throw Error('Prompt context is not implemented');
  },
});

interface ProviderProps {
  children: ReactNode;
}

interface DialogPromise<Config = unknown> {
  dialog: DialogProps<Config>;
  resolve: (value: Value) => void;
}

export const usePromptContext = () => useContext(PromptContext);

export default function PromptProvider(props: ProviderProps) {
  const [promise, setPromise] = useState<DialogPromise<unknown> | null>(null);
  const [visible, show, hide] = useToggle();

  const prompt: Prompt = useCallback(
    async dialog => {
      show();
      const promise = new Promise<Value>(resolve => {
        setPromise({ dialog: dialog as DialogProps<unknown>, resolve });
      });
      return promise.then(value => {
        hide();
        setPromise(null);
        return value;
      });
    },
    [hide, show],
  );

  return (
    <PromptContext.Provider value={{ prompt }}>
      {promise !== null && (
        <PromptDialog
          visible={visible}
          {...promise.dialog}
          actions={promise.dialog.actions(
            promise.resolve,
            promise.dialog.config,
          )}
          hide={hide}
        />
      )}
      {props.children}
    </PromptContext.Provider>
  );
}
