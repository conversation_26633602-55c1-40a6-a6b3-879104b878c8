import {
  View,
  ViewProps,
  StyleProp,
  ViewStyle,
  ScrollView,
  ScrollViewProps,
} from 'react-native';
import React, { useMemo } from 'react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface ResponsiveViewProps extends ViewProps {
  wideStyle?: StyleProp<ViewStyle>;
  narrowStyle?: StyleProp<ViewStyle>;
  scrollMode?: 'horizontal' | 'vertical' | false;
  onScroll?: ScrollViewProps['onScroll'];
  scrollEventThrottle?: ScrollViewProps['scrollEventThrottle'];
}

const ResponsiveView = React.forwardRef<View | ScrollView, ResponsiveViewProps>(
  (
    {
      style,
      wideStyle,
      narrowStyle,
      scrollMode,
      onScroll,
      scrollEventThrottle = 0,
      ...viewProps
    }: ResponsiveViewProps,
    ref,
  ) => {
    const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

    const viewStyle = useMemo(() => {
      const combineStyle = [
        style,
        isWideScreen && wideStyle,
        isNarrowScreen && narrowStyle,
      ];

      return combineStyle;
    }, [isWideScreen, isNarrowScreen]);

    const Component = scrollMode ? ScrollView : View;

    return (
      <Component
        ref={ref}
        style={viewStyle}
        onScroll={scrollMode ? onScroll : () => null}
        horizontal={scrollMode == 'horizontal'}
        scrollEventThrottle={scrollEventThrottle}
        showsHorizontalScrollIndicator={false}
        {...viewProps}
      />
    );
  },
);

export default ResponsiveView;
