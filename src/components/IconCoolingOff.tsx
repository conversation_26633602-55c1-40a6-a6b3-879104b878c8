import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function CoolingOffIcon(props: SvgIconProps) {
  return (
    <Svg
      viewBox="0 0 40 40"
      fill="none"
      width={props.width || 40}
      height={props.height || 40}
      {...props}>
      {/* <circle cx="20" cy="20" r="20" fill="#E87722" /> */}
      <Path
        d="M24.5 30H15.5C14.67 30 14 29.33 14 28.5V24.585L18.585 20L14 15.415V11.5C14 10.67 14.67 10 15.5 10H24.5C25.33 10 26 10.67 26 11.5V15.415L21.415 20L26 24.585V28.5C26 29.33 25.33 30 24.5 30ZM16 28H24V25.415L20 21.415L16 25.415V28ZM16 14.59L20 18.59L24 14.59V12.005H16V14.59Z"
        fill="white"
      />
    </Svg>
  );
}

export default CoolingOffIcon;
