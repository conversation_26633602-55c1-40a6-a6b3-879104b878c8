import { TouchableOpacity, TextInput, Pressable } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import {
  Row,
  Typography,
  Icon,
  Box,
  LoadingIndicator,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

export default function CustomSearchingBar({
  onExitSearch,
  setSearchKeyWords,
  isFetching,
  placeholderText,
  BelowBarLabelComponent,
  smallLabelText,
}: {
  isFetching?: boolean;
  onExitSearch: () => void;
  setSearchKeyWords: React.Dispatch<React.SetStateAction<string>>;
  placeholderText: string;
  BelowBarLabelComponent?: JSX.Element;
  smallLabelText?: string;
}) {
  const { colors, space, borderRadius, typography } = useTheme();
  const [isTyping, setIsTyping] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [localQuery, setLocalQuery] = useState('');

  const textInputRef = useRef<TextInput>(null);
  const searchBarOpen = useSharedValue(false);

  useEffect(() => {
    if (!searchBarOpen.value) {
      searchBarOpen.value = true;
    }
  }, []);

  const exitSearchHandler = () => {
    setLocalQuery('');
    setSearchKeyWords('');
    searchBarOpen.value = false;
    onExitSearch();
  };

  const focusInput = () => {
    textInputRef.current?.focus();
  };

  const onBlurHandler = () => {
    setIsFocused(false);
    setIsTyping(false);
    textInputRef?.current?.blur();
  };

  useEffect(() => {
    if (isTyping) {
      // const timer = setTimeout(() => {
      //   textInputRef?.current?.blur();
      //   setSearchKeyWords(localQuery);
      // }, 1000);
      // return () => clearTimeout(timer);
    } else {
      if (localQuery.trim().length === 0) {
        setSearchKeyWords(localQuery);
      }
    }
  }, [localQuery, setSearchKeyWords, isTyping]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: searchBarOpen.value
        ? withTiming('100%', { duration: 300 }, finished => {
            if (finished) {
              runOnJS(focusInput)();
            }
          })
        : '9%',
    };
  }, [searchBarOpen.value]);

  return (
    <Row>
      <TouchableOpacity
        style={{
          paddingVertical: space[3],
          marginRight: space[2],
        }}
        onPress={exitSearchHandler}>
        <Icon.ArrowLeft fill={colors.secondary} />
      </TouchableOpacity>
      <Box flex={1} alignItems="flex-end">
        <Animated.View style={animatedStyle}>
          {!isTyping && !(localQuery.trim().length > 0) && (
            <Pressable
              style={{
                position: 'absolute',
                zIndex: 100,
                left: space[4],
                top: space[3] - 2,
              }}
              onPress={focusInput}>
              <Typography.LargeBody
                numberOfLines={1}
                style={{
                  overflow: 'hidden',
                  color: colors.palette.fwdGreyDark,
                }}>
                {placeholderText}
              </Typography.LargeBody>
            </Pressable>
          )}
          <TextInput
            enablesReturnKeyAutomatically
            returnKeyType="search"
            ref={textInputRef}
            selectionColor={colors.primary}
            style={{
              paddingRight: space[9],
              paddingLeft: space[4],
              paddingVertical: space[3],
              borderRadius: borderRadius.full,
              height: space[11],
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.palette.white,
              borderWidth: 1,
              borderColor: isFocused
                ? colors.primary
                : colors.palette.fwdDarkGreen[20],
              position: 'relative',
              fontFamily: 'FWDCircularTT-Book',
              fontSize: typography.body.size,
              textAlignVertical: 'center',
            }}
            value={localQuery}
            onChangeText={text => {
              setSearchKeyWords('');
              if (!isTyping && text.trim() !== '') {
                setIsTyping(true);
              }
              setLocalQuery(text);
            }}
            onBlur={onBlurHandler}
            onFocus={() => setIsFocused(true)}
            onSubmitEditing={e => {
              textInputRef?.current?.blur();
              setSearchKeyWords(localQuery.trim());
            }}
          />
        </Animated.View>
        {localQuery.trim().length > 0 && (
          <TouchableOpacity
            onPress={() => {
              setLocalQuery('');
              setSearchKeyWords('');
              textInputRef.current?.focus();
            }}
            // disabled={isTyping}
            style={{
              position: 'absolute',
              right: space[3],
              top: space[3],
            }}>
            {isTyping ? (
              <Icon.CloseCircle fill={colors.secondary} size={space[5]} />
            ) : isFetching ? (
              <LoadingIndicator size={space[5]} />
            ) : (
              <Icon.CloseCircle fill={colors.secondary} size={space[5]} />
            )}
          </TouchableOpacity>
        )}
        {smallLabelText && (
          <Typography.SmallLabel
            style={{
              marginTop: space[2],
              alignSelf: 'flex-start',
            }}
            color={colors.palette.fwdGreyDarker}>
            {smallLabelText}
          </Typography.SmallLabel>
        )}

        {BelowBarLabelComponent ? BelowBarLabelComponent : null}
      </Box>
    </Row>
  );
}
