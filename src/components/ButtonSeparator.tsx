import { View } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Row, Typography } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export default function ButtonSeparator({ text }: { text?: string }) {
  const { sizes } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();

  return (
    <OptionsSeparatorContainer
      style={[
        isWideScreen && {
          marginTop: sizes[6],
          marginBottom: sizes[6],
        },
      ]}>
      <OptionsSeparatorText children={text} />
      <SeparationLine />
    </OptionsSeparatorContainer>
  );
}

const OptionsSeparatorContainer = styled(Row)(({ theme: { sizes } }) => ({
  minHeight: sizes[5],
  justifyContent: 'center',
  marginTop: sizes[3],
  marginBottom: sizes[3],
}));

const OptionsSeparatorText = styled(Typography.Label)(
  ({ theme: { sizes, colors } }) => ({
    backgroundColor: colors.background,
    position: 'absolute',
    height: '100%',
    zIndex: 10,
    paddingHorizontal: sizes[4],
    color: colors.palette.fwdGreyDarker,
  }),
);

const SeparationLine = styled(View)(({ theme: { colors } }) => ({
  alignSelf: 'center',
  borderBottomColor: colors.palette.fwdGrey[100],
  width: '100%',
  borderBottomWidth: 1,
}));
