import React from 'react';
import Modal, { ModalProps } from 'react-native-modal';
import { useSafeAreaFrame } from 'react-native-safe-area-context';

export default function CustomModal(props: Partial<ModalProps>) {
  const { width, height } = useSafeAreaFrame();

  return (
    <Modal
      {...props}
      deviceWidth={props.deviceWidth || width}
      deviceHeight={props.deviceHeight || height}
      backdropColor="rgba(0,0,0,0.5)"
      useNativeDriver
      hideModalContentWhileAnimating
      avoidKeyboard>
      {props.children}
    </Modal>
  );
}
