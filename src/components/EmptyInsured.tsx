import React from 'react';
import { Typography, Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

import { ViewStyle } from 'react-native';
import EmptyInsuredSVG from 'features/lead/assets/EmptyInsuredSVG';

export default function EmptyInsured({
  width = 100,
  height = 84,
  containerStyle,
}: {
  width?: number;
  height?: number;
  containerStyle?: ViewStyle;
}) {
  const { t } = useTranslation('lead');
  const { space, colors } = useTheme();

  return (
    <Box
      style={[
        {
          backgroundColor: colors.background,
          width: '100%',
          borderBottomLeftRadius: space[4],
          borderBottomEndRadius: space[4],
          alignItems: 'center',
          justifyContent: 'center',
        },
        containerStyle,
      ]}
      gap={space[2]}>
      <EmptyInsuredSVG width={width} height={height} />
      <Typography.Body color={colors.palette.fwdGreyDarker}>
        {t('lead.insured.noSavedInsured')}
      </Typography.Body>
    </Box>
  );
}
