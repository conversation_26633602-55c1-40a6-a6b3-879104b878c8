import React, { ComponentProps, ForwardedRef, forwardRef } from 'react';
import {
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  RadioButtonGroupProps,
  Row,
  SmallLabel,
  Text,
} from 'cube-ui-components';
import { StyleProp, View, ViewStyle } from 'react-native';
import { useTheme } from '@emotion/react';
import { TextStyle } from 'react-native';

interface Props<T> extends RadioButtonGroupProps<T> {
  label?: string;
  labelFontWeight?: ComponentProps<typeof Text>['fontWeight'];
  labelStyle?: StyleProp<TextStyle>;
  labelColor?: ComponentProps<typeof Text>['color'];
  gap?: number;
  items: { label: string; value: T }[];
  style?: StyleProp<ViewStyle>;
  error?: string;
}

export type RadioButtonGroupInputProps<T> = Props<T>;
export type RadioButtonGroupInputRef = View;

const RadioButtonGroupInputInner = <T,>(
  {
    items,
    style,
    label,
    labelFontWeight,
    labelStyle,
    labelColor,
    gap,
    error,
    ...props
  }: Props<T>,
  ref: ForwardedRef<RadioButtonGroupInputRef>,
) => {
  const { space, colors } = useTheme();
  return (
    <RadioButtonGroup {...props}>
      <View style={style}>
        {Boolean(label) && (
          <LargeLabel
            fontWeight={labelFontWeight}
            style={labelStyle}
            color={labelColor}>
            {label}
          </LargeLabel>
        )}
        <Row gap={gap ?? space[8]} ref={ref}>
          {items.map(item => (
            <RadioButton
              key={String(item.value)}
              label={item.label}
              value={item.value}
            />
          ))}
        </Row>
        {Boolean(error) && (
          <SmallLabel color={colors.error}>{error}</SmallLabel>
        )}
      </View>
    </RadioButtonGroup>
  );
};

const RadioButtonGroupInput = forwardRef(RadioButtonGroupInputInner) as <T>(
  props: Props<T> & { ref?: ForwardedRef<RadioButtonGroupInputRef> },
) => ReturnType<typeof RadioButtonGroupInputInner>;

export default RadioButtonGroupInput;
