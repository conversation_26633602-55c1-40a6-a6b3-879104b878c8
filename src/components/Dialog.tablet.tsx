import styled from '@emotion/native';
import { Portal } from '@gorhom/portal';
import React from 'react';
import {
  BackHandler,
  Pressable,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface Props {
  visible?: boolean;
  children?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  dismissable?: boolean;
  onDismiss?: () => void;
}

const DialogTablet = (props: Props) => {
  React.useEffect(() => {
    if (!props.visible) {
      return undefined;
    }

    const onHardwareBackPress = () => {
      if (props.dismissable) {
        props.onDismiss?.();
      }

      return true;
    };

    const subscription = BackHandler.addEventListener(
      'hardwareBackPress',
      onHardwareBackPress,
    );
    return () => subscription.remove();
  }, [props]);

  return (
    <>
      {props.visible && (
        <Portal>
          <Backdrop
            onPress={props.dismissable ? props.onDismiss : undefined}
            renderToHardwareTextureAndroid
            entering={FadeIn}
            exiting={FadeOut}
          />
          <Wrapper>
            <Container style={props.style}>{props.children}</Container>
          </Wrapper>
        </Portal>
      )}
    </>
  );
};

export default DialogTablet;

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);
const Backdrop = styled(AnimatedPressable)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Wrapper = styled(View)(() => ({
  ...StyleSheet.absoluteFillObject,
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  pointerEvents: 'box-none',
}));

const Container = styled(View)(({ theme: { colors, space, borderRadius } }) => {
  return {
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    padding: space[6],
  };
});
