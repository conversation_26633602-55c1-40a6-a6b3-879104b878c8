import { TextInputProps } from 'react-native';
import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Row } from 'cube-ui-components';
import { SearchIcon } from 'features/savedProposals/components/header/SearchIcon';

export default function SearchTextInput(props: TextInputProps) {
  const theme = useTheme();
  return (
    <Row borderBottom={1} borderBottomColor={theme.colors.palette.white} py={8}>
      <StyledSearchTextInput
        autoFocus
        returnKeyType="done"
        placeholderTextColor={theme.colors.primaryVariant}
        cursorColor={theme.colors.background}
        selectionColor={theme.colors.background}
        {...props}
      />
      <SearchIcon fill={theme.colors.palette.white} />
    </Row>
  );
}

const StyledSearchTextInput = styled.TextInput(({ theme }) => ({
  fontSize: theme.typography.h6.size,
  lineHeight: theme.typography.h6.lineHeight,
  fontFamily: 'FWDCircularTT-Bold',
  color: theme.colors.palette.white,
  flex: 1,
}));
