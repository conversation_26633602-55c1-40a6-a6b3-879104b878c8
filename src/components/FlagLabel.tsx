import { StyleSheet, TextStyle, View, ViewStyle } from 'react-native';
import React from 'react';

import { SvgProps } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import { SvgIconProps, Typography } from 'cube-ui-components';

export type FlagLabelTypes =
  | 'disable_grey'
  | 'normal_lightBlue'
  | 'approved_green'
  | 'alertMild_lightRed'
  | 'alert_deepRed'
  | 'ai_lightGreen'
  | 'leadStatus_lightGreen20'
  | 'primary_orange'
  | 'pending_allLightBlue';

type FlagLabelProps = {
  type: FlagLabelTypes;
  content: string;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  Icon?: (props: SvgIconProps) => JSX.Element;
  medium?: boolean;
  bold?: boolean;
};

export default function FlagLabel({
  type = 'normal_lightBlue',
  content,
  containerStyle,
  textStyle,
  medium = false,
  bold = false,
  Icon,
}: FlagLabelProps) {
  const { colors, sizes, space } = useTheme();

  const selectStyle = (
    type: FlagLabelTypes,
  ): {
    backgroundColor: string;
    textColor: string;
  } => {
    switch (type) {
      case 'alert_deepRed':
        return {
          backgroundColor: colors.palette.alertRed,
          textColor: colors.palette.white,
        };
      case 'alertMild_lightRed':
        return {
          backgroundColor: colors.palette.alertRedLight,
          textColor: colors.palette.alertRed,
        };
      case 'approved_green':
        return {
          backgroundColor: colors.palette.alertGreen,
          textColor: colors.palette.white,
        };
      case 'disable_grey':
        return {
          backgroundColor: colors.palette.fwdGrey[50],
          textColor: colors.palette.fwdDarkGreen[50],
        };
      case 'ai_lightGreen':
        return {
          backgroundColor: colors.palette.fwdLightGreen[100],
          textColor: colors.palette.fwdDarkGreen[100],
        };
      case 'leadStatus_lightGreen20':
        return {
          backgroundColor: colors.palette.fwdLightGreen[20],
          textColor: colors.palette.alertGreen,
        };
      case 'primary_orange':
        return {
          backgroundColor: colors.palette.fwdOrange[20],
          textColor: colors.palette.fwdOrange[100],
        };
      case 'pending_allLightBlue':
        return {
          backgroundColor: colors.palette.fwdBlue[20],
          textColor: colors.palette.fwdBlue[100],
        };
      case 'normal_lightBlue':
      default:
        return {
          backgroundColor: colors.palette.fwdLightGreen[20],
          textColor: colors.palette.fwdBlue[100],
        };
    }
  };

  return (
    <View
      style={[
        styles.labelContainer,
        { backgroundColor: selectStyle(type).backgroundColor },
        containerStyle,
      ]}>
      {Icon && (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: space[1],
          }}>
          <Icon width={14} height={14} fill={selectStyle(type).textColor} />
        </View>
      )}
      <Typography.SmallLabel
        fontWeight={bold ? 'bold' : medium ? 'medium' : 'normal'}
        color={selectStyle(type).textColor}>
        {content}
      </Typography.SmallLabel>
    </View>
  );
}

const styles = StyleSheet.create({
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 2,
    alignSelf: 'flex-start',
  },
  labelText: {
    fontSize: 12,
  },
});
