import React from 'react';
import { Typography, Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

import { ViewStyle } from 'react-native';
import EmptyProposalSVG from 'features/lead/assets/EmptyProposalSVG';

export default function EmptyProposalRecord({
  width = 100,
  height = 84,
  containerStyle,
}: {
  width?: number;
  height?: number;
  containerStyle?: ViewStyle;
}) {
  const { t } = useTranslation('savedProposals');
  const { space, colors } = useTheme();

  return (
    <Box
      style={[
        {
          backgroundColor: colors.background,
          minHeight: space[39],
          width: '100%',
          padding: space[6],
          borderBottomLeftRadius: space[4],
          borderBottomEndRadius: space[4],
          alignItems: 'center',
          justifyContent: 'center',
        },
        containerStyle,
      ]}>
      <EmptyProposalSVG width={width} height={height} />
      <Typography.Body color={colors.palette.fwdGreyDarker}>
        {t('filter.emptyRecord')}
      </Typography.Body>
    </Box>
  );
}
