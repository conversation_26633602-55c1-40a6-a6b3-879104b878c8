import React, {
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
  useMemo,
} from 'react';
import {
  Keyboard,
  Pressable,
  StyleProp,
  TextStyle,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';
import {
  TextField,
  TextFieldRef,
  Icon,
  Typography,
  Box,
  SvgIconProps,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import useLatest from 'hooks/useLatest';
import SearchableDropdownPanel from 'components/SearchableDropdownPanel';
import SearchableDropdownModal from 'components/SearchableDropdownModal';
import { useTranslation } from 'react-i18next';
import Svg, { Path, Rect } from 'react-native-svg';

export type SearchableDropdownProps<T, V> = ViewProps & {
  label?: string;
  modalTitle?: string;
  actionLabel?: string;
  disabled?: boolean;
  preventPopup?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  isModal?: boolean;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  getDisplayedLabel?: (item: T) => string;
  getExternalDisplayedLabel?: () => string;
  searchable?: boolean;
  searchMode?: 'auto' | 'manual';
  searchLabel?: string;
  onQuery?: (text: string) => void;
  multiline?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  dropDownModalStyle?: ViewStyle;
  inputStyle?: StyleProp<TextStyle>;
  highlight?: boolean;
  getItemDisabled?: (item: T) => boolean;
  keyExtractor?: (item: T) => string;
  emptyMessage?: JSX.Element;
} & (
    | {
        type?: 'single';
        value?: V;
        onChange?: (value: V) => void;
      }
    | {
        type?: 'multiple';
        value?: V[];
        onChange?: (value: V[]) => void;
      }
  );

export type SearchableDropdownRef = TextFieldRef;

function EmptyCase(props: SvgIconProps) {
  return (
    <Svg width={97} height={105} viewBox="0 0 97 105" fill="none" {...props}>
      <Path
        d="M47.928 73.38l-.606-.215-.52-.365-.603-.219-.509-.383-.464-.46-.668-.105-.538-.332-.484-.427-.559-.296-.494-.408-.511-.38-.672-.1-.514-.374-.557-.3-.559-.296-.58-.26-.41-.551-.59-.243-.55-.311-.579-.262-.553-.307-.522-.36-.62-.192-.542-.322-.513-.376-.58-.259-.553-.307-.556-.299-.55-.312-.449-.486-.634-.166-.583-.254-.435-.512-.678-.09-.392-.585-.526-.353-.603-.22-.475-.442-.554-.304-.647-.146-.458-.47-.727-.006-.448-.489-.474-.446-.57-.278-.528-.348-.572-.274-.65-.139-.558-.3-.5-.396-.556-.303-.422-.536-.61-.206-.583-.26-.633-.17-.359-.605-.65-.219-.363-.538-.216-.61-.203-.585-.321-.543-.203-.603.17-.635-.069-.621.144-.605-.029-.68.499-.464.3-.554.348-.528.318-.545.255-.581.216-.604.425-.483.335-.535.22-.601.34-.533.193-.617.519-.43.381-.508.215-.605.378-.51.27-.572.108-.667.323-.543.283-.564.49-.446.44-.474.27-.573.365-.518.294-.56.134-.65.429-.482.34-.532.267-.575.346-.529.087-.678.39-.504.415-.489.396-.5.187-.62.432-.481.192-.618.523-.427.212-.606.13-.654.29-.561.33-.538.452-.468.206-.609.316-.546.485-.449.249-.585.374-.513.425-.483.185-.622.308-.55.415-.49.305-.553.056-.696.388-.505.417-.488.462-.463.219-.602.084-.68.394-.502.249-.586.382-.508.326-.542.542-.415.037-.708.418-.487.364-.52.283-.566.21-.607.266-.577.447-.471.472-.457.192-.619.399-.5.099-.672.641-.33.13-.73.503-.424.589-.291.635-.149.623-.095.578-.284.633-.218.65.076.616.219.579.272.62.182.52.364.518.368.502.394.681.085.385.598.564.285.67.103.492.414.622.185.465.46.537.335.627.178.566.283.405.564.583.254.596.232.465.459.69.067.401.571.71.033.41.556.637.16.41.554.654.129.58.26.429.522.574.269.55.31.506.387.666.11.415.546.647.145.617.195.396.578.605.218.639.157.521.362.435.511.573.272.606.217.543.323.48.432.67.104.489.421.54.328.477.439.68.087.547.316.521.363.54.33.435.513.723.012.498.402.454.482.56.298.452.43.66.196.349.544.471.46.083.667.135.608.23.587.106.626.05.647-.298.59-.191.597-.124.65-.425.483-.354.523-.41.492-.123.658-.343.53-.435.479-.136.65-.258.578-.524.427-.182.623-.463.461-.277.57-.446.47.02.741-.388.505-.459.464-.175.627-.418.488-.275.57-.296.556-.457.465-.32.545-.075.684-.589.389-.213.606-.357.522-.154.64-.493.444-.266.576-.22.602-.212.607-.57.4-.36.52-.073.687-.556.407-.159.638-.398.498-.215.605-.503.438-.145.645-.32.544-.183.622-.612.376-.154.64-.413.49-.372.514-.05.7-.51.434-.368.516-.03.711-.315.547-.462.463-.255.583-.303.553-.329.539-.558.406.02.741-.603.382-.084.68-.34.532-.391.504-.437.477-.293.56-.259.58-.166.633-.281.566-.516.432-.4.498-.03.712-.629.366-.058.697-.38.51-.38.511-.23.616-.623.297-.465.4-.36.605-.61.231-.685-.065-.594.147-.611.27-.646-.007-.62-.177-.646-.11-.484-.467z"
        fill="#636566"
      />
      <Path
        d="M46.124 72.298l-.684-.077-.446-.492-.556-.3-.632-.171-.49-.415-.577-.265-.587-.245-.527-.354-.382-.6-.654-.132-.486-.423-.57-.278-.627-.177-.447-.49-.684-.081-.53-.345-.46-.467-.627-.18-.387-.593-.667-.11-.541-.326-.463-.463-.671-.1-.425-.528-.67-.101-.404-.566-.602-.222-.673-.098-.483-.428-.45-.486-.563-.285-.527-.353-.595-.234-.545-.32-.59-.24-.48-.436-.644-.148-.532-.344-.6-.225-.495-.407-.548-.316-.455-.476-.625-.184-.51-.382-.614-.2-.598-.229-.476-.438-.49-.418-.59-.244-.602-.22-.568-.284-.417-.542-.545-.322-.679-.09-.496-.408-.608-.26-.325-.602-.565-.356-.379-.531-.17-.636-.326-.572.15-.678-.112-.615-.143-.649.334-.583.025-.662.54-.434.026-.713.293-.56.549-.411.163-.635.394-.501.278-.568.232-.594.495-.442.169-.632.249-.585.624-.368.316-.547.217-.603.33-.538.128-.654.29-.562.605-.38.093-.674.255-.581.311-.55.44-.474.286-.564.496-.442.141-.647.356-.524.448-.47.138-.649.499-.44v-.73l.64-.358.076-.685.24-.59.35-.527.507-.437.146-.645.35-.526.418-.488.128-.655.37-.515.284-.565.274-.57.377-.51.255-.582.495-.443.292-.56.25-.584.363-.52.202-.611.506-.437.105-.668.593-.387.037-.707.469-.458.186-.62.504-.44.355-.523.37-.516.272-.572.26-.579.34-.533.283-.566.276-.57.22-.602.519-.429.066-.691.313-.549.531-.421.25-.586.278-.57.422-.485.154-.64.432-.481.328-.54.212-.63.605-.313.406-.486.457-.472.637-.156.615-.141.59-.285.643.167.613.066.673-.146.588.275.587.26.417.543.64.155.519.364.56.293.492.413.577.266.521.36.701.05.555.304.496.406.469.45.545.321.641.152.524.357.593.238.434.513.622.186.561.292.558.3.528.349.464.46.666.11.503.391.61.21.443.497.527.35.598.227.426.527.557.298.627.18.586.248.557.3.448.49.625.18.438.506.733-.006.483.428.597.231.544.322.45.484.478.438.643.152.535.338.61.208.537.336.492.41.608.213.55.311.491.414.652.138.437.507.495.406.6.228.647.145.548.318.54.37.258.667.52.371.408.49.372.537.115.643.137.625-.205.64.069.622.145.682-.201.629-.618.403-.084.68-.44.474-.282.566-.193.617-.369.516-.264.576-.413.49-.36.52-.262.578-.341.531-.476.455-.29.56-.302.555-.217.603-.136.65-.502.44-.258.58-.398.499-.427.482-.27.572-.21.607-.45.47-.165.632-.371.515-.343.53-.236.593-.266.575-.228.597-.534.42-.282.568-.299.556-.15.642-.521.428-.097.673-.584.392-.169.63-.28.567-.283.566-.449.47-.215.604-.364.519-.218.602-.382.508-.459.463-.105.668-.332.537-.426.483-.296.558-.408.493-.378.51-.101.671-.374.513-.299.557-.295.558-.26.58-.552.41-.241.589-.311.55-.262.578-.307.552-.36.522-.19.618-.323.544-.376.511-.258.58-.306.552-.3.557-.312.549-.486.449-.165.634-.255.583-.511.434-.09.678-.614.359-.438.435-.394.49-.595.223-.542.28-.526.387-.648-.052-.61.4-.633-.24-.583-.266-.599-.156-.566-.26z"
        fill="#B3B6B8"
      />
      <Path
        d="M53.112 52.264l-.688-.08-.5-.403-.536-.341-.497-.41-.606-.22-.546-.325-.614-.208-.554-.31-.423-.537-.668-.113-.44-.51-.497-.407-.566-.29-.581-.264-.535-.343-.597-.236-.627-.183-.431-.523-.617-.203-.504-.398-.525-.361-.695-.066-.393-.59-.612-.21-.523-.363-.541-.334-.55-.32-.602-.225-.62-.2-.483-.433-.54-.335-.612-.212-.541-.333-.451-.49-.536-.342-.555-.31-.687-.083-.468-.46-.535-.343-.62-.198-.593-.245-.53-.354-.556-.31-.504-.339-.65.037-.349.542-.108.56.11.59.557.244.548.322.414.553.533.346.528.356.687.081.52.368.5.406.656.134.477.444.624.19.377.617.71.037.39.595.663.123.617.2.466.464.595.24.537.338.553.314.449.493.628.182.473.452.58.266.572.28.465.465.634.171.646.154.417.548.53.351.703.054.434.518.666.119.453.486.658.13.443.504.498.408.539.34.659.129.482.435.525.362.51.388.688.082.415.554.682.068.642-.1.348-.527.241-.562-.307-.533-.33-.558zm3.954-6.85l-.636-.167-.521-.366-.49-.422-.57-.285-.424-.535-.742.015-.518-.374-.555-.31-.541-.33-.523-.366-.428-.529-.736.007-.447-.498-.53-.352-.62-.193-.584-.26-.433-.52-.525-.36-.558-.306-.608-.216-.535-.345-.469-.459-.565-.292-.675-.098-.53-.353-.592-.245-.541-.336-.527-.355-.562-.3-.406-.567-.632-.176-.491-.42-.61-.216-.66-.126-.372-.627-.545-.326-.708-.047-.566-.29-.56-.302-.529-.354-.578-.272-.523-.366-.51-.39-.552-.353-.654.152-.277.58-.37.53.185.653.67.168.493.416.492.418.577.27.552.314.576.273.426.533.574.276.668.114.585.258.5.402.546.325.422.537.565.292.578.271.667.113.446.498.572.28.572.278.512.384.677.098.395.585.676.101.452.487.65.146.57.282.542.332.562.297.456.482.615.205.397.583.6.23.684.087.51.388.474.45.671.107.373.625.573.28.688.08.5.403.537.342.498.41.607.22.547.326.58.047.517-.107.594-.22-.088-.627.011-.628-.518-.363zm4.72-7.704l-.48-.439-.524-.365-.586-.256-.544-.328-.56-.298-.595-.24-.47-.458-.64-.161-.602-.228-.505-.394-.456-.48-.606-.22-.491-.42-.655-.137-.46-.472-.525-.36-.703-.053-.388-.599-.586-.253-.691-.074-.553-.313-.361-.645-.696-.066-.474-.449-.54-.334-.633-.176-.504-.398-.525-.36-.593-.245-.5-.403-.52-.372-.603-.225-.524-.365-.683-.085-.44-.51-.486-.43-.582-.263-.528-.357-.55-.318-.693-.072-.538-.338-.533-.35-.538-.34-.566-.072-.646-.158-.287.588-.35.541.215.634.623.217.554.312.56.297.476.446.55.319.539.338.511.384.49.421.723.017.45.49.509.392.53.349.668.114.596.236.56.302.512.384.582.26.51.386.512.384.504.398.483.434.583.262.67.107.552.315.425.534.543.331.66.128.477.444.574.275.591.248.604.227.47.458.563.294.527.358.492.418.61.215.645.153.477.444.638.168.522.367.49.422.57.285.425.536.744-.014.5.217.538-.112.405-.354.178-.534.011-.684-.629-.272zm4.072-7.055l-.654-.136-.508-.391-.423-.538-.735.002-.474-.449-.505-.396-.543-.33-.633-.173-.504-.398-.624-.188-.552-.314-.51-.386-.6-.23-.42-.545-.574-.274-.594-.241-.59-.25-.41-.56-.641-.158-.448-.494-.673-.105-.462-.47-.534-.346-.574-.277-.529-.355-.632-.176-.514-.38-.471-.454-.59-.251-.623-.19-.6-.232-.395-.587-.541-.335-.65-.142-.486-.43-.515-.38-.566-.29-.666-.117-.552-.317-.507-.392-.553-.314-.546-.326-.661-.128-.498-.206-.67-.183-.446.529-.034.637.152.593.516.327.541.334.503.397.546.325.598.235.425.534.558.304.68.092.58.265.52.37.54.335.534.344.5.405.669.11.4.576.569.287.578.267.65.146.525.36.44.509.579.268.48.439.676.096.597.239.46.474.502.4.537.34.612.21.519.373.556.307.496.415.727.012.436.515.483.433.559.303.687.08.585.258.486.428.482.439.523.365.586.255.544.33.562.3.597.24.564.335.588-.292.339-.448.208-.515-.035-.652-.529-.396zm-26.875 22.53l-.552-.316-.639-.163-.526-.36-.648-.148-.445-.502-.484-.433-.658-.13-.501-.403-.526-.36-.537-.341-.643-.158-.451-.491-.679-.096-.54-.337-.486-.428-.607-.22-.6-.23-.515-.381-.497-.411-.566-.292-.606-.221-.538-.34-.508-.396-.498-.41-.727-.024-.649-.014-.702.264-.003.769.316.647.66.271.433.52.518.375.723.019.361.646.701.056.443.504.572.28.677.1.43.526.578.27.46.474.54.336.6.233.608.219.465.466.603.226.614.21.47.455.553.315.507.393.556.31.524.364.734.002.442.507.706.038.642.05.72-.253-.248-.727-.161-.634-.416-.627z"
        fill="#fff"
      />
      <Path
        d="M4.08 57.677l.424-1.27-1.178-.805-.116-1.122-1.09-.849-.114-1.122.098-1.165-.086-1.104-.47-1.019.132-1.12-.659-1.007-.288-1.074-.235-1.104.066-1.126.45-1.076.934-.823.762-.849 1.058-.62 1.25-.25 1.28.142 1.262.247 1.267-.838 1.268-.044 1.269.475 1.268.231 1.271-.293 1.273-.34 1.273.532 1.272-.298 1.271.039 1.27.271 1.274.29 1.274-.346 1.275.017 1.277.153 1.277.131 1.279-.072 1.279-.65 1.279.259 1.279.394 1.279-.804 1.279.781 1.279-.234 1.279-.403 1.279.268 1.279.284 1.276-.133 1.277.383 1.275-.678 1.275-.211 1.275.742 1.27.114 1.271-.656 1.269.244 1.269.044 1.266-.447 1.27.838 1.272-.08 1.27-.801 1.269.195 1.269.508 1.266-.591 1.267.318 1.266.268 1.265-.05 1.27-.131 1.271.001 1.27.391 1.27-.298 1.268.259 1.269-.952 1.268.178 1.267-.174 1.267.323 1.266.257 1.269.008 1.264-.57 1.271.17 1.215.673 1.224.1.836.775.743.698.585.841.657.932.348 1.067-.601 1.078.346 1.108-.425 1.047-.255 1.064-.033 1.111-.614.987.524 1.271-1.314.794.422 1.271-.297 1.072-1.293.767-.041 1.13-.073 1.148-1.031.808-.579.964-.385 1.051-.62.952-.338 1.079-.848.847-.707.914-.333 1.1-1.236.652-.61.954-.706.903-.167 1.192-.91.799-.468 1.036-.922.79-.075 1.26-.792.862-.57.987-1.403.517-.227 1.182-.799.858-.453 1.056-1.139.659-1.058.703-.507 1.024-.21 1.2-1.055.701-.537 1.007-.408 1.085-1.328.53-.171 1.227-1.275.558-.16 1.23-1.57.371-.233 1.195-.39 1.099-.88.795-.532 1.012-1.3.535-1.054.685-.136 1.234-.039-.103.323 1.124.103 1.12-.11 1.117.098 1.115-.923 1.109.639 1.113-.639.61.507 1.061-.41.354h-.821l-.919-.313-13.034.244-2.052.038-1.642.031-2.145-.354.024-.566-.364-1.121.482-1.116-.11-1.115-.032-1.11-.536-1.115.167-1.113-.132-1.122-.113.247-.064-1.26-.846-.814-1.355-.508.051-1.372-.752-.877-1.223-.593-.468-1.058-.492-1.044-.826-.842-1.384-.506-.611-.974-.23-1.197-1.144-.65-.03-1.311-1.109-.672-.562-.996-.87-.814-.78-.865-.974-.753-.154-1.223-.84-.827-1.022-.725-.468-1.035-.823-.84-.73-.892-.525-1.005-1.118-.68-.325-1.11-.672-.92-.5-1.008-.464-1.024-.998-.748-1.038-.744-.202-1.16-.002-1.247-1.316-.621-.71-.913-.592-.97.136-1.269-.412-1.026-1.139-.756-.548-.989z"
        fill="#636566"
      />
      <Path
        d="M10.444 57.516l.401-1.264-1.112-.803-.11-1.118-1.028-.845-.11-1.117.094-1.16-.082-1.1-.443-1.015.124-1.115-.622-1.003-.272-1.07-.222-1.1.062-1.12.426-1.072.881-.82.72-.845 1-.617 1.18-.25 1.208.142 1.193.245 1.196-.834 1.198-.044 1.198.474 1.198.23 1.2-.292 1.202-.34 1.203.53 1.201-.296 1.2.038 1.2.271 1.203.288 1.204-.345 1.204.018 1.206.152 1.206.131 1.207-.073 1.208-.647 1.208.259 1.208.392 1.208-.8 1.208.777 1.208-.233 1.208-.402 1.208.267 1.208.283 1.205-.132 1.206.382 1.204-.676 1.204-.21 1.204.739 1.2.113 1.2-.652 1.199.242 1.198.044 1.196-.446 1.2.835 1.2-.08 1.2-.797 1.199.194 1.198.506 1.196-.589 1.196.317 1.196.267 1.195-.05 1.2-.13 1.2.001 1.2.39 1.198-.298 1.198.258 1.198-.947 1.199.176 1.196-.173 1.196.322 1.196.256 1.198.007L81 39.575l1.2.17 1.147.67 1.156.099.79.772.701.695.553.838.62.928.33 1.063-.569 1.073.327 1.104-.4 1.043-.242 1.059-.031 1.107-.58.983.494 1.266-1.24.79.398 1.266-.28 1.068-1.221.764-.04 1.124-.067 1.144-.975.805-.546.96-.364 1.047-.586.947-.319 1.075-.801.844-.667.91-.315 1.097-1.167.648-.576.95-.667.9-.158 1.186-.86.796-.441 1.033-.871.786-.07 1.254-.75.86-.538.982-1.325.515-.214 1.177-.754.854-.428 1.052-1.076.656-1 .7-.478 1.02-.198 1.196-.996.698-.508 1.003-.385 1.08-1.255.529-.161 1.222-1.204.555-.152 1.225-1.482.37-.22 1.19-.367 1.094-.833.792-.502 1.008-1.227.532-.996.683-.128 1.229-.037-.103.305 1.12.097 1.115-.105 1.112.094 1.11-.872 1.105.603 1.109-.603.607.479 1.057-.388.353h-.775l-.868-.313-12.31.244-1.937.038-1.55.031-2.027-.353.023-.564-.344-1.116.455-1.112-.103-1.11-.031-1.106-.506-1.11.157-1.108-.124-1.118-.107.246-.06-1.255-.8-.81-1.28-.506.05-1.367-.71-.873-1.156-.59-.442-1.054-.465-1.04-.78-.838-1.307-.504-.577-.97-.218-1.193-1.08-.647-.029-1.306-1.046-.669-.531-.991-.821-.812-.737-.861-.92-.75-.146-1.218-.794-.824-.965-.721-.441-1.031-.778-.837-.689-.889-.496-1-1.056-.678-.307-1.105-.634-.916-.473-1.004-.438-1.02-.943-.745-.98-.74-.19-1.157-.003-1.241-1.243-.619-.67-.909-.559-.965.129-1.264-.39-1.022-1.075-.753-.518-.985z"
        fill="#8B8E8F"
      />
      <Path
        d="M45.454 42.903l1.372-.498 1.371.376 1.374-.151 1.374.184 1.376.038 1.374.073 1.374-.266 1.374.028 1.376.281 1.376-.055 1.374-.015 1.376.067 1.378-.445 1.372.257 1.37.105 1.37.05 1.379-.192 1.376.23 1.376-.09 1.38-.431 1.379.29 1.38-.011 1.374-.34 1.38.48 1.383-.303 1.388-.12 1.387.253.777-.128-.806.784.078.694-.147.68-.27.668-1.135.543-.718.593.42.78-1.175.513.64.819-1.584.47.228.794-1.052.538-.672.614.098.806-1.427.427-.304.717-.683.618-.733.606-.897.33-1.208.466-1.352-.296-1.35-.02-1.349.29-1.35-.428-1.349-.024-1.35.276-1.35-.08-1.355-.2-1.356.01-1.354.124-1.354.091-1.351-.092-1.352.038-1.354.054-1.356.177-1.356-.318-1.354.428-1.354-.281-1.354.008-1.356.332-1.356-.157-1.356.125L40.923 55l-1.356-.101-1.352-.03-1.352-.308-1.354.387-1.358-.12-1.356.063-1.356-.287-1.36.383-1.36-.427-1.36.473-1.354-.404-1.362.267-1.363.173-1.367-.254-1.374.032-.685-.633-1.292-.453-.49-.674-.137-.759-1.29-.464-.589-.645-.295-.71-.05-.75-.954-.561-.505-.65-.795-.578.452-.79-1.217-.51-.004-.707-.078-.688-1.07-.56-.113-.683.104-.693-.363-.477 1.256.038 1.374-.316 1.376.297 1.374-.15 1.374.02 1.374-.323 1.376.146 1.376.132 1.374.18 1.376-.067 1.376-.047 1.371.09 1.37-.478 1.371.579 1.379-.447 1.376.367 1.376.127 1.38-.127 1.379.113 1.38.01 1.374-.508 1.38.283 1.383-.392 1.356.407 1.405.107z"
        fill="#EDEFF0"
      />
      <Path
        d="M71.645 4.515l-.094-.271.03-.27.074-.262.092-.254-.001-.28.149-.234.11-.243.119-.239.063-.273.117-.249.19-.197.15-.228.198-.186.244-.133.217-.155.203-.172.24-.117.183-.217.26-.078.238-.126.234-.15.259-.083.267-.051.278.03.263-.077.27.066.267-.004.26.076.263.027.279-.025.258.079.273.038.252.105.241.128.225.155.21.172.227.148.171.216.152.227.219.159.218.169.075.278.2.189.136.233.044.275.11.242.105.246.192.227-.084.295.096.256.082.266.031.272-.035.273-.097.263-.08.259.062.289-.136.243-.13.238-.046.271-.152.225-.132.231-.165.21-.092.266-.125.25-.282.104-.2.175-.188.188-.187.197-.244.114-.22.154-.26.072-.207.198-.263.066-.245.13-.294-.098-.257.06-.264.01-.263.097-.264-.076-.267.01-.254-.091-.274.013-.276-.015-.248-.11-.213-.185-.293-.011-.238-.133-.183-.214-.241-.126-.23-.149-.151-.235-.245-.142-.163-.22-.07-.284-.122-.239-.238-.174-.001-.299-.166-.22-.076-.259-.138-.242.026-.283-.013-.269-.16-.257.145-.275z"
        fill="#636566"
      />
      <Path
        d="M69.524 4.478l-.024-.272.073-.265.094-.257.016-.27.111-.246.055-.264.082-.257.088-.257.09-.262.273-.146.037-.308.273-.124.1-.278.276-.102.207-.168.185-.2.23-.142.237-.13.205-.197.274-.055.285.003.254-.079.26-.055.25-.147.28.106.264-.025.265.021.266.006.286-.089.272.045.25.12.222.178.273.042.243.117.221.156.21.17.28.073.143.25.197.182.27.117.119.254.217.178.045.293.179.207.18.215.013.288.074.258.118.246.033.268.057.264.03.268v.27l-.048.267.086.279-.087.262-.147.243.075.301-.19.222-.004.292-.215.197-.12.24-.157.22-.18.199-.188.19-.18.195-.198.176-.11.289-.3.049-.2.184-.234.13-.244.112-.235.135-.266.055-.24.144-.272.029-.261.1-.282-.12-.266.132-.271-.041-.274.003-.246-.17-.266-.026-.267-.042-.251-.095-.267-.06-.23-.144-.268-.078-.229-.15-.145-.256-.287-.077-.126-.26-.24-.144-.087-.277-.152-.218-.24-.164-.074-.267-.13-.234-.068-.261-.097-.248-.163-.235-.056-.266.06-.281-.01-.266-.09-.265z"
        fill="#8B8E8F"
      />
      <Path
        d="M2.645 25.515l-.094-.271.03-.27.074-.262.092-.255-.001-.28.149-.233.11-.243.119-.239.063-.273.117-.249.19-.197.15-.228.198-.186.244-.133.217-.155.203-.172.24-.117.183-.217.26-.078.238-.126.234-.15.259-.083.267-.051.278.03.263-.077.27.066.267-.004.26.076.263.027.279-.025.258.079.273.038.252.105.241.128.225.155.21.172.227.148.171.216.152.227.219.159.218.169.075.278.2.189.136.233.044.276.11.241.104.246.194.227-.085.295.096.256.082.265.031.273-.035.273-.097.264-.08.258.062.289-.136.243-.13.238-.046.271-.152.225-.132.231-.165.21-.092.266-.125.25-.282.104-.2.175-.188.188-.187.197-.244.114-.22.154-.26.072-.208.198-.262.066-.244.13-.295-.098-.257.06-.264.01-.263.097-.264-.076-.267.01-.254-.091-.274.013-.276-.015-.248-.11-.213-.185-.293-.011-.237-.133-.184-.215-.241-.125-.23-.149-.151-.235-.245-.142-.163-.22-.07-.284-.122-.239-.238-.174-.001-.299-.166-.22-.076-.259-.139-.242.027-.283-.013-.268-.16-.258.145-.275z"
        fill="#636566"
      />
      <Path
        d="M.524 25.478L.5 25.206l.073-.265.094-.257.016-.27.111-.246.055-.264.082-.257.088-.257.09-.262.273-.146.037-.308.274-.124.1-.277.275-.103.207-.168.185-.2.23-.142.237-.13.206-.197.273-.055.285.003.254-.079.26-.055.25-.147.28.106.264-.025.265.021.266.006.286-.089.272.045.25.12.222.178.273.042.243.117.221.156.21.17.28.073.143.25.197.181.27.118.119.254.217.178.045.293.179.207.18.215.013.288.074.258.118.246.033.268.057.264.03.268v.27l-.048.267.086.279-.087.262-.147.243.074.301-.189.222-.004.292-.215.197-.12.24-.157.22-.18.199-.188.19-.18.195-.199.176-.11.288-.299.05-.2.184-.234.13-.244.112-.235.135-.266.055-.24.144-.273.029-.26.1-.282-.12-.266.132-.271-.041-.274.003-.246-.17-.266-.026-.267-.042-.251-.095-.267-.06-.23-.144-.268-.078-.229-.15-.145-.256-.287-.077-.126-.26-.24-.144-.087-.277-.152-.218-.24-.164-.074-.267-.13-.234-.068-.261-.097-.248-.162-.235-.057-.266.06-.281-.01-.266-.09-.265z"
        fill="#8B8E8F"
      />
      <Path
        d="M87.645 29.515l-.094-.271.03-.27.074-.262.092-.255-.001-.28.149-.233.11-.243.119-.239.063-.273.117-.249.19-.197.15-.228.198-.186.244-.133.217-.155.203-.172.24-.117.183-.217.26-.078.238-.126.234-.15.259-.083.267-.051.278.03.263-.077.27.066.267-.004.26.076.263.027.279-.025.258.079.273.038.252.105.241.128.225.155.21.172.227.148.171.216.152.227.219.159.218.169.075.278.2.189.136.233.044.276.11.241.105.246.192.227-.084.295.096.256.082.265.031.273-.035.273-.097.264-.08.258.062.289-.136.243-.13.238-.046.271-.152.225-.132.231-.165.21-.092.266-.125.25-.282.104-.2.175-.188.188-.187.197-.244.114-.22.154-.26.072-.207.198-.263.066-.245.13-.294-.098-.257.06-.264.01-.263.097-.264-.076-.267.01-.254-.091-.274.013-.276-.015-.248-.11-.213-.185-.293-.011-.238-.133-.183-.215-.241-.125-.23-.149-.151-.235-.245-.142-.163-.22-.07-.284-.122-.239-.238-.174-.001-.299-.166-.22-.076-.259-.138-.242.026-.283-.013-.268-.16-.258.145-.275z"
        fill="#636566"
      />
      <Path
        d="M85.524 29.478l-.024-.272.073-.265.094-.257.016-.27.111-.246.055-.264.082-.257.088-.257.09-.262.273-.146.037-.308.273-.124.1-.277.276-.103.207-.168.185-.2.23-.142.237-.13.205-.197.274-.055.285.003.254-.079.26-.055.25-.147.28.106.264-.025.265.021.266.006.286-.089.272.045.25.12.222.178.273.042.243.117.221.156.21.17.28.073.143.25.197.181.27.118.119.254.217.178.045.293.179.207.18.215.013.288.074.258.118.246.033.268.057.264.03.268v.27l-.048.267.086.279-.087.262-.147.243.075.301-.19.222-.004.292-.215.197-.12.24-.157.22-.18.199-.188.19-.18.195-.198.176-.11.288-.3.05-.2.184-.234.13-.244.112-.235.135-.266.055-.24.144-.272.029-.261.1-.282-.12-.266.132-.271-.041-.274.003-.246-.17-.266-.026-.267-.042-.251-.095-.267-.06-.23-.144-.268-.078-.229-.15-.145-.256-.287-.077-.126-.26-.24-.144-.087-.278-.152-.217-.24-.164-.074-.267-.13-.234-.068-.261-.097-.248-.163-.235-.056-.266.06-.281-.01-.266-.09-.265z"
        fill="#8B8E8F"
      />
    </Svg>
  );
}

export function EmptySearchResultOfModal() {
  const { t } = useTranslation();
  const { space, colors } = useTheme();
  return (
    <Box
      flexDirection="column"
      gap={space[4]}
      alignItems="center"
      mt={space[12]}>
      <EmptyCase />
      <Typography.LargeBody color={colors.palette.fwdGreyDark}>
        {t('emptyCase')}
      </Typography.LargeBody>
    </Box>
  );
}

function SearchableDropdownInner<T, V>(
  {
    label,
    modalTitle,
    actionLabel,
    disabled,
    preventPopup,
    hint,
    error,
    isError,
    isModal,
    type,
    value,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    getDisplayedLabel,
    getExternalDisplayedLabel,
    searchable,
    searchMode,
    searchLabel,
    onQuery,
    multiline,
    onFocus,
    onBlur,
    inputStyle,
    dropDownModalStyle,
    highlight,
    getItemDisabled,
    keyExtractor,
    emptyMessage,
    ...viewProps
  }: SearchableDropdownProps<T, V>,
  ref: React.ForwardedRef<SearchableDropdownRef>,
) {
  type ||= 'single'; // because default param not working
  const { colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const textFieldRef = useRef<TextFieldRef>(null);
  const getItemValueRef = useLatest(getItemValue);

  const getExternalDisplayedLabelRef = useLatest(getExternalDisplayedLabel);
  const getDisplayedLabelRef = useLatest(getDisplayedLabel || getItemLabel);
  const displayedLabel = useMemo(() => {
    if (getExternalDisplayedLabelRef.current) {
      return getExternalDisplayedLabelRef.current();
    }
    if (type === 'single') {
      const selectedItem = data?.find?.(
        item => getItemValueRef.current?.(item) === value,
      );
      return selectedItem ? getDisplayedLabelRef.current?.(selectedItem) : '';
    } else if (type === 'multiple') {
      return data
        ?.filter?.(item => value?.includes(getItemValueRef.current?.(item)))
        .map(i => getDisplayedLabelRef.current?.(i))
        .join(', ');
    }
  }, [
    data,
    getDisplayedLabelRef,
    getExternalDisplayedLabelRef,
    getItemValueRef,
    type,
    value,
  ]);

  useImperativeHandle(ref, () => textFieldRef.current as SearchableDropdownRef);

  const renderSearchPanel = () => {
    if (isModal) {
      return (
        <SearchableDropdownModal
          visible={modalVisible}
          title={modalTitle ?? label}
          data={data}
          searchable={searchable}
          searchLabel={searchLabel ?? `Search for ${label}`}
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          onDismiss={() => {
            setModalVisible(false);
            onQuery?.('');
            textFieldRef.current?.blur();
            onBlur?.();
          }}
          value={value as V}
          onDone={(item: V) => onChange?.(item)}
          dropDownModalStyle={dropDownModalStyle}
          getItemDisabled={getItemDisabled}
        />
      );
    }
    return (
      modalVisible && (
        <SearchableDropdownPanel
          keyExtractor={keyExtractor}
          title={modalTitle ?? label}
          actionLabel={actionLabel}
          data={data}
          searchable={searchable}
          searchMode={searchMode}
          searchLabel={searchLabel ?? label}
          onQuery={onQuery}
          getItemValue={getItemValue}
          getItemLabel={getItemLabel}
          getItemDisabled={getItemDisabled}
          emptyMessage={emptyMessage}
          onDismiss={() => {
            setModalVisible(false);
            onQuery?.('');
            textFieldRef.current?.blur();
            onBlur?.();
          }}
          {...(type === 'single'
            ? {
                type: 'single',
                value: value as V,
                onDone: (item: V) => onChange?.(item),
              }
            : {
                type: 'multiple',
                value: (value || []) as V[],
                onDone: (items: V[]) =>
                  (onChange as (value: V[]) => void)?.(items),
              })}
        />
      )
    );
  };

  return (
    <View {...viewProps}>
      <TextField
        ref={textFieldRef}
        label={label}
        value={displayedLabel}
        disabled={disabled}
        hint={hint}
        error={error}
        isError={isError}
        onFocus={() => {
          if (!preventPopup) {
            setModalVisible(true);
            onFocus?.();
          }
          Keyboard.dismiss();
        }}
        onBlur={Keyboard.dismiss}
        right={
          disabled ? null : (
            <Pressable
              hitSlop={ICON_HIT_SLOP}
              onPress={() => textFieldRef.current?.focus()}>
              <Icon.Dropdown
                fill={disabled ? colors.primaryVariant : colors.primary}
              />
            </Pressable>
          )
        }
        showSoftInputOnFocus={false}
        multiline={multiline}
        autoExpand={multiline}
        highlight={highlight}
        inputStyle={inputStyle}
      />
      {renderSearchPanel()}
    </View>
  );
}

const SearchableDropdown = forwardRef(SearchableDropdownInner) as <T, V>(
  props: SearchableDropdownProps<T, V> & {
    ref?: React.ForwardedRef<SearchableDropdownRef>;
  },
) => ReturnType<typeof SearchableDropdownInner>;
export default SearchableDropdown;
