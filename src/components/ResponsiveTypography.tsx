import React, { useMemo } from 'react';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { Typography } from 'cube-ui-components';

interface ResponsiveTextProps extends Typography.TextProps {
  TypographyDefault: React.FC<Typography.TextProps>;
  TypographyNarrow?: React.FC<Typography.TextProps>;
  TypographyWide?: React.FC<Typography.TextProps>;
}

export default function ResponsiveText({
  TypographyDefault = Typography.Body,
  TypographyWide,
  TypographyNarrow,
  ...textProps
}: ResponsiveTextProps) {
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const TypographyComponent = useMemo(() => {
    if (isWideScreen && TypographyWide) {
      return TypographyWide;
    } else if (isNarrowScreen && TypographyNarrow) {
      return TypographyNarrow;
    }

    return TypographyDefault;
  }, [isWideScreen, isNarrowScreen]);

  return <TypographyComponent {...textProps} />;
}
