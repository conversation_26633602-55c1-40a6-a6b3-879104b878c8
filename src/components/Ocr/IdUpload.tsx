import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Body,
  Box,
  Button,
  Column,
  H6,
  Icon,
  Label,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Image,
  Modal,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Animated, {
  SharedValue,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { useTranslation } from 'react-i18next';
import * as Crypto from 'expo-crypto';
import { getDocumentUri } from 'utils/helper/fileUtils';
import ImagePicker, {
  DEFAULT_MAX_PICKER_FILE_SIZE,
} from 'components/ImagePicker';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useLatest from 'hooks/useLatest';
import useToggle from 'hooks/useToggle';
import DialogPhone from 'components/Dialog.phone';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { DocumentType } from 'types/document';
import {
  ImagePickerOnDoneProps,
  OcrFeatureProps,
  resizeImage,
} from 'components/ImagePicker/utils';
import { useGetFeatureFlags } from 'hooks/useGetFeatureFlags';
import { OcrModal } from 'features/ocr/components';
import { useCheckNewOcrEnable } from 'hooks/useCheckNewOcrEnable';
import { OcrFile } from 'types/ocr';
import { moduleConfigs } from 'utils/config/module';
import { country } from 'utils/context';

export interface IdUploadProps extends OcrFeatureProps {
  variant?: 'default' | 'highlight';
  docType?: DocumentType;
  disabled?: boolean;
  verifying?: boolean;
  defaultImage?: {
    base64?: string;
    name?: string;
    thumbnail?: string;
  };
  logo?: React.ComponentType<unknown>;
  labels?: {
    title: string;
    idle?: {
      description: string;
    };
  };
  onFinish?: (
    image: {
      base64: string;
      name: string;
      thumbnail?: string;
    },
    progress: SharedValue<number>,
  ) => void;
  onDelete?: () => void;
  style?: StyleProp<ViewStyle>;
  allowToRetake?: boolean;
}

export interface IdUploadRef {
  resetAndOpen: () => void;
  reset: () => void;
  setStatus: (status: 'idle' | 'uploading' | 'finished') => void;
}

const IdUpload = forwardRef<IdUploadRef, IdUploadProps>(
  (
    {
      variant = 'default',
      docType,
      disabled,
      verifying,
      defaultImage,
      onFinish,
      onDelete,
      labels,
      logo,
      style,
      allowToRetake = true,
    }: IdUploadProps,
    ref: React.ForwardedRef<IdUploadRef>,
  ) => {
    const { t } = useTranslation(['common']);
    const { colors, space, sizes } = useTheme();
    const frontIdInstructionShown = useRef(false);
    const [
      frontIdInstructionVisible,
      showFrontIdInstruction,
      hideFrontIdInstruction,
    ] = useToggle();
    const [imagePickerVisible, showImagePicker, hideImagePicker] = useToggle();
    const [isOcrModalVisible, showOcrModal, hideOcrModal] = useToggle();
    const [status, setStatus] = useState<'idle' | 'uploading' | 'finished'>(
      defaultImage?.base64 || defaultImage?.thumbnail ? 'finished' : 'idle',
    );
    const [internalImage, setInternalImage] = useState<{
      uri: string;
      base64: string;
      name: string;
    } | null>(null);
    const progress = useSharedValue(0);

    const isNewOcr = useCheckNewOcrEnable();
    const { data: featureFlags } = useGetFeatureFlags();
    const isAlbumEnabled = featureFlags?.ocr_album.available ?? false;
    const onDeleteRef = useLatest(onDelete);
    const reset = useCallback(async () => {
      hideFrontIdInstruction();
      hideImagePicker();
      setInternalImage(null);
      setStatus('idle');
      progress.value = 0;
      onDeleteRef.current?.();
    }, [hideFrontIdInstruction, hideImagePicker, onDeleteRef, progress]);

    useImperativeHandle(ref, () => ({
      resetAndOpen: async () => {
        await reset();
        showImagePicker();
      },
      reset,
      setStatus,
    }));

    const { isTabletMode } = useLayoutAdoptionCheck();

    const onUploadButtonPress = useCallback(() => {
      if (
        docType === DocumentType.FrontID &&
        !frontIdInstructionShown.current
      ) {
        showFrontIdInstruction();
        return;
      }

      if (isNewOcr && !isAlbumEnabled) {
        showOcrModal();
      } else {
        showImagePicker();
      }
    }, [
      docType,
      isAlbumEnabled,
      isNewOcr,
      showFrontIdInstruction,
      showImagePicker,
      showOcrModal,
    ]);
    const onAcceptOcrInstruction = useCallback(() => {
      frontIdInstructionShown.current = true;
      hideFrontIdInstruction();
      onUploadButtonPress();
    }, [hideFrontIdInstruction, onUploadButtonPress]);

    const imageSource = useMemo(() => {
      let imageUri = '';
      if (internalImage?.uri) {
        imageUri = internalImage?.uri;
      } else if (defaultImage?.base64) {
        imageUri = `data:image/png;base64,${defaultImage.base64}`;
      } else if (defaultImage?.name) {
        imageUri = getDocumentUri(defaultImage.name);
      }
      return { uri: imageUri };
    }, [defaultImage?.base64, defaultImage?.name, internalImage?.uri]);

    const handleShutter = async (file: OcrFile) => {
      const [resizedResult, size] = await resizeImage(
        {
          canceled: false,
          assets: [file],
        },
        {
          maxHeight: 1200,
          maxWidth: 1200,
        },
        DEFAULT_MAX_PICKER_FILE_SIZE,
      );
      if (resizedResult) {
        onImagePickerDone({
          finished: true,
          file: {
            base64: resizedResult.base64 || '',
            uri: file.uri,
            size: size || 0,
            name: Crypto.randomUUID(),
            isAttachment: false,
          },
        });
      }
    };

    const onImagePickerDone = async ({
      finished,
      failed,
      file,
    }: ImagePickerOnDoneProps) => {
      try {
        setStatus('uploading');
        if (finished) {
          progress.value = 0;
          onDelete?.();
          const { uri, base64, name } = file;
          setInternalImage({
            uri,
            base64,
            name,
          });
          await onFinish?.(
            {
              base64: base64,
              name: name,
            },
            progress,
          );
        } else if (failed) {
          setStatus('idle');
        }
      } catch {
        reset();
      }
    };

    return (
      <Column
        marginTop={space[isTabletMode ? 5 : 4]}
        marginBottom={space[5]}
        style={style}>
        {status === 'idle' && (
          <IdleView
            disabled={disabled}
            onPress={onUploadButtonPress}
            labels={labels}
            logo={logo}
            variant={variant}
          />
        )}
        {status === 'uploading' && <UploadingView progress={progress} />}
        {status === 'finished' && verifying && <VerifyingView />}
        {status === 'finished' && !verifying && allowToRetake && (
          <Row alignItems="center" gap={space[8]}>
            <Column flex={1}>
              <Row alignItems="center" marginBottom={9}>
                <Label color={colors.primary} fontWeight="bold">
                  {labels?.title || t('common:ocr.upload.title')}
                </Label>
              </Row>
              <Row alignItems="center" alignSelf="stretch">
                <Box
                  borderRadius={sizes[2]}
                  overflow="hidden"
                  border={1}
                  borderColor={colors.palette.fwdGrey[100]}>
                  <Image
                    source={imageSource}
                    style={{
                      width: sizes[12],
                      height: sizes[12],
                      backgroundColor: colors.palette.fwdGrey[50],
                    }}
                    resizeMode="cover"
                  />
                </Box>
                <Box w={space[2]} />
                <Body style={{ flex: 1 }} numberOfLines={1}>
                  {defaultImage?.name || internalImage?.name}
                </Body>
                <TouchableOpacity
                  hitSlop={ICON_HIT_SLOP}
                  onPress={() => {
                    reset();
                  }}>
                  <Icon.Delete size={sizes[5]} fill={colors.palette.black} />
                </TouchableOpacity>
              </Row>
            </Column>
            <Button
              onPress={onUploadButtonPress}
              mini
              variant="secondary"
              text={t('common:ocr.re-upload')}
              icon={<Icon.Upload size={18} />}
            />
          </Row>
        )}
        {status === 'finished' && !verifying && !allowToRetake && (
          <Row alignItems="center" gap={space[8]}>
            <Column flex={1}>
              <Row alignItems="center" marginBottom={9}>
                <Label color={colors.primary} fontWeight="bold">
                  {labels?.title || t('common:ocr.upload.title')}
                </Label>
              </Row>
              <Row alignItems="center" alignSelf="stretch">
                <Box
                  borderRadius={sizes[2]}
                  overflow="hidden"
                  border={1}
                  borderColor={colors.palette.fwdGrey[100]}>
                  <Image
                    source={imageSource}
                    style={{
                      width: sizes[12],
                      height: sizes[12],
                      backgroundColor: colors.palette.fwdGrey[50],
                    }}
                    resizeMode="cover"
                  />
                </Box>
                <Box w={space[2]} />
                <Icon.TickCircleFill
                  size={sizes[5]}
                  fill={colors.palette.alertGreen}
                />
                <Box w={space[1]} />
                <Label fontWeight="bold" color={colors.palette.alertGreen}>
                  {t('common:ocr.verified')}
                </Label>
              </Row>
            </Column>
          </Row>
        )}
        <OcrInstruction
          visible={frontIdInstructionVisible}
          onDismiss={hideFrontIdInstruction}
          onAccept={onAcceptOcrInstruction}
        />
        <ImagePicker
          visible={imagePickerVisible}
          title={t('common:ocr.picker.title')}
          galleryPanel
          shouldOpenInAppCamera={isNewOcr}
          onCamera={showOcrModal}
          onDismiss={hideImagePicker}
          onDone={async ({
            finished,
            failed,
            file,
          }: ImagePickerOnDoneProps) => {
            await onImagePickerDone({
              finished,
              failed,
              file,
            } as ImagePickerOnDoneProps);
          }}
          config={{
            maxHeight: 1200,
            maxWidth: 1200,
          }}
        />
        {isNewOcr && (
          <OcrModal
            isVisible={isOcrModalVisible}
            onShutterPress={async file => {
              hideImagePicker();
              hideOcrModal();
              await handleShutter(file);
            }}
            onBackPress={hideOcrModal}
          />
        )}
      </Column>
    );
  },
);
export default IdUpload;

const IdleView = ({
  disabled,
  onPress,
  labels,
  logo: Logo,
  variant,
}: {
  disabled?: boolean;
  onPress?: () => void;
} & Pick<IdUploadProps, 'labels' | 'logo' | 'variant'>) => {
  const theme = useTheme();
  const { t } = useTranslation(['common']);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const title = labels?.title || t('common:ocr.upload.title');
  const description = labels?.idle
    ? labels.idle.description
    : t('common:ocr.upload.description');
  return (
    <>
      <TouchableOpacity disabled={disabled} onPress={onPress}>
        <Column
          borderRadius={8}
          border={variant === 'highlight' ? 2 : 1}
          borderColor={
            variant === 'highlight'
              ? theme.colors.primary
              : theme.colors.palette.fwdGrey[100]
          }
          backgroundColor={
            variant === 'highlight'
              ? theme.colors.primaryVariant3
              : theme.colors.background
          }
          paddingTop={8}
          paddingBottom={8}
          paddingLeft={8}
          paddingRight={12}>
          <Row alignItems="center">
            {Logo && <Logo />}
            <Column flex={1} marginLeft={4}>
              <Row alignItems="center">
                <Column>
                  <LargeLabel
                    fontWeight="bold"
                    color={
                      isTabletMode
                        ? theme.colors.secondary
                        : theme.colors.primary
                    }>
                    {title}
                  </LargeLabel>
                </Column>
                {moduleConfigs[country].eAppConfig.ocrInfoIcon && (
                  <Column ml={8}>
                    <TouchableOpacity onPress={() => setTooltipVisible(true)}>
                      <Icon.InfoCircle
                        size={24}
                        fill={theme.colors.palette.fwdAlternativeOrange[100]}
                      />
                    </TouchableOpacity>
                  </Column>
                )}
              </Row>
              {Boolean(description) && (
                <Body color={theme.colors.secondaryVariant}>{description}</Body>
              )}
            </Column>
            {isTabletMode && (
              <Button
                disabled={disabled}
                size="small"
                text={t('common:ocr.upload')}
                icon={<Icon.Upload size={18} />}
                onPress={onPress}
              />
            )}
          </Row>
        </Column>
      </TouchableOpacity>
      {/* TODO: consider removing */}
      <Modal
        visible={tooltipVisible}
        statusBarTranslucent
        transparent
        animationType="fade">
        <Backdrop>
          <Container
            style={isTabletMode && { maxWidth: '68%', alignSelf: 'center' }}>
            <TouchableOpacity
              hitSlop={ICON_HIT_SLOP}
              onPress={() => setTooltipVisible(false)}
              style={{ alignSelf: 'flex-end' }}>
              <Icon.Close fill={theme.colors.secondary} />
            </TouchableOpacity>
            <Box p={isTabletMode ? theme.space[6] : 0} pt={0}>
              <H6 fontWeight="bold">{t('common:ocr.scanID.info.title')}</H6>
              <Box h={theme.space[4]} />
              {(['1', '2', '3', '4', '5', '6'] as const).map(i => (
                <Row key={i}>
                  <LargeBody style={{ marginHorizontal: theme.space[2] }}>
                    •
                  </LargeBody>
                  <LargeBody>
                    {t(`common:ocr.scanID.info.point.${i}`)}
                  </LargeBody>
                </Row>
              ))}
            </Box>
          </Container>
        </Backdrop>
      </Modal>
    </>
  );
};

const UploadingView = ({
  progress,
  labels,
}: { progress: SharedValue<number> } & Pick<IdUploadProps, 'labels'>) => {
  const { colors, space } = useTheme();
  const progressStyle = useAnimatedStyle(
    () => ({
      width: `${progress.value * 100}%`,
    }),
    [progress],
  );
  const { t } = useTranslation(['common']);
  return (
    <Column>
      <Row alignItems="center">
        <Label color={colors.primary} fontWeight="bold">
          {labels?.title || t('common:ocr.upload.title')}
        </Label>
      </Row>
      <Column marginTop={space[3]} marginBottom={space[2]}>
        <Body color={colors.secondaryVariant}>{t('common:ocr.uploading')}</Body>
      </Column>
      <UploadProgressContainer>
        <UploadProgress style={progressStyle} />
      </UploadProgressContainer>
    </Column>
  );
};

const VerifyingView = ({ labels }: Pick<IdUploadProps, 'labels'>) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['common']);
  return (
    <Column>
      <Row alignItems="center">
        <Label color={colors.primaryVariant} fontWeight="bold">
          {labels?.title || t('common:ocr.upload.title')}
        </Label>
        <Box flex={1} />
        <Button
          disabled
          mini
          variant="secondary"
          text={t('common:ocr.upload')}
          icon={<Icon.Upload size={18} />}
        />
      </Row>
      <Column marginTop={space[3]} marginBottom={space[2]}>
        <Body color={colors.palette.fwdDarkGreen[20]}>
          {t('common:ocr.verifying')}
        </Body>
      </Column>
      <UploadProgressContainer>
        <UploadProgress
          style={{ width: '100%' }}
          color={colors.primaryVariant2}
        />
      </UploadProgressContainer>
    </Column>
  );
};

const OcrInstruction = ({
  visible,
  onDismiss,
  onAccept,
}: {
  visible?: boolean;
  onDismiss?: () => void;
  onAccept?: () => void;
}) => {
  const { colors, space } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation(['common']);

  return (
    <DialogPhone visible={visible}>
      <TouchableOpacity
        hitSlop={ICON_HIT_SLOP}
        onPress={onDismiss}
        style={{ alignSelf: 'flex-end' }}>
        <Icon.Close fill={colors.secondary} />
      </TouchableOpacity>
      <H6 fontWeight="bold">
        {t('common:ocr.instruction.takeA')}
        <H6 fontWeight="bold" color={colors.primary}>
          {t('common:ocr.instruction.closeUp')}
        </H6>
        {t('common:ocr.instruction.photo')}
      </H6>
      <Box h={space[4]} />
      <Row justifyContent="center">
        <Image
          source={require('./assets/upload-instruction.png')}
          style={{ width: 230, height: 195 }}
          resizeMode="contain"
        />
      </Row>
      <Column marginTop={space[4]}>
        <Body>{t('common:ocr.instruction.makeSure')}</Body>
        <Row marginTop={space[2]}>
          <Icon.Tick />
          <Body>
            {t('common:ocr.instruction.makeSure.clear')}
            <Body fontWeight="bold">
              {t('common:ocr.instruction.makeSure.clear.1')}
            </Body>
          </Body>
        </Row>
        <Row>
          <Icon.Tick />
          <Body>
            {t('common:ocr.instruction.makeSure.withoutFlash')}
            <Body fontWeight="bold">
              {t('common:ocr.instruction.makeSure.withoutFlash.1')}
            </Body>
          </Body>
        </Row>
      </Column>
      <Row justifyContent="center">
        <Button
          style={{
            marginTop: space[6],
            flex: 1,
            maxWidth: isWideScreen ? 400 : undefined,
          }}
          text={t('common:ocr.upload')}
          icon={<Icon.Camera size={18} />}
          onPress={onAccept}
        />
      </Row>
    </DialogPhone>
  );
};

const Backdrop = styled(View)(() => ({
  ...StyleSheet.absoluteFillObject,
  position: 'absolute',
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Container = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    padding: space[6],
    alignSelf: 'stretch',
    marginHorizontal: space[4],
  }),
);

const UploadProgressContainer = styled(View)(({ theme }) => ({
  height: theme.sizes[1],
  borderRadius: theme.borderRadius['x-small'],
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

const UploadProgress = Animated.createAnimatedComponent(
  styled(View)<{ color?: string }>(({ theme, color }) => ({
    height: theme.sizes[1],
    borderRadius: theme.borderRadius['x-small'],
    backgroundColor: color || theme.colors.primary,
  })),
);
