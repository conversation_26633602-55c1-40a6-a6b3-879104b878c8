import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Box, Button, H6, Icon, LargeBody, Row } from 'cube-ui-components';
import { TouchableOpacity } from 'react-native';
import { SaveModalProps } from './types';

export default function SaveModalTablet({
  dialogVisible,
  onConfirm,
  onCancel,
  onDeny,
  title,
  subTitle,
  isLoading,
  denyLabel,
  saveLabel,
  style,
  preventCloseOnLoading,
}: SaveModalProps) {
  const { colors, space } = useTheme();
  return (
    <SaveDialog visible={dialogVisible} style={style}>
      {(!preventCloseOnLoading || !isLoading) && (
        <CloseButton onPress={onCancel} hitSlop={ICON_HIT_SLOP}>
          <Icon.Close fill={colors.palette.black} />
        </CloseButton>
      )}
      <Wrapper>
        <H6 fontWeight="bold">{title}</H6>
        <Box h={space[4]} />
        <LargeBody>{subTitle}</LargeBody>
        <Box h={space[6]} />
        <Row gap={space[4]}>
          <ActionButton
            text={denyLabel}
            variant="secondary"
            size="medium"
            onPress={onDeny}
            disabled={isLoading}
          />
          <ActionButton
            text={saveLabel}
            variant="primary"
            size="medium"
            onPress={onConfirm}
            loading={isLoading}
          />
        </Row>
      </Wrapper>
    </SaveDialog>
  );
}

const SaveDialog = styled(DialogPhone)({
  minWidth: 380,
});

const Wrapper = styled(Box)(({ theme: { space } }) => ({
  padding: space[6],
  width: '100%',
}));

const CloseButton = styled(TouchableOpacity)(({ theme: { space } }) => ({
  padding: space[6],
  position: 'absolute',
  right: 0,
  top: 0,
  zIndex: 1,
}));

const ActionButton = styled(Button)({
  flex: 1,
});
