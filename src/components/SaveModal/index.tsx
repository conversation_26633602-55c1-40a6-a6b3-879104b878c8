import SaveModalTablet from './SaveModal.tablet';
import SaveModalPhone from './SaveModal.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

import { SaveModalProps as Props } from './types';

export type SaveModalProps = Props;

export default function SaveModal(props: Props) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return isTabletMode ? (
    <SaveModalTablet {...props} />
  ) : (
    <SaveModalPhone {...props} />
  );
}
