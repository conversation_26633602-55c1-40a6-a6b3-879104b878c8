import { memo } from 'react';
import { TouchableOpacity } from 'react-native';
import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Box, Button, H6, Icon, LargeBody, Row } from 'cube-ui-components';
import { SaveModalProps } from './types';

export default function SaveModalPhone({
  dialogVisible,
  onConfirm,
  onCancel,
  onDeny,
  title,
  subTitle,
  isLoading,
  denyLabel,
  saveLabel,
  preventCloseOnLoading,
}: SaveModalProps) {
  const { colors, space } = useTheme();
  return (
    <DialogPhone visible={dialogVisible}>
      {(!preventCloseOnLoading || !isLoading) && (
        <TouchableOpacity
          onPress={onCancel}
          hitSlop={ICON_HIT_SLOP}
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'flex-end',
          }}>
          <Icon.Close fill={colors.palette.black} />
        </TouchableOpacity>
      )}
      <H6 fontWeight="bold">{title}</H6>
      <Box h={space[4]} />
      <LargeBody>{subTitle}</LargeBody>
      <Box h={space[6]} />
      <Row>
        <Button
          style={{ flex: 1 }}
          text={denyLabel}
          variant="secondary"
          onPress={onDeny}
          disabled={isLoading}
        />
        <Box w={space[4]} />
        <Button
          style={{ flex: 1 }}
          text={saveLabel}
          variant="primary"
          onPress={onConfirm}
          loading={isLoading}
        />
      </Row>
    </DialogPhone>
  );
}
