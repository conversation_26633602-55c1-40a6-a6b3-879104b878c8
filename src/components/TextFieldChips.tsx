import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Icon, LargeLabel, Row, Text } from 'cube-ui-components';
import _ from 'lodash';
import React, {
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  DimensionValue,
  Keyboard,
  LayoutChangeEvent,
  NativeSyntheticEvent,
  Platform, ScrollView,
  StyleProp,
  TextInput,
  TextInputKeyPressEventData,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle
} from 'react-native';
import Animated, {
  cancelAnimation,
  Easing,
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

const SPACE = ' ';
const ENTER = 'Enter';
const BACKSPACE = 'Backspace';

type Status =
  | 'default'
  | 'filled'
  | 'focus'
  | 'error'
  | 'locked'
  | 'highlight'
  | 'highlightError';

type InputContainerProps = {
  status?: Status;
};

interface TextFieldChipsProps
  extends Omit<TextInputProps, 'onChange' | 'onChangeText' | 'value'> {
  label?: string;
  isError?: boolean;
  error?: string;
  hint?: string;
  left?: React.ReactElement<unknown> | React.ComponentType<unknown> | null;
  right?: React.ReactElement<unknown> | React.ComponentType<unknown> | null;
  inputContainerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  disabled?: boolean;
  value?: string[];
  fixedValue?: string[];
  errorIndexes?: number[];
  onChange?: (value: string[]) => void;
  // autoExpand?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  labelStyle?: TextStyle;
  validator?: (value: string) => boolean;
}

const InputContainer = styled.Pressable<InputContainerProps>(
  ({ theme, status }) => {
    const {
      container: {
        borderRadius,
        borderColor,
        backgroundColor,
        borderWidth,
        height,
        width,
      },
    } = theme.components.textField;

    return {
      borderRadius,
      borderColor,
      backgroundColor,
      borderWidth,
      height,
      width: width as DimensionValue,
      ...(status ? theme.components.textField.container[status] : undefined),
    };
  },
);

const InputWrapper = styled.Pressable(
  {
    flex: 1,
    zIndex: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  props => ({
    paddingVertical: props.theme.space[2],
    paddingLeft: props.theme.space[4],
    paddingRight: props.theme.space[3],
  }),
);

const Input = styled.TextInput<{ status: Status }>(
  ({ theme: { components }, status }) => ({
    flex: 1,
    alignSelf: 'stretch',
    fontSize: components.textField.input.fontSize.default,
    lineHeight: components.textField.input.lineHeight.default,
    fontFamily: components.textField.input.fontFamily,
    color: components.textField.input.color[status],
  }),
);

const Hint = styled.Text<{ status?: Status }>(
  ({
    theme: {
      components: { textField },
    },
    status,
  }) => {
    const { hint } = textField;
    return {
      ...hint,
      ...((status && hint[status]) ?? {}),
    };
  },
);

const AnimatedLabel = Animated.createAnimatedComponent(
  styled(Text)(() => ({
    alignSelf: 'flex-start',
  })),
);

const LabelWrapper = styled.View(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: 'center',
}));

const InternalInput = styled(Input)(() => ({
  paddingTop: 0,
  paddingBottom: 0,
  textAlignVertical: 'center',
  alignSelf: 'center',
}));

export default function TextFieldChips({
  label,
  isError,
  error,
  hint,
  left,
  right,
  fixedValue,
  onChange,
  style,
  inputContainerStyle,
  inputStyle,
  editable = true,
  disabled = false,
  onFocus: onFocusProp,
  onBlur: onBlurProp,
  onLayout: onLayoutProp,
  // onContentSizeChange: onContentSizeChangeProp,
  // autoExpand,
  // numberOfLines,
  labelStyle,
  validator,
  ...props
}: TextFieldChipsProps) {
  const theme = useTheme();
  const inputRef = useRef<TextInput>(null);
  const scrollRef = useRef<ScrollView>(null);
  const lastContentSize = useRef(0);

  const [placeholder, setPlaceholder] = useState<string>('');
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [labelStartingPosition, setLabelStartingPosition] = useState(
    theme.space[4],
  );
  const [labelWidth, setLabelWidth] = useState(0);
  const [measuredLabel, setMeasuredLabel] = useState(false);
  const placeholderTimer = useRef<NodeJS.Timeout>();
  // const [textAreaHeight, setTextAreaHeight] = useState<number>(0);

  const isControlled = props.value !== undefined;
  const validInputValue = isControlled ? props.value : undefined;
  const [uncontrolledValue, setUncontrolledValue] = useState<
    string[] | undefined
  >(validInputValue);
  const value = isControlled ? props.value : uncontrolledValue;
  const [inputValue, setInputValue] = useState<string>('');
  const animatedValue = useSharedValue(props.value ? 1 : 0);

  const {
    colors,
    space,
    components: { textField: textFieldTheme },
  } = theme;

  const status: Status = useMemo(() => {
    if (!_.isEmpty(error) || isError) {
      return 'error';
    }

    if (isFocused) {
      return 'focus';
    }

    if (disabled) {
      return 'locked';
    }

    if (!_.isEmpty(inputValue) || !_.isEmpty(fixedValue) || !_.isEmpty(value)) {
      return 'filled';
    }

    return 'default';
  }, [error, isError, isFocused, disabled, inputValue, fixedValue, value]);

  useEffect(() => {
    cancelAnimation(animatedValue);
    const moveLabelUp = () => {
      animatedValue.value = withTiming(1, {
        duration: 200,
        easing: Easing.in(Easing.ease),
      });
    };

    const moveLabelDown = () => {
      animatedValue.value = withTiming(0, {
        duration: 200,
        easing: Easing.in(Easing.ease),
      });
    };

    if (
      isFocused ||
      inputValue ||
      !_.isEmpty(value) ||
      !_.isEmpty(fixedValue)
    ) {
      moveLabelUp();
    } else {
      moveLabelDown();
    }
  }, [isFocused, animatedValue, inputValue, value, fixedValue]);

  const onAddNewItem = () => {
    if (!inputValue.trim()) return;
    setInputValue('');
    const newValue = [...(value ? value : []), inputValue];
    if (typeof onChange === 'function') {
      onChange(newValue);
    }
    if (!isControlled) {
      setUncontrolledValue(newValue);
    }
  };

  const onUndoLastItem = () => {
    if (inputValue || !value || value.length === 0) return;
    const newValue = value.slice(0, value.length - 1);
    if (typeof onChange === 'function') {
      onChange(newValue);
    }
    if (!isControlled) {
      setUncontrolledValue(newValue);
    }
    setInputValue(value.slice(-1)?.[0] ?? '');
  };

  const onKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => {
    const key = e.nativeEvent.key;
    switch (key) {
      case BACKSPACE: {
        onUndoLastItem();
      }
    }
  };

  const onDeleteItem = (index: number) => {
    if (!value || value.length === 0) return;
    const newValue = value.filter((item, curIdx) => curIdx !== index);
    if (typeof onChange === 'function') {
      onChange(newValue);
    }
    if (!isControlled) {
      setUncontrolledValue(newValue);
    }
  };

  const onFocus = () => {
    onFocusProp?.();
    setIsFocused(true);
  };

  const onBlur = () => {
    onBlurProp?.();
    setIsFocused(false);
    onAddNewItem();
  };

  const onLayout = (e: LayoutChangeEvent) => {
    onLayoutProp?.(e);
    setLabelStartingPosition(e.nativeEvent.layout.x);
  };

  useEffect(() => {
    if (isFocused || !label) {
      const placeholderProp = props.placeholder;
      if (placeholderProp) {
        placeholderTimer.current = setTimeout(
          () => setPlaceholder(placeholderProp),
          50,
        ) as unknown as NodeJS.Timeout;
      }
    } else {
      setPlaceholder('');
    }

    return () => {
      if (placeholderTimer.current) {
        clearTimeout(placeholderTimer.current);
      }
    };
  }, [isFocused, label, props.placeholder]);

  // const handleAutoExpand = (
  //   e: NativeSyntheticEvent<TextInputContentSizeChangeEventData>,
  // ) => {
  //   onContentSizeChangeProp?.(e);
  //   if (autoExpand) {
  //     const contentHeight = e.nativeEvent.contentSize.height;
  //     if (typeof numberOfLines === 'number') {
  //       if (
  //         numberOfLines > 0 &&
  //         contentHeight / textFieldTheme.input.lineHeight.default <=
  //           numberOfLines
  //       ) {
  //         setTextAreaHeight(contentHeight);
  //       }
  //     } else {
  //       setTextAreaHeight(contentHeight);
  //     }
  //   }
  // };

  // const autoExpandHeight = Math.max(
  //   textFieldTheme.container.height,
  //   textAreaHeight + space[4],
  // );

  const autoExpandHeight = textFieldTheme.container.height;

  const labelAnimStyles = useAnimatedStyle(() => {
    const scaleFactor = 0.75;
    const padding = space[1] / scaleFactor;
    if (!measuredLabel) {
      return {
        transform: [{ translateX: labelStartingPosition - padding }],
        marginRight: space[8] - padding * 2,
        fontSize: theme.components.textField.label.fontSize,
        lineHeight: theme.components.textField.label.lineHeight,
        paddingHorizontal: padding,
        color: theme.components.textField.label[status].default,
        backgroundColor: colors.palette.whiteTransparent,
      };
    }

    const translateY = interpolate(
      animatedValue.value,
      [0, 1],
      [0, -autoExpandHeight / 2],
    );
    const translateX = interpolate(
      animatedValue.value,
      [0, 1],
      [
        labelStartingPosition - padding,
        space[3] - (labelWidth * (1 - scaleFactor)) / 2,
      ],
    );
    const scale = interpolate(animatedValue.value, [0, 1], [1, scaleFactor]);

    const backgroundColor = interpolateColor(
      animatedValue.value,
      [0, 1],
      [
        colors.palette.whiteTransparent,
        label ? colors.background : colors.palette.whiteTransparent,
      ],
    );
    const color = interpolateColor(
      animatedValue.value,
      [0, 1],
      [
        theme.components.textField.label[status].default,
        theme.components.textField.label[status].floated,
      ],
    );

    return {
      transform: [
        {
          translateY,
        },
        { translateX },
        { scale },
      ],
      marginRight: space[8] - padding * 2,
      fontSize: theme.components.textField.label.fontSize,
      lineHeight: theme.components.textField.label.lineHeight,
      paddingHorizontal: padding,
      color,
      backgroundColor,
    };
  }, [
    animatedValue.value,
    label,
    status,
    space,
    colors,
    labelStartingPosition,
    autoExpandHeight,
    theme.components.textField,
    labelWidth,
    measuredLabel,
  ]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      const keyboardHideCallback = () => {
        inputRef.current?.blur();
      };
      const keyboardDidHideSubscription = Keyboard.addListener(
        'keyboardDidHide',
        keyboardHideCallback,
      );
      const keyboardWillHideSubscription = Keyboard.addListener(
        'keyboardWillHide',
        keyboardHideCallback,
      );

      return () => {
        keyboardDidHideSubscription?.remove();
        keyboardWillHideSubscription?.remove();
      };
    }
  }, []);

  return (
    <View style={style}>
      <InputContainer
        status={status}
        style={[
          // autoExpand && {
          //   height: autoExpandHeight,
          // },
          inputContainerStyle,
        ]}>
        <LabelWrapper>
          <AnimatedLabel
            onLayout={e => {
              setLabelWidth(e.nativeEvent.layout.width);
              setMeasuredLabel(true);
            }}
            style={[labelAnimStyles, labelStyle]}>
            {label}
          </AnimatedLabel>
        </LabelWrapper>

        <InputWrapper onPress={() => inputRef.current?.focus()}>
          {left && (
            <LeftUnitContainer>
              {React.isValidElement(left) ? left : React.createElement(left)}
            </LeftUnitContainer>
          )}

          <ScrollView
            ref={scrollRef}
            onLayout={onLayout}
            horizontal
            bounces={false}
            onContentSizeChange={size => {
              if (lastContentSize.current < size) {
                scrollRef.current?.scrollToEnd({ animated: false });
              }
              lastContentSize.current = size;
            }}
            showsHorizontalScrollIndicator={false}>
            <ChipContainer
              onPress={() => inputRef.current?.focus()}
              mr={
                (fixedValue && fixedValue.length > 0) ||
                (value && value.length > 0)
                  ? space[4]
                  : 0
              }>
              {fixedValue?.map((item, idx) => (
                <Chip
                  key={idx + '_fixed'}
                  text={item}
                  disabled
                  isError={validator ? !validator(item) : false}
                />
              ))}
              {value?.map((item, idx) => (
                <Chip
                  key={idx}
                  text={item}
                  onDelete={() => onDeleteItem(idx)}
                  disabled={disabled}
                  isError={validator ? !validator(item) : false}
                />
              ))}
            </ChipContainer>
            <InternalInput
              ref={inputRef}
              autoCorrect={false}
              cursorColor={theme.components.textField.label.focus.default}
              selectionColor={theme.components.textField.label.focus.default}
              placeholderTextColor={
                theme.components.textField.placeholder.color
              }
              // numberOfLines={numberOfLines}
              blurOnSubmit={false}
              onSubmitEditing={onAddNewItem}
              {...props}
              value={measuredLabel ? inputValue : ''}
              status={status}
              placeholder={placeholder}
              onChangeText={setInputValue}
              // onContentSizeChange={handleAutoExpand}
              onFocus={onFocus}
              onBlur={onBlur}
              onKeyPress={onKeyPress}
              style={[
                /*autoExpand && { height: textAreaHeight },*/ {
                  // minWidth: inputWidth,
                },
                inputStyle,
              ]}
              editable={!disabled && editable}
            />
          </ScrollView>

          {right && (
            <RightUnitContainer>
              {React.isValidElement(right) ? right : React.createElement(right)}
            </RightUnitContainer>
          )}
        </InputWrapper>
      </InputContainer>
      {(!_.isEmpty(hint) || !_.isEmpty(error)) && (
        <Hint status={status}>{error || hint}</Hint>
      )}
    </View>
  );
}

const LeftUnitContainer = styled.View(({ theme: { space } }) => ({
  paddingRight: space[3],
}));

const RightUnitContainer = styled.View(({ theme: { space } }) => ({
  paddingLeft: space[3],
}));

const ChipContainer = styled.Pressable<{ mr: number }>(({ theme, mr }) => ({
  flexDirection: 'row',
  gap: theme.space[2],
  marginRight: mr,
}));

const Chip = ({
  text,
  isError,
  disabled,
  onDelete,
}: {
  text: string;
  isError?: boolean;
  disabled?: boolean;
  onDelete?: () => void;
}) => {
  const { borderRadius, space, colors } = useTheme();
  return (
    <Row
      alignItems="center"
      gap={space[3]}
      px={space[3]}
      py={space[2]}
      borderWidth={1}
      borderRadius={borderRadius['x-small']}
      borderColor={
        isError
          ? colors.error
          : disabled
          ? '#DBDFE1'
          : colors.palette.fwdGrey[100]
      }
      backgroundColor={
        disabled ? colors.palette.fwdGrey[20] : colors.background
      }>
      <LargeLabel color={disabled ? '#879490' : colors.secondary}>
        {text}
      </LargeLabel>
      {!disabled && (
        <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={onDelete}>
          <Icon.CloseCircle size={20} fill={colors.palette.fwdGreyDarkest} />
        </TouchableOpacity>
      )}
    </Row>
  );
};
