import {
  LegacyRef,
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import styled from '@emotion/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import {
  LayoutChangeEvent,
  NativeScrollEvent,
  NativeSyntheticEvent,
  FlatList,
  Image,
  View,
} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import NavHeader from 'navigation/components/NavHeader';
import { Zoom, createZoomListComponent } from 'react-native-reanimated-zoom';
import useLatest from 'hooks/useLatest';
import useBoundStore from 'hooks/useBoundStore';
import { getDocumentUri } from 'utils/helper/fileUtils';
import Pdf from 'react-native-pdf';
import { urlRegex } from 'constants/regex';

export type ReviewDocumentImageType = {
  name: string;
  uri: string;
  base64: string;
};

const ZoomFlatList = createZoomListComponent(Animated.FlatList);

const Container = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
}));

const ZoomContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

export const ImageListView = memo(function ImageListView() {
  const { data, index: initialIndex } =
    useRoute<RouteProp<RootStackParamList, 'SellerExpImageList'>>().params;
  const [size, setSize] = useState<
    { width: number; height: number } | undefined
  >();

  const [index, setIndex] = useState(initialIndex);

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    const { width, height } = e.nativeEvent.layout;
    setSize({ width, height });
  }, []);

  const scrollX = useSharedValue(0);

  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollX.value = event.contentOffset.x;
  });

  const scrollRef = useRef<FlatList<ReviewDocumentImageType> | null>(null);

  const onPress = useCallback(
    (index: number) => {
      if (size) {
        setIndex(index);
        scrollRef.current?.scrollToIndex({
          index,
          animated: true,
        });
      }
    },
    [size],
  );

  const latestData = useLatest(data);

  const onMomentumScrollEnd = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      if (!size || size.width === 0) return 0;
      const index = event.nativeEvent.contentOffset.x / size.width;
      if (index >= 0 && index <= latestData.current.length - 1) {
        setIndex(event.nativeEvent.contentOffset.x / size.width);
      }
    },
    [latestData, size],
  );

  const { colors } = useTheme();
  const renderBackIcon = useCallback(
    () => <Icon.Close size={24} fill={colors.onBackground} />,
    [],
  );

  const renderItem = useCallback(
    ({ item }: { item: ReviewDocumentImageType }) => {
      return <Item width={size?.width} height={size?.height} item={item} />;
    },
    [size],
  );

  return (
    <Container>
      <NavHeader title={data[index]?.name} renderLeftIcon={renderBackIcon} />
      <ZoomContainer onLayout={onLayout}>
        {size && (
          <ZoomFlatList
            data={data}
            pagingEnabled
            horizontal
            renderItem={renderItem}
            decelerationRate="fast"
            ref={
              scrollRef as LegacyRef<Animated.FlatList<ReviewDocumentImageType>>
            }
            onScroll={scrollHandler}
            contentOffset={{ x: initialIndex * size.width, y: 0 }}
            style={{ flex: 1 }}
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={onMomentumScrollEnd}
            scrollEventThrottle={16}
          />
        )}
      </ZoomContainer>
      <Indicator data={data} onPress={onPress} activeIndex={index} />
    </Container>
  );
});
export default ImageListView;

const IndicatorContainer = styled.View<{ bottomInset: number }>(
  ({ theme, bottomInset }) => ({
    backgroundColor: theme.colors.background,
    paddingTop: theme.space[4],
    paddingBottom: bottomInset + 3 ? bottomInset : theme.space[4],
    alignItems: 'center',
  }),
);

const ImageContainer = styled.TouchableOpacity(({ theme }) => ({
  width: theme.sizes[14],
  height: theme.sizes[14],
  borderRadius: theme.borderRadius['x-small'],
  overflow: 'hidden',
  marginHorizontal: 6,
}));

const SImage = styled.Image(({ theme }) => ({
  width: theme.sizes[14],
  height: theme.sizes[14],
  borderRadius: theme.borderRadius['x-small'],
  overflow: 'hidden',
}));

const GreyBorder = styled.View(({ theme }) => ({
  width: '100%',
  height: '100%',
  borderRadius: theme.borderRadius['x-small'],
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  position: 'absolute',
  top: 0,
  left: 0,
}));

const PrimaryBorder = styled(Animated.View)(({ theme }) => ({
  width: '100%',
  height: '100%',
  borderRadius: theme.borderRadius['x-small'],
  borderWidth: 2,
  borderColor: theme.colors.primary,
  position: 'absolute',
  top: 0,
  left: 0,
}));

const Indicator = memo(
  ({
    data,
    onPress,
    activeIndex,
  }: {
    data: ReviewDocumentImageType[];
    onPress: (index: number) => void;
    activeIndex: number;
  }) => {
    const { bottom } = useSafeAreaInsets();
    const scrollRef = useRef<Animated.ScrollView | null>(null);
    const itemWidth = 68;

    useEffect(() => {
      scrollRef.current?.scrollTo({
        x: activeIndex * itemWidth,
        y: 0,
        animated: true,
      });
    }, [activeIndex]);

    return (
      <IndicatorContainer bottomInset={bottom}>
        <Animated.ScrollView
          ref={scrollRef}
          horizontal
          showsHorizontalScrollIndicator={false}>
          {data.map((image, index) => (
            <IndicatorItem
              key={index}
              image={image}
              index={index}
              onPress={onPress}
              activeIndex={activeIndex}
            />
          ))}
        </Animated.ScrollView>
      </IndicatorContainer>
    );
  },
);

const IndicatorItem = memo(
  ({
    image,
    index,
    onPress,
    activeIndex,
  }: {
    image: ReviewDocumentImageType;
    index: number;
    onPress: (index: number) => void;
    activeIndex: number;
  }) => {
    const { sizes } = useTheme();
    const onPressFn = useCallback(() => {
      onPress(index);
    }, [onPress, index]);
    const primaryStyle = useAnimatedStyle(() => ({
      opacity: activeIndex === index ? 1 : 0,
    }));
    const imageUriHeader = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization:
        'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
    };
    return (
      <ImageContainer onPress={onPressFn}>
        {image.name?.endsWith('.pdf') ? (
          <Icon.DocumentCopy size={sizes[14]} />
        ) : (
          <SImage
            resizeMode="cover"
            resizeMethod="resize"
            source={{
              uri: getImageUri(image),
              headers: imageUriHeader,
            }}
          />
        )}
        <GreyBorder />
        <PrimaryBorder style={primaryStyle} />
      </ImageContainer>
    );
  },
);

const Item = memo(
  ({
    item,
    width,
    height,
    maximumZoomScale = 3,
  }: {
    item: ReviewDocumentImageType;
    width?: number;
    height?: number;
    maximumZoomScale?: number;
  }) => {
    const imageUriHeader = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization:
        'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken,
    };
    return (
      <View
        style={{
          width: width,
          height: height,
          overflow: 'hidden',
        }}>
        {item.name?.endsWith('.pdf') ? (
          <Pdf
            source={{
              uri: getImageUri(item),
            }}
            style={{ flex: 1 }}
            onError={e => {
              console.log(e);
            }}
          />
        ) : (
          <Zoom maximumZoomScale={maximumZoomScale}>
            <Image
              resizeMode="contain"
              source={{
                uri: getImageUri(item),
                headers: imageUriHeader,
              }}
              style={{
                width: width,
                height: height,
              }}
            />
          </Zoom>
        )}
      </View>
    );
  },
);

const getImageUri = (item: ReviewDocumentImageType) => {
  if (item.uri && urlRegex.test(item.uri)) return item.uri;
  return getDocumentUri(item.name);
};
