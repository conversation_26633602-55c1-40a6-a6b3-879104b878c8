import { Platform, TouchableOpacity } from 'react-native';

import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  H6,
  Icon,
  LargeBody,
  Row,
  TextField,
} from 'cube-ui-components';
import MobileChatbotSVG from 'features/eRecruit/assets/icon/MobileChatbotSVG';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView as RNKeyboardAvoidingScrollView } from 'react-native-keyboard-aware-scroll-view';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';

interface OTPCardProps {
  phoneNumber: string;
  isVerifyLoading: boolean;
  isResendLoading: boolean;
  disabledResend: boolean;
  onClose: () => void;
  onResend: () => void;
  onTestPress?: () => void;
  onVerifyAndContinue: (otpInput: string | undefined) => void;
  errorVerify?: string | null;
  errorSend?: string | null;
  isPhone?: boolean;
}

export const OTPCard = ({
  onClose,
  phoneNumber,
  onResend,
  onVerifyAndContinue,
  isVerifyLoading,
  isResendLoading,
  disabledResend,
  onTestPress,
  errorVerify,
  errorSend,
  isPhone,
}: OTPCardProps) => {
  const { space, colors, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  const AvoidingScrollView =
    Platform.OS == 'android'
      ? KeyboardAwareScrollView
      : RNKeyboardAvoidingScrollView;

  const defaultRetryCountDown = 60;

  const [retryCountDown, setRetryCountDown] = useState(defaultRetryCountDown);
  const [text, onChangeText] = useState<undefined | string>(undefined);

  useEffect(() => {
    if (retryCountDown > 0) {
      const interval = setInterval(() => {
        setRetryCountDown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [retryCountDown > 0]);

  const isResendButtonDisabled =
    retryCountDown > 0 || disabledResend || isVerifyLoading || isResendLoading;

  return (
    <AnimatedViewWrapper>
      <AvoidingScrollView
        contentContainerStyle={{
          justifyContent: 'center',
          alignItems: 'center',
          flex: 1,
        }}>
        <Box
          maxW={isPhone ? space[86] : space[70] + space[73]}
          gap={isPhone ? space[4] : space[7]}
          backgroundColor={colors.background}
          borderRadius={borderRadius['large']}
          p={isPhone ? space[6] : space[12]}
          pt={space[6]}>
          <Row>
            <TouchableOpacity
              onPress={onClose}
              style={{
                position: 'absolute',
                right: isPhone ? 0 : -space[6],
              }}>
              <Icon.Close fill={colors.secondary} />
            </TouchableOpacity>
          </Row>
          <H6 fontWeight="bold" onPress={onTestPress}>
            {t('otpModal.requested')}
          </H6>
          <Box
            width={isPhone ? 96 : 140}
            height={isPhone ? 96 : 140}
            alignSelf="center">
            <MobileChatbotSVG />
          </Box>
          <Box gap={space[4]}>
            <LargeBody>
              {t('otpModal.sentMessageGeneric.withPhone', {
                phone: phoneNumber,
              })}
            </LargeBody>
            <TextField
              keyboardType="number-pad"
              maxLength={6}
              placeholder={isPhone ? ' ' : t('otpModal.placeholder')}
              label={isPhone ? t('otpModal.placeholder') : undefined}
              value={text}
              onChange={v => {
                const digitsOnly = v.replace(/\D/g, '');
                onChangeText(digitsOnly);
              }}
            />
            {(errorVerify || errorSend) && (
              <Box
                borderRadius={8}
                backgroundColor={colors.palette.fwdOrange[5]}
                p={space[4]}>
                <LargeBody fontWeight="medium" color={colors.error}>
                  {errorVerify || errorSend}
                </LargeBody>
              </Box>
            )}
          </Box>
          <Box alignItems="center" gap={space[2]}>
            <LargeBody>{t('otpModal.failToReceiveOtp')}</LargeBody>
            <TouchableOpacity
              disabled={isResendButtonDisabled}
              style={{
                flexDirection: 'row',
                gap: space[1],
                paddingVertical: isPhone ? space[1] : undefined,
                opacity: isResendButtonDisabled ? 0.5 : 1,
              }}
              onPress={() => {
                onResend();
                setRetryCountDown(defaultRetryCountDown);
              }}>
              <LargeBody
                fontWeight={'medium'}
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('otpModal.resendCode')}
              </LargeBody>
              <LargeBody color={colors.palette.fwdGreyDarker}>
                ({retryCountDown}s)
              </LargeBody>
            </TouchableOpacity>
          </Box>
          <Button
            disabled={!text || isVerifyLoading || isResendLoading}
            style={{
              marginTop: isPhone ? space[2] : undefined,
              width: isPhone ? undefined : space[50],
              alignSelf: isPhone ? undefined : 'center',
            }}
            text={t('otpModal.verifyAndContinue')}
            loading={isVerifyLoading}
            onPress={() => {
              console.log('======= sending out OTP ========: ', text);
              // setIsModalOn;
              onVerifyAndContinue(text);
            }}
          />
        </Box>
      </AvoidingScrollView>
    </AnimatedViewWrapper>
  );
};
