import React from 'react';
import { Typography, Box, PictogramIcon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

import EmptyTaskSVG from 'features/home/<USER>/image/EmptyTaskSVG';

export default function ListEmptyRecord({
  width,
  height,
  title,
}: {
  width?: number;
  height?: number;
  title?: string;
}) {
  const { sizes, space, colors, borderRadius } = useTheme();
  const { t } = useTranslation('savedProposals');

  return (
    <Box
      backgroundColor={colors.background}
      minH={space[39]}
      width={'100%'}
      padding={space[6]}
      borderBottomLeftRadius={space[4]}
      borderBottomEndRadius={space[4]}
      alignItems={'center'}
      justifyContent={'center'}>
      <EmptyTaskSVG width={width} height={height} />
      <Typography.Body color={colors.palette.fwdGreyDarker}>
        {title ? title : t('filter.emptyRecord')}
      </Typography.Body>
    </Box>
  );
}
