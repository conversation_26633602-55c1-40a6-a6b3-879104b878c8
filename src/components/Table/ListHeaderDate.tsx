import { TouchableOpacity, ViewStyle } from 'react-native';
import React from 'react';
import { Icon, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

export default function ListHeaderDate({
  dateTitle,
  iconSize,
  containerStyle,
  onPress,
  isSortDateDesc,
  isDisableSort = false,
  isSmallerLineHeight = false,
  type,
  ...rest
}: {
  dateTitle: string;
  onPress?: () => void;
  isSortDateDesc: boolean;
  isDisableSort?: boolean;
  isSmallerLineHeight?: boolean;
  iconSize?: number;
  containerStyle?: ViewStyle;
} & (
  | {
      type: 'width';
      width: string;
    }
  | {
      type: 'flex';
      flex: number;
    }
)) {
  const { t } = useTranslation();
  const { sizes, space, colors, borderRadius, typography } = useTheme();

  return (
    <TouchableOpacity
      onPress={() => {
        if (isDisableSort) {
          return;
        }
        onPress && onPress();
      }}
      style={[
        {
          justifyContent: 'space-between',
          gap: space[2],
          padding: space[4],
          flexDirection: 'row',
          alignItems: 'center',
          ...containerStyle,
        },
        type == 'width' && 'width' in rest
          ? {
              width: rest?.width as any,
            }
          : type == 'flex' && 'flex' in rest
          ? { flex: rest?.flex }
          : {},
      ]}>
      <Typography.Body
        fontWeight="medium"
        color={colors.background}
        style={{
          overflow: 'visible',
          maxWidth: '80%',
          alignItems: 'center',
          justifyContent: 'center',
          textAlignVertical: 'center',
          lineHeight: isSmallerLineHeight
            ? typography.label.lineHeight
            : typography.body.lineHeight,
        }}
        numberOfLines={3}>
        {dateTitle}
      </Typography.Body>

      {isDisableSort ? (
        <></>
      ) : isSortDateDesc ? (
        <Icon.ArrowDown fill={colors.background} size={iconSize ?? sizes[4]} />
      ) : (
        <Icon.ArrowUp fill={colors.background} size={iconSize ?? sizes[4]} />
      )}
    </TouchableOpacity>
  );
}
