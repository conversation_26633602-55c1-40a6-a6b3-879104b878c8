import React from 'react';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ViewStyle } from 'react-native';

export default function ListItemComponent({
  children,
  width,
  containerStyle,
}: {
  children: React.ReactNode;
  width?: string;
  containerStyle?: ViewStyle;
}) {
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <Row
      px={space[4]}
      width={width}
      alignItems={'center'}
      justifyContent="space-between"
      style={{
        ...containerStyle,
      }}>
      {children}
    </Row>
  );
}
