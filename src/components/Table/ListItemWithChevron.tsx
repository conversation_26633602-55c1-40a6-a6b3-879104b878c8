import React from 'react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ViewStyle } from 'react-native';

export default function ListItemWithChevron({
  text,
  width,
  iconSize,
  showIcon = true,
  containerStyle,
  isShorterLineHeight,
}: {
  text: string;
  width?: string;
  iconSize?: number;
  showIcon?: boolean;
  containerStyle?: ViewStyle;
  isShorterLineHeight?: boolean;
}) {
  const { sizes, space, colors } = useTheme();

  return (
    <Row
      width={width}
      alignItems="center"
      justifyContent="space-between"
      px={space[4]}
      style={{ ...containerStyle }}>
      {isShorterLineHeight ? (
        <Typography.Label>{text}</Typography.Label>
      ) : (
        <Typography.Body>{text}</Typography.Body>
      )}
      {showIcon && (
        <Icon.ChevronRight fill={colors.primary} size={iconSize ?? sizes[4]} />
      )}
    </Row>
  );
}
