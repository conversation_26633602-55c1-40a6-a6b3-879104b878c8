import { View, ViewStyle } from 'react-native';
import React from 'react';
import { Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

export default function ListHeaderTitle({
  name,
  wrapperStyle,
  isSmallerLineHeight = false,
  type,
  ...rest
}: {
  name: string;
  wrapperStyle?: ViewStyle;
  isSmallerLineHeight?: boolean;
} & (
  | {
      type: 'width';
      width: string;
    }
  | {
      type: 'flex';
      flex: number;
    }
)) {
  const { sizes, space, colors, typography } = useTheme();

  return (
    <View
      style={[
        {
          justifyContent: 'center',
          padding: 16,
        },
        //redesigned to optional width, default flex 1
        type == 'width' && 'width' in rest
          ? {
              width: rest?.width as any,
            }
          : type == 'flex' && 'flex' in rest
          ? { flex: rest?.flex }
          : {},
        !!wrapperStyle && wrapperStyle,
      ]}>
      <Typography.Body
        fontWeight="medium"
        color={colors.background}
        style={{
          lineHeight: isSmallerLineHeight
            ? typography.label.lineHeight
            : typography.body.lineHeight,
        }}>
        {name}
      </Typography.Body>
    </View>
  );
}
