import { ViewStyle, TouchableOpacity } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';

export default function ListItemStyleWrapper({
  index,
  dataCount,
  onPress,
  children,
  wrapperStyle,
}: {
  index: number;
  dataCount: number;
  onPress?: (data?: any) => void;
  children: React.ReactNode;
  wrapperStyle?: ViewStyle | ViewStyle[];
}) {
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <TouchableOpacity
      style={[
        { minHeight: space[12], flexDirection: 'row' },
        {
          backgroundColor:
            index % 2 === 0 ? colors.background : colors.palette.fwdGrey[20],
        },
        index === dataCount - 1 && {
          borderBottomLeftRadius: space[4],
          borderBottomEndRadius: space[4],
          marginBottom: space[6],
        },
        !!wrapperStyle && wrapperStyle,
      ]}
      onPress={onPress}>
      {children}
    </TouchableOpacity>
  );
}
