import { ViewStyle } from 'react-native';
import React from 'react';
import { Box, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export default function ListHeaderStyleWrapper({
  children,
  wrapperStyle,
  backgroundColor,
}: {
  children: React.ReactNode;
  wrapperStyle?: ViewStyle;
  backgroundColor?: string;
}) {
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <Box
      backgroundColor={
        backgroundColor ? backgroundColor : colors.palette.whiteTransparent
      }>
      <Row
        style={[
          {
            minHeight: space[12],
            backgroundColor: colors.primary,
            borderTopLeftRadius: space[4],
            borderTopRightRadius: space[4],
          },
          !!wrapperStyle && wrapperStyle,
        ]}>
        {children}
      </Row>
    </Box>
  );
}
