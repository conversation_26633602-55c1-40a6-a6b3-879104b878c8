import React from 'react';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ViewStyle } from 'react-native';

export default function ListItemText({
  text,
  width,
  containerStyle,
  isShorterLineHeight,
  color,
  fontWeight,
}: {
  text: string;
  width?: string;
  containerStyle?: ViewStyle;
  isShorterLineHeight?: boolean;
  color?: string;
  fontWeight?: Typography.FontWeight;
}) {
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <Row
      px={space[4]}
      width={width}
      alignItems={'center'}
      justifyContent="space-between"
      style={{
        ...containerStyle,
      }}>
      {isShorterLineHeight ? (
        <Typography.Label
          color={color ?? colors.onBackground}
          fontWeight={fontWeight}>
          {text}
        </Typography.Label>
      ) : (
        <Typography.Body
          color={color ?? colors.onBackground}
          fontWeight={fontWeight}>
          {text}
        </Typography.Body>
      )}
    </Row>
  );
}
