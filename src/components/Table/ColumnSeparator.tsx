import styled from '@emotion/native';
import { Theme } from '@emotion/react';

const ColumnSeparator = styled.View(
  ({
    theme,
    height,
    color,
  }: {
    theme?: Theme;
    height?: number;
    color?: string;
  }) => [
    {
      width: 1,
      marginVertical: theme?.space[4],
      backgroundColor: color ?? theme?.colors.background,
    },
    !!height && { height: height, alignSelf: 'center' },
  ],
);

export default ColumnSeparator;
