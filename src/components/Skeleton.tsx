import React, { useEffect, useRef } from 'react';
import {
  Animated,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';

type Props = {
  width: number | string;
  height: number | string;
  radius: number;
  containerStyle?: StyleProp<ViewStyle>;
  backgroundColor?: string;
  animated?: boolean;
};

export default function Skeleton({
  width,
  height,
  radius,
  containerStyle,
  backgroundColor = colors.fwdGrey[50],
  animated = true,
}: Props) {
  const deviceWidth = Dimensions.get('screen').height;
  const translateX = useRef(new Animated.Value(-deviceWidth)).current;

  useEffect(() => {
    if (animated) {
      Animated.loop(
        Animated.timing(translateX, {
          toValue: deviceWidth / 5,
          useNativeDriver: true,
          duration: 1800,
        }),
      ).start();
    } else {
      // reset to initial position if animation disabled
      translateX.setValue(0);
    }
  }, [animated, deviceWidth, translateX]);

  return (
    <SafeAreaView
      style={StyleSheet.flatten([
        {
          width: width,
          height: height,
          borderRadius: radius,
          backgroundColor: backgroundColor,
          overflow: 'hidden',
        },
        containerStyle,
      ])}>
      {animated ? (
        <Animated.View
          style={{
            width: '100%',
            height: '100%',
            transform: [{ translateX: translateX }],
          }}>
          <LinearGradient
            style={{ width: '100%', height: '100%' }}
            colors={[
              'transparent',
              'rgba(0,0,0,0.05)',
              'rgba(0,0,0,0.05)',
              'transparent',
            ]}
            start={{ x: 1, y: 1 }}
          />
        </Animated.View>
      ) : (
        <LinearGradient
          style={{ width: '100%', height: '100%' }}
          colors={[
            'transparent',
            'rgba(0,0,0,0.05)',
            'rgba(0,0,0,0.05)',
            'transparent',
          ]}
          start={{ x: 1, y: 1 }}
        />
      )}
    </SafeAreaView>
  );
}
