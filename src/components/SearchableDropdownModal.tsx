import React, {
  useRef,
  useState,
  useMemo,
  useEffect,
  useCallback,
  memo,
} from 'react';
import {
  Modal,
  StyleSheet,
  useWindowDimensions,
  Pressable,
  TextInput,
  ViewStyle,
} from 'react-native';
import { Icon, H6, Box, Row, Body, LargeBody } from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { runOnJS } from 'react-native-reanimated';
import useLatest from 'hooks/useLatest';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import useDebounceFn from 'features/policy/hooks/useDebounceFn';
import { useFocusEffect } from '@react-navigation/native';

export type SearchableDropdownModalProps<T, V> = {
  visible: boolean;
  title?: string;
  data?: T[];
  searchable?: boolean;
  searchLabel?: string;
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  onDismiss?: () => void;
  renderItem?: <V>(props: ItemProps<V> & { item: T }) => JSX.Element;
  emptyMessage?: string;
  estimatedItemSize?: number;
  loading?: boolean;
  onClear?: () => void;
  value?: V;
  onDone?: (value: V) => void;
  dropDownModalStyle?: ViewStyle;
  getItemDisabled?: (item: T) => boolean;
};

export default function SearchableDropdownModal<T, V>({
  visible = false,
  title,
  data,
  searchable,
  searchLabel = 'Search',
  getItemLabel,
  getItemValue,
  onDismiss: onDismissAction,
  onDone,
  renderItem: customRenderItem,
  value,
  emptyMessage = 'Adjust keyword to get better result or try another search.',
  estimatedItemSize = 67,
  loading,
  onClear: onClearAction,
  dropDownModalStyle,
  getItemDisabled,
}: SearchableDropdownModalProps<T, V>) {
  const flatListRef = useRef<FlashList<T>>(null);
  const [query, setQuery] = useState('');
  const getItemLabelRef = useLatest(getItemLabel);
  const getItemValueRef = useLatest(getItemValue);
  const onDoneRef = useLatest(onDone);

  const getItemDisabledRef = useLatest(getItemDisabled);
  // useFocusEffect(() => {
  //   if (value && data) {
  //     const selectedIndex = data.findIndex?.(
  //       item => getItemValueRef.current(item) === value,
  //     );
  //     if (selectedIndex && selectedIndex > 0) {
  //       setTimeout(() => {
  //         flatListRef.current?.scrollToIndex({
  //           index: selectedIndex,
  //           viewPosition: 0.1,
  //           animated: false,
  //         });
  //       }, 100);
  //     }
  //   }
  // });

  const onDismiss = () => {
    if (onDismissAction) {
      runOnJS(onDismissAction)();
    }
  };

  const { colors } = useTheme();

  const onQuery = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const onClear = useCallback(() => {
    onQuery('');
    onClearAction?.();
  }, [onQuery, onClearAction]);

  const filteredData = useMemo(() => {
    return data?.filter(item => {
      return String(getItemLabelRef.current(item))
        .toLowerCase()
        .includes(query.trim().toLowerCase());
    });
  }, [data, getItemLabelRef, query]);

  const onSelectItem = useCallback((newValue: V) => {
    (onDoneRef.current as (v: V) => void)?.(newValue as V);
    onDismiss && onDismiss();
    onClear && onClear();
  }, []);

  const renderItem = useCallback<ListRenderItem<T>>(
    props => {
      const { item } = props;
      const disabled = getItemDisabledRef?.current?.(item);
      return (
        <Item<V>
          label={getItemLabelRef.current(item)}
          value={getItemValueRef.current(item)}
          selected={getSelected(getItemValueRef.current(item), value)}
          onSelect={onSelectItem}
          disabled={disabled}
        />
      );
    },
    [value, getItemLabelRef, getItemValueRef, onSelectItem, getItemDisabledRef],
  );

  const { height: h } = useWindowDimensions();

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onDismiss}>
      <BG>
        <BGPressable onPress={onDismiss}></BGPressable>
        <Wrap style={dropDownModalStyle}>
          <Box>
            {Boolean(title) && <Label fontWeight="bold">{title}</Label>}
            {searchable && (
              <SearchInput
                searchLabel={searchLabel}
                query={query}
                onQuery={onQuery}
                onClear={() => {
                  onClear();
                }}
              />
            )}
          </Box>
          <Box>
            <Body color={colors.placeholder}>
              {!query
                ? 'Please select'
                : `Search result (${filteredData?.length ?? 0})`}
            </Body>
          </Box>
          <FlashList
            ref={flatListRef}
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="on-drag"
            data={filteredData}
            keyExtractor={item => String(getItemValueRef.current(item))}
            estimatedItemSize={estimatedItemSize}
            renderItem={data => {
              if (customRenderItem) {
                return customRenderItem({
                  item: data.item,
                  label: getItemLabelRef.current(data.item),
                  value: getItemValueRef.current(data.item),
                  selected: getSelected(
                    getItemValueRef.current(data.item),
                    value,
                  ),
                  onSelect: onSelectItem,
                });
              } else {
                return renderItem(data);
              }
            }}
            ListEmptyComponent={
              !loading && emptyMessage ? (
                <EmptySearchResult message={emptyMessage} />
              ) : null
            }
          />
        </Wrap>
      </BG>
    </Modal>
  );
}

const SearchInput = ({
  searchLabel,
  query: initialQuery,
  onQuery,
  onClear,
}: {
  searchLabel?: string;
  query?: string;
  onQuery?: (text: string) => void;
  onClear?: () => void;
}) => {
  const [query, setQuery] = useState(initialQuery || '');
  const [isFocused, setIsFocused] = useState(false);
  const { space, colors } = useTheme();

  const onChangeText = useCallback((text: string) => {
    setQuery(text);
  }, []);

  const latestOnQuery = useLatest(onQuery);

  useDebounceFn(
    () => {
      latestOnQuery.current?.(query);
    },
    500,
    [query],
  );

  const onClearFn = useCallback(() => {
    setQuery('');
    onClear?.();
  }, [onClear]);

  return (
    <SearchInputWrap
      borderColor={
        isFocused ? colors.primary : colors.palette.fwdDarkGreen[20]
      }>
      <CustomTextInput
        enablesReturnKeyAutomatically
        returnKeyType="done"
        selectionColor={colors.primary}
        placeholder={searchLabel}
        placeholderTextColor={colors.palette.fwdGreyDark}
        value={query}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
      />
      {!!query && (
        <Pressable onPress={onClearFn}>
          <Icon.CloseCircle fill={colors.secondary} size={space[5]} />
        </Pressable>
      )}
    </SearchInputWrap>
  );
};

export const EmptySearchResult = memo(({ message }: { message?: string }) => {
  const theme = useTheme();
  return (
    <Row
      padding={theme.space[2]}
      borderRadius={theme.borderRadius['x-small']}
      backgroundColor={theme.colors.palette.alertRedLight}>
      <Icon.Warning fill={theme.colors.error} />
      <Box w={theme.space[1]} />
      <Body style={{ flex: 1 }} color={theme.colors.error}>
        {message}
      </Body>
    </Row>
  );
});

const BG = styled(Box)(({theme: {space}}) => ({
  ...StyleSheet.absoluteFillObject,
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: space[14],
}));
const BGPressable = styled(Pressable)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
}));
const Wrap = styled(Box)(({ theme: { space, colors, borderRadius } }) => ({
  width: '76%',
  padding: space[12],
  borderRadius: borderRadius.large,
  backgroundColor: colors.background,
  flex: 1
}));

const SearchInputWrap = styled(Row)(({ theme: { space, sizes } }) => ({
  height: sizes[11],
  gap: space[4],
  alignItems: 'center',
  paddingHorizontal: space[4],
  borderWidth: 1,
  borderRadius: 22,
  marginBottom: space[7],
}));

const CustomTextInput = styled(TextInput)(
  ({ theme: { sizes, typography } }) => ({
    flex: 1,
    height: sizes[11],
    fontFamily: 'FWDCircularTT-Book',
    fontSize: typography.body.size,
    textAlignVertical: 'center',
    backgroundColor: 'transparent',
  }),
);

const Label = styled(H6)(({ theme: { space } }) => ({
  marginBottom: space[4],
}));

const ItemContainer = styled(Pressable)(({ theme: { space, colors } }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingVertical: space[3],
  borderBottomWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
}));

type ItemProps<V> = {
  value: V;
  label: string;
  selected: boolean;
  onSelect: (value: V) => void;
  disabled?: boolean;
};
const Item = React.memo(
  <V,>({ label, value, selected, onSelect, disabled }: ItemProps<V>) => {
    const theme = useTheme();
    return (
      <ItemContainer disabled={disabled} onPress={() => onSelect(value)}>
        <LargeBody style={{ flex: 1, opacity: disabled ? theme.opacity.disabled : 1 }}>{label}</LargeBody>
        {selected && <Icon.Tick />}
      </ItemContainer>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

const getSelected = <V,>(value: V, selectedValue: V | V[]) => {
  return selectedValue
    ? Array.isArray(selectedValue)
      ? selectedValue.some(curVal => curVal === value)
      : value === selectedValue
    : false;
};
