import { useIsFocused } from '@react-navigation/native';
import * as ScreenOrientation from 'expo-screen-orientation';
import { RefObject, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Camera,
  CameraPosition,
  CameraProps,
  CameraRuntimeError,
  DeviceFilter,
  FormatFilter,
  Point,
  useCameraDevice,
  useCameraFormat,
  useCameraPermission,
} from 'react-native-vision-camera';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { runOnJS } from 'react-native-reanimated';
import {
  Platform,
  StyleProp,
  useWindowDimensions,
  ViewStyle,
} from 'react-native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export type CubeVisionCameraProps = {
  cameraRef: RefObject<Camera>;
  cameraDevice: {
    cameraPosition: CameraPosition;
    filter?: DeviceFilter;
  };
  cameraFormatFilters?: FormatFilter[];
  cameraProps?: Omit<CameraProps, 'device' | 'isActive'>;
  isPaused?: boolean;
};

export const CubeVisionCamera = ({
  cameraRef,
  cameraDevice,
  cameraFormatFilters,
  cameraProps,
  isPaused,
}: CubeVisionCameraProps) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { width, height } = useWindowDimensions();
  const landscapeScreenWidth = isTabletMode ? width : height;
  const landscapeScreenHeight = isTabletMode ? height : width;
  const originalScreenRatio = landscapeScreenWidth / landscapeScreenHeight;
  let screenRatio = originalScreenRatio;
  if (originalScreenRatio >= 16 / 9) {
    screenRatio = 16 / 9;
  } else if (originalScreenRatio <= 4 / 3) {
    screenRatio = 4 / 3;
  }

  const device = useCameraDevice(
    cameraDevice.cameraPosition,
    cameraDevice.filter,
  );
  const format = useCameraFormat(
    device,
    cameraFormatFilters ?? [
      { videoAspectRatio: screenRatio },
      {
        videoResolution: {
          width: landscapeScreenWidth * 2,
          height: landscapeScreenHeight * 2,
        },
      },
      { photoAspectRatio: screenRatio },
      { photoResolution: { width: 1920, height: 1080 } },
    ],
  );
  const isFocused = useIsFocused();
  const previewOrientation = isTabletMode ? 'landscape-right' : 'portrait';
  // TODO: Find a way to trigger state change when ui rotate to cater issue of incorrect initial camera view for iPad
  const [orientation, setOrientation] = useState(0);

  const { hasPermission, requestPermission } = useCameraPermission();
  useEffect(() => {
    if (!hasPermission) {
      requestPermission();
    }
  }, [hasPermission, requestPermission]);

  // Orientation lock for Skia Frame Processor
  useEffect(() => {
    const orientationLock = isTabletMode
      ? ScreenOrientation.OrientationLock.LANDSCAPE_RIGHT
      : ScreenOrientation.OrientationLock.PORTRAIT_UP;
    ScreenOrientation.lockAsync(orientationLock);
    return () => {
      // Revert
      const orientationLock = isTabletMode
        ? ScreenOrientation.OrientationLock.LANDSCAPE
        : ScreenOrientation.OrientationLock.PORTRAIT_UP;
      ScreenOrientation.lockAsync(orientationLock);
    };
  }, [isTabletMode]);

  // Custom Camera style
  const style: StyleProp<ViewStyle> = useMemo(() => {
    const style = { flex: 1 };

    if (!isTabletMode) {
      return style;
    }
    if (Platform.OS === 'android') {
      return device?.position === 'back'
        ? style
        : { ...style, transform: [{ scaleX: -1 }] };
    }
    const rotateDeg = 90 * (previewOrientation === 'landscape-right' ? -1 : 1);
    return {
      ...style,
      transform: [{ rotate: `${rotateDeg}deg` }, { scale: width / height }],
    };
  }, [isTabletMode, device?.position, previewOrientation, width, height]);

  // Workaround for incorrect orientation for iPad
  const { frameProcessor, ...props } = cameraProps ?? {};
  if (frameProcessor?.type === 'drawable-skia') {
    frameProcessor.previewOrientation.value = `portrait`;
  }

  const focus = useCallback((point: Point) => {
    if (!cameraRef.current) return;

    console.log('Camera focusing...');
    cameraRef.current.focus(point);
  }, []);

  const gesture = Gesture.Tap().onEnd(({ x, y }) => {
    runOnJS(focus)({ x, y });
  });

  if (!device || !hasPermission) {
    return null;
  }

  return (
    <GestureDetector gesture={gesture}>
      <Camera
        ref={cameraRef}
        style={style}
        device={device}
        format={format}
        isActive={isFocused && !isPaused}
        frameProcessor={frameProcessor}
        onError={(e: CameraRuntimeError) => console.error(e)}
        onUIRotationChanged={setOrientation}
        resizeMode={'contain'}
        photo
        {...props}
      />
    </GestureDetector>
  );
};
