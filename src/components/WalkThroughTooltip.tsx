import {
  useWindowDimensions,
  Platform,
  StatusBar,
  Animated,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useTheme } from '@emotion/react';
import { ObjectUtil } from 'utils';

export default function WalkThroughTooltip({
  contentStyle,
  children,
  arrowStyle,
  ...props
}: React.ComponentProps<typeof Tooltip>) {
  const { sizes, colors, space, borderRadius, elevation } = useTheme();

  const { width } = useWindowDimensions();

  const elevationStyle = ObjectUtil.cloneDeep(elevation[5]);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    if (props.isVisible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [props.isVisible, fadeAnim]);

  return (
    <Tooltip
      placement="bottom"
      backgroundColor={'transparent'}
      disableShadow
      arrowSize={{ width: sizes[9], height: sizes[5] }}
      tooltipStyle={{
        ...elevationStyle,
      }}
      displayInsets={{
        top: space[2],
        left: space[2],
        right: space[2],
        bottom: space[2],
      }}
      arrowStyle={[
        {
          borderBottomColor: colors.palette.fwdGrey[100],
          borderLeftColor: colors.palette.fwdGrey[100],
          borderBottomWidth: 1,
          borderLeftWidth: 1,
          borderTopWidth: 1,
          borderRightWidth: 1,
          height: 24,
          width: 24,
          backgroundColor: colors.background,
          transform: [
            { rotateZ: '-45deg' },
            { translateX: 2 },
            { translateY: -12 },
          ],
          top: -3,
          zIndex: 99,
        },
        arrowStyle,
      ]}
      contentStyle={[
        {
          backgroundColor: colors.background,
          borderRadius: borderRadius.small,
          width: width - space[4],
          padding: space[4],
          borderWidth: 1,
          borderColor: colors.palette.fwdGrey[100],
          top: -2,
          overflow: 'visible',
          maxWidth: 700,
        },
        contentStyle,
      ]}
      topAdjustment={Platform.select({
        ios: 0,
        android: -(StatusBar.currentHeight ?? 24),
      })}
      {...props}
      content={
        <Animated.View style={{ opacity: fadeAnim }}>
          {props.content}
        </Animated.View>
      }>
      {children}
    </Tooltip>
  );
}
