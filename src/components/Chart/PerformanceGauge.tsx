import {
  Dimensions,
  StyleSheet,
  View,
  ViewProps,
  LayoutChangeEvent,
  Pressable,
  TouchableOpacity,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import Svg, {
  Circle,
  Line,
  Path,
  Rect,
  Text as SvgText,
} from 'react-native-svg';
import { useFocusEffect } from '@react-navigation/native';
import Animated, {
  FadeIn,
  FadeOut,
  useAnimatedProps,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);
const initialNumber = 0;

type PerformanceGaugeProps = {
  radius?: number;
  customPadding?: number;
  percentOuterFirst: number;
  percentOuterSecond?: number;
  percentInner: number;
  title: string;
  subTitle?: string;
  asOfDate: string;
  insideLabel: string;
  outsideLabel: string;
  inTarget: string;
  outTarget: string;
  buttonLabel?: string;
  buttonLabelColor?: string;
  buttonOnPress?: () => void;
} & ViewProps;

const animationDuration = 1000;

export default function PerformanceGauge({
  radius = 90,
  // padding = 24,
  customPadding,
  percentOuterFirst = 0,
  percentOuterSecond = 0,
  percentInner = 0,
  title,
  subTitle,
  asOfDate,
  insideLabel,
  outsideLabel,
  inTarget,
  outTarget,
  buttonLabel,
  buttonLabelColor,
  buttonOnPress,
  ...props
}: PerformanceGaugeProps) {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const { colors, space, borderRadius, sizes } = useTheme();
  const paddingInDefaultSize = space[6];
  const paddingInNarrow = space[1] + space[6] + space[4];

  // const customPadding
  const padding =
    customPadding === undefined
      ? isNarrowScreen
        ? paddingInNarrow
        : paddingInDefaultSize
      : isNarrowScreen && customPadding >= paddingInNarrow
      ? customPadding
      : paddingInNarrow;
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });
  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  const boldFont = 'FWDCircularTT-Bold';
  const bookFont = 'FWDCircularTT-Book';

  const chartRadius =
    Math.min(containerDimensions.height, containerDimensions.width) / 2 -
    // strokeWidth / 2 -
    padding;

  const windowWidth = Dimensions.get('window').width;
  // const chartRadius = windowWidth * 0.125;

  const strokeWidth = chartRadius * 0.06;
  const innerRadius = chartRadius - strokeWidth / 2;
  const radiusInner = innerRadius - strokeWidth;
  const innerRadiusInner = radiusInner - strokeWidth * 0.75;

  const circumference = innerRadius * 2 * Math.PI;
  const circumferenceInner = innerRadiusInner * 2 * Math.PI;

  const arc = circumference * (270 / 360);
  const arcInner = circumferenceInner * (270 / 360);

  const dashArray = `${arc} ${circumference}`;
  const dashArrayInner = `${arcInner} ${circumference}`;

  const transform = `rotate(135, ${chartRadius + padding}, ${
    chartRadius + padding
  })`;

  const percentNormalizedOuterFirst = Math.max(
    0.7,
    Math.min(Math.max(percentOuterFirst, 0), 100),
  );
  const offsetOuterFirst = arc - (percentNormalizedOuterFirst / 100) * arc;

  const percentNormalizedOuterSecond = Math.min(
    Math.max(percentOuterSecond, 0),
    100,
  );
  const offsetOuterSecond =
    arc -
    (Math.min(percentNormalizedOuterSecond + percentNormalizedOuterFirst, 100) /
      100) *
      arc;

  const percentNormalizedInner = Math.max(
    0.7,
    Math.min(Math.max(percentInner, 0), 100),
  );
  const offsetInner = arcInner - (percentNormalizedInner / 100) * arcInner;

  const innerOffset = useSharedValue(0);
  const outerFirstOffset = useSharedValue(0);
  const outerSecondOffset = useSharedValue(0);

  useEffect(() => {
    const percentNormalizedInner = Math.min(Math.max(percentInner, 0), 100);
    const percentNormalizedOuterFirst = Math.min(
      Math.max(percentOuterFirst, 0),
      100,
    );
    const percentNormalizedOuterSecond = Math.min(
      Math.max(percentOuterSecond, 0),
      100,
    );

    innerOffset.value = arc;
    innerOffset.value = withTiming(
      arcInner - (percentNormalizedInner / 100) * arcInner,
      {
        duration: animationDuration,
      },
    );

    outerFirstOffset.value = arc;
    outerFirstOffset.value = withTiming(
      arc - (percentNormalizedOuterFirst / 100) * arc,
      {
        duration: animationDuration,
      },
    );

    outerSecondOffset.value = arc;
    outerSecondOffset.value = withTiming(
      arc - (percentNormalizedOuterSecond / 100) * arc,
      {
        duration: animationDuration,
      },
    );
  });
  const innerAnimatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: innerOffset.value,
    };
  });

  const outerFirstAnimatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: outerFirstOffset.value,
    };
  });

  const outerSecondAnimatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: outerSecondOffset.value,
    };
  });
  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      {...props}
      style={[styles.container, props.style]}>
      <View
        onLayout={onLayoutChart}
        style={{
          height: '100%',
          width: '100%',
          alignItems: 'center',
          // borderWidth: 1,
          justifyContent: 'center',
          alignContent: 'center',
          alignSelf: 'center',
        }}>
        {buttonLabel && (
          <View
            style={{
              zIndex: 100,
              position: 'absolute',
              minWidth: 160,
              paddingTop: radius * 0.6,

              paddingLeft: chartRadius / 3,
            }}>
            <TouchableOpacity
              onPress={
                buttonOnPress
                  ? buttonOnPress
                  : () => console.log('no onPress function provided')
              }
              style={{
                alignItems: 'center',
                flexDirection: 'row',
                minWidth: 160,
              }}>
              <Icon.ArrowRight
                width={space[5]}
                height={space[4]}
                fill={buttonLabelColor ?? colors.primary}
              />
              <Typography.H7
                fontWeight="bold"
                style={{ flex: 1, marginLeft: space[1] }}
                color={buttonLabelColor ?? colors.primary}>
                {buttonLabel}
              </Typography.H7>
            </TouchableOpacity>
          </View>
        )}
        <Svg
          style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
          height={chartRadius * 2 + padding * 2}
          width={chartRadius * 2 + padding * 2}>
          {/* //* Outer Circle */}
          <Circle
            // class="gauge_base"
            cx={chartRadius + padding}
            cy={chartRadius + padding}
            fill="transparent"
            r={innerRadius}
            stroke={colors.palette.fwdGrey[50]}
            // stroke={'red'}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            transform={transform}
          />

          <AnimatedCircle
            // class="gauge_percent"
            // animatedProps={outerCircleFirstAnimation.animatedProps}
            animatedProps={outerFirstAnimatedProps}
            cx={chartRadius + padding}
            cy={chartRadius + padding}
            fill="transparent"
            r={innerRadius}
            stroke={colors.primary}
            strokeDasharray={dashArray}
            // strokeDashoffset={arc}
            strokeWidth={strokeWidth}
            transform={transform}
          />
          {/* //? Inner Circle */}
          <Circle
            // class="gauge_base"
            cx={chartRadius + padding}
            cy={chartRadius + padding}
            fill="transparent"
            r={innerRadiusInner}
            stroke={colors.palette.fwdGrey[50]}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArrayInner}
            transform={transform}
          />
          <AnimatedCircle
            // class="gauge_percent"

            animatedProps={innerAnimatedProps}
            cx={chartRadius + padding}
            cy={chartRadius + padding}
            fill="transparent"
            r={innerRadiusInner}
            stroke="#F3BB90"
            strokeDasharray={dashArrayInner}
            // strokeDashoffset={offsetInner}
            strokeWidth={strokeWidth}
            transform={transform}
          />
          {/* // * Title, Subtile, data */}
          <SvgText
            x="50%"
            // y={subTitle ? '40%' : '45%'}
            y={'40%'}
            alignmentBaseline="middle"
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="20"
            fill="#000">
            {title}
          </SvgText>
          {subTitle && (
            <SvgText
              x="50%"
              y="50%"
              alignmentBaseline="middle"
              textAnchor="middle"
              fontFamily={boldFont}
              fontSize="16"
              fill="#000">
              {subTitle}
            </SvgText>
          )}
          <SvgText
            x="50%"
            // y={'48%'}
            y={subTitle ? '60%' : '48%'}
            alignmentBaseline="middle"
            textAnchor="middle"
            fontFamily={bookFont}
            fontSize="12"
            fill="#8B8E8F">
            {asOfDate}
          </SvgText>
          {/* {buttonLabel && (
            <ButtonOnSVG
              buttonLabel={buttonLabel}
              onPress={
                buttonOnPress
                  ? buttonOnPress
                  : () => console.log('no onPress function provided')
              }
              // isSubTitleOn={!!subTitle}
              chartRadius={chartRadius}
              padding={padding}
            />
          )} */}
          {/* <SvgText
                    x="50%"
                    y="70%"
                    // alignmentBaseline='middle'
                    textAnchor="middle"
                    // fontFamily={Fonts.FWDCircularTT.Book}
                    fontSize="12"
                    fill={Colors.darkGreen}>
                    Persistency: 50%
                </SvgText> */}
          {/* // ? Inner circle Text and line */}
          <SvgText
            x={chartRadius * 0.45 + padding}
            y={chartRadius * 1.66 + padding + 3}
            // alignmentBaseline='middle'
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {initialNumber}
          </SvgText>
          <Line
            x1={chartRadius * 0.7 + padding}
            y1={chartRadius * 1.66 + padding}
            x2={chartRadius * 0.5 + padding}
            y2={chartRadius * 1.66 + padding}
            stroke={colors.palette.fwdGrey[100]}
            strokeWidth="1"
          />

          <SvgText
            x={chartRadius * 1 + padding}
            y={chartRadius * 1.66 + padding + 3}
            // alignmentBaseline='middle'
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {insideLabel}
          </SvgText>

          <Line
            x1={chartRadius * 1.43 + padding - 2}
            y1={chartRadius * 1.66 + padding}
            x2={chartRadius * 1.28 + padding}
            y2={chartRadius * 1.66 + padding}
            stroke={colors.palette.fwdGrey[100]}
            strokeWidth="1"
          />
          {/* <Line
                    x1="100"
                    y1="100"
                    x2="50"
                    y2="70"
                    stroke="red"
                    strokeWidth="1"
                /> */}
          <SvgText
            x={chartRadius * 1.56 + padding - 8}
            y={chartRadius * 1.66 + padding + 3}
            // alignmentBaseline='middle'
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {inTarget}
          </SvgText>
          {/* //* Outer circle Text and line */}
          <SvgText
            x={chartRadius * 0.4 + padding}
            y={chartRadius * 1.8 + padding}
            // alignmentBaseline='middle'
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {initialNumber}
          </SvgText>
          <Line
            x1={chartRadius * 0.75 + padding}
            y1={chartRadius * 1.78 + padding}
            x2={chartRadius * 0.46 + padding}
            y2={chartRadius * 1.78 + padding}
            stroke={colors.palette.fwdGrey[100]}
            strokeWidth="1"
          />
          <SvgText
            x={chartRadius * 1 + padding}
            y={chartRadius * 1.76 + padding + 6}
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {outsideLabel}
          </SvgText>
          <Line
            x1={chartRadius * 1.48 + padding + 4}
            y1={chartRadius * 1.78 + padding}
            x2={chartRadius * 1.25 + padding}
            y2={chartRadius * 1.78 + padding}
            stroke={colors.palette.fwdGrey[100]}
            strokeWidth="1"
          />
          <SvgText
            x={chartRadius * 1.6 + padding + 2}
            y={chartRadius * 1.8 + padding}
            // alignmentBaseline='middle'
            textAnchor="middle"
            fontFamily={boldFont}
            fontSize="12"
            fill={colors.palette.fwdGreyDarker}>
            {outTarget}
          </SvgText>
        </Svg>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: -40,
    flex: 1,
  },
  svg: {
    // alignItems: 'center',
    // justifyContent: 'center',
    // backgroundColor: 'transparent',
  },
});

type ButtonOnSVGProps = {
  buttonLabel: string;
  onPress: () => void;
  // isSubTitleOn: boolean;
  chartRadius: number;
  padding: number;
};

const ButtonOnSVG = ({
  buttonLabel,
  onPress,
  // isSubTitleOn,
  chartRadius,
  padding,
}: ButtonOnSVGProps) => {
  const { colors, space, borderRadius, sizes } = useTheme();

  const [isPressed, setIsPressed] = useState(false);
  const boldFont = 'FWDCircularTT-Bold';
  const bookFont = 'FWDCircularTT-Book';

  return (
    <>
      <Rect
        onPressIn={() => setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        onPress={onPress}
        x={chartRadius * 0.55 + padding}
        y={(chartRadius * 2 + padding) * 0.6}
        width="125"
        height="28"
        // stroke="red"
        // strokeWidth="2"
        fill="yellow"
      />
      <Path
        opacity={isPressed ? 0.5 : 1}
        x={(chartRadius + padding) * 0.8}
        y={chartRadius * 1.2}
        // x="54%"
        // y="60%"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.735.954l-.265.266 4.719 4.719H.249v1.5h10.94l-4.719 4.72.265.264c.44.44 1.152.44 1.591 0L14.06 6.69 8.326.954a1.125 1.125 0 00-1.59 0z"
        fill="#E87722"
      />
      <SvgText
        opacity={isPressed ? 0.5 : 1}
        x="54%"
        y="60%"
        alignmentBaseline="middle"
        textAnchor="middle"
        fontFamily={boldFont}
        fontSize="16"
        fill={colors.primary}>
        {buttonLabel}
      </SvgText>
    </>
  );
};
