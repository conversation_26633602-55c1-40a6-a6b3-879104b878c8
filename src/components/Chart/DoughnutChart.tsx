import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import React, { Fragment } from 'react';
import { View } from 'react-native';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';

interface DataPoint {
  color: string;
  percentage: number;
  opacity: number;
}
interface TextStyle {
  fontFamily?: string;
  fontSize?: number;
  color?: string;
  height?: string;
}
interface CustomStyle {
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
}

export default function DoughnutChart({
  data,
  radius = 90,
  padding = 30,
  title,
  subtitle,
  titleMargin = 16,
  showPercentageLabel = true,
  customStyle,
}: {
  data?: DataPoint[];
  radius?: number;
  padding?: number;
  title: string;
  subtitle?: string | number;
  titleMargin?: number;
  showPercentageLabel?: boolean;
  customStyle?: CustomStyle;
}) {
  let noOfZeroPercentage = 0;
  const strokeWidth = radius * 0.2;
  const innerRadius = radius - strokeWidth / 2;
  const circumference = innerRadius * 2 * Math.PI;
  const { colors } = useTheme();

  const renderPies = () => {
    let accumulatedPercent = 0;
    return data?.map((item, i) => {
      const percentNormalized = Math.min(Math.max(item.percentage, 0), 100);
      const dashArray = `${
        (percentNormalized / 100) * circumference
      } ${circumference}`;

      const rotate = (accumulatedPercent * 360) / 100;
      accumulatedPercent += item.percentage;

      const transform = `rotate(${-90 + rotate} ${radius + padding}, ${
        radius + padding
      })`;

      const percentageTextPosition = accumulatedPercent - item.percentage / 2;

      const percentageTextX =
        radius +
        padding +
        (radius + 20) *
          Math.cos(
            (Math.PI / 180) * (-90 + (360 * percentageTextPosition) / 100),
          );

      const percentageTextY =
        radius +
        padding +
        (radius + 20) *
          Math.sin(
            (Math.PI / 180) * (-90 + (360 * percentageTextPosition) / 100),
          );

      // rsinDeg = corX
      // rcosDeg = corY

      if (percentNormalized === 0) {
        ++noOfZeroPercentage;
        //* if all item.percentage = 0, draw the grey circle
        if (noOfZeroPercentage === data?.length) {
          // console.log('💦 all zero for percentage');
          return (
            <Fragment key={'RenderingPie_' + title + i}>
              <Circle
                key={'allZero'}
                cx={radius + padding}
                cy={radius + padding}
                fill="transparent"
                opacity={1}
                r={innerRadius}
                stroke={colors.palette.fwdGrey[100]}
                strokeDasharray={`${circumference} ${circumference}`}
                strokeWidth={strokeWidth}
                transform={transform}
              />
            </Fragment>
          );
        }
        return;
      }

      return (
        <Fragment key={'RenderingPie_' + title + i}>
          <Circle
            key={item.color}
            // class="gauge_percent"
            cx={radius + padding}
            cy={radius + padding}
            fill="transparent"
            opacity={item.opacity || 1}
            r={innerRadius}
            stroke={item.color}
            strokeDasharray={dashArray}
            strokeWidth={strokeWidth}
            transform={transform}
          />
          {showPercentageLabel && (
            <SvgText
              textAnchor="middle"
              x={percentageTextX}
              y={percentageTextY}
              translateY={7}
              fontFamily={'FWDCircularTT-Book'}
              fill={colors.primary}
              fontSize={14}>
              {Math.floor(item.percentage) >= 3 &&
                Math.floor(item.percentage) + '%'}
            </SvgText>
          )}
        </Fragment>
      );
    });
  };

  return (
    <Container>
      <ContainerSVG
        height={radius * 2 + padding * 2}
        width={radius * 2 + padding * 2}>
        <Circle
          // class="gauge_base"
          cx={radius + padding}
          cy={radius + padding}
          fill="transparent"
          r={innerRadius}
          stroke="#fff"
          strokeWidth={strokeWidth}
          // transform={transform}
        />
        <SvgText
          x="50%"
          y={
            customStyle?.titleStyle?.height
              ? customStyle?.titleStyle?.height
              : '48%'
          }
          textAnchor="middle"
          fontSize={
            customStyle?.titleStyle?.fontSize
              ? customStyle.titleStyle?.fontSize
              : 25
          }
          fontFamily={
            customStyle?.titleStyle?.fontFamily
              ? customStyle?.titleStyle?.fontFamily
              : 'FWDCircularTT-Bold'
          }
          // fontSize={`${
          //   defaultRadius
          //     ? (radius / defaultRadius) * sizeAdjustFactor * defaultTitleSize
          //     : defaultTitleSize
          // }`}
          fill={
            customStyle?.titleStyle?.color
              ? customStyle.titleStyle?.color
              : colors.primary
          }>
          {title}
        </SvgText>
        <SvgText
          x="50%"
          y={
            customStyle?.subtitleStyle?.height
              ? customStyle?.subtitleStyle?.height
              : '50%'
          }
          transform={`translate(0 ${titleMargin})`}
          textAnchor="middle"
          fontFamily={
            customStyle?.subtitleStyle?.fontFamily
              ? customStyle?.subtitleStyle?.fontFamily
              : 'FWDCircularTT-Book'
          }
          fontSize={
            customStyle?.subtitleStyle?.fontSize
              ? customStyle.subtitleStyle?.fontSize
              : 14
          }
          // fontSize={`${
          //   defaultRadius
          //     ? (radius / defaultRadius) * sizeAdjustFactor * defaultTitleSize
          //     : defaultTitleSize
          // }`}
          fill={
            customStyle?.subtitleStyle?.color
              ? customStyle.subtitleStyle?.color
              : '#000'
          }>
          {subtitle}
        </SvgText>
        {renderPies()}
      </ContainerSVG>
    </Container>
  );
}

const Container = styled(View)(() => ({
  position: 'relative',
  justifyContent: 'center',
  alignItems: 'center',
}));

const ContainerSVG = styled(Svg)(() => ({
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: 'transparent',
}));
