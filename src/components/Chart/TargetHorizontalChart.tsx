import React, { useState } from 'react';
import styled from '@emotion/native';
import { LargeBody, SmallBody, XView } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  Dimensions,
  LayoutChangeEvent,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@emotion/react';
import Svg, { G, Path, Rect } from 'react-native-svg';

type TargetHorizontalChartProps = {
  target: number;
  current: number;
  currency: string;
  gapTitle: string;
  currentTitle: string;
};

export const TargetHorizontalChart = ({
  target,
  current,
  currency,
  currentTitle,
  gapTitle,
}: TargetHorizontalChartProps) => {
  const { width } = Dimensions.get('screen');
  const spacing = 15;
  const { colors } = useTheme();
  const { t } = useTranslation('leadProfile');
  const [gapWidth, setGapWidth] = useState<number>(0);
  const [progressWidth, setProgressWidth] = useState<number>(0);
  const currentPercent = (current * 100) / target;
  const gapPercent = 100 - currentPercent;
  const gapValue = target - current;

  const onLayoutProgress = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setGapWidth(width);
  };

  const onLayoutGap = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setProgressWidth(width);
  };

  const dashes = new Array(Math.floor(width / spacing)).fill(null);
  return (
    <Container>
      <XView style={{ width: `${currentPercent}%` }}>
        <CurrentTitleWrapper>
          <CurrentAssetTitle>{`${currentTitle}: ${currency} ${current}m`}</CurrentAssetTitle>
        </CurrentTitleWrapper>
        <VerticalLineLeft>
          <Svg height="11" width="25">
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="8"
                  y="0"
                  width="5"
                  height="3"
                  fill={colors.palette.fwdDarkGreen[100]}
                  translateX={12 * index}
                />
              ))}
            </G>
          </Svg>
        </VerticalLineLeft>
        <HorizontalLineLeft onLayout={onLayoutProgress}>
          <Svg width="100%" height="5" viewBox={`0 0 ${gapWidth - 10} ${5}`}>
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="0"
                  y="0"
                  width="8"
                  height="3"
                  fill={colors.palette.fwdDarkGreen[100]}
                  translateX={14 * index}
                />
              ))}
            </G>
          </Svg>
        </HorizontalLineLeft>
        <VerticalLineRight>
          <Svg height="11" width="25">
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="8"
                  y="0"
                  width="5"
                  height="3"
                  fill={colors.palette.fwdDarkGreen[100]}
                  translateX={12 * index}
                />
              ))}
            </G>
          </Svg>
        </VerticalLineRight>
      </XView>
      <ProgressLine>
        <CurrentProgressLine percent={currentPercent} />
      </ProgressLine>
      <XView style={{ width: `${100}%` }}>
        <View style={{ width: `${currentPercent - 2}%` }} />
        <VerticalLineLeft>
          <Svg height="11" width="25">
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="8"
                  y="0"
                  width="5"
                  height="3"
                  fill={colors.palette.alertRed}
                  translateX={12 * index}
                />
              ))}
            </G>
          </Svg>
        </VerticalLineLeft>
        <HorizontalLineRight widthPercent={gapPercent} onLayout={onLayoutGap}>
          <Svg width="100%" height="5" viewBox={`0 0 ${progressWidth} ${5}`}>
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="0"
                  y="0"
                  width="8"
                  height="3"
                  fill={colors.palette.alertRed}
                  translateX={14 * index}
                />
              ))}
            </G>
          </Svg>
        </HorizontalLineRight>
        <VerticalLineRight>
          <Svg height="11" width="25">
            <G>
              {dashes.map((_, index) => (
                <Rect
                  key={index}
                  x="8"
                  y="0"
                  width="5"
                  height="3"
                  fill={colors.palette.alertRed}
                  translateX={12 * index}
                />
              ))}
            </G>
          </Svg>
        </VerticalLineRight>
        <GapTitleWrapper>
          <GapTitle>{`${gapTitle}: `}</GapTitle>
          <GapCurrency fontWeight="bold">{`${currency} `}</GapCurrency>
          <GapValue fontWeight="bold">{gapValue}</GapValue>
        </GapTitleWrapper>
        <GapIconWrapper>
          <Svg width={12} height={10} fill="none">
            <Path
              fill={colors.palette.fwdBlue[100]}
              d="M6.85749 8.57084C6.46909 9.21818 5.53091 9.21818 5.14251 8.57084L0.908699 1.5145C0.508785 0.847971 0.988897 -9.62621e-07 1.76619 -8.94668e-07L10.2338 -1.54405e-07C11.0111 -8.6452e-08 11.4912 0.847971 11.0913 1.51449L6.85749 8.57084Z"
            />
          </Svg>
        </GapIconWrapper>
      </XView>
    </Container>
  );
};

const Container = styled(View)(({ theme }) => ({
  marginVertical: theme.space[7],
}));

const CurrentTitleWrapper = styled(View)(({ theme }) => ({
  position: 'absolute',
  left: 0,
  bottom: 18,
}));

const CurrentAssetTitle = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  lineHeight: 18,
}));

const GapTitle = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.palette.alertRed,
}));

const GapCurrency = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.palette.alertRed,
}));

const GapValue = styled(LargeBody)(({ theme }) => ({
  color: theme.colors.palette.alertRed,
}));

const CurrentProgressLine = styled(View)<ViewStyle & { percent: number }>(
  ({ theme, percent }) => ({
    backgroundColor: theme.colors.palette.fwdDarkGreen[100],
    width: `${percent}%`,
    borderRadius: 20,
    height: theme.space[3],
    position: 'absolute',
    left: 0,
    top: 0,
    borderRightWidth: 1,
    borderRightColor: theme.colors.palette.white,
  }),
);

const ProgressLine = styled(View)(({ theme }) => ({
  backgroundColor: theme.colors.palette.alertRed,
  width: '100%',
  borderRadius: 20,
  height: theme.space[3],
  marginTop: 12,
}));

const VerticalLineLeft = styled(View)(({ theme }) => ({
  transform: [
    {
      rotate: '90deg',
    },
    {
      translateY: theme.space[3],
    },
  ],
}));

const VerticalLineRight = styled(View)(({ theme }) => ({
  transform: [
    {
      rotate: '90deg',
    },
    {
      translateY: -5,
    },
  ],
  position: 'absolute',
  right: 0,
}));

const HorizontalLineLeft = styled(View)(({ theme }) => ({
  transform: [
    {
      translateX: -27,
    },
  ],
  width: '100%',
}));

const HorizontalLineRight = styled(View)<ViewProps & { widthPercent: number }>(
  ({ theme, widthPercent }) => ({
    transform: [
      {
        translateX: -21,
      },
      {
        translateY: 20,
      },
    ],
    width: `${widthPercent}%`,
  }),
);

const GapTitleWrapper = styled(XView)(({ theme }) => ({
  position: 'absolute',
  right: 0,
  top: 26,
}));
const GapIconWrapper = styled(XView)(({ theme }) => ({
  position: 'absolute',
  right: 0,
  top: -25,
}));
