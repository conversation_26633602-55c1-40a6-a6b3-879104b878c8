import { View } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import { Typography, Icon, Row, FloatingButton, Box } from 'cube-ui-components';
import CrownSVG from 'features/performance/assets/CrownSVG';

export type DotData = {
  x: number;
  y: number;
};

type RankingDataDotProps = {
  dotPerformance: number;
  type?: 'Me' | 'Top' | 'Others' | 'MeAndTop';
  dataCount?: number;
  getDotPosition?: (performance: number) => DotData;
  isNoShadow?: boolean;
};

export default function RankingDataDot({
  dotPerformance,
  type = 'Others',
  dataCount,
  getDotPosition,
  isNoShadow = false,
}: RankingDataDotProps) {
  const { colors, space, borderRadius, sizes } = useTheme();

  const dotRadius =
    type !== 'Others' || (dataCount && dataCount > 1) ? 13 : 6.5;
  const dotColor =
    type === 'Me' || type === 'MeAndTop'
      ? colors.palette.fwdOrange[100]
      : type === 'Top'
      ? colors.palette.fwdOrange[50]
      : colors.background;
  const borderWidth = type !== 'Others' || (dataCount && dataCount > 1) ? 1 : 0;
  const borderColor =
    type !== 'Others' ? colors.background : colors.palette.fwdOrange[50];

  const dotPosition = getDotPosition
    ? getDotPosition(dotPerformance)
    : undefined;

  return (
    <View
      style={[
        {
          width: dotRadius * 2,
          height: dotRadius * 2,
          borderRadius: dotRadius,
          borderWidth: borderWidth,
          borderColor: borderColor,
          backgroundColor: dotColor,
        },
        !!(dotPosition && dotPosition.y && dotPosition.x) && {
          position: 'absolute',
          top: dotPosition.y - dotRadius,
          left: dotPosition.x - dotRadius,
        },
        {
          justifyContent: 'center',
          alignItems: 'center',
        },
        isNoShadow
          ? {}
          : {
              shadowColor: colors.palette.black,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            },
      ]}>
      {type === 'Me' && (
        // <Text bold style={styles.dotText}>
        //   Me
        // </Text>
        <Typography.SmallBody fontWeight="bold" color={colors.palette.white}>
          Me
        </Typography.SmallBody>
      )}
      {type === 'MeAndTop' && (
        <>
          <Box style={{ position: 'absolute', top: -5 }}>
            <CrownSVG
              fill={colors.palette.fwdYellow[100]}
              width={13}
              height={8}
            />
          </Box>

          <Typography.SmallBody fontWeight="bold" color={colors.palette.white}>
            Me
          </Typography.SmallBody>
        </>
      )}
      {type === 'Top' && (
        // <Icon.CrownSVG fill={colors.background} width={13} height={8} />
        <CrownSVG fill={colors.background} width={sizes[3]} height={sizes[2]} />
      )}
      {type === 'Others' && dataCount && dataCount > 1 && (
        <Row
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
          }}>
          <Icon.Account
            // style={{ marginLeft: 1 }}
            fill={colors.palette.fwdDarkGreen[100]}
            width={9.5}
            height={9.5}
          />
          {/* <Text style={styles.groupedDotText}>{dataCount}</Text> */}
          <Typography.SmallBody style={{ flex: 1, textAlign: 'center' }}>
            {dataCount}
          </Typography.SmallBody>
        </Row>
      )}

      {/* <Typography.SmallBody>12</Typography.SmallBody>
      <Typography.Body>14</Typography.Body> */}
    </View>
  );
}
