import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Typography } from 'cube-ui-components';
import { View } from 'react-native';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';

export type RoundedBarChartData = {
  data: number;
  color: string;
};

export default function RoundedBarChart({
  dataset,
}: {
  dataset: RoundedBarChartData[];
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const maxData = Math.max(...dataset.map(item => item.data));

  return (
    <Container>
      {dataset.map((item, index) => (
        <Column
          key={index}
          alignItems="center"
          gap={space[2]}
          marginRight={space[3]}
          justifyContent="flex-end">
          <Box
            width={space[7]}
            key={index}
            height={item.data !== 0 ? singleBarHeight(item.data, maxData) : 1}
            backgroundColor={item.color}
            borderRadius={borderRadius['x-small']}
          />

          <Typography.ExtraSmallLabel>
            {numberToThousandsFormat(item.data)}
          </Typography.ExtraSmallLabel>
        </Column>
      ))}
    </Container>
  );
}

const Container = styled(View)(({ theme }) => ({
  flexDirection: 'row',
  flex: 1,
  justifyContent: 'center',
}));

/**
 * max bar height = 104px
 * bar height = signleData / maxData
 * height [if dataValue = 0] = 1px
 */

function singleBarHeight(data: number, maxData: number) {
  return (data / maxData) * 104;
}
