import React from 'react';
import { View } from 'react-native';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import Svg, { G, Path } from 'react-native-svg';
import {
  Icon,
  Label,
  LargeLabel,
  SmallLabel,
  SvgIconProps,
  XView,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';

interface BaseChartSVGProps {
  scaleX?: number;
  scaleY?: number;
}

interface FunnelChartProps extends BaseChartSVGProps {
  type?: 'record' | 'conversion';
  titlesArray: string[];
  valuesArray: any[];
  conversionArray?: any[];
  showStatusLabel?: boolean;
  statusLabelLevel?: 1 | 2 | 3 | 4 | 5;
}

export default function FunnelChart({
  type,
  scaleX = 1,
  scaleY = 1,
  titlesArray = [], // From top to bottom
  valuesArray = [], // From top to bottom
  conversionArray = [], // From top to bottom
  showStatusLabel = false,
  statusLabelLevel, // From top to bottom : 1 to 5
}: FunnelChartProps) {
  const { colors, space, sizes } = useTheme();

  // Status label positioning
  const statusLabelArray = Array(5).fill('label');
  if (
    showStatusLabel &&
    statusLabelLevel &&
    statusLabelLevel >= 1 &&
    statusLabelLevel <= 5
  )
    statusLabelArray[statusLabelLevel - 1] = 'show';

  return (
    <Container>
      <BaseChartSVG scaleX={scaleX} scaleY={scaleY} />

      <TitlesContainer style={{ gap: space[3] * scaleY }}>
        {titlesArray.map(title => (
          <SmallLabel key={'title_' + title}>{title}</SmallLabel>
        ))}
      </TitlesContainer>

      <ValuesContainer style={{ gap: space[3] * scaleY }}>
        {valuesArray.map((value, index) => (
          <ValuesField key={'value_' + index}>
            {type === 'conversion' && country === 'ib' && (
              <Icon.Account
                size={sizes[4]}
                fill={
                  index === 0 || index === 3 || index === 4
                    ? colors.onPrimary
                    : colors.onBackground
                }
              />
            )}
            <Label
              fontWeight="bold"
              // eslint-disable-next-line react-native/no-color-literals
              style={{
                color:
                  index === 0 || index === 3 || index === 4
                    ? colors.onPrimary
                    : colors.onBackground,
              }}>
              {value}
            </Label>
          </ValuesField>
        ))}
      </ValuesContainer>

      {showStatusLabel && (
        <StatusLabelContainer
          style={{ gap: space[3] * scaleY, paddingRight: space[3] }}>
          {statusLabelArray.map((label, index) => {
            return label === 'show' ? (
              <StatusLabel key={'statusLabel' + index} />
            ) : (
              <View
                key={'statusLabel' + index}
                style={{ minHeight: sizes[5] }}
              />
            );
          })}
        </StatusLabelContainer>
      )}

      {type === 'conversion' && (
        <ConversionLabelsContainer style={{ top: space[5] * scaleY }}>
          {conversionArray.map((label, index) => {
            return (
              <ConversionLabelsField
                key={'conversionLabel_' + index}
                style={{ right: space[3] * index }}>
                <FunnelArrowSVG
                  fill={colors.secondary}
                  width={20}
                  height={20}
                />
                <LargeLabel fontWeight="bold">{label}%</LargeLabel>
              </ConversionLabelsField>
            );
          })}
        </ConversionLabelsContainer>
      )}
    </Container>
  );
}

function StatusLabel() {
  const { t } = useTranslation('leadProfile');
  const { space } = useTheme();
  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <StatusLabelSVG />
      <SmallLabel
        style={{
          position: 'absolute',
          paddingLeft: space[2],
          paddingBottom: space[1] / 2,
        }}>
        {t('leadProfile.activityRecord.chart.currentStatus')}
      </SmallLabel>
    </View>
  );
}

function BaseChartSVG(props: SvgIconProps & BaseChartSVGProps) {
  const { scaleX, scaleY } = props;

  const BASE_WIDTH = 142;
  const BASE_HEIGHT = 200;

  const scaledWidth = scaleX && BASE_WIDTH * scaleX;
  const scaledHeight = scaleY && BASE_HEIGHT * scaleY;

  return (
    <Svg
      width={scaledWidth}
      height={scaledHeight}
      viewBox={`0 0 ${scaledWidth} ${scaledHeight}`}
      fill="none"
      {...props}>
      <G scaleX={scaleX} scaleY={scaleY}>
        <Path
          d="M.24 9.95L7.78 40h125.93l7.54-30.05C142.52 4.9 138.7 0 133.49 0H8C2.8 0-1.02 4.9.24 9.95z"
          fill="#E87722"
        />
        <Path d="M17.82 80h105.859l10.03-40H7.779l10.04 40z" fill="#F3BB90" />
        <Path d="M113.64 120l10.04-40H17.82l10.03 40h85.79z" fill="#FAE4D3" />
        <Path d="M103.61 160l10.03-40H27.85l10.04 40h65.72z" fill="#183028" />
        <Path
          d="M95.09 193.95l8.521-33.95h-65.72l8.52 33.95a7.992 7.992 0 007.76 6.05h33.17c3.67 0 6.87-2.49 7.76-6.05h-.01z"
          fill="#859D99"
        />
      </G>
    </Svg>
  );
}

function StatusLabelSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 98}
      height={props.height || 21}
      viewBox="0 0 98 21"
      fill="none"
      {...props}>
      <Path
        d="M8.99.62A2 2 0 0110.438 0H96a2 2 0 012 2v16.103a2 2 0 01-2 2H10.438a2 2 0 01-1.448-.62L1.315 11.43a2 2 0 010-2.76L8.99.62z"
        fill="#FED141"
      />
    </Svg>
  );
}

function FunnelArrowSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.size || 20}
      height={props.size || 20}
      viewBox="0 0 20 20"
      fill="none">
      <Path
        d="M2.113 14.6449L5.71292 18.2609C5.88467 18.4335 6.14266 18.4851 6.36691 18.3913C6.59115 18.2975 6.73777 18.0779 6.73777 17.8346L6.73778 16.0265L7.33776 16.0265C14.2965 16.0265 19.9375 10.3603 19.9375 3.37051L19.9375 2.16517C19.9375 1.83257 19.6686 1.5625 19.3375 1.5625L16.9376 1.5625C16.6064 1.5625 16.3376 1.83257 16.3376 2.16517L16.3376 3.37051C16.3376 8.36286 12.3079 12.4105 7.33776 12.4105L6.73778 12.4105L6.73778 10.6025C6.73778 10.3592 6.59153 10.1385 6.36691 10.0458C6.14266 9.95165 5.88467 10.0036 5.71292 10.1761L2.113 13.7922C1.879 14.0279 1.879 14.4088 2.113 14.6449Z"
        fill={props.fill ?? '#183028'}
      />
    </Svg>
  );
}

const Container = styled.View(() => ({
  // flex: 1,
  alignItems: 'center',
}));

const TitlesContainer = styled.View(() => ({
  height: '100%',
  position: 'absolute',
  left: 0,
  justifyContent: 'space-evenly',
}));

const ValuesContainer = styled.View(() => ({
  height: '100%',
  position: 'absolute',
  justifyContent: 'space-evenly',
}));

const ValuesField = styled(XView)(({ theme }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.space[1],
  flexDirection: 'row',
}));

const StatusLabelContainer = styled.View(() => ({
  height: '100%',
  position: 'absolute',
  right: 0,
  justifyContent: 'space-evenly',
}));

const ConversionLabelsContainer = styled.View(() => ({
  height: '80%',
  position: 'absolute',
  right: 0,
  justifyContent: 'space-around',
}));

const ConversionLabelsField = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  gap: theme.space[1],
}));
