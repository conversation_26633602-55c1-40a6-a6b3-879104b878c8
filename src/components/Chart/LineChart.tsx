import { View, Text, Platform, ColorValue } from 'react-native';
import React from 'react';
import Svg, { NumberProp, Polyline, Text as SvgText } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import numberToKFormat from 'utils/helper/numberToKFormat';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

type LineChartProps = {
  submissionStrokeData: { x: number | string; y: number }[];
  completionStrokeData: { x: number | string; y: number }[];
  subLineColor?: ColorValue;
  comLineColor?: ColorValue;
  height?: number;
  width?: number;
  lineChartPadding?: number;
  horizontalGuides?: number;
  verticalGuides?: number;
  precision?: number;
  xAxisTitle?: string;
  // xAxisTitlePosition: { x: number; y: number };
  xAxisLabels?: number[];
  adjustChartPositionHorizontal?: number;
  adjustChartPositionVertical?: number;
  inputLabelFontSize?: number;
};

const boldFont = 'FWDCircularTT-Bold';
const bookFont = 'FWDCircularTT-Book';
const STROKE = 2;

export default function LineChart({
  height = 250,
  width = 328,
  lineChartPadding = 32,
  horizontalGuides: numberOfHorizontalGuides = 10,
  verticalGuides: numberOfVerticalGuides = 12,
  precision = 0,
  xAxisTitle = 'Month',
  xAxisLabels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
  adjustChartPositionHorizontal = -20,
  adjustChartPositionVertical = 8,
  subLineColor,
  comLineColor,
  submissionStrokeData,
  completionStrokeData,
  inputLabelFontSize,
}: LineChartProps) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const FONT_SIZE = width / 50;

  const theme = useTheme();
  const { sizes, colors, borderRadius, space } = theme;

  // * Max value of data
  const maximumXFromData = Math.max(...xAxisLabels.map(element => element)); // * No. of months: 12 as default value in PerformanceLineChart
  const maximumYFromData = Math.max(
    Math.max(...submissionStrokeData.map(element => element.y)) * 1.5 || 0,
    Math.max(...completionStrokeData.map(element => element.y)) * 1.5 || 0,
    5,
  ); // !: Handling when maximumYFromData === 0
  const padding = lineChartPadding;
  const chartWidth = width - padding * 2;
  const chartHeight = height - padding * 2;

  const labelPadding = 8;
  const secondLabelPadding = 4;
  const labelFonts = inputLabelFontSize
    ? inputLabelFontSize
    : Platform.OS == 'web'
    ? 8
    : 12;

  const firstXLabel = ((1 / maximumXFromData) * chartWidth) / 1 + padding;
  const xAxisTitlePosition: { x: number; y: number } = {
    x: isTabletMode ? chartWidth + padding + 8 : firstXLabel + 14,
    y: height - padding + labelFonts * 2 + labelPadding + secondLabelPadding,
    // -
    // //* For no padding top
    // (padding - labelFonts),
  };
  // * Point of Submission
  const pointsSub = submissionStrokeData
    .map(element => {
      const x = (+element.x / maximumXFromData) * chartWidth + padding;
      const y =
        chartHeight - (element.y / maximumYFromData) * chartHeight + padding;
      return `${x},${y}`;
    })
    .join(' ');
  // * Points of Completion
  const pointsCom = completionStrokeData
    .map(element => {
      const x = (+element.x / maximumXFromData) * chartWidth + padding;
      const y =
        chartHeight - (element.y / maximumYFromData) * chartHeight + padding;
      return `${x},${y}`;
    })
    .join(' ');

  // * X Y axes
  const XAxis = () => (
    <Polyline
      fill="none"
      stroke={colors.palette.fwdGreyDarker}
      strokeWidth="1"
      points={`${padding},${
        height - padding
        // - //* For no padding top
        // (padding - labelFonts / 2)
      } ${width - padding},${
        height - padding
        //  - //* For no padding top
        // (padding - labelFonts / 2)
      }`}
    />
  );

  const YAxis = () => (
    <Polyline
      fill="none"
      stroke={colors.palette.fwdGrey[20]}
      strokeWidth="1"
      points={`${padding},${padding} ${padding},${height - padding}`}
    />
  );

  // * Grid
  const VerticalGuides = () => {
    const guideCount = numberOfVerticalGuides; // || data.length - 1;

    const startY = padding;
    // - //* For no padding top
    // (padding - labelFonts);
    const endY = height - padding;
    //  - //* For no padding top
    // (padding - labelFonts);
    return (
      <>
        {new Array(guideCount).fill(0).map((_, index) => {
          const ratio = (index + 1) / guideCount;
          const xCoordinate = padding + ratio * (width - padding * 2);
          return (
            <React.Fragment key={'VerticalGuides_' + index}>
              <Polyline
                fill="none"
                stroke={colors.palette.fwdGrey[20]}
                // stroke={'red'}
                strokeWidth="1"
                points={`${xCoordinate},${startY} ${xCoordinate},${endY}`}
              />
            </React.Fragment>
          );
        })}
      </>
    );
  };

  const HorizontalGuides = () => {
    const startX = padding;
    const endX = width - padding;

    return (
      <>
        {new Array(numberOfHorizontalGuides).fill(0).map((_, index) => {
          const ratio = (index + 1) / numberOfHorizontalGuides;

          const yCoordinate = chartHeight - chartHeight * ratio + padding;

          return (
            <React.Fragment key={'HorizontalGuides_' + index}>
              <Polyline
                fill="none"
                stroke={colors.palette.fwdGrey[20]}
                // stroke={'red'}
                strokeWidth="1"
                points={`${startX},${yCoordinate} ${endX},${yCoordinate}`}
              />
            </React.Fragment>
          );
        })}
      </>
    );
  };

  // * X Y Labels
  const LabelsXAxis = () => {
    const y = height - padding + labelFonts + labelPadding;
    // - //* For no padding top
    // (padding - labelFonts); // ! Original y value: height - padding + FONT_SIZE * 2
    return (
      <>
        {xAxisLabels.map((element, index) => {
          const x =
            ((element / maximumXFromData) * chartWidth) / 1 +
            padding -
            FONT_SIZE / 2;
          return (
            <SvgText
              key={'label_x_' + index}
              x={x}
              y={y}
              fontSize={labelFonts}
              fill={colors.palette.fwdGreyDarker}
              fontFamily={bookFont}>
              {element}
            </SvgText>
          );
        })}
      </>
    );
  };

  const LabelsYAxis = () => {
    const PARTS = numberOfHorizontalGuides;

    return (
      <>
        {new Array(PARTS + 1).fill(0).map((_, index) => {
          const x = 12;
          const ratio = index / numberOfHorizontalGuides;

          const yCoordinate =
            chartHeight + padding + FONT_SIZE / 2 - chartHeight * ratio;
          // -
          // //* For no padding top
          // (padding - labelFonts);
          if (index === 0) {
            const zeroYCoordinate = height - padding / 4;
            // - //* For no padding top
            // (padding - labelFonts);
            return (
              <SvgText
                key={'label_y_' + index}
                x={x}
                y={zeroYCoordinate}
                fontSize={labelFonts}
                fill={colors.palette.fwdGreyDarker}
                textAnchor="end"
                fontFamily={bookFont}>
                0
              </SvgText>
            );
          }
          return (
            <SvgText
              key={'label_y_' + index}
              x={x}
              y={yCoordinate}
              fontSize={labelFonts}
              fill={colors.palette.fwdGreyDarker}
              textAnchor="end"
              fontFamily={bookFont}>
              {index % 2 === 0 &&
                numberToKFormat(maximumYFromData * (index / PARTS), precision)}
            </SvgText>
          );
        })}
      </>
    );
  };

  return (
    <View>
      <Svg
        style={
          {
            // marginBottom: -lineChartPadding,
          }
        }
        viewBox={`${adjustChartPositionHorizontal} ${adjustChartPositionVertical} ${width} ${height}`}>
        <XAxis />
        <LabelsXAxis />
        <YAxis />
        <LabelsYAxis />

        {numberOfVerticalGuides && <VerticalGuides />}
        <HorizontalGuides />
        {pointsSub && (
          <Polyline
            fill="none"
            stroke={subLineColor ? subLineColor : colors.primary}
            strokeWidth={isTabletMode ? 3 : STROKE}
            points={pointsSub}
          />
        )}

        {pointsCom && (
          <Polyline
            fill="none"
            stroke={comLineColor ? comLineColor : colors.primaryVariant}
            strokeWidth={isTabletMode ? 3 : STROKE}
            points={pointsCom}
          />
        )}

        <SvgText
          x={xAxisTitlePosition.x}
          y={xAxisTitlePosition.y}
          fontSize={labelFonts}
          fill={colors.palette.fwdGreyDarker}
          textAnchor="end"
          fontFamily={bookFont}>
          {xAxisTitle}
        </SvgText>
      </Svg>
    </View>
  );
}
