import React, { useState } from 'react';
import Svg, { Circle, Polyline } from 'react-native-svg';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import { H5, Icon, XView } from 'cube-ui-components';

export type ActivityFeedback = 'I' | 'U';

const INTEREST_X_COR = 9;
const NOT_INTEREST_X_COR = 100;
const CIRCLE_RADIUS = 6;
const CIRCLE_STROKE_WIDTH = 3;
const INTERESTED_CONTAINER_WIDTH = 75;

export default function LeadFeedbackLineChart({
  data = [],
}: {
  data: ActivityFeedback[];
}) {
  const { colors, sizes } = useTheme();

  let points = '';
  const [chartWidth, setChartWidth] = useState(0);
  const countPoints: number = data.length;
  const pointXDistance: number = chartWidth / (countPoints - 1);

  if (countPoints === 1) {
    //* draw 1 more point to draw the line
    const feedback = data[0];
    if (feedback === 'I') {
      points += `0,${INTEREST_X_COR} `;
      points += `${chartWidth},${INTEREST_X_COR} `;
    } else if (feedback === 'U') {
      points += `0,${NOT_INTEREST_X_COR} `;
      points += `${chartWidth},${NOT_INTEREST_X_COR} `;
    }
  } else if (countPoints > 1) {
    data.map((feedback: ActivityFeedback, index: number) => {
      if (feedback === 'I')
        points += `${index * pointXDistance},${INTEREST_X_COR} `;
      else if (feedback === 'U')
        points += `${index * pointXDistance},${NOT_INTEREST_X_COR} `;
    });
  }

  // Counts 'I' and 'U'
  const counts: { I: number; U: number } = {
    I: 0,
    U: 0,
  };
  for (const feedback of data) {
    counts[feedback] = counts[feedback] ? counts[feedback] + 1 : 1;
  }

  return (
    <Container
      onLayout={event => {
        setChartWidth(
          event.nativeEvent.layout.width * 1.0 -
            INTERESTED_CONTAINER_WIDTH -
            (CIRCLE_RADIUS + CIRCLE_STROKE_WIDTH),
        );
      }}>
      <InterestedContainer>
        <Icon.Interested size={sizes[7]} fill={colors.secondary} />
        <Number fontWeight="bold">{counts['I']}</Number>
      </InterestedContainer>

      <NotInterestedContainer>
        <Icon.NotInterested size={sizes[7]} fill={colors.secondary} />
        <Number fontWeight="bold">{counts['U']}</Number>
      </NotInterestedContainer>

      <View style={{ left: INTERESTED_CONTAINER_WIDTH }}>
        <Svg height="100%" width="100%">
          {countPoints > 0 ? (
            <>
              <Polyline
                // points="0,10 40,100 80.234,10, 120,10 160,10 200,100 "
                points={points}
                fill="none"
                stroke={colors.primary}
                strokeWidth="3"
              />
              <Circle
                cx={chartWidth}
                cy={
                  data[countPoints - 1] === 'I'
                    ? INTEREST_X_COR
                    : NOT_INTEREST_X_COR
                }
                fill={colors.background}
                r={CIRCLE_RADIUS}
                stroke={colors.primary}
                strokeWidth={CIRCLE_STROKE_WIDTH}
              />
            </>
          ) : (
            <>
              <Polyline
                points={`0,50 ${chartWidth},50`}
                fill="none"
                stroke={colors.primary}
                strokeWidth="3"
              />
              <Circle
                cx={chartWidth}
                cy={50}
                fill={colors.background}
                r={CIRCLE_RADIUS}
                stroke={colors.primary}
                strokeWidth={CIRCLE_STROKE_WIDTH}
              />
            </>
          )}
        </Svg>
      </View>
    </Container>
  );
}

const Container = styled.View(() => ({
  width: '100%',
  height: 100 + 9, //  + CIRCLE_RADIUS && CIRCLE_STROKE_WIDTH
}));

const Number = styled(H5)(({ theme }) => ({
  minWidth: theme.sizes[10],
  textAlign: 'center',
  color: theme.colors.primary,
}));

const InterestedContainer = styled(XView)(() => ({
  position: 'absolute',
  left: 0,
  top: -8, // Adjust container position
  alignItems: 'center',
}));

const NotInterestedContainer = styled(XView)(() => ({
  position: 'absolute',
  left: 0,
  bottom: -8, // Adjust container position
  alignItems: 'center',
}));
