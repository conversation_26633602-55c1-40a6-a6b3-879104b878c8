import { View, ViewProps, LayoutChangeEvent } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import Svg, {
  Circle,
  Line,
  Linecap,
  Rect,
  Text as SvgText,
} from 'react-native-svg';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

interface Props extends ViewProps {
  strokeWidth?: number;
  padding?: number;
  percent: number;
  center?: React.ReactNode;
  color?: string;
  customColorConfig?: ColorConfig;
}

const animationDuration = 1000;

export default function SemiProgressGauge({
  strokeWidth = 16,
  padding = 16,
  percent,
  center,
  color,
  customColorConfig,
  ...props
}: Props) {
  const { colors } = useTheme();
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });

  const chartRadius =
    Math.max(containerDimensions.height, containerDimensions.width) / 2 -
    strokeWidth / 2 -
    padding -
    20;
  const otherChartRadius =
    Math.max(containerDimensions.height, containerDimensions.width) / 2 -
    strokeWidth / 2 -
    padding;
  const circumference = chartRadius * 2 * Math.PI;
  const arc = circumference * (180 / 360);

  const outerRadiusIncrement = 18;
  const outerCircumference = (chartRadius + outerRadiusIncrement) * 2 * Math.PI;
  const outerArc = outerCircumference * (180 / 360);
  const outerdashArray = `${outerArc} ${outerCircumference}`;

  const dashArray = `${arc} ${circumference}`;
  const cx = containerDimensions.width / 2;
  const cy = containerDimensions.height - strokeWidth / 2 - padding;
  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  const offset = useSharedValue(0);
  const percentNormalized = Math.min(Math.max(percent, 0), 100);
  const endArc = arc - (percentNormalized / 100) * arc;

  useEffect(() => {
    offset.value = arc;
    offset.value = withTiming(endArc, {
      duration: animationDuration,
    });
  });

  const getTransform = (deg: number) => {
    return `rotate(${180 + (deg * 270) / 360}, ${cx}, ${cy})`;
  };

  const getSemiTransformByFraction = (fraction: number) => {
    const startDegree = 240;
    const endDegree = 480;
    const targetDegree =
      startDegree +
      fraction * (endDegree - startDegree) +
      (isStrokeLinecapRound ? 8 : 0);
    return getTransform(targetDegree);
  };

  const animatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: offset.value,
    };
  });

  const strokeLinecap: Linecap | undefined = 'round';
  const isStrokeLinecapRound = strokeLinecap === 'round';

  const colorConfigArr = [
    {
      color: lowColor,
      startAtfaction: lowStartAtfaction,
      endAtfaction: lowEndAtfaction,
    },
    {
      color: midColor,
      startAtfaction: midStartAtfaction,
      endAtfaction: midEndAtfaction,
    },
    {
      color: highColor,
      startAtfaction: highStartAtfaction,
      endAtfaction: highEndAtfaction,
    },
  ];

  const currentColor = colorConfigArr.find(
    (config, i) =>
      (percentNormalized / 100 >= config.startAtfaction &&
        percentNormalized / 100 < colorConfigArr[i + 1]?.startAtfaction) ||
      (i === colorConfigArr.length - 1 &&
        percentNormalized / 100 >= colorConfigArr[i]?.startAtfaction),
  )?.color;

  const activceColorConfig = customColorConfig
    ? customColorConfig
    : defaultColorConfigMap;

  return (
    <View {...props}>
      <View
        onLayout={onLayoutChart}
        style={{
          height: '100%',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Svg
          width={containerDimensions.width}
          height={containerDimensions.height}>
          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={colors.surface}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            transform={getTransform(0)}
            strokeLinecap="round"
          />
          <AnimatedCircle
            animatedProps={animatedProps}
            stroke={color ?? colors.primary}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            fill="transparent"
            transform={getTransform(0)}
            strokeLinecap="round"
          />
          {/* //* Outer Arc Starts ---VVV--- */}
          {/* HIGH */}
          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={activceColorConfig?.high?.color}
            r={chartRadius + outerRadiusIncrement}
            cx={cx}
            cy={cy}
            strokeWidth={4}
            strokeDasharray={getOuterSemiCircleDashArray({
              circumference: outerCircumference,
              factionOfSemicicle: 1,
              arc: outerArc,
            })}
            transform={getTransform(0)}
            strokeLinecap="round"
          />
          {/* MID */}

          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={activceColorConfig?.mid?.color}
            r={chartRadius + outerRadiusIncrement}
            cx={cx}
            cy={cy}
            strokeWidth={4}
            strokeDasharray={getOuterSemiCircleDashArray({
              circumference: outerCircumference,
              factionOfSemicicle: midEndAtfaction,
              arc: outerArc,
            })}
            transform={getTransform(0)}
            strokeLinecap="round"
          />
          {/* LOW */}
          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={activceColorConfig?.low?.color}
            r={chartRadius + outerRadiusIncrement}
            cx={cx}
            cy={cy}
            strokeWidth={4}
            strokeDasharray={getOuterSemiCircleDashArray({
              circumference: outerCircumference,
              factionOfSemicicle: defaultColorConfigMap.low.endAtfaction,
              arc: outerArc,
            })}
            transform={getTransform(0)}
            strokeLinecap="round"
          />
          {/* //* Outer Arc Ends ---^^^--- */}
          {/* Approach 1 */}
          {/* <Circle
            stroke={'white'}
            strokeWidth={strokeWidth + outerRadiusIncrement + 3}
            strokeDasharray={`6 ${circumference - 4.5}`}
            strokeDashoffset={endArc - strokeWidth / 2}
            cx={cx}
            cy={cy}
            r={chartRadius}
          />
          <Circle
            stroke={
              colorConfigArr.find(
                (c, i) =>
                  percentNormalized / 100 <= c.percent &&
                  (percentNormalized / 100 >= colorConfigArr[i + 1]?.percent ||
                    i + 2 > colorConfigArr.length),
              )?.color ?? 'black'
            }
            strokeWidth={strokeWidth + outerRadiusIncrement}
            strokeDasharray={`3 ${circumference}`}
            strokeDashoffset={endArc - strokeWidth / 2}
            cx={cx}
            cy={cy}
            r={chartRadius}
          /> */}
          {/* Approach 2 */}
          <Line
            x1={cx - chartRadius - 10}
            y1={cy}
            x2={cx - chartRadius + 10}
            y2={cy}
            stroke={'white'}
            strokeWidth={10}
            strokeLinecap="round"
            transform={getSemiTransformByFraction(percentNormalized / 100)}
          />
          <Line
            x1={cx - chartRadius - 10}
            y1={cy}
            x2={cx - chartRadius + 10}
            y2={cy}
            stroke={currentColor}
            strokeWidth={6}
            strokeLinecap="round"
            transform={getSemiTransformByFraction(percentNormalized / 100)}
          />
        </Svg>
        <View style={{ position: 'absolute' }}>{center}</View>
      </View>
    </View>
  );
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const getOuterSemiCircleDashArray = ({
  circumference,
  factionOfSemicicle,
  arc,
}: {
  factionOfSemicicle: number;
  circumference: number;
  arc: number;
}) => {
  const defaultDashArray = `${arc} ${circumference}`;
  if (
    factionOfSemicicle > 1 ||
    factionOfSemicicle < 0 ||
    typeof factionOfSemicicle !== 'number'
  ) {
    console.log(
      'factionOfSemicicle does not have a valid value, please check: ',
      factionOfSemicicle,
    );
    return defaultDashArray;
  }
  return `${arc * factionOfSemicicle} ${circumference}`;
};

type ColorConfig = Record<
  'low' | 'mid' | 'high',
  {
    color: string;
    startAtfaction: number;
    endAtfaction: number;
  }
>;

const defaultColorConfigMap = {
  low: {
    color: 'red',
    startAtfaction: 0,
    endAtfaction: 0.3,
  },
  mid: {
    color: 'yellow',
    startAtfaction: 0.3,
    endAtfaction: 0.7,
  },
  high: {
    color: 'green',
    startAtfaction: 0.7,
    endAtfaction: 1,
  },
} satisfies ColorConfig;

const {
  low: {
    color: lowColor,
    startAtfaction: lowStartAtfaction,
    endAtfaction: lowEndAtfaction,
  },
  mid: {
    color: midColor,
    startAtfaction: midStartAtfaction,
    endAtfaction: midEndAtfaction,
  },
  high: {
    color: highColor,
    startAtfaction: highStartAtfaction,
    endAtfaction: highEndAtfaction,
  },
} = defaultColorConfigMap;
