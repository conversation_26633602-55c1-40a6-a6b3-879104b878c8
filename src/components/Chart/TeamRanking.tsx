import { View, LayoutChangeEvent, ViewProps } from 'react-native';
import React, { useEffect, useState } from 'react';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import { Typography, Icon, Row, FloatingButton } from 'cube-ui-components';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import RankingDataDot, { DotData } from 'components/Chart/RankingDataDot';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export type TeamRankingProps = {
  radius?: number;
  customPadding?: number;

  title: string;
  AsOfDate: string;
  teamData: TeamRankingData[];
  myData: TeamRankingData;
  topData: TeamRankingData;
  teamSize: number;
  rankingType: RankingType;
} & ViewProps;

export default function TeamRanking({
  customPadding,
  teamData,
  myData,
  topData,
  teamSize,
  title,
  AsOfDate,
  rankingType = 'APE',
  // radius = 90,
  ...props
}: TeamRankingProps) {
  const { colors, space, borderRadius, sizes } = useTheme();
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });
  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const paddingInDefaultSize = space[2];
  const paddingInNarrow = space[2];

  // const customPadding
  const padding =
    customPadding === undefined
      ? isNarrowScreen
        ? paddingInNarrow
        : paddingInDefaultSize
      : isNarrowScreen && customPadding >= paddingInNarrow
      ? customPadding
      : paddingInNarrow;

  const radius = Math.max(containerDimensions.height / 2, 90) - padding * 2;
  const strokeWidth = 8;
  const innerRadius = radius - strokeWidth / 2;
  const circumference = innerRadius * 2 * Math.PI;
  const arc = circumference * (270 / 360);
  const dashArray = `${arc} ${circumference}`;
  const transform = `rotate(135, ${radius + padding}, ${radius + padding})`;

  const getDotPosition: (
    dotProgressPercentage: number,
  ) => DotData = dotProgressPercentage => {
    // const dotPercentage =
    //   performance === 0 ? 0 : (performance / topPerformance) * 100;

    const x =
      radius +
      padding +
      (radius - strokeWidth / 2) *
        Math.cos((Math.PI / 180) * (225 - (270 * dotProgressPercentage) / 100));
    const y =
      radius +
      padding -
      (radius - strokeWidth / 2) *
        Math.sin((Math.PI / 180) * (225 - (270 * dotProgressPercentage) / 100));

    return {
      x,
      y,
    };
  };

  const meDotPerformance = parseInt(
    myData?.[(rankingType + 'Ranking') as keyof typeof myData],
  );
  const meDotPerformancePercentage = isNaN(meDotPerformance)
    ? 0
    : ((teamSize - meDotPerformance + 1) / teamSize) * 100;

  const orangeArcDashArray = `${
    arc * (meDotPerformancePercentage / 100)
  } ${circumference}`;

  return (
    <View
      style={{
        overflow: 'visible',
      }}
      {...props}>
      <View
        style={{
          zIndex: 0,
          // borderWidth: 1,
          height: '100%',
          position: 'relative',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: colors.palette.whiteTransparent,
        }}
        onLayout={onLayoutChart}>
        <View>
          <Svg
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: colors.palette.whiteTransparent,
            }}
            height={radius * 2}
            width={radius * 2 + padding * 2}>
            <Circle
              cx={radius + padding}
              cy={radius + padding}
              fill="transparent"
              r={innerRadius}
              stroke="#EDEFF0"
              strokeWidth={strokeWidth}
              strokeDasharray={dashArray}
              transform={transform}
            />
            <Circle
              cx={radius + padding}
              cy={radius + padding}
              fill="transparent"
              r={innerRadius}
              stroke={colors.palette.fwdOrange[100]}
              strokeWidth={strokeWidth}
              strokeDasharray={orangeArcDashArray}
              transform={transform}
            />
          </Svg>
          {myData?.AgentCode !== topData?.AgentCode && (
            <RankingDataDot
              type="Top"
              dotPerformance={100}
              // dotPerformance={(topData && topData[rankingType]) || 0}
              getDotPosition={getDotPosition}
            />
          )}
          <RankingDataDot
            type={myData.APERanking == 1 ? 'MeAndTop' : 'Me'}
            dotPerformance={
              meDotPerformancePercentage == Infinity
                ? 0
                : meDotPerformancePercentage
            }
            // dotPerformance={
            //   Number(myData?.[rankingType as keyof typeof myData]) ?? 0
            // }
            getDotPosition={getDotPosition}
          />
          {/* <DataDot
            type="Me"
            dotPerformance={0}
            // dotPerformance={(myData && myData[rankingType]) || 0}
            getDotPosition={getDotPosition}
          />
          {myData?.AgentCode !== topData?.AgentCode && (
            <DataDot
              type="Top"
              dotPerformance={0}
              // dotPerformance={(topData && topData[rankingType]) || 0}
              getDotPosition={getDotPosition}
            />
          )} */}
        </View>
        {/* //* hiding dot label of other team members */}
        {/* 
        // ? if country specific
        {country === 'th'
          ? // * not showing other team members
            null
          : // ? Should other regions showing other team member?
            teamDataProcessed.map((data, idx) => {
              return (
                <DataDot
                  key={`DataDot_${idx}`}
                  dotPerformance={data[rankingType] || 0}
                  dataCount={data.dataCount}
                />
              );
            })} */}
        <View
          style={[
            {
              zIndex: 0,
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center',
            },
            {
              transform: [{ translateY: padding }],
            },
          ]}>
          <Row>
            <Typography.H5
              fontWeight="bold"
              color={colors.palette.fwdOrange[100]}>
              {myData &&
                numberToThousandsFormat(
                  myData[(rankingType + 'Ranking') as RankingRankingType] ?? 0,
                )}
              {/* test */}
            </Typography.H5>
            <Typography.Label
              fontWeight="bold"
              style={{
                textAlign: 'center',
                alignSelf: 'flex-end',
              }}>
              {` / ${numberToThousandsFormat(teamSize)}`}
            </Typography.Label>
          </Row>
          <Typography.Body
            style={{ marginTop: sizes[4], textAlign: 'center', maxWidth: 140 }}
            color={colors.palette.fwdGreyDarker[100]}>
            {title}
          </Typography.Body>
          <Typography.SmallBody
            color={colors.palette.fwdGreyDarker}
            style={{
              marginTop: sizes[1],
              maxWidth: 100,
              textAlign: 'center',
            }}>
            {AsOfDate}
          </Typography.SmallBody>
        </View>
        <View
          style={[
            {
              zIndex: 0,
              position: 'absolute',
              alignItems: 'center',
              // borderWidth: 1,
            },
            {
              transform: [{ translateY: radius * 0.72 + padding }],
              flexDirection: 'row',
              justifyContent: 'space-between',
              width: radius + padding,
            },
          ]}>
          <Typography.SmallBody color={colors.palette.fwdGreyDarker}>
            0 {rankingType}
          </Typography.SmallBody>
          <Typography.SmallBody color={colors.palette.fwdGreyDarker}>
            Top {rankingType}
          </Typography.SmallBody>
        </View>
      </View>
    </View>
  );
}

export type TeamRankingData = {
  AgentCode: string;
  DisplayNameEN?: string;
  DisplayNameTC?: string;
  DisplayNameTH?: string;
  MM: number;
  YYYY: number;
  SC?: number;
  SCRanking?: number;
  APE?: number;
  APERanking?: number;
  FYP?: number;
  FYPRanking?: number;
};

export type RankingType = 'FYP' | 'SC' | 'APE' | 'Case';
export type RankingMyType = 'MyAPE' | 'MyFYP' | 'MySC';
export type RankingRankingType = 'APERanking' | 'FYPRanking' | 'SCRanking';
export type RankingYtdRankingType =
  | 'YtdAPERanking'
  | 'YtdFYPRanking'
  | 'YtdSCRanking';
export type RankingMtdRankingType =
  | 'MtdAPERanking'
  | 'MtdFYPRanking'
  | 'MtdSCRanking';
export type RankingYTDType = 'YTD_APE' | 'YTD_FYP' | 'YTD_SC';
