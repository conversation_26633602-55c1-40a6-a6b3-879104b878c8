import { useTheme } from '@emotion/react';
import * as d3 from 'd3';
import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import Svg, { Defs, LinearGradient, Path, Stop } from 'react-native-svg';

export type LineGraphProps = {
  data: number[];
  strokeColor: string;
  gradientColorFrom: string;
  gradientColorTo: string;
  width?: number;
  height?: number;
  style?: StyleProp<ViewStyle>;
};

export function GradientLineChart(props: LineGraphProps) {
  const { colors, space, sizes } = useTheme();

  const width = props.width !== undefined ? props.width : sizes[41];
  const height = props.height !== undefined ? props.height : sizes[30];

  const min = 0; // value start from zero
  const max = Math.max(...props.data); // value end to max.value

  const yScale =
    max == 0
      ? d3.scaleLinear().domain([min, max]).range([height, height])
      : d3.scaleLinear().domain([min, max]).range([height, 0]); // more positive the height is, more deep the line stay;

  // scaling data values to screen coordinates.
  const xScale = d3
    .scaleLinear()
    .domain([0, props.data.length - 1]) // each data width = total width / num of data
    .range([0, width]); // total width

  const lineFn = d3
    .line<number>()
    .x((d, ix) => xScale(ix))
    .y((d, ix) => yScale(d));

  const areaFn = d3
    .area<number>()
    .x((d, ix) => xScale(ix))
    .y0(height)
    .y1((d, ix) => yScale(d));

  const svgLine = lineFn(props.data);
  const svgArea = areaFn(props.data);

  return (
    <View>
      <Svg width={width} height={height}>
        <Defs>
          <LinearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <Stop
              offset={'0%'}
              stopColor={props.gradientColorFrom}
              stopOpacity={0.5}
            />
            <Stop
              offset={'100%'}
              stopColor={props.gradientColorTo}
              stopOpacity={0}
            />
          </LinearGradient>
        </Defs>
        <Path
          d={svgLine ? svgLine : ''}
          stroke={props.strokeColor}
          fill={'none'}
          strokeWidth={2}
        />
        <Path
          d={svgArea ? svgArea : ''}
          stroke={'none'}
          fill={'url(#gradient)'}
        />
      </Svg>
    </View>
  );
}
