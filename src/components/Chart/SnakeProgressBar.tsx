import { View, Text, ColorValue } from 'react-native';
import React, { useEffect } from 'react';
import { Box } from 'cube-ui-components';
import { NumberProp, Path, Svg } from 'react-native-svg';
import { useTheme } from '@emotion/react';
import MaskedView from '@react-native-masked-view/masked-view';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';

const AnimatedPath = Animated.createAnimatedComponent(Path);

type SnakeProgressBarProps = {
  progress: number;
  width: number;
  height: number;
  strokeWidth?: number;
  strokeLineWidth?: number;
  strokeColor?: ColorValue;
  strokeBackgroundColor?: ColorValue;
  strokeLineColor?: ColorValue;
  strokeLineDashArray?: NumberProp | readonly NumberProp[] | undefined;
};

export default function SnakeProgressBar({
  progress = 100,
  width = 320,
  height = 320,
  strokeWidth = 24,
  strokeColor,
  strokeBackgroundColor,
  strokeLineColor,
  strokeLineWidth = 2,
  strokeLineDashArray,
}: SnakeProgressBarProps) {
  const { colors } = useTheme();

  const SvgContainer = ({ children }: { children: React.ReactNode }) => (
    <Svg
      viewBox={`${-strokeWidth / 2} ${-strokeWidth / 2} ${
        width + strokeWidth
      } ${height + strokeWidth}`}>
      {children}
    </Svg>
  );

  const ProgressSnakePath = () => (
    <SnakePath
      strokeColor={strokeColor ?? colors.primary}
      strokeWidth={strokeWidth}
      width={width}
      height={height}
      progress={progress}
    />
  );

  return (
    <Box h={height} w={width}>
      <SvgContainer>
        <SnakePath
          strokeColor={strokeBackgroundColor ?? colors.palette.fwdGrey[100]}
          strokeWidth={strokeWidth}
          width={width}
          height={height}
        />
        <MaskedView
          maskElement={
            <SvgContainer>
              <ProgressSnakePath />
            </SvgContainer>
          }>
          <SvgContainer>
            <SnakePath
              strokeColor={strokeLineColor ?? colors.background}
              strokeWidth={strokeLineWidth}
              opacity={0.5}
              width={width}
              height={height}
              strokeDasharray={strokeLineDashArray ?? [10]}
            />
          </SvgContainer>
        </MaskedView>
        <ProgressSnakePath />
      </SvgContainer>
    </Box>
  );
}

type SnakePathProps = {
  strokeWidth: number;
  strokeColor: ColorValue;
  progress?: number;
  width: number;
  height: number;
  strokeDasharray?: NumberProp | readonly NumberProp[] | undefined;
  strokeDashoffset?: NumberProp;
  opacity?: number;
};

const SnakePath = ({
  width,
  height,
  strokeWidth,
  strokeColor,
  progress,
  strokeDasharray,
  strokeDashoffset,
  opacity = 1,
}: SnakePathProps) => {
  const arcRadius = width / 4;
  const { animation } = useTheme();

  const snakeLength =
    2 * Math.PI * arcRadius +
    width / 2 +
    (width * 3) / 4 -
    width / 8 +
    (width * 5) / 8;

  const progressVal = useSharedValue(0);

  useEffect(() => {
    progressVal.value = 0;

    if (progress) {
      progressVal.value = withDelay(
        0,
        withTiming(progress, { duration: animation.duration }),
      );
    }
  }, []);

  const animatedProps = useAnimatedProps(() => {
    if (strokeDashoffset) {
      return {
        strokeDashoffset: strokeDashoffset,
      };
    }

    if (!progress && progress != 0) {
      return {
        strokeDashoffset: 0,
      };
    }

    return {
      strokeDashoffset: (1 - progressVal.value / 100) * snakeLength,
    };
  });

  return (
    <AnimatedPath
      d={`
          M ${width / 8} ${height} H ${(width * 3) / 4} 
          A ${arcRadius} ${arcRadius} 0 0 0 ${(width * 3) / 4} ${height / 2}
          h ${-width / 2}
          A ${arcRadius} ${arcRadius} 0 0 1 ${width / 4} 0
          h ${(width * 5) / 8}
          `}
      stroke={strokeColor}
      opacity={opacity}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeDasharray={strokeDasharray ?? [snakeLength]}
      // strokeDashoffset={
      //   strokeDashoffset
      //     ? strokeDashoffset
      //     : progress
      //     ? (1 - progress / 100) * snakeLength
      //     : snakeLength
      // }
      animatedProps={animatedProps}
    />
  );
};
