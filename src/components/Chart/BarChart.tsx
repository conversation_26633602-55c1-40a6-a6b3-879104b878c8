import { View } from 'react-native';
import React, { Fragment } from 'react';
import Svg, { Line, G, Text as TextSvg, Rect } from 'react-native-svg';
import Animated from 'react-native-reanimated';
import * as d3 from 'd3';
import { formatCurrency } from 'utils/helper';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Box, SmallLabel } from 'cube-ui-components';
import Skeleton from 'components/Skeleton';

const gridColor = '#F8F9F9';

export enum TargetLineStyle {
  solid = 'solid',
  dashed = 'dashed',
}
export enum BarStyle {
  straight = 'straight',
  curved = 'curved',
}

export const yIntervalHandler = (yAxisMax: number) =>
  yAxisMax < 10
    ? 2
    : yAxisMax < 30
    ? 5
    : yAxisMax <= 200
    ? 10
    : yAxisMax < 400
    ? 20
    : yAxisMax < 600
    ? 50
    : yAxisMax < 1000
    ? 100
    : 200;

export const calculateYAxisMax = (
  array1: Array<number>,
  array2: Array<number>,
) => Math.max(Math.max(...array1), Math.max(...array2));

export default function BarChart({
  data = exData,
  dataOptions = exDataOptions,
  isFormatToK = false,
  barWidthInput = 50,
  targetLineStyle = TargetLineStyle.solid,
  barTopBorderStyle = BarStyle.straight,
  xlabelWidth = 52,
  xlabelColor = '#757575',
  isWidthOffset = true,
  isLoading,
  widthOffset = 20,
}: {
  data: DataPoint[];
  dataOptions: DataOptions;
  isFormatToK?: boolean;
  barWidthInput?: number;
  targetLineStyle?: TargetLineStyle;
  barTopBorderStyle?: BarStyle;
  xlabelWidth?: number;
  xlabelColor?: string;
  isWidthOffset?: boolean;
  isLoading?: boolean;
  widthOffset?: number;
}) {
  const {
    x,
    y,
    width,
    height,
    leftPadding,
    bottomPadding,
    yFormat,
    // xPadding,
    yInterval,
    yLabelSuffix,
  } = dataOptions;
  const X = d3.map(data, x);
  const Y = d3.map(data, y);

  const max = Math.max(
    Math.max(
      ...data.map(val => val.value),
      ...data.map(val => val.target ?? 0),
    ) ?? yInterval,
    yInterval * 2,
  );
  // const min = Math.min(...data.map(val => val.value)) ?? 0;

  // const digits_of_max = max.toString().length;
  // const round_factor = Math.pow(10, digits_of_max - 1);

  // const xDomain = dataOptions.xDomain ?? X;

  // console.log(
  //   '🫧 round_factor:',
  //   round_factor,
  //   '🫧🫧 max:',
  //   max,
  //   '🫧🫧🫧 max / round_factor:',
  //   max / round_factor,
  //   '🫧🫧🫧🫧 Math.ceil(max / round_factor) * round_factor:',
  //   Math.ceil(max * 1.1),
  // );
  const MAX = Math.round((max * 1.1) / yInterval) * yInterval;

  const yDomain = dataOptions.yDomain ?? [0, MAX];

  const topPadding = dataOptions.topPadding ?? bottomPadding;
  const rightPadding = dataOptions.rightPadding || leftPadding;
  const internalPadding = dataOptions.internalPadding;

  const chartHeight = height - bottomPadding - topPadding;
  const chartWidth = width - leftPadding - rightPadding;

  //* Chart width = n * bar width + (n+1) white space separator
  //* Chart width from x=0 to x=max
  const real_chart_width = chartWidth - 2 * leftPadding;
  //* Total white space = chart width minus the width of all bars
  const total_width_of_spacing =
    typeof internalPadding === 'number'
      ? real_chart_width - data.length * barWidthInput - internalPadding * 2
      : real_chart_width - data.length * barWidthInput;
  const number_of_separator_internal =
    data.length === 1 ? 2 : Math.max(data.length - 1, 1);
  const number_of_separator =
    typeof internalPadding === 'number'
      ? number_of_separator_internal
      : data.length + 1;
  //* Each white space width
  const width_bar_separator = total_width_of_spacing / number_of_separator;
  //* correctFactor is the y-interval
  const correctFactor = 10;

  const total_word_width_white_spacing =
    real_chart_width -
    (typeof internalPadding === 'number'
      ? internalPadding
      : width_bar_separator) *
      2 +
    (xlabelWidth - barWidthInput);
  const width_word_separator =
    (total_word_width_white_spacing - xlabelWidth * data.length) /
    (data.length - 1);

  const yRange = [height - bottomPadding, topPadding];
  const yScale = d3.scaleLinear(yDomain, yRange);

  const ceilYAxis = Math.ceil(yDomain[1] / yInterval + 1);

  const yAxis = new Array(isNaN(ceilYAxis) ? 0 : ceilYAxis)
    .fill(null)
    .map((_, i) => {
      const ratio = (1 / (yDomain[1] / yInterval)) * i;
      return ratio;
    });

  let title: DataOptions['title'];

  if (dataOptions.title === undefined) {
    const formatValue = yScale.tickFormat(100, yFormat);
    title = (_: DataPoint, i: number) => `${X[i]}\n${formatValue(Y[i])}`;
  } else {
    const O = d3.map(data, d => d);
    const T = dataOptions.title;
    title = (_: DataPoint, i: number) => T(O[i], i, data);
  }

  const { colors } = useTheme();

  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'space-between',
        transform: [{ translateX: isWidthOffset ? widthOffset / 2 : 0 }],
      }}>
      <Animated.View>
        <Svg width={width} height={height} stroke="#6231ff">
          <G y={topPadding}>
            {yAxis.map((yRatio, idx) => {
              const yLabel = `${
                isFormatToK
                  ? formatCurrency(yInterval * idx, 1)
                  : yInterval * idx
              }${yLabelSuffix ? yLabelSuffix : ''}`;
              const showLabel = idx % 2 === 0 && !isLoading;
              return (
                <Fragment key={`GridLine_${idx}_${yRatio}`}>
                  {/*  //* Y-axis Grid Line */}
                  <Line
                    key={yRatio}
                    x1={leftPadding * 2}
                    y1={chartHeight * yRatio}
                    x2={width - rightPadding * 2}
                    y2={chartHeight * yRatio}
                    stroke={gridColor}
                    strokeWidth="1"
                  />
                  {/* //* Y-axis label */}
                  {showLabel && (
                    <TextSvg
                      strokeWidth={0}
                      fill={'gray'}
                      x={leftPadding}
                      y={chartHeight + 5 - chartHeight * yRatio}
                      fontFamily={'FWDCircularTT-Book'}
                      fontSize={12}
                      textAnchor="middle">
                      {yInterval * idx <= MAX && yLabel}
                    </TextSvg>
                  )}
                  {/*  //* X-axis Grid Line */}
                  <Line
                    x1={
                      width -
                      rightPadding * 2 -
                      (width - rightPadding * 4) * yRatio
                    }
                    y1={chartHeight}
                    x2={
                      width -
                      rightPadding * 2 -
                      (width - rightPadding * 4) * yRatio
                    }
                    y2={0}
                    stroke={gridColor}
                    strokeWidth="1"
                  />
                </Fragment>
              );
            })}
          </G>
          {!isLoading && (
            <G y={topPadding}>
              {Y.map((y, idx) => {
                const yRatio = (y === undefined ? 0 : y) / yDomain[1];
                const targetRatio = data[idx].target! / yDomain[1];
                const height = chartHeight * yRatio + correctFactor;
                const multiple = Y.length === 1 ? idx + 1 : idx;
                const x_optimize =
                  typeof internalPadding === 'number'
                    ? width_bar_separator * multiple +
                      barWidthInput * idx +
                      2 * leftPadding +
                      internalPadding
                    : width_bar_separator * (idx + 1) +
                      barWidthInput * idx +
                      2 * leftPadding;
                const x_line_center = x_optimize + barWidthInput / 2;

                return (
                  <Fragment key={`Bars_${idx}_${yRatio}`}>
                    {/* The bars */}
                    <Rect
                      rx={barTopBorderStyle === BarStyle.straight ? 0 : 4}
                      x={x_optimize}
                      y={chartHeight - height + correctFactor}
                      width={barWidthInput}
                      height={height}
                      stroke="none"
                      fill={data[idx].color}
                    />
                    {/*// * ~~~~~~~~The Targets*/}
                    <Line
                      x1={x_line_center}
                      x2={x_line_center}
                      stroke={colors.palette.white}
                      strokeWidth={barWidthInput}
                      y1={chartHeight * (1 - targetRatio) - 1}
                      y2={
                        chartHeight * (1 - targetRatio) +
                        (targetLineStyle === TargetLineStyle.solid ? 1 : 2)
                      }
                    />
                    {/* // * The Targets lines */}
                    <Line
                      x1={x_line_center}
                      x2={x_line_center}
                      stroke={colors.palette.black}
                      strokeWidth={barWidthInput}
                      y1={
                        chartHeight * (1 - targetRatio) -
                        (targetLineStyle === TargetLineStyle.solid ? 1 : 0)
                      }
                      y2={chartHeight * (1 - targetRatio) + 1}
                    />
                  </Fragment>
                );
              })}
              {/* fill the white space */}
              <Rect
                x={leftPadding * 2}
                y={chartHeight}
                width={real_chart_width}
                height={correctFactor + 1}
                stroke="none"
                fill={colors.palette.white}
              />
            </G>
          )}
        </Svg>
      </Animated.View>
      <TextStyled
        style={{
          justifyContent: 'center',
          width,
        }}>
        {Y.map((_, idx) => {
          if (isLoading) {
            return (
              <Box
                key={'Loading_' + idx}
                ml={idx === 0 ? 0 : width_word_separator}>
                <Skeleton
                  key={`label_On_Y_${idx}`}
                  width={xlabelWidth}
                  height={16}
                  radius={2}
                />
                <Box
                  width={'100%'}
                  position="absolute"
                  top={-35}
                  alignItems="center">
                  <Skeleton
                    key={`label_On_Y_${idx}`}
                    width={xlabelWidth * 0.74}
                    height={20}
                    radius={0}
                    containerStyle={{
                      borderTopLeftRadius: 4,
                      borderTopRightRadius: 4,
                    }}
                  />
                </Box>
              </Box>
            );
          }
          return (
            <SmallLabel
              key={`label_On_Y_${idx}`}
              style={{
                width: xlabelWidth,
                textAlign: 'center',
                color: xlabelColor,
                fontSize: 12,
                marginLeft: idx === 0 ? 0 : width_word_separator,
              }}>
              {data[idx].name}
            </SmallLabel>
          );
        })}
      </TextStyled>
    </View>
  );
}

const exData = [
  { name: 'Contacted', value: 35, target: 40, color: '#F3BB90' },
  { name: 'Appt', value: 15, target: 35, color: '#FFECDE' },
  { name: 'Illus', value: 10, target: 15, color: '#FED141' },
  { name: 'Submitted', value: 5, target: 10, color: '#183028' },
];

const exDataOptions: DataOptions = {
  x: (d: DataPoint, i: number) => i,
  y: (d: DataPoint) => d.value,
  title: (d: DataPoint) => d.name,
  bottomPadding: 10,
  leftPadding: 10,
  width: 300,
  height: 200,
  yDomain: [0, 50],
  yInterval: 10,
  xPadding: 0.1,
  color: '#E87722',
};

const TextStyled = styled.View(({ theme }) => ({
  flexDirection: 'row',
}));

export type DataPoint = {
  name: string;
  value: number;
  target?: number;
  color?: string;
};

type DataOptions = {
  x: (d: DataPoint, i: number) => number;
  y: (d: DataPoint) => number;
  title: (d: DataPoint, i: number, data: DataPoint[]) => string;
  rightPadding?: number;
  topPadding?: number;
  leftPadding: number;
  bottomPadding: number;
  width: number;
  height: number;
  xDomain?: number[];
  yDomain?: number[];
  yInterval: number;
  xPadding: number;
  yFormat?: string;
  yLabel?: string;
  color?: string;
  yLabelSuffix?: string;
  internalPadding?: number;
};
