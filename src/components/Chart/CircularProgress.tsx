import { View, ViewProps, LayoutChangeEvent, Text } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import Svg, { Circle, Text as SvgText } from 'react-native-svg';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Typography } from 'cube-ui-components';

interface Props extends ViewProps {
  strokeWidth?: number;
  padding?: number;
  percent: number;
  center?: React.ReactNode;
  color?: string;
  isLinecapRound?: boolean;
  isLabelShown?: boolean;
  customLabel?: React.ReactNode;
}

export default function CircularProgress({
  strokeWidth = 32,
  padding = 16,
  percent,
  center,
  color,
  isLinecapRound,
  isLabelShown,
  customLabel,
  ...props
}: Props) {
  const { colors } = useTheme();
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });

  const chartRadius =
    Math.min(containerDimensions.height, containerDimensions.width) / 2 -
    strokeWidth / 2 -
    padding;

  const circumference = chartRadius * 2 * Math.PI;
  const arc = circumference * (360 / 360);
  const dashArray = `${arc} ${circumference}`;
  const cx = containerDimensions.width / 2;
  const cy = containerDimensions.height / 2;
  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  const offset = useSharedValue(0);

  useEffect(() => {
    const percentNormalized = Math.min(Math.max(percent, 0), 100);
    offset.value = arc;
    offset.value = withTiming(arc - (percentNormalized / 100) * arc, {
      duration: 1500,
    });
  });

  const getTransform = (deg: number) => {
    return `rotate(${270}, ${cx}, ${cy})`;
  };

  const animatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: offset.value,
    };
  });

  return (
    <View {...props}>
      <View
        onLayout={onLayoutChart}
        style={{
          height: '100%',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <View style={{ position: 'absolute' }}>
          {isLabelShown ? (
            <Typography.H7 fontWeight="bold" color={colors.primary}>
              {Math.abs(percent).toFixed(0)}%
            </Typography.H7>
          ) : customLabel ? (
            <>{customLabel}</>
          ) : (
            <></>
          )}
        </View>
        <Svg
          width={containerDimensions.width}
          height={containerDimensions.height}>
          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={colors.surface}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            transform={getTransform(0)}
          />
          <AnimatedCircle
            animatedProps={animatedProps}
            stroke={color ?? colors.primary}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            strokeLinecap={isLinecapRound ? 'round' : 'square'}
            fill="transparent"
            transform={getTransform(0)}
          />
        </Svg>
        <View style={{ position: 'absolute' }}>{center}</View>
      </View>
    </View>
  );
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);
