import {
  View,
  ViewProps,
  LayoutChangeEvent,
  Image,
  ColorValue,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { useTheme } from '@emotion/react';
import Svg, {
  Circle,
  Linecap,
  ForeignObject,
  NumberProp,
} from 'react-native-svg';
import Animated, {
  useAnimatedProps,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  progressFlag,
  progressNewFlag,
} from 'features/teamManagement/assets/images';

type DefaultProps = {
  type?: 'default' | 'semi';
  strokeWidth?: number;
  padding?: number;
  percent: number | number[]; // if multi storkes please set percent as an array of multi percent values
  center?: React.ReactNode;
  color?: string;
  benchmark?: number; // the position percent of the benchmark showing
  strokeLinecap?: Linecap;
  IconAtEnd?: React.ReactNode;
  fullAmount?: number;
  iconPositionOffset?: {
    x: number;
    y: number;
  };
  hasEndFlag?: boolean;
} & ViewProps;

type MultiProgressProps = {
  type: 'multi';
  secondaryPercent: number;
  secondaryColor?: string;
} & Omit<DefaultProps, 'type'>;

const animationDuration = 1000;

const CustomAnimatedCircle = ({
  percent,
  animatedProps,
  stroke,
  r,
  cx,
  cy,
  strokeWidth,
  strokeLinecap,
  strokeDasharray,
  fill,
  transform: getTransform,
}: {
  percent: number;
  animatedProps: Partial<{
    strokeDashoffset: number;
  }>;
  stroke: ColorValue;
  r: NumberProp;
  cx: NumberProp;
  cy: NumberProp;
  strokeWidth?: number;
  strokeLinecap?: 'round' | 'square' | 'butt';
  strokeDasharray: NumberProp;
  fill: ColorValue;
  transform: (t: number) => string;
}) => {
  const circumference = (r as number) * 2 * Math.PI;
  const arcRoundEnd = circumference * (1 / 360);
  const dashArrayRoundEnd = `${arcRoundEnd} ${circumference}`;
  return (
    <>
      {strokeLinecap === 'round' && percent > 0 && percent < 100 && (
        <AnimatedCircle
          animatedProps={animatedProps}
          stroke={stroke}
          r={r}
          cx={cx}
          cy={cy}
          strokeWidth={strokeWidth}
          strokeLinecap={'round'}
          strokeDasharray={dashArrayRoundEnd}
          fill={fill}
          transform={getTransform(-125)}
        />
      )}
      <AnimatedCircle
        animatedProps={animatedProps}
        stroke={stroke}
        r={r}
        cx={cx}
        cy={cy}
        strokeWidth={strokeWidth}
        strokeDasharray={strokeDasharray}
        fill={fill}
        transform={getTransform(0)}
      />
    </>
  );
};

export default function ProgressGauge({
  type = 'default',
  percent,
  strokeWidth = 32,
  strokeLinecap = 'square',
  padding = 16,
  center,
  color,
  hasEndFlag = false,
  benchmark = 0,
  IconAtEnd,
  iconPositionOffset,
  fullAmount = 100, // Provide a default value for fullAmount
  ...props
}: DefaultProps | MultiProgressProps) {
  const { colors } = useTheme();
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });

  const chartRadius =
    Math.min(containerDimensions.height, containerDimensions.width) / 2 -
    strokeWidth / 2 -
    padding;

  const cx = containerDimensions.width / 2;
  const cy = containerDimensions.height / 2;
  const circumference = chartRadius * 2 * Math.PI;
  const angle = type === 'default' ? 270 : 180;
  const angleOffset = type === 'default' ? 135 : 180;
  const arc = (circumference * angle) / 360;
  const dashArray = `${arc} ${circumference}`;
  const benchmarkArc = circumference * (2 / 360);
  const benchmarkDashArray = `${benchmarkArc} ${circumference}`;

  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };
  const secondaryColor =
    'secondaryColor' in props ? props.secondaryColor : undefined;

  const offset = useSharedValue(0);
  const secondaryOffset = useSharedValue(0);
  const benchmarkOffset = useSharedValue(0);

  //* as the round end will hit some issues if it is less than 3%
  const getCorrectPercent = (percent: number) => {
    if (strokeLinecap === 'round' && percent > 0 && percent < 3) return 3;
    return percent;
  };

  const primaryPercent = Array.isArray(percent)
    ? getCorrectPercent(percent[0])
    : getCorrectPercent(percent);
  const secondaryPercent =
    Array.isArray(percent) && percent[1] ? getCorrectPercent(percent[1]) : 0;

  useEffect(() => {
    const percentNormalized = Math.min(Math.max(primaryPercent, 0), fullAmount);
    offset.value = arc;
    offset.value = withTiming(arc - (percentNormalized / fullAmount) * arc, {
      duration: animationDuration,
    });

    const secondaryPercentNormalized = Math.min(
      Math.max(secondaryPercent, 0),
      fullAmount,
    );
    secondaryOffset.value = arc;
    secondaryOffset.value = withTiming(
      arc - (secondaryPercentNormalized / fullAmount) * arc,
      {
        duration: animationDuration,
      },
    );

    const benchmarkPercentNormalized = Math.min(Math.max(benchmark, 0), 100);
    benchmarkOffset.value = arc;
    benchmarkOffset.value = withTiming(
      arc - (benchmarkPercentNormalized / fullAmount) * arc,
      {
        duration: animationDuration,
      },
    );
  });

  const getTransform = (deg: number) => {
    return `rotate(${angleOffset + (deg * angle) / 360}, ${cx}, ${cy})`;
  };

  const animatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: offset.value,
    };
  });
  const secondaryAnimatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: secondaryOffset.value,
    };
  });

  const numDashes = 30; // Number of dashes in the pattern

  // Calculate the angle between dashes
  const angleIncrement = (2 * Math.PI) / numDashes;

  const benchmarkAnimatedProps = useAnimatedProps(() => {
    return {
      strokeDashoffset: benchmarkOffset.value,
    };
  });

  //* Pythagorean theorem to calculate the flag position
  const flagPosition = Math.sqrt(
    chartRadius * chartRadius -
      ((circumference * 45) / 360) * ((circumference * 45) / 360),
  );

  return (
    <View {...props}>
      <View
        onLayout={onLayoutChart}
        style={{
          height: '100%',
          width: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Svg
          width={containerDimensions.width}
          height={containerDimensions.height}>
          <Circle
            fill={colors.palette.whiteTransparent}
            stroke={
              type === 'default' ? colors.surface : colors.palette.fwdGrey[100]
            }
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeDasharray={dashArray}
            transform={getTransform(0)}
            // strokeLinecap={strokeLinecap}
          />
          <ForeignObject
            x={chartRadius * 2 - strokeWidth / 2 + (iconPositionOffset?.x ?? 0)}
            y={
              chartRadius * 2 - strokeWidth / 2 + (iconPositionOffset?.y ?? 0)
            }>
            {IconAtEnd}
          </ForeignObject>
          <CustomAnimatedCircle
            percent={secondaryPercent}
            animatedProps={secondaryAnimatedProps}
            stroke={colors.palette.fwdYellow[100]}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeLinecap={strokeLinecap}
            strokeDasharray={dashArray}
            fill="transparent"
            transform={getTransform}
          />
          <CustomAnimatedCircle
            percent={primaryPercent}
            animatedProps={animatedProps}
            stroke={color ?? colors.primary}
            r={chartRadius}
            cx={cx}
            cy={cy}
            strokeWidth={strokeWidth}
            strokeLinecap={strokeLinecap}
            strokeDasharray={dashArray}
            fill="transparent"
            transform={getTransform}
          />
          {hasEndFlag && (
            <Image
              style={{
                position: 'absolute',
                top: cy + flagPosition - 22, // -20 height of flag and -4 offset for design,
                left: cx + flagPosition + 4, // -2 offset for design
                height: 40,
                width: 40,
              }}
              source={progressNewFlag}
            />
          )}
          {benchmark > 0 && (
            <AnimatedCircle
              animatedProps={benchmarkAnimatedProps}
              stroke={colors.palette.fwdDarkGreen[100]}
              r={chartRadius}
              cx={cx}
              cy={cy}
              strokeWidth={strokeWidth + 2}
              strokeLinecap={'butt'}
              strokeDasharray={benchmarkDashArray}
              fill="transparent"
            />
          )}
        </Svg>
        <View style={{ position: 'absolute' }}>{center}</View>
      </View>
    </View>
  );
}

const AnimatedCircle = Animated.createAnimatedComponent(Circle);
