import { View, ViewProps, LayoutChangeEvent } from 'react-native';
import React, { FC, Fragment, useState } from 'react';
import { Theme, useTheme } from '@emotion/react';
import { Circle, Svg, Text } from 'react-native-svg';
import RankingDataDot, { DotData } from 'components/Chart/RankingDataDot';
import { Row, Typography } from 'cube-ui-components';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

// The difference in deg between the upright 0 deg and the chart 0 deg
const TEXT_TO_DEFAULT_DEG_ROTATION = -270;

interface Props extends ViewProps {
  title: string;
  asOfDate: string;
  myRanking: number;
  totalRanking: number;
  rankingType?: string;
  customPadding?: number;
  strokeWidth?: number;
  fullDeg?: number;
  startDeg?: number;
  arcArr?: (colors: Theme['colors']) => {
    opacity: number;
    percent: number;
    text: string;
    textColor: string;
    color: string;
  }[];
}

// const boldFont = 'FWDCircularTT-Bold';
const bookFont = 'FWDCircularTT-Book';

const oldArcArr = (colors: Theme['colors']) => [
  {
    opacity: 0.25,
    percent: 0,
    text: 'Average',
    textColor: colors.secondary,
    color: colors.primary,
  },
  {
    opacity: 0.5,
    percent: 25,
    text: 'Top 75%',
    textColor: colors.secondary,
    color: colors.primary,
  },
  {
    opacity: 0.75,
    percent: 50,
    text: 'Top 50%',
    textColor: colors.background,
    color: colors.primary,
  },
  {
    opacity: 1,
    percent: 75,
    text: 'Top 25%',
    textColor: colors.background,
    color: colors.primary,
  },
];

export const newArcArr = (colors: Theme['colors']) => [
  {
    opacity: 1,
    percent: 0,
    text: '',
    textColor: colors.secondary,
    color: '#FED141',
  },
  {
    opacity: 1,
    percent: 25,
    text: 'Top 75%',
    textColor: colors.secondary,
    color: '#FF6816',
  },
  {
    opacity: 1,
    percent: 50,
    text: 'Top 50%',
    textColor: colors.secondary,
    color: '#FF9B0A',
  },
  {
    opacity: 1,
    percent: 75,
    text: 'Top 25%',
    textColor: colors.secondary,
    color: '#88C057',
  },
];

export default function RankingPercentage({
  strokeWidth = 32,
  customPadding,
  rankingType,
  asOfDate,
  title,
  myRanking,
  totalRanking,
  fullDeg = 270,
  startDeg = 135,
  arcArr = oldArcArr,
  ...props
}: Props) {
  const { colors, typography, sizes, space } = useTheme();
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const paddingInDefaultSize = space[2];
  const paddingInNarrow = space[1] + space[2] + space[4];

  // const customPadding
  const padding = 8;
  customPadding === undefined
    ? isNarrowScreen
      ? paddingInNarrow
      : paddingInDefaultSize
    : isNarrowScreen && customPadding >= paddingInNarrow
    ? customPadding
    : paddingInNarrow;

  const chartRadius =
    containerDimensions.height / 2 - strokeWidth / 2 - padding;

  const circumference = chartRadius * 2 * Math.PI;
  const arc = circumference * (fullDeg / 360);
  const dashArray = `${arc} ${circumference}`;
  const offset25 = arc - 0.25 * arc;
  const cx = containerDimensions.width / 2;
  const cy = containerDimensions.height / 2;
  const onLayoutChart = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerDimensions({ width, height });
  };

  const getTransform = (percent: number) => {
    return `rotate(${startDeg + (percent / 100) * fullDeg}, ${cx}, ${cy})`;
  };

  const getTextTransform = (index: number) => {
    return `rotate(${
      TEXT_TO_DEFAULT_DEG_ROTATION + startDeg + (index + 0.5) * (fullDeg / 4)
    }, ${cx}, ${cy})`;
  };

  const fontSize = typography.smallBody.size;

  const getDotPosition: (ranking: number) => DotData = ranking => {
    const rankDegPerRank = fullDeg / (totalRanking - 1);
    const rankInDeg = ranking ? fullDeg - rankDegPerRank * (ranking - 1) : 0;

    const radius = chartRadius;
    // const dotSize = 26;
    const x =
      Math.max(radius, containerDimensions.width / 2) +
      (radius + strokeWidth / 2) *
        Math.cos((Math.PI / 180) * (startDeg + rankInDeg));

    const y =
      padding +
      radius +
      strokeWidth / 2 -
      (radius + strokeWidth / 2) *
        Math.sin((Math.PI / 180) * (360 - startDeg - rankInDeg));

    return {
      x,
      y,
    };
  };

  const labelContainerWidth = 110;
  const labelContainerHeight = 120;
  const marginTopFotAlignment = strokeWidth / 2;
  return (
    <View {...props}>
      <View
        onLayout={onLayoutChart}
        style={{
          height: '100%',
          width: '100%',
          marginTop: marginTopFotAlignment,
        }}>
        <Svg
          width={containerDimensions.width}
          height={containerDimensions.height}>
          {arcArr(colors).map(
            ({ opacity, percent, text, textColor, color }, idx) => (
              <Fragment key={'RankingPercentagePart_' + idx}>
                <Circle
                  fill={colors.palette.whiteTransparent}
                  stroke={color}
                  r={chartRadius}
                  cx={containerDimensions.width / 2}
                  cy={containerDimensions.height / 2}
                  strokeWidth={strokeWidth}
                  strokeDasharray={dashArray}
                  strokeDashoffset={offset25}
                  transform={getTransform(percent)}
                  strokeOpacity={opacity}
                />
                <Text
                  x={cx}
                  y={
                    containerDimensions.height > containerDimensions.width
                      ? cy - chartRadius + fontSize / 2
                      : strokeWidth * 0.7 + fontSize / 2
                  }
                  textAnchor="middle"
                  fill={textColor}
                  fontSize={fontSize}
                  fontFamily={bookFont}
                  transform={getTextTransform(idx)}>
                  {text}
                </Text>
              </Fragment>
            ),
          )}
        </Svg>
        <RankingDataDot
          type={myRanking == 1 ? 'MeAndTop' : 'Me'}
          dotPerformance={0}
          getDotPosition={() => getDotPosition(myRanking)}
        />
      </View>
      <View
        style={[
          {
            zIndex: 0,
            position: 'absolute',
          },
          {
            transform: [
              {
                translateY:
                  chartRadius - strokeWidth - labelContainerHeight / 8,
              },
              {
                translateX:
                  Math.max(
                    chartRadius + padding,
                    containerDimensions.width / 2,
                  ) -
                  110 / 2,
              },
            ],
          },
        ]}>
        <View
          style={{
            width: labelContainerWidth,
            height: labelContainerHeight,
            // borderWidth: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Row
            style={{
              justifyContent: 'center',
            }}>
            <Typography.H5
              fontWeight="bold"
              color={colors.palette.fwdOrange[100]}>
              {/* {myData &&
                  numberToThousandsFormat(
                    myData[(rankingType + 'Ranking') as RankingRankingType] ??
                      0,
                  )} */}
              {myRanking ? numberToThousandsFormat(myRanking) : ''}
            </Typography.H5>
            <Typography.Label
              fontWeight="bold"
              style={{
                textAlign: 'center',
                alignSelf: 'flex-end',
              }}>
              {` / ${numberToThousandsFormat(totalRanking ?? '')}`}
            </Typography.Label>
          </Row>
          <Typography.Body
            style={{ marginTop: sizes[4], textAlign: 'center', maxWidth: 140 }}
            color={colors.palette.fwdGreyDarker[100]}>
            {title}
          </Typography.Body>
          <Typography.SmallBody
            color={colors.palette.fwdGreyDarker}
            style={{
              marginTop: sizes[1],
              maxWidth: 100,
              textAlign: 'center',
            }}>
            {asOfDate}
          </Typography.SmallBody>
        </View>
      </View>
      <ChartEndLabels
        padding={padding}
        chartRadius={chartRadius}
        strokeWidth={strokeWidth}
        rankingType={rankingType}
        containerDimensions={containerDimensions}
        marginTopFotAlignment={marginTopFotAlignment}
      />
    </View>
  );
}

const ChartEndLabels: FC<{
  chartRadius: number;
  strokeWidth: number;
  padding: number;
  marginTopFotAlignment: number;
  rankingType: string | undefined;
  containerDimensions: {
    width: number;
    height: number;
  };
}> = ({
  chartRadius,
  strokeWidth,
  padding,
  containerDimensions,
  rankingType,
  marginTopFotAlignment,
}) => {
  const { colors } = useTheme();

  return (
    <View
      style={[
        {
          zIndex: 0,
          position: 'absolute',
          marginTop: marginTopFotAlignment,
        },
        {
          transform: [
            {
              translateY: chartRadius * 2 - strokeWidth / 2,
            },
            {
              translateX:
                Math.max(
                  chartRadius + padding,
                  containerDimensions.width + padding / 2,
                ) /
                  2 -
                chartRadius +
                strokeWidth +
                padding,
            },
          ],
        },
      ]}>
      <Row
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          width: chartRadius * 2 - strokeWidth * 2 - padding * 2,
        }}>
        <Typography.SmallBody color={colors.palette.fwdGreyDarker}>
          0 {rankingType ?? ''}
        </Typography.SmallBody>
        <Typography.SmallBody color={colors.palette.fwdGreyDarker}>
          Top {rankingType ?? ''}
        </Typography.SmallBody>
      </Row>
    </View>
  );
};
