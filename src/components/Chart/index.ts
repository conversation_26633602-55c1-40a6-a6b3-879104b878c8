import TeamRanking from './TeamRanking';
import ProgressGauge from './ProgressGauge';
import PerformanceGauge from './PerformanceGauge';
import DoughnutChart from './DoughnutChart';
import CircularProgress from './CircularProgress';
import LineChart from './LineChart';
import RankingPercentage from './RankingPercentage';
import SemiProgressGauge from './SemiProgressGauge';
import Bar<PERSON>hart from './BarChart';
import Funnel<PERSON>hart from './FunnelChart';
import FunnelChartTablet from './FunnelChart.tablet';
import LeadFeedbackLineChart from './LeadFeedbackLineChart';
import RankingDataDot from './RankingDataDot';
import SnakeProgressBar from './SnakeProgressBar';
import { TargetHorizontalChart } from './TargetHorizontalChart';

export {
  Bar<PERSON>hart,
  CircularProgress,
  Doughnut<PERSON><PERSON>,
  Funnel<PERSON>hart,
  FunnelChartTablet,
  LeadFeedbackLineChart,
  LineChart,
  PerformanceGauge,
  ProgressGauge,
  RankingDataDot,
  RankingPercentage,
  SemiProgressGauge,
  SnakeProgressBar,
  TargetHorizontalChart,
  TeamRanking,
};

export interface ChartProps {
  strokeWidth?: number;
  padding?: number;
}
