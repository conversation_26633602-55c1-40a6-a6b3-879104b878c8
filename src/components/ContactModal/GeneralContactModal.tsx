import { useTheme } from '@emotion/react';
import { Modal, Platform, TouchableOpacity, View, Share } from 'react-native';
import {
  Button,
  Column,
  H6,
  Label,
  Row,
  SvgIconProps,
  addErrorToast,
} from 'cube-ui-components';
import * as Linking from 'expo-linking';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import RecruitCallSVG from 'features/eRecruit/assets/RecruitCallSVG';
import SmsSVG from 'features/contactBook/assets/IconV2/SmsSVG';
import OthersSVG from 'features/contactBook/assets/IconV2/OthersSVG';
import { country } from 'utils/context';
import { t } from 'i18next';

interface PhoneContact {
  phoneNumber?: string | null;
  emailAddress?: string | null;
}

type ButtonConfigType = {
  Icon: (props: SvgIconProps) => JSX.Element;
  label: string;
  type: string;
  onPress: () => void;
  show: boolean;
}[];

export const contactLead = (
  method: 'call' | 'Message' | 'email',
  { phoneNumber, emailAddress }: PhoneContact,
) => {
  let phoneNumberByPlatform = '';
  let linkPrefix = '';
  switch (method) {
    case 'Message':
      linkPrefix = 'sms';
      phoneNumberByPlatform = `${linkPrefix}:${phoneNumber}`;
      break;
    case 'call':
      linkPrefix = Platform.OS === 'android' ? 'tel' : 'telprompt';
      phoneNumberByPlatform = `${linkPrefix}:${phoneNumber}`;
      break;
    case 'email':
      linkPrefix = `mailto`;
      phoneNumberByPlatform = `${linkPrefix}:${emailAddress}`;
      break;
  }
  if (!phoneNumber && !emailAddress) {
    addErrorToast([{ message: t('contact.phone.notProvide') }]);
  }

  Linking.canOpenURL(phoneNumberByPlatform)
    .then(supported => {
      if (!supported) {
        if (Platform.OS === 'android' && method === 'call')
          return Linking.openURL(phoneNumberByPlatform); //canOpenURL() fails in android
        addErrorToast([{ message: t('contact.phone.notValid') }]);
      } else {
        return Linking.openURL(phoneNumberByPlatform);
      }
    })
    .catch(err => addErrorToast([{ message: t('contact.phone.notValid') }]));
};

export default function GeneralContactModal({
  showModal,
  setShowModal,
  phoneMobile,
  emailAddress,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  phoneMobile?: string;
  emailAddress?: string;
}) {
  const { colors, space } = useTheme();
  const [showMore, setShowMore] = useState(false);

  const { t } = useTranslation();

  const onShare = async () => {
    try {
      const result = await Share.share({
        title: t('contact.title'),
        message: phoneMobile ?? t('contact.message'),
        // url: 'https://www.cube.io',
      });
      setShowModal(false);
      setShowMore(false);
    } catch (error) {
      setShowModal(false);
      setShowMore(false);
      console.log(error);
    }
  };

  const buttonConfig: ButtonConfigType = [
    {
      Icon: RecruitCallSVG,
      label: t('contact.call'),
      type: 'call',
      onPress: () => {
        contactLead('call', { phoneNumber: phoneMobile });
        setShowModal(false);
      },
      show: Boolean(phoneMobile),
    },

    {
      Icon: SmsSVG,
      label: t('contact.sms'),
      type: 'sms',
      onPress: () => {
        contactLead('Message', { phoneNumber: phoneMobile });
        setShowModal(false);
      },
      show: Boolean(phoneMobile),
    },

    {
      Icon: OthersSVG,
      label: t('contact.more'),
      type: 'more',
      onPress: () => {
        onShare();
        setShowMore(true);
      },
      show: true,
    },
  ];

  const getCountryButtonConfig = () => {
    if (country === 'ib' || country === 'my') {
      return buttonConfig.filter(config => config.type === 'call');
    }
    return buttonConfig;
  };

  const ContactIconButton = ({
    Icon,
    label,
    onPress,
  }: {
    Icon: (props: SvgIconProps) => JSX.Element;
    label: string;
    onPress: () => void;
  }) => {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: space[5],
          paddingTop: space[4],
          flex: 1,
        }}>
        <Column
          justifyContent="center"
          alignItems="center"
          flex={1}
          gap={space[2]}>
          {Icon && <Icon fill={colors.palette.white} />}
          <Label>{label}</Label>
        </Column>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={showModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => {
        setShowModal(false);
      }}>
      <MainContainer
        onPressOut={() => {
          setShowModal(false);
          setShowMore(false);
        }}>
        <ModalContainer
          style={{ opacity: showMore ? 0 : 1, justifyContent: 'center' }}>
          <Column>
            <Row>
              <H6 fontWeight="bold">{t('contact.title')}</H6>
            </Row>
            <Row>
              {getCountryButtonConfig().map(
                ({ Icon, label, onPress, show }, index) =>
                  show && (
                    <ContactIconButton
                      key={`${label}+${index}`}
                      Icon={Icon}
                      label={label}
                      onPress={onPress}
                    />
                  ),
              )}
            </Row>
            <Row justifyContent="center">
              <CloseButton
                text={t('contact.close')}
                onPress={() => {
                  setShowModal(false);
                }}
              />
            </Row>
          </Column>
        </ModalContainer>
      </MainContainer>
    </Modal>
  );
}

export const MainContainer = styled(TouchableOpacity)(
  ({ theme: { space } }) => ({
    flex: 1,
    backgroundColor: '#000000aa',
    alignItems: 'center',
    paddingVertical: space[76],
    justifyContent: 'center',
  }),
);

export const ModalContainer = styled(View)(
  ({ theme: { borderRadius, colors, space, sizes } }) => ({
    flex: 1,
    minHeight: sizes[70],
    width: sizes[95],
    backgroundColor: colors.palette.white,
    borderRadius: borderRadius.large,
    padding: space[12],
  }),
);

export const CloseButton = styled(Button)(({ theme: { space, sizes } }) => ({
  height: sizes[10],
  width: sizes[50],
  marginTop: space[6],
}));
