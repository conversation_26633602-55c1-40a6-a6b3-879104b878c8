import { useTheme } from '@emotion/react';
import { Platform, TouchableOpacity, View, StyleSheet } from 'react-native';
import {
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import {
  Button,
  Column,
  Row,
  SvgIconProps,
  addErrorToast,
  Icon,
  Typography,
} from 'cube-ui-components';
import * as Linking from 'expo-linking';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { useCallback, useMemo, useRef } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { t } from 'i18next';
import Animated from 'react-native-reanimated';

interface PhoneContact {
  phoneNumber?: string | null;
  emailAddress?: string | null;
}

type ButtonConfigType = {
  Icon: (props: SvgIconProps) => JSX.Element;
  label: string;
  type: string;
  onPress: () => void;
  show: boolean;
}[];

export const contactLeadPhone = (
  method: 'call' | 'whatsapp' | 'email',
  { phoneNumber, emailAddress }: PhoneContact,
) => {
  let phoneNumberByPlatform = '';
  let linkPrefix = '';
  const formattedPhoneNumber = phoneNumber?.replace(/\+/g, '');

  switch (method) {
    case 'call':
      linkPrefix = Platform.OS === 'android' ? 'tel' : 'telprompt';
      phoneNumberByPlatform = `${linkPrefix}:${phoneNumber}`;
      break;
    case 'whatsapp':
      linkPrefix = 'http://api.whatsapp.com/send?phone=';
      phoneNumberByPlatform = `${linkPrefix}${formattedPhoneNumber}`;
      break;
    case 'email':
      linkPrefix = `mailto`;
      phoneNumberByPlatform = `${linkPrefix}:${emailAddress}`;
      break;
  }
  console.log({ phoneNumberByPlatform });

  if (!phoneNumber && !emailAddress) {
    addErrorToast([{ message: t('contact.phone.notProvide') }]);
    return;
  }

  Linking.canOpenURL(phoneNumberByPlatform)
    .then(supported => {
      if (!supported) {
        if (Platform.OS === 'android' && method === 'call')
          return Linking.openURL(phoneNumberByPlatform); //canOpenURL() fails in android
        addErrorToast([{ message: t('contact.phone.notValid') }]);
      } else {
        return Linking.openURL(phoneNumberByPlatform);
      }
    })
    .catch(err => addErrorToast([{ message: t('contact.phone.notValid') }]));
};

export function GeneralContactModalPhone({
  showModal,
  onClose,
  phoneMobile,
  emailAddress,
}: {
  showModal: boolean;
  onClose: () => void;
  phoneMobile?: string;
  emailAddress?: string;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { t } = useTranslation();

  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => {
      return (
        <Animated.View
          style={[
            StyleSheet.absoluteFillObject,
            { backgroundColor: `${colors.palette.black}80` },
          ]}
          onTouchEnd={onClose}
        />
      );
    },
    [onClose, colors.palette.black],
  );

  const buttonConfig: ButtonConfigType = [
    {
      Icon: Icon.Call,
      label: t('contact.call'),
      type: 'call',
      onPress: () => {
        contactLeadPhone('call', { phoneNumber: phoneMobile });
        onClose();
      },
      show: Boolean(phoneMobile),
    },
    {
      Icon: Icon.Whatsapp,
      label: 'WhatsApp', // Note: Add to translations if needed
      type: 'whatsapp',
      onPress: () => {
        contactLeadPhone('whatsapp', { phoneNumber: phoneMobile });
        onClose();
      },
      show: Boolean(phoneMobile),
    },
    {
      Icon: Icon.Email,
      label: t('contact.email'),
      type: 'email',
      onPress: () => {
        contactLeadPhone('email', { emailAddress });
        onClose();
      },
      show: Boolean(emailAddress),
    },
  ];

  const ContactIconButton = ({
    Icon,
    label,
    onPress,
  }: {
    Icon: (props: SvgIconProps) => JSX.Element;
    label: string;
    onPress: () => void;
  }) => {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          flex: 1,
        }}>
        <Column
          justifyContent="center"
          alignItems="center"
          flex={1}
          gap={space[3]}>
          <IconContainer>
            {Icon && <Icon fill={colors.palette.white} size={space[6]} />}
          </IconContainer>
          <Typography.H8 fontWeight="normal">{label}</Typography.H8>
        </Column>
      </TouchableOpacity>
    );
  };

  // Present the bottom sheet when visible changes to true
  useMemo(() => {
    if (showModal) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.close();
    }
  }, [showModal]);

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetRef}
          snapPoints={animatedSnapPoints}
          handleHeight={animatedHandleHeight}
          contentHeight={animatedContentHeight}
          backdropComponent={renderBackdrop}
          onDismiss={onClose}
          enablePanDownToClose
          backgroundStyle={{
            borderRadius: borderRadius.large,
          }}
          handleIndicatorStyle={{
            backgroundColor: colors.palette.fwdGrey[100],
            width: 40,
            height: 5,
          }}
          handleStyle={{
            paddingTop: space[2],
            paddingBottom: space[4],
          }}>
          <BottomSheetScrollView
            onLayout={handleContentLayout}
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: bottom + space[4],
            }}>
            <Column gap={space[4]}>
              <Row>
                <Typography.H6 fontWeight="bold">
                  {t('application')}
                </Typography.H6>
              </Row>

              <Row style={{ marginTop: space[2] }}>
                {buttonConfig.map(
                  ({ Icon, label, onPress, show }, index) =>
                    show && (
                      <ContactIconButton
                        key={`${label}+${index}`}
                        Icon={Icon}
                        label={label}
                        onPress={onPress}
                      />
                    ),
                )}
              </Row>
            </Column>
          </BottomSheetScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}

const IconContainer = styled(View)(({ theme: { colors, space } }) => ({
  width: space[14],
  height: space[14],
  borderRadius: space[7],
  backgroundColor: colors.primary,
  justifyContent: 'center',
  alignItems: 'center',
}));

const CloseButton = styled(Button)(({ theme: { space, sizes } }) => ({
  height: sizes[10],
  width: sizes[50],
  marginTop: space[2],
}));
