import React, { useEffect } from 'react';
import { Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import {
  Modal,
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';

export default function SlidingSideModal({
  children,
  title,
  visible,
  onClose,
  hasHeaderBottomBar = true,
}: {
  children?: React.ReactNode;
  title: string;
  visible: boolean;
  onClose: () => void;
  hasHeaderBottomBar?: boolean;
}) {
  const { colors } = useTheme();
  const { top } = useSafeAreaInsets();

  const slideAnimation = React.useMemo(() => new Animated.Value(0), []);

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        delay: 100,
      }).start();
    } else {
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        delay: 200,
      }).start();
    }
  }, [visible, slideAnimation]);

  const closingHandler = () => {
    Animated.timing(slideAnimation, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(({ finished }) => {
      onClose();
    });
  };

  const modalTranslateX = slideAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1000, 0],
  });
  const deviceWidth = Dimensions.get('window').width;
  return (
    <Modal
      transparent
      statusBarTranslucent
      visible={visible}
      animationType="fade"
      onDismiss={() => console.log('onDismiss')}>
      <View
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
        }}>
        <Animated.View
          style={{
            transform: [{ translateX: modalTranslateX }],
            position: 'absolute',
            right: 0,
            zIndex: 100,
            height: '100%',
            width: deviceWidth * (400 / 1112) > 400 ? 400 : '40%',
          }}>
          <View
            style={{
              backgroundColor: colors.background,
              height: '100%',
              paddingTop: top > sizes[4] ? top : sizes[4],
            }}>
            <Row
              style={{
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingBottom: sizes[4],
                paddingHorizontal: sizes[4],
                borderBottomColor: colors.palette.fwdGrey[100],
                borderBottomWidth: hasHeaderBottomBar ? 1 : 0,
              }}>
              <Typography.H6 fontWeight="bold">{title ?? '--'}</Typography.H6>
              <TouchableOpacity onPress={closingHandler}>
                <Icon.Close fill={colors.secondary} />
              </TouchableOpacity>
            </Row>
            {children}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}
