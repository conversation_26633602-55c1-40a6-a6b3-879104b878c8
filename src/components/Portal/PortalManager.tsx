import * as React from 'react';
import { View, StyleSheet } from 'react-native';

export interface PortalManagerRef {
	mount: (key: number, children: React.ReactNode) => void;
	update: (key: number, children: React.ReactNode) => void;
	unmount: (key: number) => void;
}

/**
 * Portal host is the component which actually renders all Portals.
 */
const PortalManager = React.memo(
	React.forwardRef((props, ref: React.ForwardedRef<PortalManagerRef>) => {
		const [portals, setPortals] = React.useState<
			{ key: number; children: React.ReactNode }[]
		>([]);

		const mount = React.useCallback(
			(key: number, children: React.ReactNode) => {
				setPortals(oldPortals => [...oldPortals, { key, children }]);
			},
			[],
		);

		const update = React.useCallback(
			(key: number, children: React.ReactNode) => {
				setPortals(oldPortals =>
					oldPortals.map(item => {
						if (item.key === key) {
							return { ...item, children };
						}
						return item;
					}),
				);
			},
			[],
		);

		const unmount = React.useCallback((key: number) => {
			setPortals(oldPortals => oldPortals.filter(item => item.key !== key));
		}, []);

		React.useImperativeHandle(ref, () => ({
			mount,
			update,
			unmount,
		}));

		return (
			<>
				{portals.map(({ key, children }) => (
					<View
						key={key}
						collapsable={
							false /* Need collapsable=false here to clip the elevations, otherwise they appear above sibling components */
						}
						pointerEvents="box-none"
						style={StyleSheet.absoluteFill}
					>
						{children}
					</View>
				))}
			</>
		);
	}),
);

export default PortalManager;
