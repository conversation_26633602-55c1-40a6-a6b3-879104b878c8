import * as React from 'react';

import type { PortalMethods } from './PortalHost';

type Props = {
	manager: PortalMethods;
	children: React.ReactNode;
};

export default function PortalConsumer(props: Props) {
	const key = React.useRef(-1);

	React.useEffect(() => {
		checkManager(props.manager);
		key.current = props.manager.mount(props.children);
		return () => {
			props.manager.unmount(key.current);
		};
	}, [props.manager]);

	React.useEffect(() => {
		checkManager(props.manager);
		props.manager.update(key.current, props.children);
	});

	return null;
}

const checkManager = (manager: PortalMethods) => {
	if (!manager) {
		throw new Error(
			'Looks like you forgot to wrap your root component with `Provider` component from `react-native-paper`.\n\n' +
				"Please read our getting-started guide and make sure you've followed all the required steps.\n\n" +
				'https://callstack.github.io/react-native-paper/docs/guides/getting-started',
		);
	}
};
