import * as React from 'react';
import styled from '@emotion/native';

import PortalManager, { PortalManagerRef } from './PortalManager';

export type Props = {
	children: React.ReactNode;
};

type Operation =
	| { type: 'mount'; key: number; children: React.ReactNode }
	| { type: 'update'; key: number; children: React.ReactNode }
	| { type: 'unmount'; key: number };

export type PortalMethods = {
	mount: (children: React.ReactNode) => number;
	update: (key: number, children: React.ReactNode) => void;
	unmount: (key: number) => void;
};

export const PortalContext = React.createContext<PortalMethods>(null as any);

/**
 * Portal host renders all of its children `Portal` elements.
 * For example, you can wrap a screen in `Portal.Host` to render items above the screen.
 * If you're using the `Provider` component, it already includes `Portal.Host`.
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { Text } from 'react-native';
 * import { Portal } from 'react-native-paper';
 *
 * const MyComponent = () => (
 *   <Portal.Host>
 *     <Text>Content of the app</Text>
 *   </Portal.Host>
 * );
 *
 * export default MyComponent;
 * ```
 *
 * Here any `Portal` elements under `<App />` are rendered alongside `<App />` and will appear above `<App />` like a `Modal`.
 */
export default function PortalHost(props: Props) {
	const nextKey = React.useRef(0);
	const queue = React.useRef<Operation[]>([]);
	const manager = React.useRef<PortalManagerRef | null>(null);

	React.useEffect(() => {
		while (queue.current.length && manager.current) {
			const action = queue.current.pop();
			if (action) {
				switch (action.type) {
					case 'mount':
						manager.current.mount(action.key, action.children);
						break;
					case 'update':
						manager.current.update(action.key, action.children);
						break;
					case 'unmount':
						manager.current.unmount(action.key);
						break;
					default:
						break;
				}
			}
		}
	}, []);

	const mount = React.useCallback((children: React.ReactNode) => {
		nextKey.current += 1;
		const key = nextKey.current;

		if (manager.current) {
			manager.current.mount(key, children);
		} else {
			queue.current.push({ type: 'mount', key, children });
		}

		return key;
	}, []);

	const update = React.useCallback((key: number, children: React.ReactNode) => {
		if (manager.current) {
			manager.current.update(key, children);
		} else {
			const op: Operation = { type: 'mount', key, children };
			const index = queue.current.findIndex(
				o => o.type === 'mount' || (o.type === 'update' && o.key === key),
			);

			if (index > -1) {
				queue.current[index] = op;
			} else {
				queue.current.push(op as Operation);
			}
		}
	}, []);

	const unmount = React.useCallback((key: number) => {
		if (manager.current) {
			manager.current.unmount(key);
		} else {
			queue.current.push({ type: 'unmount', key });
		}
	}, []);

	const value = React.useMemo(
		() => ({
			mount,
			update,
			unmount,
		}),
		[mount, update, unmount],
	);

	return (
		<PortalContext.Provider value={value}>
			{/* Need collapsable=false here to clip the elevations, otherwise they appear above Portal components */}
			<Container collapsable={false} pointerEvents="box-none">
				{props.children}
			</Container>
			<PortalManager ref={manager} />
		</PortalContext.Provider>
	);
}

const Container = styled.View(() => ({
	flex: 1,
}));
