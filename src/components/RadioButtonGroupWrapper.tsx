import React, { useRef } from 'react';
import { View } from 'react-native';
import { RadioButtonGroup } from 'cube-ui-components';
import type { ComponentProps } from 'react';

// RadioButtonGroup wrapper component that forwards refs
// We need that because the RadioButtonGroup does not support scrolling for useIncompleteFields
const RadioButtonGroupWrapper = React.forwardRef<
  any,
  ComponentProps<typeof RadioButtonGroup>
>((props, ref) => {
  const innerRef = useRef<any>(null);
  const wrapperRef = useRef<View>(null);

  React.useImperativeHandle(
    ref,
    () => {
      // Return the wrapper View but with custom properties that point to the inner component
      const wrapper = wrapperRef.current;
      const inner = innerRef.current;

      if (wrapper && inner) {
        // Create a hybrid object that combines wrapper properties with inner functionality
        const hybridRef = Object.assign(wrapper, {
          // Override elm to point to the inner RadioButtonGroup for measureLayout
          elm: inner,
          // Forward focus and other methods to inner component
          focus: inner.focus?.bind(inner),
          select: inner.select?.bind(inner),
          setCustomValidity: inner.setCustomValidity?.bind(inner),
          reportValidity: inner.reportValidity?.bind(inner),
          // Keep measureLayout from wrapper for positioning
          measureLayout: wrapper.measureLayout?.bind(wrapper),
        });

        return hybridRef;
      }

      // Fallback: return wrapper if inner not available
      return wrapper;
    },
    [],
  );

  return (
    <View ref={wrapperRef}>
      <RadioButtonGroup {...props} ref={innerRef} />
    </View>
  );
});

RadioButtonGroupWrapper.displayName = 'RadioButtonGroupWrapper';

export default RadioButtonGroupWrapper;
