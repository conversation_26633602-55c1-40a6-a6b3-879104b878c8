import { View, Text, Modal } from 'react-native';
import React from 'react';
import { Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

type Props = {
  children?: React.ReactNode;
  isCentered?: boolean;
  isDefaultBackground?: boolean;
} & React.ComponentProps<typeof Modal>;

export default function BasicModal({
  children,
  isDefaultBackground = true,
  isCentered = true,
  ...rest
}: Props) {
  const { space, colors, borderRadius, elevation } = useTheme();

  return (
    <Modal transparent statusBarTranslucent animationType="fade" {...rest}>
      <Box
        flex={1}
        backgroundColor={isDefaultBackground ? 'rgba(0, 0, 0, 0.5)' : undefined}
        alignItems={isCentered ? 'center' : undefined}
        justifyContent={isCentered ? 'center' : undefined}>
        {children}
      </Box>
    </Modal>
  );
}
