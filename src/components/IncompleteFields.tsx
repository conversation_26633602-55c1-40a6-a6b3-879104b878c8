import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Box, Icon, Row, Typography } from 'cube-ui-components';
import useDeviceCheck from 'hooks/useDeviceCheck';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, ViewStyle } from 'react-native';

type IncompleteFieldsProps = {
  incompleteCount?: number;
  focusNext?: () => void;
  style?: ViewStyle;
  message?: string;
  colorIcon?: string;
};

const IncompleteFields = ({
  incompleteCount,
  focusNext,
  style,
  colorIcon,
  message,
}: IncompleteFieldsProps) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['common']);

  const { isTabletMode } = useDeviceCheck();

  if (!incompleteCount) return null;

  return (
    <Row alignItems="center" style={style}>
      <Icon.Warning size={space[6]} fill={colorIcon ?? colors.error} />
      <Box width={sizes[2]} />
      <Typography.LargeBody>
        {message ??
          t('common:incompleteFields', {
            count: incompleteCount,
          })}
      </Typography.LargeBody>
      <Box {...(isTabletMode ? { width: sizes[4] } : { flex: 1 })} />
      <TouchableOpacity hitSlop={ICON_HIT_SLOP} onPress={focusNext}>
        <Row alignItems="center" gap={2}>
          <Typography.Label
            fontWeight="medium"
            color={colors.palette.fwdAlternativeOrange[100]}>
            {t('common:goToTheField')}
          </Typography.Label>
          <Icon.ChevronDown
            size={space[4]}
            fill={colors.palette.fwdAlternativeOrange[100]}
          />
        </Row>
      </TouchableOpacity>
    </Row>
  );
};

export default IncompleteFields;
