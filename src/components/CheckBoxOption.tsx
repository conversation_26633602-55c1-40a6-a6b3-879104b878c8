import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { TextFieldRef, Typography } from 'cube-ui-components';
import React, { forwardRef } from 'react';
import { Pressable, View, ViewProps, ViewStyle } from 'react-native';

export interface CheckBoxOptionProps<T, V> extends ViewProps {
  label?: string;
  modalTitle?: string;
  actionLabel?: string;
  disabled?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  value?: V;
  onChange: (value: V) => void;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  itemStyle?: ViewStyle;
  highlight?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
}

export type CheckBoxOptionRef = TextFieldRef;

const ChoiceContent = styled(View)<{ isSelected: boolean }>(
  ({ theme: { colors, borderRadius, space }, isSelected }) => ({
    paddingVertical: space[1],
    paddingHorizontal: space[2],
    borderWidth: 1,
    borderColor: isSelected
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
    backgroundColor: colors.palette.fwdOrange[5],
    borderRadius: borderRadius['large'],
  }),
);

const Container = styled(View)(({ theme: { space } }) => ({
  flexDirection: 'row',
  gap: space[2],
  flexWrap: 'wrap',
}));


function CheckBoxOptionInner<T, V>(
  {
    value,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    ...viewProps
  }: CheckBoxOptionProps<T, V>,
  ref: React.ForwardedRef<CheckBoxOptionRef>,
) {
  const { colors } = useTheme();
  return (
    <Container ref={ref} {...viewProps}>
      {data.map((option, index) => {
        const isSelected = getItemValue(option) === value;

        return (
          <Pressable
            key={`${getItemLabel(option)}-${index}`}
            onPress={() => {
              onChange(getItemValue(option))
            }}>
            {({ pressed }) => (
              <ChoiceContent isSelected={pressed || isSelected}>
                <Typography.LargeBody
                  style={{
                    color:
                      pressed || isSelected
                        ? colors.palette.fwdOrange[100]
                        : colors.palette.fwdDarkGreen[100],
                  }}
                  fontWeight={pressed || isSelected ? 'bold' : 'normal'}>
                  {getItemLabel(option)}
                </Typography.LargeBody>
              </ChoiceContent>
            )}
          </Pressable>
        );
      })}
    </Container>
  );
}

const CheckBoxOption = forwardRef(CheckBoxOptionInner) as <T, V>(
  props: CheckBoxOptionProps<T, V> & {
    ref?: React.ForwardedRef<CheckBoxOptionRef>;
  },
) => ReturnType<typeof CheckBoxOptionInner>;
export default CheckBoxOption;
