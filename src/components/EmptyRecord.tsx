import React from 'react';
import { Typography, Box, PictogramIcon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

import EmptyCaseSVG from 'features/lead/assets/EmptyCaseSVG';
import { ViewStyle } from 'react-native';
import { country } from 'utils/context';
import EmptyCaseIBSVG from 'features/lead/assets/EmptyCaseIBSVG';

export default function EmptyRecord({
  width = 100,
  height = 84,
  containerStyle,
}: {
  width?: number;
  height?: number;
  containerStyle?: ViewStyle;
}) {
  const { t } = useTranslation('savedProposals');
  const { sizes, space, colors, borderRadius } = useTheme();

  return (
    <Box
      style={[
        {
          backgroundColor: colors.background,
          minHeight: space[39],
          width: '100%',
          padding: space[6],
          borderBottomLeftRadius: space[4],
          borderBottomEndRadius: space[4],
          alignItems: 'center',
          justifyContent: 'center',
        },
        containerStyle,
      ]}>
      {country === 'ib' ? (
        <EmptyCaseIBSVG />
      ) : (
        <EmptyCaseSVG width={width} height={height} />
      )}
      <Typography.Body color={colors.palette.fwdGreyDarker}>
        {t('filter.emptyRecord')}
      </Typography.Body>
    </Box>
  );
}
