import React, { useCallback, useMemo, useRef } from 'react';
import { useTheme } from '@emotion/react';
import { BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import { Icon, Row, Typography } from 'cube-ui-components';
import { TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import CheckmarkedBottomSheetListItem from './CheckmarkedBottomSheetListItem';

export type CheckmarkedBottomSheetListOption<T = string> = {
  value: T;
  label: string;
  icon?: React.JSX.Element;
};

type CheckmarkedBottomSheetListProps<T = string> = {
  selectedValue: T;
  onSelect?: (value: T) => void;
  onPress?: () => void;
  title?: string;
  options: CheckmarkedBottomSheetListOption<T & React.Key>[];
  snapPoints?: Array<string | number>;
  renderSelectedOption?: (value: T) => React.ReactNode;
};

export default function CheckmarkedBottomSheetList<T = string>({
  selectedValue,
  onSelect,
  onPress,
  title,
  options,
  snapPoints = ['60%'],
  renderSelectedOption,
}: CheckmarkedBottomSheetListProps<T>) {
  const ref = useRef<BottomSheetModal>(null);
  const { colors, space, sizes } = useTheme();

  const handleOpenSelector = useCallback(() => {
    ref.current?.present();
    onPress?.();
  }, []);

  const handleDismissModal = useCallback(() => {
    ref.current?.dismiss();
  }, [ref]);

  const handlePress = useCallback(
    (value: T) => {
      onSelect?.(value);
      handleDismissModal();
    },
    [onSelect, handleDismissModal],
  );

  const renderBackdrop = useCallback(
    (backdropProps: any) => (
      <BottomSheetBackdrop
        {...backdropProps}
        onPress={handleDismissModal}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        pressBehavior="close"
      />
    ),
    [handleDismissModal],
  );

  const selectedOption = useMemo(
    () => options.find(option => option.value === selectedValue),
    [options, selectedValue],
  );

  return (
    <>
      <TouchableOpacity onPress={handleOpenSelector}>
        {selectedOption ? (
          renderSelectedOption ? (
            renderSelectedOption(selectedOption.value)
          ) : (
            <Row alignItems="center">
              <Typography.LargeBody>
                {selectedOption.label}
              </Typography.LargeBody>
              <Icon.ChevronRight
                fill={colors.palette.fwdGreyDarkest}
                size={space[4]}
              />
            </Row>
          )
        ) : null}
      </TouchableOpacity>

      <BottomSheetModal
        ref={ref}
        handleIndicatorStyle={{
          width: sizes[10],
          backgroundColor: colors.palette.fwdGrey[100],
        }}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}>
        <Row
          style={{
            position: 'relative',
            padding: space[3],
          }}
          justifyContent="center"
          alignItems="center">
          <TouchableOpacity
            onPress={handleDismissModal}
            style={{
              position: 'absolute',
              left: 0,
              paddingHorizontal: space[4],
            }}>
            <Icon.Close
              size={space[6]}
              fill={colors.palette.fwdDarkGreen[100]}
            />
          </TouchableOpacity>
          <Typography.H6
            fontWeight="bold"
            color={colors.palette.fwdDarkGreen[100]}>
            {title || ''}
          </Typography.H6>
        </Row>
        {options.map((option, index) => (
          <React.Fragment key={option.value}>
            {index > 0 && <Divider />}
            <CheckmarkedBottomSheetListItem
              value={option.value}
              label={option.label}
              icon={option.icon}
              isSelected={option.value === selectedValue}
              onPress={handlePress}
            />
          </React.Fragment>
        ))}
      </BottomSheetModal>
    </>
  );
}

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  display: 'flex',
  marginHorizontal: theme.space[4],
  marginVertical: theme.space[3],
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));
