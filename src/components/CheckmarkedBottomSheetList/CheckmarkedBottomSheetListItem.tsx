import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon, Row, Typography } from 'cube-ui-components';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';

export default function CheckmarkedBottomSheetListItem<T = string>({
  value,
  label,
  icon,
  isSelected,
  onPress,
}: {
  isSelected: boolean;
  value: T;
  label: string;
  icon?: React.JSX.Element;
  onPress: (value: T) => void;
}) {
  const { colors, space } = useTheme();

  return (
    <TouchableContainer onPress={() => onPress(value)}>
      <Row flex={1} alignItems="center" gap={space[4]}>
        {icon && icon}
        <Typography.LargeBody color={colors.palette.fwdDarkGreen[100]}>
          {label}
        </Typography.LargeBody>
      </Row>
      <View>{isSelected && <Icon.Tick />}</View>
    </TouchableContainer>
  );
}

const TouchableContainer = styled(TouchableOpacity)(({ theme }) => ({
  display: 'flex',
  height: theme.space[11],
  alignItems: 'center',
  gap: theme.space[2],
  flexShrink: 0,
  alignSelf: 'stretch',
  flexDirection: 'row',
  paddingHorizontal: theme.space[4],
}));
