import { useTheme } from '@emotion/react';
import { Column, Typography } from 'cube-ui-components';

const RoundBorderTag = ({ text, color }: { text: string; color?: string }) => {
  const { colors } = useTheme();
  return (
    <Column
      backgroundColor={color}
      paddingX={10}
      paddingY={2}
      borderRadius={10}>
      <Typography.ExtraSmallLabel color={colors.secondary}>
        {text}
      </Typography.ExtraSmallLabel>
    </Column>
  );
};

export default RoundBorderTag;
