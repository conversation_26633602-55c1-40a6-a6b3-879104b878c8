import { useTheme } from '@emotion/react';
import {
  Header as <PERSON><PERSON><PERSON>er,
  HeaderOptions,
  Layout,
} from '@react-navigation/elements';
import { Icon, Typography } from 'cube-ui-components';
import { TouchableOpacity } from 'react-native';

export interface HeaderProps extends HeaderOptions {
  variant?: 'primary' | 'white';
  onPressCloseButton?: () => void;
  onPressBackButton?: () => void;

  // filled up the rest of the props from @react-navigation/elements/src/Header/Header
  modal?: boolean;
  layout?: Layout;
  title: string;
}

export default function Header({
  variant,
  onPressCloseButton,
  onPressBackButton,
  title,
  ...props
}: HeaderProps) {
  const { colors, space } = useTheme();

  const backgroundColor =
    !variant || variant === 'primary' ? colors.primary : colors.background;
  const elementColor =
    !variant || variant === 'primary' ? colors.onPrimary : colors.onBackground;
  const borderColor =
    !variant || variant === 'primary'
      ? colors.primary
      : colors.palette.fwdGreyDark;

  let headerLeft = props.headerLeft;
  if (!headerLeft && onPressCloseButton) {
    headerLeft = () => (
      <TouchableOpacity onPress={onPressCloseButton}>
        <Icon.Close size={space[5]} fill={elementColor} />
      </TouchableOpacity>
    );
  }

  if (!headerLeft && onPressBackButton) {
    headerLeft = () => (
      <TouchableOpacity onPress={onPressBackButton}>
        <Icon.ChevronLeft size={space[5]} fill={elementColor} />
      </TouchableOpacity>
    );
  }

  return (
    <NavigationHeader
      headerTitle={({ children }) => (
        <Typography.ExtraLargeBody
          fontWeight="bold"
          color={colors.onBackground}>
          {children}
        </Typography.ExtraLargeBody>
      )}
      headerTitleStyle={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      title={title}
      headerLeft={headerLeft}
      headerLeftContainerStyle={{
        paddingLeft: space[3],
        justifyContent: 'center',
      }}
      headerRightContainerStyle={{
        paddingRight: space[3],
        justifyContent: 'center',
      }}
      headerStyle={{
        backgroundColor,
      }}
      {...props}
    />
  );
}
