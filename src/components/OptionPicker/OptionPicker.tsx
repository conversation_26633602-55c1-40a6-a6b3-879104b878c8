import { useTheme } from '@emotion/react';
import React, { forwardRef, useCallback, useState } from 'react';
import { PickerProps, Picker } from 'cube-ui-components';

export type OptionPickerProps = PickerProps;

const OptionPicker = forwardRef(
  ({ ...props }: OptionPickerProps): JSX.Element => {
    const { space } = useTheme();

    return (
      <Picker
        type="chip"
        size="large"
        containerStyle={{
          flexWrap: 'wrap',
          rowGap: space[3],
        }}
        {...props}
      />
    );
  },
);

export default OptionPicker;
