import { useTheme } from '@emotion/react';
import React, { useMemo } from 'react';
import { StyleSheet, TextStyle, View } from 'react-native';
import Markdown, {
  RenderRules,
  hasParents,
  renderRules,
} from '@ronradtke/react-native-markdown-display';
import { Text } from 'react-native';

const MarkdownText = ({
  children,
  rules,
  style,
}: {
  children: string;
  rules?: RenderRules;
  style?: StyleSheet.NamedStyles<unknown>;
}): JSX.Element => {
  const mdStyle = useMarkdownStyle();
  return (
    <Markdown
      style={style ? { ...mdStyle, ...style } : mdStyle}
      rules={rules ?? customRenderRules}
      children={children}
    />
  );
};

export default MarkdownText;

export const useMarkdownStyle = () => {
  const theme = useTheme();
  return useMemo(
    () =>
      ({
        body: {
          fontFamily: 'FWDCircularTT-Book',
          fontSize: theme.typography.body.size,
          lineHeight: theme.typography.body.lineHeight,
          color: theme.colors.secondary,
        },
        heading1: {
          flexDirection: 'row',
          fontSize: theme.typography.h1.size,
          lineHeight: theme.typography.h1.lineHeight,
        },
        heading2: {
          flexDirection: 'row',
          fontSize: theme.typography.h2.size,
          lineHeight: theme.typography.h2.lineHeight,
        },
        heading3: {
          flexDirection: 'row',
          fontSize: theme.typography.h3.size,
          lineHeight: theme.typography.h3.lineHeight,
        },
        heading4: {
          flexDirection: 'row',
          fontSize: theme.typography.h4.size,
          lineHeight: theme.typography.h4.lineHeight,
        },
        heading5: {
          flexDirection: 'row',
          fontSize: theme.typography.h5.size,
          lineHeight: theme.typography.h5.lineHeight,
        },
        heading6: {
          flexDirection: 'row',
          fontSize: theme.typography.h6.size,
          lineHeight: theme.typography.h6.lineHeight,
        },
        strong: {
          fontFamily: 'FWDCircularTT-Bold',
        },
        link: {
          color: theme.colors.primary,
          textDecorationLine: 'underline',
        },
        bullet_list_icon: {
          marginLeft: theme.space[2],
          marginRight: theme.space[2],
        },
      } as const),
    [
      theme.colors.primary,
      theme.colors.secondary,
      theme.space,
      theme.typography.body.lineHeight,
      theme.typography.body.size,
      theme.typography.h1.lineHeight,
      theme.typography.h1.size,
      theme.typography.h2.lineHeight,
      theme.typography.h2.size,
      theme.typography.h3.lineHeight,
      theme.typography.h3.size,
      theme.typography.h4.lineHeight,
      theme.typography.h4.size,
      theme.typography.h5.lineHeight,
      theme.typography.h5.size,
      theme.typography.h6.lineHeight,
      theme.typography.h6.size,
    ],
  );
};

const customRenderRules: RenderRules = {
  list_item: (node, children, parent, styles, inheritedStyles = {}) => {
    const refStyle = {
      ...inheritedStyles,
      ...StyleSheet.flatten(styles.list_item),
    };
    const arr = Object.keys(refStyle);
    const modifiedInheritedStylesObj: TextStyle = {};
    for (let b = 0; b < arr.length; b++) {
      if (textStyleProps.includes(arr[b])) {
        modifiedInheritedStylesObj[arr[b] as keyof TextStyle] =
          refStyle[arr[b]];
      }
    }
    if (hasParents(parent, 'bullet_list')) {
      return (
        <View key={node.key} style={[styles._VIEW_SAFE_list_item, { flexShrink: 1 }]}>
          <Text
            style={[modifiedInheritedStylesObj, styles.bullet_list_icon]}
            accessible={false}>
            {'\u2022'}
          </Text>
          <View style={[styles._VIEW_SAFE_bullet_list_content, { flexShrink: 1 }]}>
            {children}
          </View>
        </View>
      );
    }

    if (hasParents(parent, 'ordered_list')) {
      const orderedListIndex = parent.findIndex(
        el => el.type === 'ordered_list',
      );

      const orderedList = parent[orderedListIndex];
      let listItemNumber;

      if (orderedList.attributes && orderedList.attributes.start) {
        listItemNumber = orderedList.attributes.start + node.index;
      } else {
        listItemNumber = node.index + 1;
      }

      const isLastItem = orderedList.children.length - 1 === node.index;
      const listLevel = parent
        .map(node => node.type)
        .filter(type => type === 'ordered_list').length;
      const isSubListItem = listLevel > 1;
      switch (listLevel) {
        case 2:
          listItemNumber = String.fromCharCode(node.index + 97);
          break;
        case 3:
          listItemNumber = romanize(node.index + 1);
      }

      return (
        <View
          key={node.key}
          style={[styles._VIEW_SAFE_list_item, { flexShrink: 1 }]}>
          <Text
            style={[
              modifiedInheritedStylesObj,
              isSubListItem
                ? styles.ordered_subList_icon || styles.ordered_list_icon
                : styles.ordered_list_icon,
              isLastItem && { marginBottom: 0 },
            ]}>
            {listItemNumber}
            {node.markup}
          </Text>
          <View
            style={[
              isSubListItem
                ? styles._VIEW_SAFE_ordered_subList_content ||
                  styles._VIEW_SAFE_ordered_list_content
                : styles._VIEW_SAFE_ordered_list_content,
              isLastItem && { marginBottom: 0 },
              { flexShrink: 1 },
            ]}>
            {children}
          </View>
        </View>
      );
    }

    return (
      renderRules as unknown as (text: React.ComponentType) => RenderRules
    )(Text)['list_item']?.(node, children, parent, styles, inheritedStyles);
  },
};

const textStyleProps = [
  'textShadowOffset',
  'color',
  'fontSize',
  'fontStyle',
  'fontWeight',
  'lineHeight',
  'textAlign',
  'textDecorationLine',
  'textShadowColor',
  'fontFamily',
  'textShadowRadius',
  'includeFontPadding',
  'textAlignVertical',
  'fontVariant',
  'letterSpacing',
  'textDecorationColor',
  'textDecorationStyle',
  'textTransform',
  'writingDirection',
];

const romanize = (num: number) => {
  if (isNaN(num)) return NaN;
  const digits = String(+num).split('');
  const key = [
    '',
    'c',
    'cc',
    'ccc',
    'cd',
    'd',
    'dc',
    'dcc',
    'dccc',
    'cm',
    '',
    'x',
    'xx',
    'xxx',
    'xl',
    'l',
    'lx',
    'lxx',
    'lxxx',
    'xc',
    '',
    'i',
    'ii',
    'iii',
    'iv',
    'v',
    'vi',
    'vii',
    'viii',
    'ix',
  ];
  let roman = '';
  let i = 3;
  while (i--) {
    roman = (key[+(digits?.pop() ?? 0) + i * 10] || '') + roman;
  }
  return Array(+digits.join('') + 1).join('m') + roman;
};
