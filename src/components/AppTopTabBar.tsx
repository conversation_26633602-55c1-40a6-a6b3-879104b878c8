import {
  ScrollView,
  StyleProp,
  TouchableOpacity,
  ViewProps,
  ViewStyle,
  TextStyle,
  useWindowDimensions,
} from 'react-native';
import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { MaterialTopTabBarProps } from '@react-navigation/material-top-tabs';
import Animated, {
  AnimateStyle,
  FadeIn,
  FadeOut,
  SharedValue,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import styled from '@emotion/native';
import { Row, Typography } from 'cube-ui-components';
import ResponsiveView from './ResponsiveView';
import { useTheme } from '@emotion/react';
import ResponsiveText from './ResponsiveTypography';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

interface AppTopTabBarProps extends MaterialTopTabBarProps, ViewProps {
  animatedProps?: AppTopTabBarAnimatedProps;
  barFooter?: React.ReactNode;
  variant?: AppTopTabBarVariant;
  isHideBottomBorder?: boolean;
  onPressCallBack?: (routeName: string) => void;
  tabStyle?: StyleProp<AnimateStyle<StyleProp<ViewStyle>>>;
  isCustomizedFocusedTabLabel?: boolean;
  isCustomizedNormalTabLabel?: boolean;
  activeTabIndicatorStyle?: ViewStyle;
  activeTabLabelStyle?: TextStyle;
}

const animatedLineHeightInterval = 1;

export interface AppTopTabBarAnimatedProps {
  show: SharedValue<boolean>;
  lastOffsetY: SharedValue<number>;
  atTop: SharedValue<boolean>;
}

export type AppTopTabBarComponentRef = {
  onChangeTab: (routeName: string) => void;
};

type AppTopTabBarVariant = 'square' | 'round' | 'scrollable';

const AppTopTabBar = forwardRef(
  (
    {
      state,
      descriptors,
      navigation,
      variant = 'square',
      animatedProps,
      style,
      barFooter,
      onLayout,
      isHideBottomBorder = false,
      onPressCallBack,
      tabStyle = {},
      isCustomizedFocusedTabLabel,
      isCustomizedNormalTabLabel,
      activeTabIndicatorStyle,
      activeTabLabelStyle,
    }: AppTopTabBarProps,
    ref,
  ) => {
    const {
      sizes,
      space,
      colors,
      animation: { duration },
    } = useTheme();
    const { show } = animatedProps || initialAnimatedProps;

    const [activeVariant, setActiveVariant] =
      useState<AppTopTabBarVariant>(variant);

    const [tabBarLayout, setTabBarLayout] = useState({ width: 0, height: 0 });
    const [tabWidths, setTabWidths] = useState<number[]>(
      Array(state.routes.length).fill(0),
    );

    const tabLeftObj = useDerivedValue(() => {
      return tabWidths.reduce<Record<string, number>>(
        (acc, _tabWidth, curIndex) => {
          if (curIndex === 0) {
            acc[curIndex] = 0;
          } else {
            acc[curIndex] = tabWidths[curIndex - 1] + acc[curIndex - 1];
          }

          return acc;
        },
        {},
      );
    });

    useImperativeHandle(
      ref,
      (): AppTopTabBarComponentRef => ({
        onChangeTab: (routeName: string) => {
          navigation.navigate({ name: routeName, merge: true });
        },
      }),
    );

    useEffect(() => {
      if (variant == 'scrollable' && tabWidths.every(cur => cur != 0)) {
        if (tabWidths.reduce((a, b) => a + b, 0) < tabBarLayout.width) {
          setActiveVariant('square');
        }
      }
    }, [tabWidths, tabBarLayout]);

    const { width } = useWindowDimensions();

    useEffect(() => {
      setActiveVariant(variant);
    }, [width]);

    const tabbarInnerRef = useRef<ScrollView>(null);

    const scrollPosition = useSharedValue(0);

    useEffect(() => {
      if (activeVariant === 'scrollable') {
        const tabRight = tabLeftObj.value[state.index] + tabWidths[state.index];

        if (
          scrollPosition.value > tabLeftObj.value[state.index] ||
          tabRight > tabBarLayout.width + scrollPosition.value
        ) {
          tabbarInnerRef.current?.scrollTo({
            x: tabLeftObj.value[state.index],
          });
        }
      }
    }, [state.index]);

    const activeIndex = useSharedValue(state.index || 0);

    const animIndicatorStyle = useAnimatedStyle(() => {
      const indicatorWidth = tabWidths[activeIndex.value];

      return {
        ...activeTabIndicatorStyle ? activeTabIndicatorStyle : {},
        height: sizes[animatedLineHeightInterval],
        width: withTiming(indicatorWidth, { duration }),
        left: withTiming(tabLeftObj.value[activeIndex.value], { duration }),
      };
    }, [tabBarLayout, tabWidths]);

    const animTabBarStyle = useAnimatedStyle(() => {
      if (tabBarLayout.width === 0) {
        return {};
      }

      return {
        height: show.value
          ? withTiming(tabBarLayout.height, {
              duration,
            })
          : withTiming(0, { duration }),
      };
    }, [tabBarLayout]);

    return (
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        onLayout={e => {
          onLayout && onLayout(e);
          if (
            tabBarLayout.width == 0 ||
            e.nativeEvent.layout.width != tabBarLayout.width
          ) {
            setTabBarLayout({
              width: e.nativeEvent.layout.width,
              height: e.nativeEvent.layout.height,
            });
          }
        }}
        style={[
          style,
          animatedProps && tabBarLayout.width > 0 && animTabBarStyle,
        ]}>
        <TabBarContainer
          ref={tabbarInnerRef}
          onScroll={e => {
            const xOffset = e.nativeEvent.contentOffset.x || 0;
            scrollPosition.value = xOffset;
          }}
          style={variant == 'round' && { paddingVertical: space[3] }}
          wideStyle={variant == 'round' && { paddingVertical: space[4] }}
          scrollMode={activeVariant == 'scrollable' && 'horizontal'}
          variant={activeVariant}
          isHideBottomBorder={isHideBottomBorder}>
          {state.routes.map((route, index) => {
            const { options } = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;
            const Badge = options.tabBarBadge;

            const isFocused = state.index === index;

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                //@ts-ignore
                if (!!onPressCallBack) {
                  onPressCallBack(route.name);
                }
                navigation.navigate({ name: route.name, merge: true });
              }
            };

            const onLongPress = () => {
              navigation.emit({
                type: 'tabLongPress',
                target: route.key,
              });
            };

            activeIndex.value = state.index;

            return (
              <Tab
                key={'TabBarContainer_AppTopTabBar_' + index}
                isFocused={isFocused}
                variant={activeVariant}
                onLayout={e => {
                  const nextTabWidths = tabWidths.slice();
                  nextTabWidths[index] = e.nativeEvent?.layout.width || 0;
                  setTabWidths(nextTabWidths);
                }}
                activeOpacity={1}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                onLongPress={onLongPress}
                style={tabStyle}>
                {typeof label == 'string' ? (
                  <Row>
                    <TabLabel
                      TypographyDefault={
                        variant === 'round'
                          ? Typography.Label
                          : Typography.LargeLabel
                      }
                      TypographyWide={Typography.LargeLabel}
                      isFocused={isFocused}
                      fontWeight={
                        isFocused
                          ? isCustomizedFocusedTabLabel
                            ? 'medium'
                            : 'bold'
                          : 'normal'
                      }
                      isCustomizedFocusedTabLabel={isCustomizedFocusedTabLabel}
                      isCustomizedNormalTabLabel={isCustomizedNormalTabLabel}
                      activeTabLabelStyle={activeTabLabelStyle}>
                      {label}
                    </TabLabel>
                    {Badge && <Badge />}
                  </Row>
                ) : (
                  <>
                    {
                      // label
                    }
                  </>
                )}
              </Tab>
            );
          })}
          {activeVariant !== 'round' && (
            <ActiveTabIndicator style={[animIndicatorStyle]} />
          )}
        </TabBarContainer>
        {barFooter}
      </Animated.View>
    );
  },
);

const TabBarContainer = styled(ResponsiveView)<
  {
    variant: AppTopTabBarVariant;
  } & { isHideBottomBorder: boolean }
>(({ theme, variant, isHideBottomBorder }) => ({
  flexDirection: 'row',
  borderBottomWidth: isHideBottomBorder ? 0 : 1,
  borderBottomColor: theme.colors.palette.fwdGrey[100],
  ...(variant === 'round' && {
    borderBottomWidth: 0,
    alignItems: 'center',
    gap: theme.space[3],
  }),
  ...(variant == 'scrollable' && {
    backgroundColor: theme.colors.background,
  }),
}));

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const Tab = styled(AnimatedTouchableOpacity)<{
  variant: AppTopTabBarVariant;
  isFocused: boolean;
}>(({ theme, variant, isFocused }) => {
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const defaultPaddingVertical = theme.space[3];
  return {
    paddingVertical: defaultPaddingVertical,
    paddingBottom:
      variant === 'round'
        ? undefined
        : defaultPaddingVertical + theme.sizes[animatedLineHeightInterval],
    ...(variant === 'scrollable'
      ? {
          paddingHorizontal: isWideScreen ? theme.space[8] : theme.space[4],
          textAlign: 'center',
        }
      : { flex: 1 }),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    ...(variant === 'round' && {
      borderRadius: theme.borderRadius.full,
      paddingVertical: theme.space[2],
      borderWidth: 2,
      borderColor: theme.colors.background,
      ...(isFocused && {
        backgroundColor: theme.colors.primaryVariant3,
        borderColor: theme.colors.primary,
      }),
    }),
    ...(variant === 'square' &&
      isWideScreen && {
        height: theme.sizes[14],
      }),
  };
});

const ActiveTabIndicator = styled(Animated.View)(({ theme }) => ({
  position: 'absolute',
  backgroundColor: theme.colors.palette.fwdAlternativeOrange[100],
  bottom: 0,
}));

const TabLabel = styled(ResponsiveText)<{
  isFocused: boolean;
  activeTabLabelStyle?: TextStyle;
  isCustomizedFocusedTabLabel?: boolean;
  isCustomizedNormalTabLabel?: boolean;
}>(
  ({
    theme,
    isFocused,
    isCustomizedFocusedTabLabel,
    isCustomizedNormalTabLabel,
    activeTabLabelStyle,
  }) => ({
    color: isFocused
      ? isCustomizedFocusedTabLabel
        ? theme.colors.palette.fwdDarkGreen[100]
        : theme.colors.palette.fwdAlternativeOrange[100]
      : isCustomizedNormalTabLabel
      ? theme.colors.palette.fwdDarkGreen[100]
      : theme.colors.palette.fwdGreyDarkest,
    fontWeight: isFocused ? 'medium' : 'normal',
    ...(isFocused && activeTabLabelStyle) ? activeTabLabelStyle : {},
  }),
);

const initialAnimatedProps = {
  show: {
    value: true,
  },
  lastOffsetY: {
    value: 0,
  },
  atTop: {
    value: true,
  },
};

export default AppTopTabBar;
