import { useTheme } from '@emotion/react';
import CustomTabBar from './CustomTabBar';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { ScrollView, View } from 'react-native';
import { useRef } from 'react';
import styled from '@emotion/native';

const Tab = createMaterialTopTabNavigator();
const EmptyComponent = () => <View />;

interface TabViewProps {
  onTabPress: (index: number) => void;
  focusedSectionIndex: number;
  tabs: { name: string; title: string }[];
  scrollEnabled?: boolean;
}

const TabContainer = styled.View(({ theme }) => ({
  height: theme.space[11],
  zIndex: 2,
}));

const TopTabView = ({
  onTabPress,
  focusedSectionIndex,
  tabs,
  scrollEnabled,
}: TabViewProps) => {
  const scrollViewRef = useRef<ScrollView>(null);

  const handleOnTabPress = (index: number) => {
    onTabPress(index);
    scrollViewRef.current?.scrollTo({
      x: index * 150,
      animated: true,
    });
  };

  const theme = useTheme();
  return (
    <TabContainer>
      <ScrollView
        horizontal
        bounces={false}
        contentContainerStyle={{
          minWidth: '100%',
        }}
        ref={scrollViewRef}
        showsHorizontalScrollIndicator={false}>
        <Tab.Navigator
          screenOptions={{
            tabBarIndicatorStyle: {
              backgroundColor: theme.colors.palette.fwdAlternativeOrange[100],
            },
          }}
          tabBar={props => (
            <CustomTabBar
              {...props}
              onTabPress={handleOnTabPress}
              focusedSectionIndex={focusedSectionIndex}
              scrollEnabled={scrollEnabled}
            />
          )}>
          {tabs.map(tab => (
            <Tab.Screen
              key={tab.title}
              name={tab.name}
              component={EmptyComponent}
              options={{
                title: tab.title,
              }}
            />
          ))}
        </Tab.Navigator>
      </ScrollView>
    </TabContainer>
  );
};

export default TopTabView;
