import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { MaterialTopTabBarProps } from '@react-navigation/material-top-tabs';
import { Typography, XView } from 'cube-ui-components';

interface CustomTabBarProps extends MaterialTopTabBarProps {
  onTabPress: (index: number) => void;
  focusedSectionIndex: number;
  scrollEnabled?: boolean;
}

const CustomTab = styled.TouchableOpacity<{
  isFocused: boolean;
  shoudlExpand?: boolean;
}>(({ theme: { colors, space }, isFocused, shoudlExpand }) => {
  return [
    {
      justifyContent: 'center',
      alignItems: 'center',
      flex: shoudlExpand ? 1 : 0,
      minWidth: shoudlExpand ? 'auto' : 150,
      paddingHorizontal: space[2],
    },
    isFocused && {
      borderBottomColor: colors.palette.fwdAlternativeOrange[100],
      borderBottomWidth: 3,
    },
  ];
});

const CustomTabBar = ({
  state,
  descriptors,
  navigation,
  onTabPress,
  focusedSectionIndex,
  scrollEnabled,
}: CustomTabBarProps) => {
  const { colors } = useTheme();
  return (
    <XView style={{ backgroundColor: colors.background, height: '100%' }}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options?.title;

        const isFocused = focusedSectionIndex === index;

        const onPress = () => {
          if (typeof onTabPress === 'function') {
            onTabPress(index);
          }
          navigation.navigate({ name: route.name, params: undefined });
        };

        return (
          <CustomTab
            key={index}
            onPress={onPress}
            isFocused={isFocused}
            shoudlExpand={!scrollEnabled}>
            <Typography.LargeBody
              fontWeight={isFocused ? 'bold' : 'normal'}
              color={isFocused ? colors.palette.fwdAlternativeOrange[100]: colors.secondary}>
              {label}
            </Typography.LargeBody>
          </CustomTab>
        );
      })}
    </XView>
  );
};

export default CustomTabBar;
