import React, { PropsWithChildren } from 'react';

import styled from '@emotion/native';
import { aiBackgroundShape } from 'assets/images';
import { Image } from 'expo-image';

const Container = styled.View(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.background,
}));

export default function AiBackground(props: PropsWithChildren<{}>) {
  return (
    <Container>
      <Image
        source={aiBackgroundShape}
        contentFit="cover"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
        }}
      />

      {props.children}
    </Container>
  );
}
