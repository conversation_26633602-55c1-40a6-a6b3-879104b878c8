import {
  <PERSON>vas,
  Circle,
  Group,
  Path,
  Skia,
  SweepGradient,
  vec,
} from '@shopify/react-native-skia';
import { Column } from 'cube-ui-components';
import React, { useEffect, useMemo } from 'react';
import { StyleSheet } from 'react-native';
import {
  Easing,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

type CircleGradientSpinnerProps = {
  size?: number;
  gradientColors?: string[];
  backgroundColor?: string;
  arcSize?: number;
  strokeWidth?: number;
  duration?: number;
  visible?: boolean;
};

const CircleGradientSpinner: React.FC<CircleGradientSpinnerProps> = ({
  size = 60,
  gradientColors = ['#FF6B35', '#F7931E', '#FFD23F', '#06D6A0', '#FF6B35'],
  backgroundColor = '#E5E5E5',
  arcSize = 0.7,
  strokeWidth = 4,
  duration = 1500,
  visible = true,
}) => {
  const rotation = useSharedValue(0);

  // Calculate dimensions
  const actualSize = size + strokeWidth * 2;
  const radius = size / 2;
  const center = useMemo(
    () => vec(actualSize / 2, actualSize / 2),
    [actualSize],
  );

  // Create arc path
  const arcPath = useMemo(() => {
    const path = Skia.Path.Make();
    const startAngle = 3;
    const sweepAngle = 360 * arcSize;

    path.addArc(
      {
        x: center.x - radius,
        y: center.y - radius,
        width: radius * 2,
        height: radius * 2,
      },
      startAngle,
      sweepAngle,
    );

    return path;
  }, [center, radius, arcSize]);

  // Create clean gradient colors without wrap-around
  const cleanGradientColors = useMemo(() => {
    // Don't repeat the first color at the end to avoid bleeding
    return gradientColors;
  }, [gradientColors]);

  // Create positions that only span the arc length (not full 360°)
  const colorPositions = useMemo(() => {
    return cleanGradientColors.map(
      (_, index) => index / (cleanGradientColors.length - 1),
    );
  }, [cleanGradientColors]);

  const animatedRotation = useDerivedValue(() => {
    return [{ rotate: Math.PI * rotation.value }];
  }, [rotation]);

  useEffect(() => {
    if (visible) {
      rotation.value = withRepeat(
        withTiming(2, { duration, easing: Easing.linear }),
        -1,
        false,
      );
    } else {
      rotation.value = 0;
    }

    return () => {
      rotation.value = 0;
    };
  }, [rotation, duration, visible]);

  if (!visible) return null;

  return (
    <Column
      alignItems="center"
      justifyContent="center"
      style={{ width: actualSize, height: actualSize }}>
      <Canvas style={StyleSheet.absoluteFill}>
        {/* Background full circle */}
        <Circle
          cx={center.x}
          cy={center.y}
          r={radius}
          style="stroke"
          strokeWidth={strokeWidth}
          color={backgroundColor}
        />

        {/* Animated gradient arc */}
        <Group origin={center} transform={animatedRotation}>
          <Path
            path={arcPath}
            style="stroke"
            strokeWidth={strokeWidth}
            strokeCap="round">
            <SweepGradient
              c={center}
              colors={cleanGradientColors}
              positions={colorPositions}
              start={0}
              end={360 * arcSize} // Critical: match gradient range to arc size
            />
          </Path>
        </Group>
      </Canvas>
    </Column>
  );
};

export default CircleGradientSpinner;
