import React, { useEffect } from 'react';
import { Animated, View } from 'react-native';

export default function CrossScreenWhiteBottom({
  isShowWhiteBottom,
}: {
  isShowWhiteBottom: boolean;
}) {
  const slideAnimation = React.useMemo(() => new Animated.Value(0), []);
  useEffect(() => {
    if (isShowWhiteBottom) {
      Animated.timing(slideAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
        delay: 100,
      }).start();
    } else {
      Animated.timing(slideAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
        delay: 200,
      }).start();
    }
  }, [isShowWhiteBottom, slideAnimation]);
  const modalTranslateY = slideAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1000, 0],
  });
  return (
    <Animated.View style={{ transform: [{ translateY: modalTranslateY }] }}>
      <View
        style={{
          height: 84,
          backgroundColor: 'white',
          width: '50%',
          position: 'absolute',
          bottom: 0,
          left: 0,
          borderTopColor: '#DBDFE1',
          borderTopWidth: 1,
        }}
      />
    </Animated.View>
  );
}
