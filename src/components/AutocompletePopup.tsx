import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DialogTablet from 'components/Dialog.tablet';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  Button,
  H6,
  Icon,
  Label,
  LargeBody,
  Row,
  TextField,
  TextFieldRef,
} from 'cube-ui-components';
import CubeFonts from 'cube-ui-components/dist/cjs/fonts';
import useLatest from 'hooks/useLatest';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  Keyboard,
  Pressable,
  StyleProp,
  StyleSheet,
  TextInput,
  TextStyle,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';

export type AutocompletePopupProps<T, V> = ViewProps & {
  label?: string;
  modalTitle?: string;
  searchLabel?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  disabled?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  getDisplayedLabel?: (item: T) => string;
  getExternalDisplayedLabel?: () => string;
  keyExtractor?: (item: T) => string;
  searchable?: boolean;
  searchMode?: 'auto' | 'manual';
  onQuery?: (text: string) => void;
  multiline?: boolean;
  autoExpand?: boolean;
  numberOfLines?: number;
  onFocus?: () => void;
  onBlur?: () => void;
  modalStyle?: StyleProp<ViewStyle>;
  selectionHint?: string;
  getItemDisabled?: (item: T) => boolean;
  inputStyle?: StyleProp<TextStyle>;
  highlight?: boolean;
  preventPopup?: boolean;
} & (
    | {
        type?: 'single';
        value?: V;
        onChange?: (value: V) => void;
      }
    | {
        type?: 'multiple';
        value?: V[];
        onChange?: (value: V[]) => void;
      }
  );

export type AutocompletePopupRef = TextFieldRef;

const AutocompletePopupInner = <T, V>(
  {
    label,
    type,
    modalTitle,
    searchLabel = '',
    confirmLabel = 'Confirm',
    cancelLabel = 'Cancel',
    disabled,
    hint,
    error,
    isError,
    value,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    getDisplayedLabel,
    getExternalDisplayedLabel,
    keyExtractor,
    searchable,
    searchMode = 'auto',
    onQuery: onQueryAction,
    multiline,
    autoExpand,
    numberOfLines,
    onFocus,
    onBlur,
    modalStyle,
    selectionHint = 'Please select',
    getItemDisabled,
    inputStyle,
    highlight,
    preventPopup,
    ...viewProps
  }: AutocompletePopupProps<T, V>,
  ref: React.ForwardedRef<AutocompletePopupRef>,
) => {
  const searchRef = useRef<TextInput>(null);
  const { space, colors, borderRadius } = useTheme();
  const [visible, setVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const onFocusRef = useLatest(onFocus);
  const onBlurRef = useLatest(onBlur);
  const getItemDisabledRef = useLatest(getItemDisabled);
  const [query, setQuery] = useState('');
  const getItemLabelRef = useLatest(getItemLabel);
  const getItemValueRef = useLatest(getItemValue);

  const show = useCallback(() => {
    setVisible(true);
    onFocusRef.current?.();
  }, [onFocusRef]);

  const flatListRef = useRef<FlatList<T>>(null);

  type ||= 'single'; // because default param not working
  const [checkedValue, setCheckedValue] = useState(value);

  const getExternalDisplayedLabelRef = useLatest(getExternalDisplayedLabel);
  const getDisplayedLabelRef = useLatest(getDisplayedLabel || getItemLabel);
  const displayedLabel = useMemo(() => {
    if (getExternalDisplayedLabelRef.current) {
      return getExternalDisplayedLabelRef.current();
    }
    if (type === 'single') {
      const selectedItem = data?.find?.(
        item => getItemValueRef.current?.(item) === value,
      );
      return selectedItem ? getDisplayedLabelRef.current?.(selectedItem) : '';
    } else if (type === 'multiple') {
      return data
        ?.filter?.(item => value?.includes(getItemValueRef.current?.(item)))
        .map(i => getDisplayedLabelRef.current?.(i))
        .join(', ');
    }
  }, [
    data,
    getDisplayedLabelRef,
    getExternalDisplayedLabelRef,
    getItemValueRef,
    type,
    value,
  ]);

  const onQueryActionRef = useLatest(onQueryAction);
  const onQuery = useCallback(
    (text: string) => {
      if (searchMode === 'manual') {
        onQueryActionRef.current?.(text);
      }
      setQuery(text);
    },
    [onQueryActionRef, searchMode],
  );

  const hide = useCallback(() => {
    setSearchText('');
    onQuery('');
    setVisible(false);
    setTimeout(() => {
      Keyboard.dismiss();
      onBlurRef.current?.();
    }, 100);
  }, [onBlurRef, onQuery]);

  const onDoneRef = useLatest((checkedValue: V | V[] | undefined) => {
    if (type === 'single') {
      onChange?.(checkedValue as V);
    } else {
      (onChange as (value: V[]) => void)?.(checkedValue as V[]);
    }
    hide();
  });

  const onCancel = useCallback(() => {
    setCheckedValue(value);
    hide();
  }, [hide, setCheckedValue, value]);

  useEffect(() => {
    setCheckedValue(value);
  }, [value]);

  const filteredData = useMemo(() => {
    if (searchMode === 'manual') {
      return data;
    } else {
      return data?.filter(item => {
        return String(getItemLabelRef.current(item))
          .toLowerCase()
          .includes(query.trim().toLowerCase());
      });
    }
  }, [searchMode, data, getItemLabelRef, query]);

  useEffect(() => {
    if (visible) {
      if (!Array.isArray(value) && value && data) {
        const selectedIndex = data.findIndex?.(
          item => getItemValueRef.current(item) === value,
        );
        if (selectedIndex > 0) {
          setTimeout(() => {
            flatListRef.current?.scrollToIndex({
              index: selectedIndex,
              viewPosition: 0.1,
            });
          }, 0);
        }
      }
    }
  }, [data, getItemValueRef, visible, value]);

  const onSelectItem = useCallback(
    (newValue: V) => {
      searchRef.current?.blur();
      let newCheckedValue: V | V[] | undefined = checkedValue;
      setCheckedValue(value => {
        if (type === 'multiple') {
          const _value = Array.isArray(value)
            ? [...value]
            : value === undefined
            ? []
            : [value];
          if (_value.find(v => v === newValue)) {
            newCheckedValue = _value.filter(v => v !== newValue);
          } else {
            newCheckedValue = _value.concat(newValue);
          }
        } else {
          newCheckedValue = newValue;
        }

        if (newCheckedValue && type === 'single') {
          onDoneRef?.current(newCheckedValue);
        }
        return newCheckedValue;
      });
    },
    [checkedValue, onDoneRef, type],
  );

  const renderItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      const disabled = getItemDisabledRef?.current?.(item);
      return (
        <Item<V>
          index={index}
          label={getItemLabelRef.current(item)}
          value={getItemValueRef.current(item)}
          selected={
            checkedValue
              ? Array.isArray(checkedValue)
                ? checkedValue.some(
                    curVal => curVal === getItemValueRef.current(item),
                  )
                : getItemValueRef.current(item) === checkedValue
              : false
          }
          onSelect={onSelectItem}
          disabled={disabled}
        />
      );
    },
    [
      checkedValue,
      getItemDisabledRef,
      getItemLabelRef,
      getItemValueRef,
      onSelectItem,
    ],
  );

  const defaultSearchLabel = label ? 'Search for ' + label?.toLowerCase() : '';

  return (
    <>
      <View {...viewProps}>
        <TextField
          ref={ref}
          label={label}
          value={displayedLabel}
          disabled={disabled}
          right={disabled ? null : <Icon.Dropdown />}
          hint={hint}
          error={error}
          isError={isError}
          showSoftInputOnFocus={false}
          multiline={multiline}
          autoExpand={autoExpand}
          numberOfLines={numberOfLines}
          inputStyle={inputStyle}
          highlight={highlight}
          onFocus={() => {
            Keyboard.dismiss();
            if (!preventPopup) {
              show();
            }
          }}
          onBlur={Keyboard.dismiss}
        />
        <Pressable
          disabled={disabled}
          onPress={show}
          style={StyleSheet.absoluteFill}
        />
      </View>
      <DialogContainer
        dismissable
        onDismiss={onCancel}
        visible={visible}
        style={modalStyle}>
        <Box p={space[6]} pt={0} w={762} h="100%">
          <CloseButton hitSlop={ICON_HIT_SLOP} onPress={onCancel}>
            <Icon.Close size={24} fill={colors.secondary} />
          </CloseButton>
          {(Boolean(modalTitle) || Boolean(label)) && (
            <Title fontWeight="bold">{modalTitle || label}</Title>
          )}
          {searchable && (
            <Row
              alignItems="center"
              rounded={borderRadius.full}
              borderColor={colors.palette.fwdDarkGreen[20]}
              border={1}
              mb={space[6]}
              py={space[3] - 2}
              pl={space[4]}
              pr={space[3]}>
              <SearchInput
                ref={searchRef}
                value={searchText}
                onChangeText={text => {
                  setSearchText(text);
                  onQuery(text);
                }}
                autoCapitalize="none"
                autoCorrect={false}
                placeholder={searchLabel || defaultSearchLabel}
                placeholderTextColor={colors.palette.fwdGreyDark}
                cursorColor={colors.primary}
                selectionColor={colors.primary}
              />
            </Row>
          )}
          <SelectionHintText fontWeight="bold" color={colors.secondaryVariant}>
            {selectionHint}
          </SelectionHintText>
          <FlatList
            ref={flatListRef}
            keyboardShouldPersistTaps="always"
            keyboardDismissMode="on-drag"
            data={filteredData}
            keyExtractor={item =>
              (keyExtractor
                ? keyExtractor(item as T)
                : getItemValueRef.current(item as T)) as string
            }
            getItemLayout={(item, index) => ({
              length: 57,
              offset: 57 * index,
              index,
            })}
            renderItem={renderItem}
          />
          {type === 'multiple' && (
            <Row mt={space[2]} gap={space[4]} justifyContent="center">
              <Action
                onPress={onCancel}
                variant="secondary"
                size="medium"
                text={cancelLabel}
              />
              <Action
                disabled={!checkedValue}
                onPress={() => onDoneRef.current(checkedValue)}
                size="medium"
                text={confirmLabel}
              />
            </Row>
          )}
        </Box>
      </DialogContainer>
    </>
  );
};

const AutocompletePopup = forwardRef(AutocompletePopupInner) as <T, V>(
  props: AutocompletePopupProps<T, V> & {
    ref?: React.ForwardedRef<AutocompletePopupRef>;
  },
) => ReturnType<typeof AutocompletePopupInner>;
export default AutocompletePopup;

const SearchInput = styled(TextInput)(({ theme }) => ({
  fontFamily: CubeFonts.FWDCircularTT.Book,
  fontSize: theme.typography.largeLabel.size,
  lineHeight: theme.typography.largeLabel.lineHeight,
  color: theme.colors.secondary,
  flex: 1,
}));

const DialogContainer = styled(DialogTablet)(({ theme: { space } }) => ({
  marginHorizontal: space[20],
  marginVertical: space[14],
}));

const Title = styled(H6)(({ theme: { space } }) => ({
  marginBottom: space[4],
}));

const SelectionHintText = styled(Label)(({ theme: { space } }) => ({
  marginBottom: space[1],
}));

const Action = styled(Button)(() => ({
  width: 200,
}));

const ItemButton = styled.TouchableOpacity<{
  disabled: boolean;
  index: number;
}>(({ theme: { space, colors }, disabled, index }) => ({
  flexDirection: 'row',
  paddingVertical: space[4],
  opacity: disabled ? 0.5 : 1,
  borderTopWidth: index === 0 ? 0 : 1,
  borderTopColor: colors.palette.fwdGrey[100],
}));

type ItemProps<V> = {
  value: V;
  label: string;
  selected: boolean;
  onSelect: (value: V) => void;
  disabled?: boolean;
  index: number;
};

const Item = React.memo(
  <V,>({
    value,
    label,
    selected,
    onSelect,
    disabled = false,
    index,
  }: ItemProps<V>) => {
    return (
      <ItemButton
        index={index}
        disabled={disabled}
        onPress={() => {
          if (!disabled) {
            onSelect(value);
          }
        }}>
        <LargeBody style={{ flex: 1 }}>{label}</LargeBody>
        {selected && <Icon.Tick />}
      </ItemButton>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

const CloseButton = styled.TouchableOpacity({
  alignSelf: 'flex-end',
});
