import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, RadioButton, Typography } from 'cube-ui-components';
import React from 'react';
import { ColumnText } from './SearchExistingLead/tablet/SearchLeadResults.tablet';

type ItemProps<V> = {
  value: V;
  label: string;
  selected: boolean;
  onSelect: (value: V) => void;
  subtexts: string[];
  searchText?: string;
};
const SearchableListItem = React.memo(
  <V,>({
    value,
    label,
    selected,
    onSelect,
    subtexts,
    searchText,
  }: ItemProps<V>) => {
    const { space, colors } = useTheme();

    return (
      <ItemContainer onPress={() => onSelect(value)}>
        <Box mt={1} pointerEvents="none">
          <RadioButton
            style={{ marginRight: 0 }}
            value={String(value)}
            selected={selected}
          />
        </Box>
        <Column>
          <ColumnText variant="LargeLabel" highlightText={searchText}>
            {label}
          </ColumnText>
          <Box h={space[1]} />
          {subtexts.map((text, index) => (
            <ColumnText
              key={index}
              textColor={colors.secondaryVariant}
              highlightText={searchText}>
              {text}
            </ColumnText>
          ))}
        </Column>
      </ItemContainer>
    );
  },
) as <V>(props: ItemProps<V>) => JSX.Element;

export default SearchableListItem;

const ItemContainer = styled.Pressable(({ theme: { space } }) => ({
  flexDirection: 'row',
}));
