import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

type Props =
  | {
      phone: React.ReactNode;
      tablet: React.ReactNode;
      default?: never;
    }
  | {
      phone?: React.ReactNode;
      tablet?: React.ReactNode;
      default: React.ReactNode;
    };

export default function DeviceBasedRendering(props: Props) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  if (isTabletMode && props.tablet) return props.tablet;
  if (!isTabletMode && props.phone) return props.phone;
  if (props.default) return props.default;
}
