import { TextField, TextFieldRef } from 'cube-ui-components';
import { TextFieldProps } from 'cube-ui-components';
import React, { forwardRef } from 'react';
import { country } from 'utils/context';

// Capitalised Each Word on the user input, ie: *B*ruce *L*ee Jun Fan
export function capitalizeFirstLetterOfEachWord(input: string) {
  return input
    .split(' ')
    .filter(Boolean)
    .map(word => {
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    })
    .join(' ');
}

// To auto converted apostrophe value ’ and ‘ to ' before the validation
export function replaceApostrophe(input: string) {
  return input.replaceAll('‘', "'").replaceAll('’', "'");
}

const DEFAULT_ENABLED = ['my', 'ib', 'id'].includes(country);

export const useNameFieldBlur = (
  props: Pick<TextFieldProps, 'value' | 'onChange' | 'onBlur'>,
  enabled = DEFAULT_ENABLED,
) => {
  const onBlur = () => {
    if (typeof props.value !== 'string' || !enabled) return;

    const handledValue = replaceApostrophe(
      capitalizeFirstLetterOfEachWord(props.value),
    );

    props?.onChange?.(handledValue);
    props?.onBlur?.();
  };

  return { onBlur };
};

const NameField = forwardRef<TextFieldRef, TextFieldProps>((props, ref) => {
  const { onBlur } = useNameFieldBlur(props);

  return <TextField ref={ref} {...props} onBlur={onBlur} />;
});

export default NameField;
