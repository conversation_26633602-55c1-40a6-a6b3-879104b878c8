import { useTheme } from '@emotion/react';
import DialogPhone from 'components/Dialog.phone';
import { Box, Button, H6, LargeBody, Row } from 'cube-ui-components';
import * as Application from 'expo-application';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { goToAppSettings } from 'utils/helper/permissionUtils';

export default function CameraPermissionSetting({
  visible,
  onDismiss,
}: {
  visible?: boolean;
  onDismiss?: () => void;
}) {
  const { space } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  return (
    <DialogPhone visible={visible}>
      <H6 fontWeight="bold">Permission required</H6>
      <Box mt={space[4]} mb={space[6]}>
        <LargeBody>
          Allow {Application.applicationName} to access your camera.
        </LargeBody>
      </Box>
      <Row justifyContent="center">
        <Button
          style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          variant="secondary"
          text="Cancel"
          onPress={onDismiss}
        />
        <Box w={space[3]} />
        <Button
          style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          text="Go to settings"
          onPress={goToAppSettings}
        />
      </Row>
    </DialogPhone>
  );
}
