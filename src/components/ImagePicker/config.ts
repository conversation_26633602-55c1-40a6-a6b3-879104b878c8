export const defaultDocTypeToPick = [
  'image/png',
  'image/jpg',
  'image/jpeg',
  'application/pdf',
] as const;

const otherImageDocTypeToPick = ['image/bmp', 'image/gif'] as const;

export const imageFileTypeFromConfig = defaultDocTypeToPick.filter(type =>
  type.startsWith('image/'),
);
// .map(type => type.slice('image/'.length));

const msftOfficeWordFileTypeToPick = [
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
] as const;

const msftOfficeExcelFileTypeToPick = [
  'application/vnd.ms-excel', //.xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', //.xlsx
] as const;

export const allDocMimeTypesMap = {
  default: defaultDocTypeToPick,
  otherImage: otherImageDocTypeToPick,
  msftOfficeWord: msftOfficeWordFileTypeToPick,
  msftOfficeExcel: msftOfficeExcelFileTypeToPick,
} as const;
type AllDocMimeTypesMapValue =
  (typeof allDocMimeTypesMap)[keyof typeof allDocMimeTypesMap][number];
export type AllDocMimeTypesArray = ReadonlyArray<AllDocMimeTypesMapValue>;

const msftOfficeFileTypeToPick = [
  ...msftOfficeWordFileTypeToPick,
  ...msftOfficeExcelFileTypeToPick,
] as const;

export const nonImgFilesToMimeTypes: Record<
  string,
  | (typeof defaultDocTypeToPick)[number]
  | (typeof msftOfficeFileTypeToPick)[number]
> = {
  pdf: 'application/pdf',
  xls: 'application/vnd.ms-excel',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
};

export const getDocTypeToPick = (isAllowedToPickerMsftOffice = false) => {
  return isAllowedToPickerMsftOffice
    ? [...defaultDocTypeToPick, ...msftOfficeFileTypeToPick]
    : defaultDocTypeToPick;
};

export const allImageFullMimeSubtypes: string[] = [
  ...defaultDocTypeToPick,
  ...otherImageDocTypeToPick,
].filter(type => type.startsWith('image/'));

export const allImageFileSubtypes: string[] =
  allImageFullMimeSubtypes?.map(type => type?.split('image/')[1]) ?? [];

export const getKeyByNonImgMimeTypesValue = (value: string) => {
  return getKeyByValue(nonImgFilesToMimeTypes, value);
};

function getKeyByValue(
  object: Record<string, string>,
  value: string,
): string | undefined {
  const entry = Object.entries(object).find(([key, val]) => val === value);
  // The 'entry' will be an array like ['pdf', 'application/pdf'] or undefined
  // We use optional chaining (?.) to safely access the key at index 0.
  return entry?.[0];
}

function getFileExtensionKey(
  filename: string,
  typesMap: Record<string, string>,
): string | undefined {
  return Object.keys(typesMap).find(key =>
    filename?.toLowerCase()?.endsWith('.' + key),
  );
}

export const checkMatchedNonImage = (filename: string) => {
  const extensionKey = getFileExtensionKey(filename, nonImgFilesToMimeTypes);

  console.log(
    '----- --- ----checkMatchedNonImage ==== extensionKey======',
    extensionKey,
  );
  return Boolean(extensionKey);
};
