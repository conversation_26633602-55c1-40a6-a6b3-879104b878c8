import React, { useCallback } from 'react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ImagePickerTablet from './tablet/ImagePicker.tablet';
import ImagePickerPhone from './phone/ImagePicker.phone';
import CameraImagePicker from './CameraImagePicker';
import useToggle from 'hooks/useToggle';
import * as Crypto from 'expo-crypto';
import { ImagePickerProps } from './utils';
import { Platform } from 'react-native';
import { compressImage } from 'utils/helper/imageUtils';

export const DEFAULT_MAX_PICKER_FILE_SIZE = 5;

export default function ImagePicker({
  onCamera,
  shouldOpenInAppCamera,
  ...props
}: ImagePickerProps) {
  const [cameraVisible, showCamera, hideCamera] = useToggle();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Component = isTabletMode ? ImagePickerTablet : ImagePickerPhone;
  const handleShutter = async (file: {
    uri: string;
    base64?: string;
    width: number;
    height: number;
  }) => {
    const [compressedImage, size] = await compressImage(
      {
        canceled: false,
        assets: [{ uri: file.uri, width: file.width, height: file.height }],
      },
      props.maxSizeInMB ?? DEFAULT_MAX_PICKER_FILE_SIZE,
    );

    if (compressedImage) {
      props.onDone?.({
        finished: true,
        file: {
          base64: compressedImage.base64 || '',
          uri: compressedImage.uri,
          size: size || 0,
          name: Crypto.randomUUID(),
          isAttachment: false,
        },
      });
    }
  };

  const onOpenInAppCamera = useCallback(() => {
    onCamera ? onCamera() : showCamera();
  }, [onCamera, showCamera]);

  if (shouldOpenInAppCamera === undefined) {
    // default in-app camera is for android tablet
    shouldOpenInAppCamera = Platform.OS === 'android' && isTabletMode;
  }

  return (
    <>
      {props.visible && (
        <Component
          {...props}
          onOpenInAppCamera={onOpenInAppCamera}
          shouldOpenInAppCamera={shouldOpenInAppCamera}
        />
      )}
      {isTabletMode && (
        <CameraImagePicker
          cameraQuality={props.config?.cameraQuality ?? 1}
          visible={cameraVisible}
          onDismiss={hideCamera}
          onDone={handleShutter}
        />
      )}
    </>
  );
}
