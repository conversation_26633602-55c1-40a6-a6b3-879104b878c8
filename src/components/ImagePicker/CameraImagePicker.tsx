import React, { useEffect, useRef, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Box, Button, Column, Icon, Label, Row } from 'cube-ui-components';
import styled from '@emotion/native';
import { Image, Pressable, useWindowDimensions, View } from 'react-native';
import {
  CameraCapturedPicture,
  CameraType,
  CameraView,
  CameraViewProps,
} from 'expo-camera';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';
import Animated, {
  cancelAnimation,
  Extrapolation,
  interpolate,
  useAnimatedProps,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { DeviceMotion, DeviceMotionOrientation } from 'expo-sensors';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { useTranslation } from 'react-i18next';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

interface Props {
  cameraQuality?: number;
  visible?: boolean;
  onDismiss?: () => void;
  onDone?: (result: {
    uri: string;
    base64?: string;
    width: number;
    height: number;
  }) => void;
}

DeviceMotion.setUpdateInterval(500);

Animated.addWhitelistedNativeProps({
  zoom: true,
});
const ReanimatedCamera = Animated.createAnimatedComponent(CameraView);

export default function CameraImagePicker({
  cameraQuality,
  visible,
  onDismiss: onDismissProp,
  onDone,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const rotationAnimatedValue = useSharedValue(0);
  const scaleAnimatedValue = useSharedValue(1);
  const zoomValue = useSharedValue(0);

  const [cameraReady, setCameraReady] = useState(false);
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [photo, setPhoto] = useState<CameraCapturedPicture | null>(null);
  const [flashModeOn, setFlashModeOn] = useState(false);
  const [orientation, setOrientation] = useState<DeviceMotionOrientation>(
    DeviceMotionOrientation.RightLandscape,
  );
  const [baseOrientation, setBaseOrientation] =
    useState<DeviceMotionOrientation>(DeviceMotionOrientation.RightLandscape);
  const [capturedRotation, setCapturedRotation] =
    useState<DeviceMotionOrientation>(DeviceMotionOrientation.RightLandscape);
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();
  const cameraRef = useRef<CameraView>(null);
  const takePicture = () => {
    if (cameraReady) {
      setCapturedRotation(orientation);
      cameraRef.current
        ?.takePictureAsync({ quality: cameraQuality, base64: true })
        .then(photo => {
          if (photo) {
            setPhoto(photo);
          }
        });
    }
  };
  const isCapturing = photo === null;
  const isRotateToLeft =
    (baseOrientation === DeviceMotionOrientation.LeftLandscape &&
      orientation === DeviceMotionOrientation.Portrait) ||
    (baseOrientation === DeviceMotionOrientation.RightLandscape &&
      orientation === DeviceMotionOrientation.UpsideDown);
  const isRotateToRight =
    (baseOrientation === DeviceMotionOrientation.LeftLandscape &&
      orientation === DeviceMotionOrientation.UpsideDown) ||
    (baseOrientation === DeviceMotionOrientation.RightLandscape &&
      orientation === DeviceMotionOrientation.Portrait);
  const isPortrait =
    orientation === DeviceMotionOrientation.Portrait ||
    orientation === DeviceMotionOrientation.UpsideDown;

  const zoomOffset = useSharedValue(0);
  const gesture = Gesture.Pinch()
    .onBegin(() => {
      zoomOffset.value = zoomValue.value;
    })
    .onUpdate(event => {
      const z =
        zoomOffset.value +
        (event.scale < 1 ? event.scale - 1 : (event.scale - 1) / 10);
      zoomValue.value = interpolate(z, [0, 1], [0, 1], Extrapolation.CLAMP);
    });

  const animatedProps = useAnimatedProps<CameraViewProps>(
    () => ({ zoom: zoomValue.value }),
    [zoomValue],
  );

  useEffect(() => {
    if (visible) {
      const subscription = DeviceMotion.addListener(
        ({ rotation: rotationMetrics, orientation: deviceOrientation }) => {
          if (rotationMetrics) {
            const newOrientation = orientationCalculation(
              Math.round(rotationMetrics.gamma * 1e2) / 1e2,
              Math.round(rotationMetrics.beta * 1e2) / 1e2,
              deviceOrientation,
            );
            setOrientation(newOrientation);
            setBaseOrientation(deviceOrientation);
          }
        },
      );
      return subscription.remove;
    }
  }, [visible]);

  useEffect(() => {
    cancelAnimation(rotationAnimatedValue);
    cancelAnimation(scaleAnimatedValue);
    scaleAnimatedValue.value = withTiming(
      orientation === DeviceMotionOrientation.Portrait ||
        orientation === DeviceMotionOrientation.UpsideDown
        ? capturedRotation === DeviceMotionOrientation.Portrait ||
          capturedRotation === DeviceMotionOrientation.UpsideDown
          ? 1.3
          : 0.7
        : 1,
      {
        duration: 200,
      },
    );

    rotationAnimatedValue.value = withTiming(
      // Support only case when user rotates device from left landscape to upside down which return value 270
      baseOrientation === DeviceMotionOrientation.LeftLandscape &&
        orientation === DeviceMotionOrientation.UpsideDown
        ? -orientation - baseOrientation
        : orientation - baseOrientation,
      {
        duration: 200,
      },
    );
  }, [
    baseOrientation,
    capturedRotation,
    orientation,
    rotationAnimatedValue,
    scaleAnimatedValue,
  ]);

  const rotationAnimationStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          rotateZ: `${rotationAnimatedValue.value}deg`,
        },
      ],
    }),
    [rotationAnimatedValue.value],
  );

  const imageRotationStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotateZ: `${rotationAnimatedValue.value}deg`,
        },
        {
          scale: scaleAnimatedValue.value,
        },
      ],
    };
  }, [rotationAnimatedValue.value, scaleAnimatedValue.value]);

  const onDismiss = async () => {
    onDismissProp?.();
    zoomValue.value = 0;
    setPhoto(null);
  };

  return (
    <CFFModal visible={visible}>
      <Row
        w="100%"
        h="100%"
        renderToHardwareTextureAndroid
        backgroundColor={'#000000'}>
        {isCapturing ? (
          <GestureDetector gesture={gesture}>
            <ReanimatedCamera
              // key={cameraType}
              ref={cameraRef}
              style={{ width: '100%', height: '100%' }}
              facing={cameraType}
              flash={flashModeOn ? 'on' : 'off'}
              onCameraReady={() => setCameraReady(true)}
              animatedProps={animatedProps}
            />
          </GestureDetector>
        ) : (
          <Animated.View
            style={[
              imageRotationStyle,
              {
                width: screenWidth,
                height: screenHeight,
              },
            ]}>
            <Image
              source={{ uri: photo?.uri }}
              style={{
                width: screenWidth,
                height: screenHeight,
              }}
              resizeMode="contain"
            />
          </Animated.View>
        )}
        {isCapturing ? (
          <Box
            pos={'absolute'}
            bottom={isRotateToRight ? undefined : 0}
            left={isPortrait ? 0 : undefined}
            top={!isPortrait || isRotateToRight ? 0 : undefined}
            right={0}
            paddingX={isPortrait ? 0 : space[6]}
            paddingY={isPortrait ? space[6] : 0}
            flexDirection={
              isRotateToRight
                ? 'row'
                : isRotateToLeft
                ? 'row-reverse'
                : 'column'
            }
            backgroundColor={'rgba(0,0,0,0.5)'}>
            <Box
              flex={1}
              flexDirection={
                isRotateToRight
                  ? 'row'
                  : isRotateToLeft
                  ? 'row-reverse'
                  : 'column'
              }
              justifyContent={'flex-end'}
              alignItems={'center'}
              pb={isPortrait ? 0 : space[12]}
              pr={isRotateToRight ? space[12] : 0}
              pl={isRotateToLeft ? space[12] : 0}
              rowGap={isPortrait ? 0 : space[10]}
              columnGap={isPortrait ? space[10] : 0}>
              <ActionButton
                style={rotationAnimationStyle}
                disabled={cameraType === 'front'}
                onPress={() => setFlashModeOn(last => !last)}>
                <Box
                  p={space[2]}
                  borderRadius={50}
                  backgroundColor={'rgba(0,0,0,0.5)'}>
                  {flashModeOn ? <FlashOnIcon /> : <FlashOffIcon />}
                </Box>
              </ActionButton>
              <ActionButton
                style={rotationAnimationStyle}
                onPress={() =>
                  setCameraType(type => (type === 'back' ? 'front' : 'back'))
                }>
                <Box
                  p={space[2]}
                  borderRadius={50}
                  backgroundColor={'rgba(0,0,0,0.5)'}>
                  <SwitchCameraIcon />
                </Box>
              </ActionButton>
            </Box>
            <Column alignItems="center">
              <CaptureButton onPress={takePicture} />
            </Column>
            <Box
              flex={1}
              flexDirection={
                isRotateToRight
                  ? 'row'
                  : isRotateToLeft
                  ? 'row-reverse'
                  : 'column'
              }
              justifyContent={'flex-end'}
              alignItems={'center'}
              pb={isPortrait ? 0 : space[12]}
              pr={isRotateToRight ? space[12] : 0}
              pl={isRotateToLeft ? space[12] : 0}>
              <ActionButton style={rotationAnimationStyle} onPress={onDismiss}>
                <Row alignItems={'center'} columnGap={space[1]}>
                  <Icon.Close fill={'#FFFFFF'} size={space[6]} />
                  <Label color={colors.onSecondary}>{t('eApp:cancel')}</Label>
                </Row>
              </ActionButton>
            </Box>
          </Box>
        ) : isPortrait ? (
          <Animated.View
            style={[
              rotationAnimationStyle,
              {
                position: 'absolute',
                bottom: 0,
                right: isRotateToRight ? 0 : undefined,
                left: isRotateToLeft ? 0 : undefined,
                width: screenHeight,
                height: screenHeight,
                justifyContent: 'flex-end',
              },
            ]}>
            <Row
              alignItems={'center'}
              columnGap={space[4]}
              bgColor={'rgba(0,0,0,0.5)'}
              py={space[4]}
              px={space[6]}>
              <Pressable onPress={onDismiss}>
                <Row alignItems={'center'} columnGap={space[1]}>
                  <Icon.Close fill={'#FFFFFF'} size={space[6]} />
                  <Label color={colors.onSecondary}>{t('eApp:cancel')}</Label>
                </Row>
              </Pressable>
              <Box flex={1} />
              <Button
                onPress={() => setPhoto(null)}
                variant={'tertiary'}
                text={t('eApp:retake')}
              />
              <Button
                style={{ width: space[50] }}
                text={t('eApp:usePhoto')}
                onPress={async () => {
                  if (photo) {
                    const rotatedImage = await manipulateAsync(photo.uri, [], {
                      base64: true,
                      format: SaveFormat.JPEG,
                    });
                    onDone?.(rotatedImage);
                    onDismiss?.();
                  }
                }}
              />
            </Row>
          </Animated.View>
        ) : (
          <Box
            pos="absolute"
            bottom={0}
            left={0}
            right={0}
            paddingX={space[6]}
            paddingY={space[4]}
            flexDirection={'row'}
            columnGap={space[4]}
            backgroundColor={'rgba(0,0,0,0.5)'}>
            <ActionButton style={rotationAnimationStyle} onPress={onDismiss}>
              <Row alignItems={'center'} columnGap={space[1]}>
                <Icon.Close fill={'#FFFFFF'} size={space[6]} />
                <Label color={colors.onSecondary}>{t('eApp:cancel')}</Label>
              </Row>
            </ActionButton>
            <Box flex={1} />
            <Animated.View style={rotationAnimationStyle}>
              <Button
                onPress={() => setPhoto(null)}
                variant={'tertiary'}
                text={t('eApp:retake')}
              />
            </Animated.View>
            <Animated.View style={rotationAnimationStyle}>
              <Button
                style={{ width: space[50] }}
                text={t('eApp:usePhoto')}
                onPress={async () => {
                  if (photo) {
                    const rotatedImage = await manipulateAsync(photo.uri, [], {
                      base64: true,
                      format: SaveFormat.JPEG,
                    });
                    onDone?.(rotatedImage);
                    onDismiss?.();
                  }
                }}
              />
            </Animated.View>
          </Box>
        )}
      </Row>
    </CFFModal>
  );
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const ActionButton = styled(AnimatedPressable)(
  ({ theme: { sizes }, disabled }) => ({
    opacity: disabled ? 0.5 : 1,
    height: sizes[11],
    justifyContent: 'center',
    alignItems: 'center',
  }),
);

const FlashOnIcon = () => {
  return (
    <Svg width={28} height={28} viewBox="0 0 28 28" fill="none">
      <G clip-path="url(#clip0_28385_286732)">
        <Path
          d="M11.9164 18.193C11.9957 17.8169 11.7551 17.4478 11.379 17.3686L6.97149 16.4398C6.4653 16.3331 6.24833 15.731 6.57011 15.326L16.3549 3.00935C16.8116 2.43439 17.7321 2.86717 17.5806 3.5857L16.2448 9.92466C16.1656 10.3007 16.4062 10.6698 16.7823 10.7491L21.1898 11.6779C21.696 11.7845 21.9129 12.3866 21.5912 12.7916L11.8064 25.1083C11.3497 25.6832 10.4292 25.2505 10.5806 24.5319L11.9164 18.193Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_28385_286732">
          <Rect width="28" height="28" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

const FlashOffIcon = () => {
  return (
    <Svg width={28} height={28} viewBox="0 0 28 28" fill="none">
      <G
        clipPath="url(#clip0_28385_286845)"
        fillRule="evenodd"
        clipRule="evenodd"
        fill="#fff">
        <Path d="M11.379 17.37c.376.079.617.448.537.824l-1.335 6.339c-.152.718.769 1.151 1.225.576l4.794-6.034-7.249-7.249-2.781 3.5a.696.696 0 00.401 1.115l4.408.928zm-1.444-6.28l7.25 7.25-7.25-7.25zm8.856 5.227l-7.249-7.25 4.813-6.057c.457-.575 1.377-.142 1.226.577l-1.336 6.339a.696.696 0 00.537.824l4.408.929a.696.696 0 01.401 1.114l-2.8 3.524zM23.91 23.91a.817.817 0 01-1.154 0L4.089 5.244a.817.817 0 011.155-1.155l18.667 18.667a.817.817 0 010 1.154z" />
      </G>
      <Defs>
        <ClipPath id="clip0_28385_286845">
          <Path fill="#fff" d="M0 0H28V28H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

const SwitchCameraIcon = () => {
  return (
    <Svg width={28} height={28} viewBox="0 0 32 32" fill="none">
      <Path
        d="M16.0064 26.0133C14.3598 26.0133 12.7531 25.6067 11.2998 24.8333C10.9531 24.6467 10.8264 24.2132 11.0398 23.8799L11.7464 22.7599C11.9264 22.4733 12.3064 22.3533 12.6064 22.5133C13.6531 23.0667 14.8198 23.3534 15.9998 23.3534C20.0464 23.3534 23.3398 20.06 23.3398 16.0133H26.0064C26.0064 21.52 21.5198 26.0133 16.0064 26.0133Z"
        fill="white"
      />
      <Path
        d="M8.66642 16.0067H5.99976C5.99976 10.4867 10.4864 6 16.0064 6C17.6531 6 19.2664 6.40668 20.7131 7.18001C21.0598 7.36668 21.1864 7.79997 20.9731 8.1333L20.2664 9.25326C20.0864 9.53992 19.7064 9.66 19.4064 9.5C18.3598 8.94667 17.1931 8.65999 16.0131 8.65999C11.9597 8.66666 8.66642 11.96 8.66642 16.0067Z"
        fill="white"
      />
      <Path
        d="M27.9996 16.0067H21.333C20.7863 16.0067 20.473 15.38 20.7996 14.94L24.1396 10.4934C24.4063 10.14 24.9396 10.14 25.2063 10.4934L28.533 14.94C28.8663 15.38 28.553 16.0067 27.9996 16.0067Z"
        fill="white"
      />
      <Path
        d="M6.79296 21.5133L3.46629 17.0666C3.13963 16.6266 3.45296 16 3.99963 16H10.6663C11.213 16 11.5263 16.6266 11.1996 17.0666L7.85963 21.5133C7.59296 21.8733 7.05962 21.8733 6.79296 21.5133Z"
        fill="white"
      />
    </Svg>
  );
};

const CaptureButton = ({ onPress }: { onPress?: () => void }) => {
  const scale = useSharedValue(1);
  const onPressIn = () => {
    scale.value = withTiming(0.8);
  };
  const onPressOut = () => {
    scale.value = withTiming(1);
  };

  const style = useAnimatedStyle(
    () => ({
      transform: [{ scale: scale.value }],
    }),
    [scale],
  );
  return (
    <OuterCircle
      style={style}
      onPress={onPress}
      onPressIn={onPressIn}
      onPressOut={onPressOut}>
      <InnerOval />
    </OuterCircle>
  );
};

const OuterCircle = Animated.createAnimatedComponent(
  styled(Pressable)(({ theme }) => ({
    borderWidth: 6,
    borderColor: theme.colors.palette.white,
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: 'center',
    alignItems: 'center',
  })),
);

const InnerOval = styled(View)(({ theme }) => ({
  backgroundColor: theme.colors.palette.white,
  width: 58,
  height: 58,
  borderRadius: 29,
}));

const orientationCalculation = (
  gamma: number,
  beta: number,
  deviceOrientation: DeviceMotionOrientation,
): DeviceMotionOrientation => {
  if (beta > 0.45) {
    return DeviceMotionOrientation.Portrait;
  } else if (beta < -0.45) {
    return DeviceMotionOrientation.UpsideDown;
  } else if (gamma > 0.45) {
    return DeviceMotionOrientation.LeftLandscape;
  } else if (gamma < -0.45) {
    return DeviceMotionOrientation.RightLandscape;
  } else {
    // Fallback to base orientation
    return deviceOrientation;
  }
};
