import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTheme } from '@emotion/react';
import { Box, Column, LargeBody, LargeLabel } from 'cube-ui-components';
import styled from '@emotion/native';
import { Modal, TouchableOpacity, View } from 'react-native';
import { Asset, getAssetInfoAsync, MediaTypeValue } from 'expo-media-library';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import Gallery from './Gallery';

interface Props {
  visible?: boolean;
  panel?: boolean;
  onDismiss?: () => void;
  onDone?: (result: {
    uri: string;
    width: number;
    height: number;
    name: string;
    isAttachment: boolean;
  }) => void;
  config?: {
    modalAnimationType?: 'none' | 'slide' | 'fade';
  };
  galleryConfig?: {
    mediaType?: MediaTypeValue | MediaTypeValue[];
    showMediaDuration?: boolean;
    isAssetDisabled?: (asset: Asset) => boolean;
    selectionForeground?: ReactNode;
  };
}

export default function GalleryImagePicker({
  visible,
  panel,
  onDismiss,
  onDone,
  config,
  galleryConfig,
}: Props) {
  const { colors, space, borderRadius } = useTheme();
  const { height } = useSafeAreaFrame();
  const { top: topInset } = useSafeAreaInsets();
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => [height - space[22]], [height, space]);
  useEffect(() => {
    if (visible) {
      setSelectedAsset(null);
      bottomSheetModalRef.current?.present();
    } else {
      bottomSheetModalRef.current?.close();
    }
  }, [visible]);

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return React.createElement(BottomSheetBackdrop, {
      ...props,
      appearsOnIndex: 0,
      disappearsOnIndex: -1,
    });
  }, []);
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [colors],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  const onDonePress = useCallback(async () => {
    if (selectedAsset) {
      const assetInfo = await getAssetInfoAsync(selectedAsset);
      onDone?.({
        uri: assetInfo.localUri ?? assetInfo.uri,
        width: selectedAsset.width,
        height: selectedAsset.height,
        name: selectedAsset.filename,
        isAttachment: false,
      });
      onDismiss?.();
    }
  }, [selectedAsset, onDone, onDismiss]);

  return (
    <>
      {panel ? (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              ref={bottomSheetModalRef}
              snapPoints={snapPoints}
              onDismiss={onDismiss}
              handleIndicatorStyle={handleIndicatorStyle}
              handleStyle={handleStyle}
              backdropComponent={renderBackdrop}
              backgroundStyle={backgroundStyle}>
              <Header
                style={{
                  paddingHorizontal: space[3],
                  paddingVertical: space[4] - 2,
                }}>
                <PanelButton>
                  <LargeLabel fontWeight="bold">Recent photo</LargeLabel>
                </PanelButton>
                <Box flex={1} />
                <PanelButton disabled={!selectedAsset} onPress={onDonePress}>
                  <LargeBody
                    color={
                      selectedAsset ? colors.primary : colors.primaryVariant
                    }
                    fontWeight="bold">
                    Select
                  </LargeBody>
                </PanelButton>
              </Header>
              <Gallery
                isBottomSheet
                visible={visible}
                mediaType={galleryConfig?.mediaType}
                selectedAsset={selectedAsset}
                setSelectedAsset={setSelectedAsset}
              />
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      ) : (
        <Modal
          visible={visible}
          statusBarTranslucent
          animationType={config?.modalAnimationType || 'fade'}>
          <Column
            flex={1}
            marginTop={topInset}
            backgroundColor={colors.background}>
            <Header style={{ paddingHorizontal: space[4] }}>
              <Title fontWeight="bold">Photo album</Title>
              <ActionButton onPress={onDismiss}>
                <LargeBody>Cancel</LargeBody>
              </ActionButton>
              <Box flex={1} />
              <ActionButton disabled={!selectedAsset} onPress={onDonePress}>
                <LargeBody
                  color={selectedAsset ? colors.primary : colors.primaryVariant}
                  fontWeight="bold">
                  Done
                </LargeBody>
              </ActionButton>
            </Header>
            <Gallery
              visible={visible}
              mediaType={galleryConfig?.mediaType}
              showMediaDuration={galleryConfig?.showMediaDuration}
              isDisabled={galleryConfig?.isAssetDisabled}
              selectionForeground={galleryConfig?.selectionForeground}
              selectedAsset={selectedAsset}
              setSelectedAsset={setSelectedAsset}
            />
          </Column>
        </Modal>
      )}
    </>
  );
}

const Header = styled(View)(() => ({
  flexDirection: 'row',
}));

const PanelButton = styled(TouchableOpacity)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const ActionButton = styled(TouchableOpacity)(({ theme: { sizes } }) => ({
  height: sizes[11],
  justifyContent: 'center',
  alignItems: 'center',
}));

const Title = styled(LargeLabel)({
  position: 'absolute',
  alignSelf: 'center',
  left: 0,
  right: 0,
  textAlign: 'center',
});
