import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  BottomSheetModalProvider,
  BottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  TouchableHighlight,
} from '@gorhom/bottom-sheet';
import { useTheme } from '@emotion/react';
import { View, StyleSheet } from 'react-native';
import {
  Column,
  H6,
  Icon,
  LargeBody,
  addErrorBottomToast,
} from 'cube-ui-components';
import styled from '@emotion/native';
import GalleryImagePicker from './GalleryImagePicker';
import { Portal } from '@gorhom/portal';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { Buffer } from 'buffer';
import {
  UIImagePickerPresentationStyle,
  launchCameraAsync,
} from 'expo-image-picker';
import { useCameraPermissions } from 'expo-camera';
import * as Crypto from 'expo-crypto';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { InternalImagePickerProps } from '../utils';
import { getMaxSizeInMbErrorMessage } from 'utils/helper/translation';
import useToggle from 'hooks/useToggle';
import CameraPermissionSetting from '../CameraPermissionSetting';
import { compressImage } from 'utils/helper/imageUtils';
import {
  AllDocMimeTypesArray,
  defaultDocTypeToPick,
} from 'components/ImagePicker/config';

export default function ImagePickerPhone({
  title,
  galleryPanel,
  attachmentEnabled,
  selectByLibraryEnabled = true,
  onDismiss,
  onDone,
  config,
  onOpenInAppCamera,
  shouldOpenInAppCamera,
  maxSizeInMB = 5,
  extraDocTypes,
}: InternalImagePickerProps) {
  const { colors, borderRadius, space } = useTheme();
  const bottomSheetModalRef = useRef<BottomSheetModal | null>(null);
  const [galleryModalVisible, setGalleryModalVisible] = useState(false);
  const [
    goToSettingModalVisible,
    showGoToSettingModalVisible,
    hideGoToSettingModalVisible,
  ] = useToggle();
  const [cameraPermissionResponse, requestCameraPermission] =
    useCameraPermissions();

  const onFinishPicking = async ({
    uri,
    width,
    height,
    name,
    isAttachment,
  }: {
    uri: string;
    width: number;
    height: number;
    name: string;
    isAttachment: boolean;
  }) => {
    bottomSheetModalRef?.current?.close();
    onDone?.({ finished: false });
    try {
      const [resizedResult, size] = await compressImage(
        {
          canceled: false,
          assets: [{ uri, width, height }],
        },
        maxSizeInMB,
      );

      // const [resizedResult, size] = await resizeImage(
      //   {
      //     canceled: false,
      //     assets: [{ uri, width, height }],
      //   },
      //   config,
      //   maxSizeInMB,
      // );

      if (!resizedResult || !size) {
        onDone?.({ finished: false, failed: true });
        return;
      }

      onDone?.({
        finished: true,
        file: {
          uri: resizedResult.uri,
          base64: resizedResult.base64 || '',
          name,
          size,
          isAttachment,
        },
      });
    } catch (e) {
      console.log('error while converting image', e);
      onDone?.({ finished: false, failed: true });
    }
  };

  const captureFromExpoCamera = async () => {
    const result = await launchCameraAsync({
      presentationStyle: UIImagePickerPresentationStyle.FULL_SCREEN,
      base64: true,
    });
    if (!result.canceled && result.assets.length >= 1) {
      const image = result.assets[0];
      await onFinishPicking({
        ...image,
        name: image?.fileName ?? Crypto.randomUUID(),
        isAttachment: false,
      });
    }
  };

  const captureFromCamera = async () => {
    const captureImage = async () => {
      if (shouldOpenInAppCamera) {
        return onOpenInAppCamera();
      } else {
        return captureFromExpoCamera();
      }
    };
    if (cameraPermissionResponse?.granted) {
      await captureImage();
    } else if (
      !cameraPermissionResponse?.granted &&
      cameraPermissionResponse?.canAskAgain
    ) {
      const permission = await requestCameraPermission();
      if (permission.granted) {
        await captureImage();
      }
    } else {
      showGoToSettingModalVisible();
    }
  };

  const pickImage = () => {
    setGalleryModalVisible(true);
  };

  const pickDocument = async (extraDocTypes?: AllDocMimeTypesArray) => {
    try {
      const mergedDocTypes = extraDocTypes
        ? [...extraDocTypes, ...defaultDocTypeToPick]
        : defaultDocTypeToPick;

      const document = await DocumentPicker.getDocumentAsync({
        type: mergedDocTypes as string[], // Specify the desired file type here
      });

      bottomSheetModalRef?.current?.close();
      if (!document.canceled) {
        onDone?.({ finished: false });
        const base64 = await convertToBase64(document.assets[0].uri);
        const size = getFileSize(base64);
        if (size >= maxSizeInMB) {
          addErrorBottomToast([
            { message: getMaxSizeInMbErrorMessage(maxSizeInMB) },
          ]);
          onDone?.({ finished: false, failed: true });
          return;
        }
        onDone?.({
          finished: true,
          file: {
            uri: document.assets[0].uri,
            base64: base64 || '',
            name: document.assets[0].name,
            size,
            isAttachment: true,
          },
        });
      }
    } catch (error) {
      console.log('Error picking document:', error);
      onDone?.({ finished: false, failed: true });
    }
  };

  const convertToBase64 = async (fileUri: string) => {
    const fileContent = await FileSystem.readAsStringAsync(fileUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return fileContent;
  };

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return React.createElement(BottomSheetBackdrop, {
      ...props,
      appearsOnIndex: 0,
      disappearsOnIndex: -1,
    });
  }, []);
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const sheetStyle = useMemo(
    () => ({ padding: space[isNarrowScreen ? 3 : 4], paddingTop: 0 }),
    [space, isNarrowScreen],
  );
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [colors],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  return (
    <>
      <Portal>
        <BottomSheetModalProvider>
          <BottomSheetModal
            ref={ref => {
              if (ref) {
                ref.present();
                bottomSheetModalRef.current = ref;
              }
            }}
            snapPoints={[attachmentEnabled ? 300 : 255]}
            onDismiss={onDismiss}
            handleIndicatorStyle={handleIndicatorStyle}
            handleStyle={handleStyle}
            backdropComponent={renderBackdrop}
            backgroundStyle={backgroundStyle}
            style={sheetStyle}>
            <Column>
              <H6 fontWeight="bold" style={{ paddingVertical: space[4] }}>
                {title}
              </H6>
              <OptionButton
                onPress={captureFromCamera}
                underlayColor={colors.palette.fwdOrange[5]}>
                <>
                  <Icon.Camera />
                  <LargeBody>Camera</LargeBody>
                </>
              </OptionButton>
              {selectByLibraryEnabled ? (
                <>
                  <Divider />
                  <OptionButton
                    onPress={pickImage}
                    underlayColor={colors.palette.fwdOrange[5]}>
                    <>
                      <Icon.Photo />
                      <LargeBody>Photo library</LargeBody>
                    </>
                  </OptionButton>
                </>
              ) : null}

              {attachmentEnabled ? (
                <>
                  <Divider />
                  <OptionButton
                    onPress={() => pickDocument(extraDocTypes)}
                    underlayColor={colors.palette.fwdOrange[5]}>
                    <>
                      <Icon.DocumentCopy />
                      <LargeBody>Attach file</LargeBody>
                    </>
                  </OptionButton>
                </>
              ) : null}
            </Column>
          </BottomSheetModal>
        </BottomSheetModalProvider>
        <GalleryImagePicker
          visible={galleryModalVisible}
          panel={galleryPanel}
          onDismiss={() => setGalleryModalVisible(false)}
          onDone={onFinishPicking}
          galleryConfig={{
            selectionForeground: <SelectionForeground />,
          }}
        />
      </Portal>
      <CameraPermissionSetting
        visible={goToSettingModalVisible}
        onDismiss={hideGoToSettingModalVisible}
      />
    </>
  );
}

const OptionButton = styled(TouchableHighlight)(({ theme }) => ({
  flexDirection: 'row',
  paddingVertical: theme.space[3],
  marginVertical: theme.space[2],
  gap: 8,
}));

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

const SelectionForeground = styled(View)(({ theme }) => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: theme.colors.background,
  opacity: 0.7,
}));

export const getFileSize = (base64String: string) => {
  const binaryData = Buffer.from(base64String, 'base64');
  const fileSizeInBytes = binaryData.length;
  const fileSizeInMB = fileSizeInBytes / (1024 * 1024); // Convert bytes to megabytes

  return fileSizeInMB;
};
