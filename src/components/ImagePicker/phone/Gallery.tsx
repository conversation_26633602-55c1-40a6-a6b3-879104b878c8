import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { BottomSheetFlatList } from '@gorhom/bottom-sheet';
import { Button, Column, LargeBody, Typography } from 'cube-ui-components';
import * as Application from 'expo-application';
import { Asset, MediaTypeValue } from 'expo-media-library';
import {
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
} from 'react';
import {
  FlatList,
  Image,
  Pressable,
  useWindowDimensions,
  StyleSheet,
} from 'react-native';
import { useMediaLibraryAssets } from 'hooks/useMediaLibraryAssets';
import { goToAppSettings } from 'utils/helper/permissionUtils';

type Props = {
  isBottomSheet?: boolean;
  visible?: boolean;
  mediaType?: MediaTypeValue | MediaTypeValue[];
  showMediaDuration?: boolean;
  isDisabled?: (asset: Asset) => boolean;
  selectionForeground?: ReactNode;
  selectedAsset: Asset | null | undefined;
  setSelectedAsset: Dispatch<SetStateAction<Asset | null | undefined>>;
};

const Gallery = ({
  isBottomSheet,
  visible,
  mediaType,
  showMediaDuration = false,
  isDisabled,
  selectionForeground,
  selectedAsset,
  setSelectedAsset,
}: Props) => {
  const { space } = useTheme();
  const { width } = useWindowDimensions();
  const imageSize = width / 4;

  const { assets, permissionResponse, requestPermission } =
    useMediaLibraryAssets({ mediaType });

  useEffect(() => {
    if (
      visible &&
      !permissionResponse?.granted &&
      permissionResponse?.canAskAgain
    ) {
      requestPermission();
    }
  }, [
    visible,
    permissionResponse?.granted,
    permissionResponse?.canAskAgain,
    requestPermission,
  ]);

  const onSelectAsset = useCallback(
    (asset: Asset) =>
      setSelectedAsset(prev => (prev?.uri !== asset.uri ? asset : null)),
    [setSelectedAsset],
  );
  const List = isBottomSheet ? BottomSheetFlatList : FlatList;

  if (!permissionResponse) return null;

  if (!permissionResponse.granted) {
    return (
      <Column flex={1} justifyContent="center" alignItems="center">
        <LargeBody style={{ textAlign: 'center' }}>
          Allow {Application.applicationName} access to your photos.
        </LargeBody>
        <Button
          style={{ marginTop: space[4] }}
          text={permissionResponse?.canAskAgain ? 'Turn on' : 'Go to settings'}
          onPress={() => {
            if (permissionResponse?.canAskAgain) {
              requestPermission();
            } else {
              goToAppSettings();
            }
          }}
        />
      </Column>
    );
  }

  return (
    <List
      data={assets}
      keyExtractor={item => item.uri}
      numColumns={4}
      renderItem={({ item }) => (
        <Pressable
          onPress={() => {
            if (isDisabled?.(item)) return;
            onSelectAsset(item);
          }}
          style={{ width: imageSize, height: imageSize, overflow: 'hidden' }}>
          <Image
            style={{ flex: 1 }}
            source={{ uri: item.uri }}
            resizeMode="cover"
          />
          <AssetOverlay>
            {showMediaDuration && item.duration !== 0 && (
              <DurationText>{item.duration.toFixed(0)}s</DurationText>
            )}
            {isDisabled?.(item) && <DimBackground />}
            {item === selectedAsset && selectionForeground}
          </AssetOverlay>
        </Pressable>
      )}
    />
  );
};

const AssetOverlay = styled.View(() => ({
  ...StyleSheet.absoluteFillObject,
}));

const DurationText = styled(Typography.SmallBody)(
  ({ theme: { colors, space } }) => ({
    position: 'absolute',
    right: space[2],
    bottom: space[2],
    color: colors.palette.white,
  }),
);

const DimBackground = styled.View(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.palette.black,
  opacity: 0.6,
}));

export default Gallery;
