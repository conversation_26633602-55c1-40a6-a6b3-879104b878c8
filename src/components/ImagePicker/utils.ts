import * as ExpoImagePicker from 'expo-image-picker';
import { addErrorBottomToast } from 'cube-ui-components';
import {
  ImageResult,
  ImageManipulator,
  SaveFormat,
} from 'expo-image-manipulator';
import { Buffer } from 'buffer';
import { OcrRole } from 'types/event';
import { getMaxSizeInMbErrorMessage } from 'utils/helper/translation';
import { AllDocMimeTypesArray } from 'components/ImagePicker/config';

export const getFileSize = (base64String: string) => {
  const binaryData = Buffer.from(base64String, 'base64');
  const fileSizeInBytes = binaryData.length;
  const fileSizeInMB = fileSizeInBytes / (1024 * 1024); // Convert bytes to megabytes

  return fileSizeInMB;
};

export interface OcrFeatureProps {
  role?: OcrRole;
  showOcr?: () => void;
}

export interface InternalImagePickerProps extends ImagePickerProps {
  onOpenInAppCamera: () => void;
}

export type ImagePickerOnDoneProps =
  | {
      finished: false;
      failed?: boolean;
      file?: null | undefined;
    }
  | {
      finished: true;
      failed?: boolean;
      file: ImagePickerFile;
    };

export interface ImagePickerProps extends OcrFeatureProps {
  visible?: boolean;
  title?: string;
  galleryPanel?: boolean;
  attachmentEnabled?: boolean;
  selectByLibraryEnabled?: boolean;
  onDismiss?: () => void;
  onCamera?: () => void;
  onDone?: (result: ImagePickerOnDoneProps) => void;
  config?: {
    maxWidth?: number;
    maxHeight?: number;
    compression?: number; // min: 0 - max: 1
    cameraQuality?: number;
  };
  maxSizeInMB?: number;
  shouldOpenInAppCamera?: boolean;
  extraDocTypes?: AllDocMimeTypesArray;
}

export interface ImagePickerFile {
  uri: string;
  size: number;
  base64: string;
  name: string;
  isAttachment?: boolean;
  fromOcr?: boolean;
}

export const resizeImage = async (
  result: ExpoImagePicker.ImagePickerResult,
  config: ImagePickerProps['config'],
  maxSizeInMB: number,
): Promise<[ImageResult | null, number | null]> => {
  if (result.canceled) return [null, null];
  let compression = config?.compression || 1;
  let resizedResult: ImageResult;
  let size: number;

  const { height, width, uri } = result.assets[0];

  const imageRef = await ImageManipulator.manipulate(uri)
    .resize({
      height: config?.maxHeight ? Math.min(config.maxHeight, height) : height,
      width: config?.maxWidth ? Math.min(config.maxWidth, width) : width,
    })
    .renderAsync();

  const manipulatedResult = await imageRef.saveAsync({
    base64: true,
    format: SaveFormat.JPEG,
    compress: compression,
  });

  resizedResult = manipulatedResult;

  if (!resizedResult.base64) {
    addErrorBottomToast([
      { message: `Failed to process image` }, //FIXME: translation
    ]);
    return [null, null];
  }
  size = getFileSize(resizedResult.base64);

  if (size > maxSizeInMB) {
    compression = compression * (maxSizeInMB / size) - 0.01; //- 0.01: subtracts a small buffer to ensure we get under the target size

    const imageRef = await ImageManipulator.manipulate(uri)
      .resize({
        height: config?.maxHeight ? Math.min(config.maxHeight, height) : height,
        width: config?.maxWidth ? Math.min(config.maxWidth, width) : width,
      })
      .renderAsync();

    const manipulatedResult = await imageRef.saveAsync({
      base64: true,
      format: SaveFormat.JPEG,
      compress: compression,
    });

    resizedResult = manipulatedResult;

    if (!resizedResult.base64) {
      addErrorBottomToast([
        { message: `Failed to process image` }, //FIXME: translation
      ]);
      return [null, null];
    }

    size = getFileSize(resizedResult.base64);
  }

  if (size >= maxSizeInMB) {
    addErrorBottomToast([
      {
        message: getMaxSizeInMbErrorMessage(maxSizeInMB),
      },
    ]);
    return [null, null];
  }
  return [resizedResult, size];
};
