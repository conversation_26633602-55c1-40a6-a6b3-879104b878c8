import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, ExtraLargeBody, addErrorBottomToast } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  TouchableOpacity,
  useWindowDimensions,
  StyleSheet,
} from 'react-native';
import { Portal } from '@gorhom/portal';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import * as ExpoImagePicker from 'expo-image-picker';
import * as DocumentPickerLibs from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import { getFileSize, InternalImagePickerProps } from '../utils';
import { getMaxSizeInMbErrorMessage } from 'utils/helper/translation';
import CameraPermissionSetting from '../CameraPermissionSetting';
import useToggle from 'hooks/useToggle';
import { compressImage } from 'utils/helper/imageUtils';
import {
  AllDocMimeTypesArray,
  defaultDocTypeToPick,
} from 'components/ImagePicker/config';

export default function ImagePickerTablet({
  attachmentEnabled,
  selectByLibraryEnabled = true,
  onDismiss,
  onDone,
  config,
  maxSizeInMB = 5,
  onOpenInAppCamera,
  shouldOpenInAppCamera,
  extraDocTypes,
}: InternalImagePickerProps) {
  const { t } = useTranslation(['eApp']);

  const { space, borderRadius } = useTheme();
  const { height } = useWindowDimensions();
  const [
    goToSettingModalVisible,
    showGoToSettingModalVisible,
    hideGoToSettingModalVisible,
  ] = useToggle();

  const [cameraStatus, requestCameraPermission] =
    ExpoImagePicker.useCameraPermissions();
  const [mediaStatus, requestMediaPermission] =
    ExpoImagePicker.useMediaLibraryPermissions();

  const captureFromExpoCamera = async () => {
    const result = await ExpoImagePicker.launchCameraAsync();
    const [resizedResult, size] = await compressImage(result, maxSizeInMB);

    // const [resizedResult, size] = await resizeImage(
    //   result,
    //   config,
    //   maxSizeInMB,
    // );
    if (!result.canceled && resizedResult) {
      onDone?.({
        finished: true,
        file: {
          base64: resizedResult.base64 || '',
          uri: resizedResult.uri,
          size: size || 0,
          name: result.assets[0].fileName || Crypto.randomUUID(),
          isAttachment: false,
        },
      });
    }
  };

  const onSelectByCamera = async () => {
    const captureImage = async () => {
      onDismiss?.();
      if (shouldOpenInAppCamera) {
        return onOpenInAppCamera();
      } else {
        return captureFromExpoCamera();
      }
    };

    if (cameraStatus?.granted) {
      await captureImage();
    } else if (!cameraStatus?.granted && cameraStatus?.canAskAgain) {
      const permission = await requestCameraPermission();
      if (permission.granted) {
        await captureImage();
      }
    } else {
      showGoToSettingModalVisible();
    }
  };

  const onSelectByLibrary = async () => {
    onDismiss?.();
    if (mediaStatus?.granted === true) {
      const restMediaPermission = await requestMediaPermission();
      if (!restMediaPermission.granted) {
        addErrorBottomToast([
          { message: 'Not granted' }, // FIXME: translation
        ]);
        return;
      }
    }

    const result = await ExpoImagePicker.launchImageLibraryAsync({
      mediaTypes: ExpoImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: false,
      allowsEditing: true,
      quality: 1,
    });

    const [resizedResult, size] = await compressImage(result, maxSizeInMB);

    // const [resizedResult, size] = await resizeImage(
    //   result,
    //   config,
    //   maxSizeInMB,
    // );
    if (!result.canceled && resizedResult) {
      onDone?.({
        finished: true,
        file: {
          base64: resizedResult.base64 || '',
          uri: resizedResult.uri,
          size: size || 0,
          name: result.assets[0].fileName || Crypto.randomUUID(),
          isAttachment: false,
        },
      });
    }
  };

  const onSelectByDocument = async (extraDocTypes?: AllDocMimeTypesArray) => {
    onDismiss?.();
    try {
      const mergedDocTypes = extraDocTypes
        ? [...extraDocTypes, ...defaultDocTypeToPick]
        : defaultDocTypeToPick;
      const document = await DocumentPickerLibs.getDocumentAsync({
        type: mergedDocTypes as string[], // Specify the desired file type here
      });

      if (!document.canceled) {
        const base64 = await convertToBase64(document.assets[0].uri);
        const size = getFileSize(base64);
        if (size >= maxSizeInMB) {
          addErrorBottomToast([
            { message: getMaxSizeInMbErrorMessage(maxSizeInMB) },
          ]);
          return;
        }
        onDone?.({
          finished: true,
          file: {
            uri: document.assets[0].uri,
            base64: base64 || '',
            size: size,
            name: document.assets[0].name,
            isAttachment: true,
          },
        });
      }
    } catch (error) {
      console.log('Error picking document:', error);
    }
  };

  const convertToBase64 = async (fileUri: string) => {
    const fileContent = await FileSystem.readAsStringAsync(fileUri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return fileContent;
  };

  return (
    <>
      <Portal>
        <Backdrop
          renderToHardwareTextureAndroid
          entering={FadeIn}
          exiting={FadeOut}>
          <Container style={{ maxHeight: height - space[16] }}>
            <TouchableOpacity onPress={onSelectByCamera}>
              <Wrapper
                borderTopLeftRadius={borderRadius.large}
                borderTopRightRadius={borderRadius.large}
                borderBottomLeftRadius={
                  attachmentEnabled || selectByLibraryEnabled
                    ? 0
                    : borderRadius.large
                }
                borderBottomRightRadius={
                  attachmentEnabled || selectByLibraryEnabled
                    ? 0
                    : borderRadius.large
                }>
                <ExtraLargeBody color="#007AFF">
                  {t('eApp:documentUpload.takePhotoWithCamera')}
                </ExtraLargeBody>
              </Wrapper>
            </TouchableOpacity>
            {selectByLibraryEnabled && (
              <TouchableOpacity onPress={onSelectByLibrary}>
                <Wrapper
                  mt={1}
                  borderBottomLeftRadius={
                    attachmentEnabled ? 0 : borderRadius.large
                  }
                  borderBottomRightRadius={
                    attachmentEnabled ? 0 : borderRadius.large
                  }>
                  <ExtraLargeBody color="#007AFF">
                    {t('eApp:documentUpload.selectFromAlbum')}
                  </ExtraLargeBody>
                </Wrapper>
              </TouchableOpacity>
            )}
            {attachmentEnabled && (
              <TouchableOpacity
                onPress={() => onSelectByDocument(extraDocTypes)}>
                <Wrapper
                  borderBottomLeftRadius={borderRadius.large}
                  borderBottomRightRadius={borderRadius.large}
                  mt={1}>
                  <ExtraLargeBody color="#007AFF">
                    {t('eApp:documentUpload.attachFile')}
                  </ExtraLargeBody>
                </Wrapper>
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={onDismiss}>
              <Wrapper mt={space[7]} borderRadius={borderRadius.large}>
                <ExtraLargeBody color="#007AFF">
                  {t('eApp:cancel')}
                </ExtraLargeBody>
              </Wrapper>
            </TouchableOpacity>
          </Container>
        </Backdrop>
      </Portal>
      <CameraPermissionSetting
        visible={goToSettingModalVisible}
        onDismiss={hideGoToSettingModalVisible}
      />
    </>
  );
}

const Wrapper = styled(Box)(({ theme: { space } }) => ({
  paddingVertical: space[5],
  width: space[90],
  backgroundColor: 'rgba(242, 242, 242, 1)',
  alignItems: 'center',
}));

const Backdrop = styled(Animated.View)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'flex-end',
  alignItems: 'center',
}));

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  const { isWideScreen } = useWindowAdaptationHelpers();
  return {
    alignSelf: isWideScreen ? 'auto' : 'stretch',
    maxWidth: isWideScreen ? 460 : undefined,
    marginBottom: space[10],
  };
});
