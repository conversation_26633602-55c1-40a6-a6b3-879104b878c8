import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import LoadingIndicator from 'components/LoadingIndicator';
import { EmptyCase } from 'components/Message/EmptyCase';
import { Box, H5, H7, H8, Icon, Text } from 'cube-ui-components';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import SearchTextInput from './SearchTextInput';

const CloseButtonContainer = styled.TouchableOpacity({
  alignItems: 'flex-end',
});

interface Props {
  visible: boolean;
  onClose?: () => void;
  query?: string;
  onChangeQuery?: (query: string) => void;
  resultCount: number;
  loading: boolean;
  label?: string;
  placeholder: string;
  hint?: string;
  children?: React.ReactNode;
  filterComponent?: React.ReactNode;
}

const SearchModal: React.FC<Props> = ({
  visible,
  onClose,
  query,
  onChangeQuery,
  resultCount,
  children,
  loading,
  label,
  placeholder,
  hint,
  filterComponent,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { width: maxWidth, height: maxHeight } = useSafeAreaFrame();
  const insets = useSafeAreaInsets();
  const [controlledVisible, setControlledVisible] = useState(visible);

  const widthValue = useSharedValue(0);
  const heightValue = useSharedValue(0);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      width: widthValue.value,
      height: heightValue.value,
      borderBottomLeftRadius: (maxWidth - widthValue.value) * 2,
    };
  });

  useEffect(() => {
    if (visible) {
      setControlledVisible(visible);
      widthValue.value = withTiming(maxWidth, { duration: 200 });
      heightValue.value = withTiming(maxHeight, { duration: 500 });
    } else {
      widthValue.value = withTiming(0, { duration: 500 });
      heightValue.value = withTiming(0, { duration: 300 });
      setTimeout(() => {
        setControlledVisible(visible);
      }, 500);
    }
  }, [visible]);

  return (
    <Modal visible={controlledVisible} transparent statusBarTranslucent>
      <Animated.View
        style={[
          {
            position: 'absolute',
            zIndex: 9999,
            top: 0,
            right: 0,
            backgroundColor: theme.colors.primary,
            overflow: 'hidden',
          },
          animatedStyles,
        ]}>
        <Box
          px={theme.sizes[4]}
          pt={insets.top + theme.sizes[4]}
          pb={theme.sizes[5]}>
          <CloseButtonContainer onPress={onClose}>
            <Icon.CloseCircleFill fill={theme.colors.palette.white} />
          </CloseButtonContainer>

          <H5 fontWeight="bold" color={theme.colors.palette.white}>
            {label}
          </H5>

          <SearchTextInput
            value={query}
            onChangeText={onChangeQuery}
            placeholder={placeholder}
          />

          {hint && (
            <Box mt={theme.sizes[2]}>
              <H8 color={theme.colors.palette.white}>{hint}</H8>
            </Box>
          )}

          {query && !loading && filterComponent && filterComponent}

          {query && !loading && (
            <Box mt={theme.space[2]}>
              <Text color={theme.colors.palette.white}>
                {t('foundXResults', { count: resultCount })}
              </Text>
            </Box>
          )}
        </Box>

        {loading && (
          <Box alignItems="center">
            <LoadingIndicator
              color={theme.colors.palette.white}
              size={theme.sizes[8]}
            />
          </Box>
        )}

        {query && !loading && resultCount === 0 && (
          <Box flex={1} bgColor={theme.colors.surface}>
            <Box mt={theme.sizes[25]} alignItems="center" mx={theme.sizes[10]}>
              <EmptyCase />
              <H7
                color={theme.colors.palette.fwdGreyDarker}
                style={{ paddingTop: theme.space[4] }}>
                {t('noResultsFound')}
              </H7>
              <H7
                color={theme.colors.palette.fwdGreyDarker}
                style={{ textAlign: 'center' }}>
                {t('searchSuggestion')}
              </H7>
            </Box>
          </Box>
        )}
        {visible && !loading && resultCount > 0 ? (
          <Box flex={1}>{children}</Box>
        ) : null}
      </Animated.View>
    </Modal>
  );
};

export default SearchModal;
