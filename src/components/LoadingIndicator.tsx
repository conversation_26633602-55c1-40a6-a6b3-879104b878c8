import { useTheme } from '@emotion/react';
import { memo, useEffect } from 'react';
import Animated, {
  cancelAnimation,
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

interface LoadingIndicatorProps {
  color?: string;
  size?: number;
}

const LoadingIndicator = memo(function LoadingIndicator({
  color,
  size = 24,
}: LoadingIndicatorProps) {
  const rotation = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [{ rotateZ: `${rotation.value}deg` }],
    }),
    [rotation.value],
  );

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 1500,
        easing: Easing.linear,
      }),
      -1,
    );
    return () => cancelAnimation(rotation);
  }, [rotation]);

  const theme = useTheme();

  return (
    <Animated.View
      style={[
        {
          width: size,
          height: size,
          alignItems: 'center',
          justifyContent: 'center',
        },
        animatedStyle,
      ]}>
      <Spinner fill={color ?? theme.colors.primary} />
    </Animated.View>
  );
});

const Spinner = memo(({ fill }: { fill: string }) => (
  <Svg width="100%" height="100%" viewBox="0 0 24 24" fill={fill}>
    <Path
      fillRule="evenodd"
      d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10h-2a8 8 0 1 1-8-8V2Z"
      clipRule="evenodd"
    />
  </Svg>
));

export default LoadingIndicator;
