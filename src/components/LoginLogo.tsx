import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function LoginLogo(props: SvgIconProps) {
  return (
    <Svg
      viewBox="0 0 167 24"
      fill="none"
      width={props.width || 167}
      height={props.height || 24}
      {...props}>
      <Path
        d="M56.295 11.57a.802.802 0 010 1.142l-4.077 3.942a.864.864 0 01-.91.174.805.805 0 01-.512-.744V8.198a.81.81 0 01.512-.746.863.863 0 01.911.175l4.076 3.944z"
        fill="#E87722"
      />
      <Path
        d="M30.736 1.272c-.484.38-.83.833-1.043 1.36-.212.524-.447 1.287-.722 2.291l-.215.789-2.65 10.17-3.088-13.01c-.263-1.243-.977-2.01-2.745-2.01H1.401C.534.86.503 1.754.5 1.762v21.082s.015.448.458.448H2.71c1.26 0 2.869-.793 2.869-2.977V13.89h6.609c2.52 0 3.134-1.755 3.134-2.754v-.37c0-.354-.122-.57-.56-.57H5.58V4.879h12.982l3.917 14.717c.12.44.805 2.768.805 2.768.265.818.235.936.948.936h1.587c1.95 0 2.623-1.173 2.968-1.999.173-.448.295-.646.575-1.657l3.454-12.687 3.653 12.624.757 2.778c.263.815.236.94.949.94h1.585c1.952 0 2.626-1.174 2.971-2.006.17-.448.34-.877.62-1.886l3.59-14.515H58.062c.956 0 1.725.081 2.42.285.896.264 1.693.919 2.358 1.945.644 1.006 1.012 2.768 1.03 4.957-.002 2.466-.759 4.656-2.163 5.84-.313.275-.68.49-1.116.646-.443.16-.872.26-1.279.3-.439.044-1.018.13-1.781.137h-.011l-2.426.004H51.61l-.017-.004c-.755 0-.778.427-.778.61v.883c0 1.285 1.087 2.761 3.27 2.761h.075l4.232-.002a18.931 18.931 0 002.91-.232 9.645 9.645 0 002.455-.745 8.497 8.497 0 002.11-1.385 9.774 9.774 0 001.988-2.518c.51-.938.886-1.983 1.123-3.133.223-1.086.334-1.86.334-3.148v-.027c-.038-4.17-1.3-7.136-3.818-9.207a7.901 7.901 0 00-3.249-1.66C61.134.925 59.821.79 58.321.79H44.848c-.74.008-1.847.38-2.274 2.077l-2.922 12.927L36.545 5.66v.008l-.208-.747c-.3-1.08-.541-1.856-.72-2.325-.182-.468-.5-.9-.967-1.3C34.188.897 33.535.7 32.699.7c-.831 0-1.482.19-1.963.572z"
        fill="#183028"
      />
      <Path
        d="M149.762 22.588V1.412h16.551v2.963h-13.066v5.896h11.697v2.964h-11.697v6.39H166.5v2.963h-16.738zM129.028 22.588V1.412h11.324c1.182 0 2.219.216 3.111.648.892.412 1.587 1.009 2.085 1.79.518.783.777 1.698.777 2.748 0 .803-.155 1.533-.466 2.192a4.532 4.532 0 01-1.245 1.667 5.23 5.23 0 01-1.773 1.018v.124a5.17 5.17 0 012.053.926 4.431 4.431 0 011.463 1.729c.373.7.56 1.543.56 2.53 0 1.338-.301 2.44-.903 3.304a5.166 5.166 0 01-2.333 1.914c-.975.39-2.084.586-3.329.586h-11.324zm3.484-2.963h7.56c.954 0 1.732-.247 2.334-.741.622-.515.933-1.338.933-2.47 0-.679-.135-1.255-.405-1.728a2.4 2.4 0 00-1.151-1.112c-.518-.267-1.172-.4-1.96-.4h-7.311v6.45zm0-9.354h7.031c.664 0 1.234-.123 1.711-.37.477-.247.84-.597 1.089-1.05.27-.452.405-.957.405-1.512 0-1.009-.28-1.75-.84-2.223-.54-.494-1.255-.74-2.147-.74h-7.249v5.895zM88.784 22.588c-2.174 0-4.027-.38-5.56-1.14-1.511-.78-2.67-1.983-3.478-3.606-.808-1.623-1.212-3.688-1.212-6.195 0-3.698.901-6.441 2.703-8.229C83.059 1.61 85.585.706 88.815.706c1.781 0 3.376.298 4.784.894 1.429.596 2.547 1.49 3.355 2.681.828 1.171 1.242 2.671 1.242 4.5h-3.541c0-1.13-.238-2.075-.715-2.836-.476-.76-1.16-1.335-2.05-1.725-.87-.39-1.884-.586-3.043-.586-1.45 0-2.682.288-3.697.863-.994.555-1.75 1.397-2.267 2.527-.497 1.13-.746 2.569-.746 4.315v.678c0 1.747.249 3.185.746 4.315.517 1.13 1.273 1.972 2.267 2.527.994.534 2.226.801 3.697.801 1.2 0 2.246-.184 3.137-.554.89-.39 1.574-.966 2.05-1.726.497-.76.745-1.706.745-2.836h3.417c0 1.829-.414 3.34-1.242 4.53-.808 1.193-1.926 2.076-3.355 2.651-1.408.576-3.013.863-4.815.863z"
        fill="#E87722"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M110.982.162c.552.268.902.824.902 1.434v10.012a2.243 2.243 0 002.252 2.235h1.204V7.496c0-.492.228-.956.619-1.259l7.626-5.9a1.619 1.619 0 011.695-.175c.552.268.902.824.902 1.434V15.44c0 .446-.188.872-.519 1.174l-7.625 6.965a1.617 1.617 0 01-1.09.422h-6.577c-5.153 0-9.329-4.145-9.329-9.257V7.496c0-.492.228-.956.619-1.259l7.626-5.9a1.619 1.619 0 011.695-.175zm4.358 16.873h-1.204a5.49 5.49 0 01-1.921-.345l-4.691 3.421c.85.445 1.819.697 2.847.697h4.969v-3.773zm3.217 1.73v-1.73h1.894l-1.894 1.73zm4.408-4.922h-4.408V8.276l4.408-3.411v8.978zm-17.822 4.044l4.455-3.25a5.37 5.37 0 01-.931-3.029V4.865l-4.408 3.41v6.468c0 1.15.323 2.227.884 3.144z"
        fill="#FFECDE"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M101.339 7.47c0-.705.576-1.277 1.287-1.277h14.298c.711 0 1.287.572 1.287 1.277s-.576 1.277-1.287 1.277h-14.298a1.282 1.282 0 01-1.287-1.277z"
        fill="#FFECDE"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M116.948 5.9c.888 0 1.608.715 1.608 1.596v14.908c0 .881-.72 1.596-1.608 1.596a1.603 1.603 0 01-1.609-1.596V7.496c0-.881.721-1.596 1.609-1.596z"
        fill="#E87722"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M102.65 5.9c.888 0 1.609.715 1.609 1.596v7.247c0 3.35 2.736 6.065 6.112 6.065h6.577c.889 0 1.609.715 1.609 1.596 0 .881-.72 1.596-1.609 1.596h-6.577c-5.153 0-9.329-4.145-9.329-9.257V7.496c0-.881.72-1.596 1.608-1.596z"
        fill="#E87722"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M108.965 1.277c0-.705.576-1.277 1.287-1.277h14.298c.71 0 1.287.572 1.287 1.277s-.577 1.277-1.287 1.277h-14.298a1.282 1.282 0 01-1.287-1.277z"
        fill="#FFECDE"
      />
      <Path
        d="M122.95 1.596c0-.881.72-1.596 1.608-1.596.889 0 1.609.715 1.609 1.596 0 .882-.72 1.596-1.609 1.596a1.602 1.602 0 01-1.608-1.596zM108.666 1.596c0-.881.72-1.596 1.609-1.596.888 0 1.608.715 1.608 1.596 0 .882-.72 1.596-1.608 1.596a1.602 1.602 0 01-1.609-1.596zM122.95 15.418c0-.882.72-1.596 1.608-1.596.889 0 1.609.714 1.609 1.596 0 .881-.72 1.596-1.609 1.596a1.602 1.602 0 01-1.608-1.596z"
        fill="#F3BB90"
      />
    </Svg>
  );
}

export default LoginLogo;
