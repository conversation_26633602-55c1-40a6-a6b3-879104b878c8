import {
  Icon,
  TextField,
  TextFieldProps,
  TextFieldRef,
} from 'cube-ui-components';
import { format } from 'date-fns';
import useToggle from 'hooks/useToggle';
import React, {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Keyboard,
  Pressable,
  StyleProp,
  TouchableOpacity,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';
import Calendar from './Calendar';
import { CalendarLocalization, DatePickerMode } from './types';
import { isValidDate } from 'utils/helper/dateUtil';

export interface DatePickerCalendarProps extends ViewProps {
  label?: string;
  modeSwitchEnabled?: boolean;
  initialMode?: DatePickerMode;
  disabled?: boolean;
  hint?: string;
  placeholder?: string;
  error?: string;
  isError?: boolean;
  value?: Date;
  onChange?: (date: Date, mode?: DatePickerMode) => void;
  minDate?: Date;
  maxDate?: Date;
  formatDate?: (date: Date, mode: DatePickerMode) => string;
  localization?: CalendarLocalization;
  defaultDate?: Date;
  onFocus?: () => void;
  onBlur?: () => void;
  highlight?: boolean;
  renderInput?: (
    props: Partial<Omit<TextFieldProps, 'value'> & { value?: string }>,
  ) => React.ReactElement<
    Partial<Omit<TextFieldProps, 'value'> & { value?: string }>
  >;
  preventPopup?: boolean;
  inputContainerStyle?: StyleProp<ViewStyle>;
}

export type DatePickerRef = TextFieldRef;

function DatePickerInner(
  {
    label,
    modeSwitchEnabled,
    initialMode = 'full',
    disabled,
    hint,
    placeholder,
    error,
    isError,
    value,
    onChange,
    minDate,
    maxDate,
    formatDate = (date: Date) => format(date, 'dd/MM/yyyy'),
    localization,
    defaultDate,
    onFocus: onFocusProp,
    onBlur: onBlurProp,
    highlight,
    renderInput,
    preventPopup,
    inputContainerStyle,
    ...viewProps
  }: DatePickerCalendarProps,
  ref: React.ForwardedRef<DatePickerRef>,
) {
  const [visible, show, hide] = useToggle();
  const textFieldRef = useRef<TextFieldRef>(null);
  const [mode, setMode] = useState<DatePickerMode>(initialMode);

  const displayValue = useMemo(
    () => (value ? formatDate(value, mode) : ''),
    [value, formatDate, mode],
  );

  useImperativeHandle(ref, () => textFieldRef.current as DatePickerRef);

  const defaultValue = useMemo(() => {
    if (value && isValidDate(value)) {
      return value;
    }
    return isValidDate(defaultDate) ? defaultDate : undefined;
  }, [defaultDate, value]);

  const onFocus = () => {
    if (!preventPopup) {
      show();
      onFocusProp?.();
    }
    Keyboard.dismiss();
  };

  const onBlur = () => {
    hide();
    setTimeout(() => {
      textFieldRef.current?.blur();
      onBlurProp?.();
    }, 100);
  };

  return (
    <TouchableOpacity {...viewProps} activeOpacity={1} onPress={onFocus}>
      <View pointerEvents="none">
        {renderInput ? (
          renderInput({
            // @ts-expect-error Property 'ref' does not exist on type 'IntrinsicAttributes & Partial<TextFieldProps>'
            ref: textFieldRef,
            label: label,
            value: displayValue,
            disabled: disabled,
            hint: hint,
            placeholder: placeholder,
            error: error,
            isError: isError,
            onFocus,
            onBlur: Keyboard.dismiss,
            right: disabled ? null : (
              <Pressable
                onPress={() => {
                  textFieldRef?.current?.focus();
                }}>
                <Icon.Calendar />
              </Pressable>
            ),
            showSoftInputOnFocus: false,
            highlight: highlight,
            editable: false,
          })
        ) : (
          <TextField
            ref={textFieldRef}
            label={label}
            value={displayValue}
            disabled={disabled}
            hint={hint}
            placeholder={placeholder}
            error={error}
            isError={isError}
            editable={false}
            onFocus={onFocus}
            onBlur={Keyboard.dismiss}
            right={
              disabled ? null : (
                <Pressable
                  onPress={() => {
                    textFieldRef?.current?.focus();
                  }}>
                  <Icon.Calendar />
                </Pressable>
              )
            }
            showSoftInputOnFocus={false}
            highlight={highlight}
            inputContainerStyle={inputContainerStyle}
          />
        )}
      </View>
      <Calendar
        key={String(value)}
        visible={visible}
        modeSwitchEnabled={modeSwitchEnabled}
        mode={mode}
        onChangeMode={setMode}
        value={isValidDate(value) ? value : undefined}
        onChange={(value, mode) => {
          onChange?.(value, mode);
          onBlur();
        }}
        minDate={minDate}
        maxDate={maxDate}
        onCancel={onBlur}
        localization={localization}
        defaultDate={defaultValue}
      />
    </TouchableOpacity>
  );
}

const DatePickerCalendar = forwardRef(DatePickerInner);
export default DatePickerCalendar;
