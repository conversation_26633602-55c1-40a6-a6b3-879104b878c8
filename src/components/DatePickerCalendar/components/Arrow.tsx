import { useTheme } from '@emotion/react';
import React from 'react';
import Animated, {
  createAnimatedPropAdapter,
  interpolateColor,
  processColor,
  SharedValue,
  useAnimatedProps,
} from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

const AnimatedSvg = Animated.createAnimatedComponent(Svg);
const AnimatedPath = Animated.createAnimatedComponent(Path);

const Arrow = ({
  type,
  animatedValue,
}: {
  type: 'left' | 'right';
  animatedValue?: SharedValue<number>;
}) => {
  const { colors } = useTheme();

  const adapter = createAnimatedPropAdapter(
    props => {
      if (Object.keys(props).includes('fill')) {
        props.fill = {
          type: 0,
          payload: processColor(props.fill as string),
        };
      }
    },
    ['fill'],
  );

  const props = useAnimatedProps(
    () => {
      return {
        fill: animatedValue
          ? interpolateColor(
              animatedValue.value,
              [0, 1, 2],
              [colors.primary, colors.primary, colors.placeholder],
            )
          : colors.primary,
      };
    },
    [],
    adapter,
  );

  return (
    <AnimatedSvg width="25" height="24" viewBox="0 0 25 24">
      {type === 'left' && (
        <AnimatedPath
          fillRule="evenodd"
          animatedProps={props}
          clipRule="evenodd"
          d="M14.1467 19.732L6.50024 12.0855L14.1467 4.4395C14.7322 3.8535 15.6822 3.8535 16.2682 4.4395L16.6212 4.7925L9.32824 12.0855L16.6212 19.3785L16.2682 19.732C15.6822 20.3175 14.7322 20.3175 14.1467 19.732Z"
        />
      )}

      {type === 'right' && (
        <AnimatedPath
          animatedProps={props}
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.8535 19.732L8.5 19.3785L15.793 12.0855L8.5 4.7925L8.8535 4.4395C9.439 3.8535 10.389 3.8535 10.975 4.4395L18.621 12.0855L10.975 19.732C10.389 20.3175 9.439 20.3175 8.8535 19.732Z"
        />
      )}
    </AnimatedSvg>
  );
};

export default Arrow;
