import { FlatList } from 'react-native';
import React, { useCallback } from 'react';

import { Body, LargeBody } from 'cube-ui-components';
import {
  getMonth,
  isAfter,
  isBefore,
  isSameDay,
  setMonth,
  startOfMonth,
} from 'date-fns';
import { CalendarLocalization } from '../types';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';

interface SelectMonthPanelProps {
  controlledDate?: Date;
  minDate?: Date;
  maxDate?: Date;
  onSelectMonth: (month: number) => void;
  localization: CalendarLocalization;
}

const SelectMonthPanel = ({
  controlledDate,
  onSelectMonth,
  minDate,
  maxDate,
  localization,
}: SelectMonthPanelProps) => {
  const { colors } = useTheme();

  const isMonthDisabled = useCallback(
    (month: number) => {
      if (!controlledDate) return true;

      const target = startOfMonth(setMonth(controlledDate, month));

      const isLargerMinMonth = minDate
        ? isSameDay(target, startOfMonth(minDate)) ||
          isAfter(target, startOfMonth(minDate))
        : true;
      const isSmallerMaxMonth = maxDate
        ? isSameDay(target, startOfMonth(maxDate)) ||
          isBefore(target, startOfMonth(maxDate))
        : true;

      return !(isLargerMinMonth && isSmallerMaxMonth);
    },
    [minDate, maxDate, controlledDate],
  );

  return (
    <Container>
      <Title color={colors.secondaryVariant}>{localization.month}</Title>
      <FlatList
        data={localization.months}
        keyExtractor={item => item.fullName}
        numColumns={3}
        renderItem={({ item: month, index }) => {
          const isActive = controlledDate
            ? getMonth(controlledDate) === index
            : false;
          const disabled = isMonthDisabled(index);
          return (
            <MonthButton
              onPress={disabled ? undefined : () => onSelectMonth(index)}
              key={index}
              disabled={disabled}
              isActive={isActive}>
              <MonthText
                fontWeight={isActive ? 'bold' : 'normal'}
                color={
                  disabled
                    ? colors.surface
                    : isActive
                    ? colors.primary
                    : colors.secondary
                }>
                {month.fullName}
              </MonthText>
            </MonthButton>
          );
        }}
      />
    </Container>
  );
};

export default React.memo(SelectMonthPanel);

const Container = styled.View(({ theme: { colors, space } }) => ({
  maxHeight: 385,
  backgroundColor: colors.background,
  borderBottomRightRadius: space[2],
  borderBottomLeftRadius: space[2],
  alignSelf: 'center',
  paddingTop: space[2],
  gap: space[2],
}));

const Title = styled(LargeBody)(() => ({
  textAlign: 'center',
}));

const MonthButton = styled.Pressable<{
  disabled?: boolean;
  isActive?: boolean;
}>(({ theme: { space, colors }, disabled, isActive }) => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: space[9],
  paddingVertical: space[3],
  borderRadius: 50,
  flexBasis: `${100 / 3}%`,
  backgroundColor: disabled
    ? colors.background
    : isActive
    ? colors.primaryVariant2
    : colors.background,
}));

const MonthText = styled(Body)(() => ({
  textAlign: 'center',
  width: 74,
}));
