import { TouchableOpacity } from 'react-native';
import React, { useCallback, useMemo } from 'react';
import { getValidDate } from '../helper/getValidDate';
import { addMonths, addYears, getMonth, getYear } from 'date-fns';
import { Box, Center, Icon, LargeBody, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { CalendarLocalization, DatePickerMode } from '../types';

interface CustomCalendarHeaderProps {
  mode?: DatePickerMode;
  date?: Date;
  minDate?: Date;
  maxDate?: Date;
  setControlledDate: (controlledDate: Date) => void;
  toggleMonthPanel: () => void;
  toggleYearPanel: () => void;
  localization: CalendarLocalization;
}

const CustomCalendarHeader = ({
  mode,
  date,
  minDate,
  maxDate,
  setControlledDate,
  toggleMonthPanel,
  toggleYearPanel,
  localization,
}: CustomCalendarHeaderProps) => {
  const { space } = useTheme();

  const moveToDate = useCallback((date: Date) => {
    const validDate = getValidDate(date, minDate, maxDate);
    setControlledDate(validDate);
  }, []);

  const addMonth = useCallback(() => {
    if (!date) return;
    moveToDate(addMonths(date, 1));
  }, [date]);

  const subtractMonth = useCallback(() => {
    if (!date) return;

    moveToDate(addMonths(date, -1));
  }, [date]);

  const addYear = useCallback(() => {
    if (!date) return;
    moveToDate(addYears(date, 1));
  }, [date]);

  const subtractYear = useCallback(() => {
    if (!date) return;
    moveToDate(addYears(date, -1));
  }, [date]);

  const monthLabel = useMemo(
    () => localization.months[date ? getMonth(date) : 0].fullName,
    [date],
  );

  const displayYear = useMemo(() => (date ? getYear(date) : 0), [date]);

  return (
    <>
      <Center mx={-10} mb={space[5]}>
        <Center flexDirection="row">
          {mode !== 'year' && (
            <CalendarStepper
              onPressLeft={() => {
                subtractMonth();
              }}
              onPressRight={() => {
                addMonth();
              }}>
              <TouchableOpacity
                onPress={() => {
                  toggleMonthPanel();
                }}>
                <LargeBody>{monthLabel}</LargeBody>
              </TouchableOpacity>
            </CalendarStepper>
          )}
          <Box w={space[8]} />
          <CalendarStepper onPressLeft={subtractYear} onPressRight={addYear}>
            <TouchableOpacity onPress={toggleYearPanel}>
              <LargeBody>{displayYear}</LargeBody>
            </TouchableOpacity>
          </CalendarStepper>
        </Center>
      </Center>
    </>
  );
};

const CalendarStepper = ({
  children,
  onPressRight,
  onPressLeft,
}: {
  children: React.ReactElement;
  onPressRight?: () => void;
  onPressLeft?: () => void;
}) => {
  const { sizes, space } = useTheme();

  return (
    <Row alignItems="center" w={sizes[40]} justifyContent="space-between">
      <TouchableOpacity onPress={onPressLeft}>
        <Icon.ChevronLeft />
      </TouchableOpacity>
      <Box w={space[3]} />
      {children}
      <Box w={space[3]} />
      <TouchableOpacity onPress={onPressRight}>
        <Icon.ChevronRight />
      </TouchableOpacity>
    </Row>
  );
};

export default React.memo(CustomCalendarHeader);
