import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, Box, LargeBody, Row } from 'cube-ui-components';
import { getYear } from 'date-fns';
import React, { useCallback, useMemo, useRef } from 'react';
import { FlatList } from 'react-native';
import useScrollToYear, {
  NUMBER_OF_YEARS_PER_ROW,
} from '../hook/useScrollToYear';
import { CalendarLocalization } from '../types';

interface SelectYearPanelProps {
  controlledDate?: Date;
  minDate?: Date;
  maxDate?: Date;
  onSelectYear: (year: number) => void;
  localization: CalendarLocalization;
}

const PAST_YEARS = 101;
const FUTURE_YEARS = 41;

const SelectYearPanel = ({
  controlledDate,
  minDate,
  maxDate,
  onSelectYear,
  localization,
}: SelectYearPanelProps) => {
  const { colors } = useTheme();
  const listRef = useRef<FlatList>(null);

  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const result = [];
    for (let i = 0; i < PAST_YEARS; i++) {
      if (i === 0) {
        result.push(currentYear);
      } else {
        result.unshift(currentYear - i);
      }
    }
    for (let i = 1; i <= FUTURE_YEARS; i++) {
      result.push(currentYear + i);
    }

    return result;
  }, []);

  const { scrollToIndex } = useScrollToYear({
    controlledDate,
    years,
    listRef,
  });

  const isYearDisabled = useCallback(
    (year: number) => {
      if (!controlledDate) return true;

      const isLargerMinYear = minDate ? year >= getYear(minDate) : true;
      const isSmallerMaxYear = maxDate ? year <= getYear(maxDate) : true;

      return !(isLargerMinYear && isSmallerMaxYear);
    },
    [minDate, maxDate, controlledDate],
  );

  return (
    <Container>
      <Title color={colors.secondaryVariant}>{localization.year}</Title>
      <FlatList
        ref={listRef}
        initialNumToRender={42}
        onScrollToIndexFailed={scrollToIndex}
        data={years}
        numColumns={NUMBER_OF_YEARS_PER_ROW}
        showsVerticalScrollIndicator
        persistentScrollbar
        keyExtractor={item => item}
        renderItem={({ item: year, index }) => {
          const isActive = controlledDate
            ? getYear(controlledDate) === year
            : false;
          const disabled = isYearDisabled(year);
          return (
            <YearButton
              onPress={disabled ? undefined : () => onSelectYear(year)}
              key={index}
              disabled={disabled}
              isActive={isActive}>
              <YearText
                fontWeight={isActive ? 'bold' : 'normal'}
                color={
                  disabled
                    ? colors.surface
                    : isActive
                    ? colors.primary
                    : colors.secondary
                }>
                {year}
              </YearText>
            </YearButton>
          );
        }}
      />
    </Container>
  );
};

const Container = styled.View(({ theme: { colors, space } }) => ({
  maxHeight: 385,
  backgroundColor: colors.background,
  borderBottomRightRadius: space[2],
  borderBottomLeftRadius: space[2],
  alignSelf: 'center',
  paddingTop: space[2],
  gap: space[2],
}));

const Title = styled(LargeBody)(() => ({
  textAlign: 'center',
}));

const YearButton = styled.Pressable<{
  disabled?: boolean;
  isActive?: boolean;
}>(({ theme: { space, colors }, disabled, isActive }) => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: space[9],
  paddingVertical: space[3],
  borderRadius: 50,
  flexBasis: `${100 / NUMBER_OF_YEARS_PER_ROW}%`,
  backgroundColor: disabled
    ? colors.background
    : isActive
    ? colors.primaryVariant2
    : colors.background,
}));

const YearText = styled(Body)(() => ({
  textAlign: 'center',
  width: 74,
}));

export default React.memo(SelectYearPanel);
