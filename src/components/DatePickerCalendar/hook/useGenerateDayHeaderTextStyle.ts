import { useTheme } from '@emotion/react';
import { useMemo } from 'react';

export default function useGenerateDayHeaderTextStyle() {
  const { colors, typography } = useTheme();
  const baseClassName = 'dayTextAtIndex';

  return useMemo(
    () =>
      new Array(7).fill(0).reduce((styleObj, _, index) => {
        styleObj[`${baseClassName}${index}`] = {
          color: colors.secondaryVariant,
          fontSize: typography.body.size,
          lineHeight: typography.body.lineHeight,
          fontFamily: 'FWDCircularTT-Book',
          width: 53,
        };
        return styleObj;
      }, {}),
    [colors, typography],
  );
}
