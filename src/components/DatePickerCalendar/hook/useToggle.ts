import { useTheme } from '@emotion/react';
import {
  interpolate,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

const useToggle = () => {
  const {colors} = useTheme();
  const animatedValue = useSharedValue(0);
  const setValue = (value: number) =>
    (animatedValue.value = withTiming(value, { duration: 300 }));

  const show = () => setValue(1);

  const hide = () => setValue(0);

  const toggle = () => setValue(animatedValue.value === 0 ? 1 : 0);

  const panelAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(animatedValue.value, [0, 1], [0, 1]),
      zIndex: interpolate(animatedValue.value, [0, 1], [0, 3]),
    };
  });

  const labelAnimatedStyle = useAnimatedStyle(() => {
    return {
      color: interpolateColor(
        animatedValue.value,
        [0, 1],
        [colors.primaryVariant, colors.primary],
      ),
    };
  });

  return {
    panelAnimatedStyle,
    labelAnimatedStyle,
    show,
    hide,
    toggle,
  };
};

export default useToggle;
