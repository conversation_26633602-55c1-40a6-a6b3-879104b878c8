import { CalendarLocalization } from './types';

export const DEFAULT_LOCALIZATION: CalendarLocalization = {
  title: 'Calendar',
  cancel: 'Cancel',
  confirm: 'Confirm',
  month: 'Month',
  year: 'Year',
  months: [
    { fullName: 'January', shortName: 'Jan' },
    { fullName: 'February', shortName: 'Feb' },
    { fullName: 'March', shortName: 'Mar' },
    { fullName: 'April', shortName: 'Apr' },
    { fullName: 'May', shortName: 'May' },
    { fullName: 'June', shortName: 'Jun' },
    { fullName: 'July', shortName: 'Jul' },
    { fullName: 'August', shortName: 'Aug' },
    { fullName: 'September', shortName: 'Sep' },
    { fullName: 'October', shortName: 'Oct' },
    { fullName: 'November', shortName: 'Nov' },
    { fullName: 'December', shortName: 'Dec' },
  ],
  daysInWeek: [
    {fullName: 'Sunday', shortName: 'Sun'},
    {fullName: 'Monday', shortName: 'Mon'},
    {fullName: 'Tuesday', shortName: 'Tue'},
    {fullName: 'Wednesday', shortName: 'Wed'},
    {fullName: 'Thursday', shortName: 'Thu'},
    {fullName: 'Friday', shortName: 'Fri'},
    { fullName: 'Saturday', shortName: 'Sat' },
  ],
  modeSwitch: {
    options: [
      { label: 'Do not have a date', value: 'month-year' },
      { label: 'Do not have a date and a month', value: 'year' },
    ],
  },
};

export const CELL_HEIGHT = 44;
export const CELL_WIDTH = 68;
export const ROOT_CONTAINER_WIDTH = 572;
