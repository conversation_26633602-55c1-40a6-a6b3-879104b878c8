import {
  Calendar as RNCalendar,
  DateData,
  LocaleConfig,
} from 'react-native-calendars';
import React, { useCallback, useLayoutEffect, useMemo, useState } from 'react';
import { Modal, StyleSheet } from 'react-native';
import CustomCalendarHeader from './components/CustomCalendarHeader';
import useGenerateDayHeaderTextStyle from './hook/useGenerateDayHeaderTextStyle';
import { CalendarProps, DatePickerMode } from './types';
import {
  CELL_HEIGHT,
  CELL_WIDTH,
  DEFAULT_LOCALIZATION,
  ROOT_CONTAINER_WIDTH,
} from './constants';
import { MarkedDates } from 'react-native-calendars/src/types';
import {
  format,
  getMonth,
  getYear,
  setMonth,
  setYear,
  startOfMonth,
  startOfYear,
} from 'date-fns';
import { useTheme } from '@emotion/react';
import { Box, Button, Center, Checkbox, H6, Row } from 'cube-ui-components';
import styled from '@emotion/native';
import SelectMonthPanel from './components/SelectMonthPanel';
import SelectYearPanel from './components/SelectYearPanel';
import { getValidDate } from './helper/getValidDate';

export const DEFAULT_FORMAT = 'yyyy-MM-dd';
const TODAY = new Date();
const DEFAULT_MIN_DATE = setYear(TODAY, 1900);
const DEFAULT_MAX_DATE = setYear(TODAY, 2100);

export default function Calendar({
  visible,
  modeSwitchEnabled,
  mode,
  onChangeMode,
  value,
  onChange,
  rnCalendarProps,
  minDate = DEFAULT_MIN_DATE,
  maxDate = DEFAULT_MAX_DATE,
  onCancel,
  localization = DEFAULT_LOCALIZATION,
  defaultDate,
}: CalendarProps) {
  const { space, borderRadius, colors, typography } = useTheme();
  const dayHeaderTextStyle = useGenerateDayHeaderTextStyle();

  // Visible date on calendar
  const [activeDate, setActiveDate] = useState<Date>(() => {
    const date = value ?? defaultDate ?? TODAY;
    switch (mode) {
      case 'month-year':
        return startOfMonth(date);
      case 'year':
        return startOfYear(date);
      default:
        return date;
    }
  });
  // Marked date
  const [selectedDate, setSelectedDate] = useState(() => {
    const date = value ?? defaultDate ?? TODAY;
    switch (mode) {
      case 'month-year':
        return startOfMonth(date);
      case 'year':
        return startOfYear(date);
      default:
        return value;
    }
  });

  const [monthPanelVisible, setMonthPanelVisible] = useState(
    mode === 'month-year',
  );
  const toggleMonthPanel = useCallback(() => {
    setMonthPanelVisible(visible => {
      if (mode !== 'full') {
        return true;
      }
      return !visible;
    });
    setYearPanelVisible(false);
  }, [mode]);
  const [yearPanelVisible, setYearPanelVisible] = useState(mode === 'year');
  const toggleYearPanel = useCallback(() => {
    setYearPanelVisible(visible => {
      if (mode !== 'full') {
        return true;
      }
      return !visible;
    });
    setMonthPanelVisible(false);
  }, [mode]);

  const markedDates: MarkedDates | undefined = useMemo(() => {
    if (!selectedDate) return;

    return {
      [format(new Date(selectedDate), DEFAULT_FORMAT)]: {
        selected: true,
        customStyles: {
          container: styles.selectedContainer,
          text: styles.selectedDayText,
        },
      },
    };
  }, [selectedDate]);

  const initialDate = useMemo(() => {
    return activeDate
      ? format(activeDate, DEFAULT_FORMAT)
      : defaultDate ?? TODAY;
  }, [activeDate, defaultDate]);

  // Because we do not change the content of day when month changes, thus we should update for selected day value and markedDates separately
  const onDayPress = useCallback(
    (date: DateData) => {
      const currentMonth = (activeDate ?? TODAY).getMonth();
      const currentYear = (activeDate ?? TODAY).getFullYear();
      const dateSelected = new Date(currentYear, currentMonth, date.day);
      setSelectedDate(dateSelected);
      setActiveDate(dateSelected);
    },
    [activeDate],
  );

  const onConfirmPress = useCallback(() => {
    let modeValue: DatePickerMode | undefined = undefined;
    if (modeSwitchEnabled) {
      modeValue = mode;
    }
    if (selectedDate) {
      onChange?.(selectedDate, modeValue);
    }
  }, [modeSwitchEnabled, selectedDate, mode, onChange]);

  const onCancelPress = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  useLayoutEffect(() => {
    LocaleConfig.locales[''] = {
      ...LocaleConfig.locales[''],
      monthNames: localization.months.map(i => i.fullName),
      monthNamesShort: localization.months.map(i => i.shortName),
      dayNames: localization.daysInWeek.map(i => i.fullName),
      dayNamesShort: localization.daysInWeek.map(i => i.shortName),
    };
  }, [localization]);

  return (
    <Modal
      transparent
      statusBarTranslucent
      visible={visible}
      style={styles.modalContainer}
      animationType="fade">
      <Center
        pos="absolute"
        top={0}
        bottom={0}
        left={0}
        right={0}
        backgroundColor="rgba(0,0,0,0.5)">
        <Box
          justifyContent="flex-start"
          maxWidth={ROOT_CONTAINER_WIDTH}
          width={ROOT_CONTAINER_WIDTH}
          p={space[12]}
          borderRadius={borderRadius.large}
          bgColor={colors.background}>
          <H6 fontWeight="bold" color={colors.secondaryVariant}>
            {localization.title}
          </H6>
          <Box h={space[5]} />
          <CustomCalendarHeader
            mode={mode}
            date={activeDate}
            minDate={minDate}
            maxDate={maxDate}
            setControlledDate={setActiveDate}
            toggleMonthPanel={toggleMonthPanel}
            toggleYearPanel={toggleYearPanel}
            localization={localization}
          />
          <Box>
            {monthPanelVisible ? (
              <SelectMonthPanel
                controlledDate={activeDate}
                minDate={minDate}
                maxDate={maxDate}
                localization={localization}
                onSelectMonth={(month: number) => {
                  const newDate = setMonth(activeDate || new Date(), month);

                  if (newDate) {
                    const validDate = getValidDate(newDate, minDate, maxDate);
                    setActiveDate(validDate);
                  }
                  if (mode === 'full') {
                    toggleMonthPanel();
                  } else {
                    setSelectedDate(startOfMonth(newDate));
                  }
                }}
              />
            ) : yearPanelVisible ? (
              <SelectYearPanel
                controlledDate={activeDate}
                minDate={minDate}
                maxDate={maxDate}
                localization={localization}
                onSelectYear={(year: number) => {
                  const newDate = setYear(activeDate || new Date(), year);

                  if (newDate) {
                    const validDate = getValidDate(newDate, minDate, maxDate);
                    setActiveDate(validDate);
                  }
                  if (mode === 'full') {
                    toggleYearPanel();
                  } else if (mode === 'month-year') {
                    setSelectedDate(startOfMonth(newDate));
                  } else if (mode === 'year') {
                    setActiveDate(startOfYear(newDate));
                    setSelectedDate(startOfYear(newDate));
                  }
                }}
              />
            ) : (
              <RNCalendar
                {...rnCalendarProps}
                minDate={minDate ? format(minDate, DEFAULT_FORMAT) : 'min-date'}
                maxDate={maxDate ? format(maxDate, DEFAULT_FORMAT) : 'max-date'}
                renderHeader={() => null}
                initialDate={initialDate.toString()}
                hideArrows
                style={styles.calendar}
                markingType="custom"
                theme={{
                  textDayStyle: {
                    fontFamily: 'FWDCircularTT-Book',
                    fontSize: typography.largeBody.size,
                    lineHeight: typography.largeBody.lineHeight,
                    color: colors.secondary,
                    alignSelf: 'center',
                  },
                  todayTextColor: colors.secondary,
                  textDayHeaderFontFamily: 'FWDCircularTT-Book',
                  textDayHeaderFontSize: typography.smallLabel.size,
                  textDisabledColor: colors.placeholder,
                  textInactiveColor: colors.placeholder,
                  selectedDayTextColor: colors.primary,
                  selectedDayBackgroundColor: colors.primaryVariant2,
                  'stylesheet.calendar.header': dayHeaderTextStyle,
                  'stylesheet.day.basic': {
                    base: styles.singleDayContainer,
                  },
                }}
                onDayPress={onDayPress}
                markedDates={markedDates}
                enableSwipeMonths={true}
                onMonthChange={date => {
                  const currentMonth = getMonth(activeDate || new Date());
                  const currentYear = getYear(activeDate || new Date());
                  const nextMonth = date.month - 1;
                  const nextYear = date.year;

                  if (currentMonth === nextMonth && currentYear === nextYear)
                    return;
                  setActiveDate(new Date(date.timestamp));
                }}
                hideExtraDays
                headerStyle={styles.headerWrapper}
              />
            )}
          </Box>
          {modeSwitchEnabled && (
            <Row mt={space[6]} mb={space[2]} gap={space[8]}>
              {localization.modeSwitch.options.map(option => (
                <Checkbox
                  key={option.value}
                  label={option.label}
                  checked={mode === option.value}
                  onChange={checked => {
                    let modeValue: DatePickerMode = 'full';
                    if (checked) {
                      modeValue = option.value;
                    }
                    onChangeMode?.(modeValue);
                    switch (modeValue) {
                      case 'month-year':
                        setMonthPanelVisible(true);
                        setYearPanelVisible(false);
                        setSelectedDate(startOfMonth(activeDate));
                        break;
                      case 'year':
                        setMonthPanelVisible(false);
                        setYearPanelVisible(true);
                        setSelectedDate(startOfYear(activeDate));
                        setActiveDate(startOfYear(activeDate));
                        break;
                      default:
                        setMonthPanelVisible(false);
                        setYearPanelVisible(false);
                        break;
                    }
                  }}
                />
              ))}
            </Row>
          )}
          <Row alignSelf="center" justifyContent="center" mt={space[6]}>
            <Action
              size="medium"
              variant="secondary"
              text={localization.cancel}
              onPress={onCancelPress}
            />
            <Box w={space[4]} />
            <Action
              size="medium"
              text={localization.confirm}
              onPress={onConfirmPress}
              disabled={!selectedDate}
            />
          </Row>
        </Box>
      </Center>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerWrapper: {
    paddingLeft: -10,
    paddingRight: -10,
    marginTop: -6,
  },
  calendar: {
    paddingLeft: 0,
    paddingRight: 0,
  },
  singleDayContainer: {
    width: CELL_WIDTH,
    height: CELL_HEIGHT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedContainer: {
    width: CELL_HEIGHT,
    height: CELL_HEIGHT,
    borderRadius: CELL_HEIGHT / 2,
  },
  selectedDayText: {
    fontFamily: 'FWDCircularTT-Bold',
  },
});

const Action = styled(Button)(({ theme }) => ({
  width: theme.sizes[30],
}));
