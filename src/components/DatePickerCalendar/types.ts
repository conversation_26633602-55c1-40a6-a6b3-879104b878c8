import { CalendarProps as RNCalendarsProps } from 'react-native-calendars';

export type DatePickerMode = 'full' | 'month-year' | 'year';

export type CalendarResult = {
  date: string;
  isDateZero: boolean;
  isMonthZero: boolean;
};

export interface CalendarProps {
  visible?: boolean;
  modeSwitchEnabled?: boolean;
  mode?: DatePickerMode;
  onChangeMode?: (mode: DatePickerMode) => void;
  value?: Date;
  onChange?: (date: Date, mode?: DatePickerMode) => void;
  rnCalendarProps?: RNCalendarsProps;
  minDate?: Date;
  maxDate?: Date;
  onCancel?: () => void;
  localization?: CalendarLocalization;
  defaultDate?: Date;
}

export interface CalendarRef {
  show: () => void;
  hide: () => void;
}

interface LocalizationLabel {
  shortName: string;
  fullName: string;
}
export interface CalendarLocalization {
  title: string;
  year: string;
  month: string;
  months: LocalizationLabel[];
  daysInWeek: LocalizationLabel[];
  confirm: string;
  cancel: string;
  modeSwitch: {
    options: { label: string; value: DatePickerMode }[];
  };
}
