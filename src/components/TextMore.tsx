import { useTheme } from '@emotion/react';
import { LargeBody } from 'cube-ui-components';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { StyleProp, TextStyle } from 'react-native';

export default function TextMore({
  text,
  numLines,
  collapsable = true,
  style,
  buttonTextStyle,
}: {
  text: string;
  numLines: number;
  collapsable?: boolean;
  style?: StyleProp<TextStyle>;
  buttonTextStyle?: StyleProp<TextStyle>;
}) {
  const { t } = useTranslation(['common']);
  const { colors } = useTheme();
  const [isTruncated, setIsTruncated] = React.useState(true);
  const [clippedText, setClippedText] = React.useState('');
  const latestText = React.useRef(text);
  if (latestText.current !== text) {
    setClippedText('');
    latestText.current = text;
  }

  const onClick = () => {
    setIsTruncated(!isTruncated);
  };

  return (
    <>
      {clippedText ? (
        <LargeBody style={style}>
          {isTruncated ? clippedText : text}
          {(isTruncated || collapsable) && (
            <LargeBody
              style={buttonTextStyle}
              suppressHighlighting
              onPress={onClick}
              fontWeight="bold"
              color={colors.palette.fwdAlternativeOrange[100]}>
              {isTruncated ? t('common:more') : t('common:close')}
            </LargeBody>
          )}
        </LargeBody>
      ) : (
        <LargeBody
          style={style}
          onTextLayout={e => {
            const { lines } = e.nativeEvent;
            const joinedText = lines
              .splice(0, numLines)
              .map(line => line.text)
              .join('');
            if (joinedText.length < text.length) {
              const clippedText =
                joinedText.substring(0, joinedText.length - 10) + '... ';
              setClippedText(clippedText);
            }
          }}>
          {text}
        </LargeBody>
      )}
    </>
  );
}
