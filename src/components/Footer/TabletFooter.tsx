import styled from '@emotion/native';
import React from 'react';
import { StyleProp, View, ViewProps, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = { children?: React.ReactNode; style?: StyleProp<ViewStyle> };

export default function TabletFooter({ children, ...props }: ViewProps) {
  return <FooterStyle {...props}>{children}</FooterStyle>;
}

const FooterStyle = styled.View(({ theme: { space, colors } }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  return {
    maxHeight: space[60],
    width: '100%',
    paddingHorizontal: space[8],
    borderWidth: 1,
    borderTopColor: colors.palette.fwdGrey[100],
    borderLeftColor: colors.background,
    borderRightColor: colors.background,
    paddingTop: space[4],
    paddingBottom: bottomInset === 0 ? space[2] : bottomInset,
    backgroundColor: colors.background,
  };
});
