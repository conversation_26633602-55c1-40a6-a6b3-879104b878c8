import { useTheme } from '@emotion/react';
import MaskedView from '@react-native-masked-view/masked-view';
import {
  Canvas,
  Circle,
  Path,
  Skia,
  SweepGradient,
  vec,
} from '@shopify/react-native-skia';
import React, { useEffect } from 'react';
import { StyleSheet, View, ViewProps } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

type GradientCircularSpinnerProps = Pick<ViewProps, 'style'> & {
  /**
   * The size of the spinner.
   * @default sizes[23]
   */
  size?: number;
};

export default function GradientCircularSpinner({
  style,
  size: propsSize,
}: GradientCircularSpinnerProps) {
  const { sizes, colors } = useTheme();

  const size = propsSize ?? sizes[23];
  const strokeWidth = size / 10; // 10% of the size
  const radius = (size - strokeWidth) / 2;
  const center = vec(size / 2, size / 2);
  const sweepAngle = 360 * 0.65;

  const gradientColors = [
    colors.palette.fwdYellow[100],
    colors.palette.fwdLightGreen[100],
    colors.palette.fwdYellow[100],
    colors.palette.fwdOrange[100],
    colors.palette.fwdYellow[100],
  ];
  const positions = [0, 0.25, 0.5, 0.75, 1];

  // Animation state
  const rotation = useSharedValue(0);
  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 1000,
        easing: Easing.linear,
      }),
      -1,
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  const path = Skia.Path.Make();
  path.addArc(
    {
      x: center.x - radius,
      y: center.y - radius,
      width: radius * 2,
      height: radius * 2,
    },
    -90,
    sweepAngle,
  );

  return (
    <View style={StyleSheet.flatten([{ width: size, height: size }, style])}>
      <MaskedView
        style={{ flex: 1 }}
        maskElement={
          <Animated.View style={[StyleSheet.absoluteFill, animatedStyle]}>
            <Canvas style={{ flex: 1 }}>
              <Path
                path={path}
                style="stroke"
                strokeWidth={strokeWidth}
                color="white"
                strokeCap="butt"
              />
            </Canvas>
          </Animated.View>
        }>
        <Canvas style={{ flex: 1 }}>
          <Circle
            cx={center.x}
            cy={center.y}
            r={radius}
            style="stroke"
            strokeWidth={strokeWidth}>
            <SweepGradient
              c={center}
              colors={gradientColors}
              positions={positions}
            />
          </Circle>
        </Canvas>
      </MaskedView>
    </View>
  );
}
