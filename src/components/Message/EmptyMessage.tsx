import styled from '@emotion/native';
import { memo } from 'react';
import { Dimensions, StyleProp, ViewStyle } from 'react-native';
import { Message } from './Message';
import EmptyTaskSVG from 'features/home/<USER>/image/EmptyTaskSVG';

const Container = styled.View(({ theme }) => ({
  alignItems: 'center',
  marginTop: Dimensions.get('window').height / 6,
}));

interface Props {
  message: string;
  icon?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

export const EmptyMessage = memo(function EmptyMessage({
  message,
  icon,
  style,
}: Props) {
  return (
    <Container style={style}>
      {icon ? icon : <EmptyTaskSVG width={160} height={134} />}
      <Message>{message}</Message>
    </Container>
  );
});
