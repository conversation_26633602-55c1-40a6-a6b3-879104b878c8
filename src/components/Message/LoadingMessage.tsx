import styled from '@emotion/native';
import LoadingIndicator from 'components/LoadingIndicator';
import { memo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { Message } from './Message';

const Container = styled.View(({ theme }) => ({
  alignItems: 'center',
  paddingHorizontal: theme.space[4],
  paddingTop: theme.space[25],
  backgroundColor: theme.colors.background,
}));

interface Props {
  message: string;
  style?: StyleProp<ViewStyle>;
}

export const LoadingMessage = memo(function LoadingMessage({
  message,
  style,
}: Props) {
  return (
    <Container style={style}>
      <LoadingIndicator size={48} />
      <Message>{message}</Message>
    </Container>
  );
});
