import styled from '@emotion/native';
import { memo } from 'react';
import { Dimensions, StyleProp, ViewStyle } from 'react-native';
import { Message } from './Message';
import EmptyTaskSVG from 'features/home/<USER>/image/EmptyTaskSVG';
import { Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

const Container = styled.View(({ theme }) => ({
  alignItems: 'center',
  marginTop: Dimensions.get('window').height / 6,
}));

interface Props {
  message: string;
  style?: StyleProp<ViewStyle>;
}

export const LoadingFailMessage = memo(function EmptyMessage({
  message,
  style,
}: Props) {
  const { colors } = useTheme();
  return (
    <Container style={style}>
      <Icon.WarningFill width={44} height={44} fill={colors.palette.alertRed} />
      <Message>{message}</Message>
    </Container>
  );
});
