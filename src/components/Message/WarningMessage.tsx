import styled from '@emotion/native';
import { memo } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';

const ErrorMessageContainer = styled.View(({ theme: { space, colors } }) => ({
  flexDirection: 'row',
  borderRadius: space[1],
  padding: space[3],
  marginLeft: space[2],
  marginRight: space[2],
  backgroundColor: colors.palette.alertRedLight,
}));

const ErrorText = styled(Typography.Body)(({ theme: { colors, space } }) => ({
  color: colors.palette.alertRed,
  paddingLeft: space[2],
  flex: 1,
}));

interface Props {
  message: string;
  style?: StyleProp<ViewStyle>;
}

export const WarningMessage = memo(function WarningMessage({
  message,
  style,
}: Props) {
  const theme = useTheme();
  return (
    <ErrorMessageContainer style={style}>
      <Icon.Warning
        height={24}
        width={24}
        fill={theme.colors.palette.alertRed}
      />
      <ErrorText fontWeight="normal">{message}</ErrorText>
    </ErrorMessageContainer>
  );
});
