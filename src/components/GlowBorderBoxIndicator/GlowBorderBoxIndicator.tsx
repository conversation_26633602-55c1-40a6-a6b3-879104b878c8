import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, StyleSheet, LayoutChangeEvent } from 'react-native';
import { useTheme } from '@emotion/react';
import {
  Blur,
  Canvas,
  Group,
  RoundedRect,
  SweepGradient,
  vec,
} from '@shopify/react-native-skia';
import {
  Easing,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

/**
 * Minimum duration (in milliseconds) that the glow effect should remain visible,
 * regardless of how quickly "isVisible" toggles off.
 */
const MIN_DURATION = 2000;

type GlowBorderBoxIndicatorProps = {
  /**
   * Array of colors for the sweep gradient.
   * Defaults to a custom orange→yellow→green→orange sequence.
   */
  glowColors?: string[];
  /**
   * Fraction of the circle to sweep (0.0–1.0).
   * Default is 0.6 (i.e., 60% of the full 360°).
   */
  glowSize?: number;
  /**
   * Radius for the blur effect on the glow.
   * Default is theme.space[1].
   */
  blurRadius?: number;
  /**
   * Corner radius for the rounded rectangle.
   * Default is theme.space[2].
   */
  borderRadius?: number;
  /**
   * Width of the stroke around the rectangle.
   * Default is (theme.space[2] - 2).
   */
  strokeWidth?: number;
  /**
   * If provided, controls how long the glow remains visible after it appears.
   * Default is undefined; glow shows only while isVisible=true, but at least MIN_DURATION.
   */
  duration?: number;

  /**
   * Whether the glow indicator is active.
   * When true, the glow starts (and resets its timer). When false,
   * the glow remains visible until the minimum duration has elapsed.
   */
  isVisible?: boolean;
};

/**
 * Draws a glowing border around its parent container.
 * The glow animates with a rotating sweep gradient and
 * respects a minimum display time even if visibility toggles off early.
 */
const GlowBorderBoxIndicator = ({
  glowColors,
  glowSize = 0.6,
  blurRadius,
  borderRadius,
  strokeWidth,
  duration,
  isVisible = false,
}: GlowBorderBoxIndicatorProps) => {
  const { colors, space } = useTheme();

  // Layout dimensions of the parent container
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);

  // Controls whether the glow is currently rendered
  const [show, setShow] = useState(false);

  // Animated rotation value (0 → 2), mapped to 0–360°
  const rotation = useSharedValue(0);

  // Timestamp when glow first became visible
  const showStart = useRef<number | null>(null);

  // Timer reference to hide the glow after min duration
  const hideTimer = useRef<NodeJS.Timeout | null>(null);

  // Compute actual values with theme fallbacks
  const actualStrokeWidth = strokeWidth ?? space[2] - 2;
  const actualBlurRadius = blurRadius ?? space[1];
  const actualBorderRadius = borderRadius ?? space[2];
  const gradientColors = glowColors ?? [
    colors.palette.fwdOrange[100],
    colors.palette.fwdOrange[100],
    colors.palette.fwdYellow[100],
    colors.palette.fwdLightGreen[100],
  ];

  // Center point for the gradient origin
  const center = useMemo(() => vec(width / 2, height / 2), [width, height]);

  // Derived value for the gradient rotation transform
  const animatedRotation = useDerivedValue(() => [
    { rotate: Math.PI * rotation.value },
  ]);

  // Capture container size on layout
  const onLayout = (e: LayoutChangeEvent) => {
    setWidth(e.nativeEvent.layout.width);
    setHeight(e.nativeEvent.layout.height);
  };

  // Start infinite rotation animation on mount
  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(2, { duration: 2000, easing: Easing.linear }),
      -1,
      false,
    );

    return () => {
      rotation.value = 0;
      if (hideTimer.current) {
        clearTimeout(hideTimer.current);
      }
    };
  }, []);

  // Sync show state with isVisible prop and enforce minimum duration
  useEffect(() => {
    if (isVisible) {
      // When turning on: show immediately & reset timer
      if (!show) {
        setShow(true);
        showStart.current = Date.now();
      }
      if (hideTimer.current) {
        clearTimeout(hideTimer.current);
        hideTimer.current = null;
      }
    } else if (show) {
      // When turning off: calculate remaining time to meet min duration
      const elapsed = showStart.current ? Date.now() - showStart.current : 0;
      const minTime = Math.max(duration ?? MIN_DURATION, MIN_DURATION);
      const remaining = Math.max(minTime - elapsed, 0);

      // Schedule hide after remaining ms
      hideTimer.current = setTimeout(() => {
        setShow(false);
        showStart.current = null;
      }, remaining);
    }
  }, [isVisible, duration, show]);

  return (
    <View
      onLayout={onLayout}
      style={StyleSheet.absoluteFill}
      pointerEvents="none">
      {width > 0 && height > 0 && show && (
        <Canvas style={StyleSheet.absoluteFill}>
          <Group>
            <RoundedRect
              x={actualStrokeWidth / 2}
              y={actualStrokeWidth / 2}
              width={width - actualStrokeWidth}
              height={height - actualStrokeWidth}
              r={actualBorderRadius}
              style="stroke"
              strokeWidth={actualStrokeWidth}>
              {/* Gradient that sweeps around the box border */}
              <SweepGradient
                c={center}
                origin={center}
                colors={gradientColors}
                start={0}
                end={360 * glowSize}
                transform={animatedRotation}
              />
              {/* Apply blur for a soft glow effect */}
              <Blur blur={actualBlurRadius} />
            </RoundedRect>
          </Group>
        </Canvas>
      )}
    </View>
  );
};

export default GlowBorderBoxIndicator;
