import { TextFieldProps } from 'cube-ui-components/dist/cjs/components/TextField/types';
import { TextField, TextFieldRef } from 'cube-ui-components';
import { forwardRef } from 'react';

const normalizeAddress = (address?: string) => {
  if (!address) {
    return '';
  }

  return address.trimStart().replace(/[’‘]/g, "'");
};

const AddressLineField = forwardRef<TextFieldRef, TextFieldProps>(
  ({ onChange, ...textFieldProps }, ref) => {
    return (
      <TextField
        ref={ref}
        onChange={value => {
          onChange?.(normalizeAddress(value));
        }}
        {...textFieldProps}
      />
    );
  },
);

export default AddressLineField;
