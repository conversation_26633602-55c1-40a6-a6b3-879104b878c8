import { View, StyleProp, ViewStyle } from 'react-native';
import { ModulePermissionKeys } from 'types';
import useCheckClientScope from 'hooks/useCheckClientScope';

export default function PermissionsChecker({
  children,
  contanierStyle,
  permissions,
}: {
  children?: React.ReactNode;
  contanierStyle?: StyleProp<ViewStyle>;
  permissions: ModulePermissionKeys;
}) {
  const { isCheckedEnabled, isBothEnabledAndWhiteListed } =
    useCheckClientScope(permissions);

  if (isCheckedEnabled == false || isBothEnabledAndWhiteListed) {
    return <View style={contanierStyle}>{children}</View>;
  }

  return null;
}
