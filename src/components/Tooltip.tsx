import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import {
  Box,
  Center,
  H6,
  Icon,
  LargeBody,
  Typography,
} from 'cube-ui-components';
import useToggle from 'hooks/useToggle';
import React from 'react';
import { TextStyle, TouchableOpacity } from 'react-native';
import { Modal, StyleProp, ViewStyle } from 'react-native';
import WalkthroughTooltip, {
  TooltipProps,
} from 'react-native-walkthrough-tooltip';
import DialogPhone from 'components/Dialog.phone';
import LoadingIndicator from 'components/LoadingIndicator';
import { ScrollView } from 'react-native-gesture-handler';

const ARROW_SIZE = { width: 10, height: 8 };

type TooltipPlacement = 'top' | 'bottom';

const Tooltip = ({
  style,
  content,
  placement = 'top',
  contentContainerStyle,
  contentStyle,
  icon = <Icon.Tooltip />,
  isLoading,
  title,
  arrowStyle,
  arrowSize,
}: {
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  content?: string | React.ReactNode;
  placement?: TooltipPlacement | 'overlay';
  contentContainerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<TextStyle>;
  icon?: React.ComponentType<unknown> | React.ReactElement<unknown>;
  isLoading?: boolean;
  title?: string;
  arrowStyle?: TooltipProps['arrowStyle'];
  arrowSize?: TooltipProps['arrowSize'];
}): JSX.Element => {
  const [visible, show, hide] = useToggle();
  const {
    typography: { body },
    colors,
    space,
    borderRadius,
    elevation,
    sizes,
  } = useTheme();

  switch (placement) {
    case 'overlay':
      return (
        <>
          <TouchableOpacity onPress={show}>
            {React.isValidElement(icon) ? icon : React.createElement(icon)}
          </TouchableOpacity>
          <DialogPhone visible={visible}>
            <ButtonClose onPress={hide} hitSlop={ICON_HIT_SLOP}>
              <Icon.Close fill={colors.palette.black} />
            </ButtonClose>
            {isLoading ? (
              <Center>
                <LoadingIndicator size={sizes[12]} color={colors.primary} />
              </Center>
            ) : (
              <Box gap={space[4]}>
                {Boolean(title) && <H6 fontWeight="bold">{title}</H6>}
                {typeof content === 'string' ? (
                  <LargeBody>{content}</LargeBody>
                ) : (
                  content
                )}
              </Box>
            )}
          </DialogPhone>
        </>
      );
    default:
      return (
        <Container>
          <WalkthroughTooltip
            useInteractionManager
            isVisible={visible}
            content={
              <ContentContainer style={contentContainerStyle}>
                {typeof content === 'string' ? (
                  <Content style={contentStyle}>{content}</Content>
                ) : (
                  content
                )}
              </ContentContainer>
            }
            childContentSpacing={space[2]}
            backgroundColor="transparent"
            disableShadow
            backgroundStyle={elevation[4]}
            arrowSize={arrowSize ?? ARROW_SIZE}
            arrowStyle={arrowStyle ?? elevation[4]}
            contentStyle={{
              backgroundColor: colors.primaryVariant3,
              padding: space[4],
              borderRadius: borderRadius['x-small'],
            }}
            modalComponent={Modal}
            placement={placement as TooltipPlacement}
            showChildInTooltip={false}
            onClose={hide}>
            <Button style={style} hitSlop={ICON_HIT_SLOP} onPress={show}>
              {React.isValidElement(icon) ? icon : React.createElement(icon)}
            </Button>
          </WalkthroughTooltip>
        </Container>
      );
  }
};

export default Tooltip;

const Container = styled.View(() => ({
  alignItems: 'center',
  justifyContent: 'center',
}));

const ContentContainer = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  maxWidth: theme.sizes[75],
  // borderWidth: 2,
}));

const Content = styled(Typography.Body)(({ theme }) => ({
  backgroundColor: theme.colors.primaryVariant3,
}));

const Button = styled.TouchableOpacity(() => ({
  alignItems: 'center',
  justifyContent: 'center',
}));

const ButtonClose = styled(TouchableOpacity)(() => ({
  justifyContent: 'center',
  alignItems: 'center',
  alignSelf: 'flex-end',
}));
