import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import React from 'react';

import {
  StyleProp,
  StyleSheet,
  ViewStyle,
  useWindowDimensions,
} from 'react-native';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface Props {
  visible?: boolean;
  children?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

const DialogPhone = (props: Props) => {
  const { space } = useTheme();
  const { height } = useWindowDimensions();
  const { top: topInset } = useSafeAreaInsets();

  return (
    <>
      {props.visible && (
        <Portal>
          <Backdrop
            renderToHardwareTextureAndroid
            entering={FadeIn}
            exiting={FadeOut}>
            <Container style={[{ maxHeight: height - space[16] - topInset }, props.style]}>
              {props.children}
            </Container>
          </Backdrop>
        </Portal>
      )}
    </>
  );
};

export default DialogPhone;

const Backdrop = styled(Animated.View)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Container = styled.View(({ theme: { colors, space, borderRadius } }) => {
  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    padding: space[isNarrowScreen ? 5 : 6],
    alignSelf: isWideScreen ? 'auto' : 'stretch',
    maxWidth: isWideScreen ? 460 : undefined,
    minWidth: isTabletMode ? 380 : undefined,
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
  };
});
