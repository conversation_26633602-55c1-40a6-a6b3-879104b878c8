import React, { forwardRef, useMemo, useCallback } from 'react';
import { View, ViewProps, StyleSheet, ViewStyle } from 'react-native';
import { TextFieldRef, Checkbox } from 'cube-ui-components';
import { useTheme } from '@emotion/react';

export interface CheckBoxListProps<T, V> extends ViewProps {
  label?: string;
  modalTitle?: string;
  actionLabel?: string;
  disabled?: boolean;
  hint?: string;
  error?: string;
  isError?: boolean;
  value?: V[];
  onChange?: (value: V[]) => void;
  data: T[];
  getItemLabel: (item: T) => string;
  getItemValue: (item: T) => V;
  itemStyle?: ViewStyle;
  highlight?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
}

export type CheckBoxListRef = TextFieldRef;

function CheckBoxListInner<T, V>(
  {
    disabled,
    value,
    onChange,
    data,
    getItemLabel,
    getItemValue,
    itemStyle,
    highlight,
    onFocus,
    onBlur,
    ...viewProps
  }: CheckBoxListProps<T, V>,
  ref: React.ForwardedRef<CheckBoxListRef>,
) {
  const { space } = useTheme();
  const checkedItems = useMemo(
    () =>
      value ? data.filter(item => value.includes(getItemValue(item))) : [],
    [data, value],
  );

  const setCheckedItemMultiple = useCallback(
    (item: T | undefined) => {
      if (typeof item === 'undefined') {
        return;
      } else {
        const differentItems = checkedItems.filter(
          itemInList => getItemValue(itemInList) !== getItemValue(item),
        );
        if (differentItems.length < checkedItems.length) {
          onChange?.(differentItems.map(getItemValue));
        } else {
          onChange?.([...differentItems, item].map(getItemValue));
        }
      }
    },
    [onChange, checkedItems],
  );

  return (
    <View ref={ref} {...viewProps}>
      {data.map((item, index) => (
        <Checkbox
          disabled={disabled}
          style={[
            styles.container,
            { marginTop: index > 0 ? space[4] : space[3] },
            itemStyle,
          ]}
          labelStyle={styles.label}
          key={String(getItemValue(item))}
          value={
            typeof checkedItems.find(
              itemInList => getItemValue(itemInList) === getItemValue(item),
            ) !== 'undefined'
          }
          label={getItemLabel(item)}
          onChange={() => {
            setCheckedItemMultiple(
              data?.find(
                itemInList => getItemValue(itemInList) === getItemValue(item),
              ),
            );
            onFocus?.();
            onBlur?.();
          }}
          highlight={highlight}
        />
      ))}
    </View>
  );
}

const CheckBoxList = forwardRef(CheckBoxListInner) as <T, V>(
  props: CheckBoxListProps<T, V> & {
    ref?: React.ForwardedRef<CheckBoxListRef>;
  },
) => ReturnType<typeof CheckBoxListInner>;
export default CheckBoxList;

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    paddingTop: 2,
  },
  label: {
    marginTop: -2,
    flex: 1,
  },
});
