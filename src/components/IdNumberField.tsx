import { TextFieldProps } from 'cube-ui-components/dist/cjs/components/TextField/types';
import { TextField, TextFieldRef } from 'cube-ui-components';
import { forwardRef } from 'react';
import { NEW_NRIC } from 'constants/optionList';
import { unMask } from 'react-native-mask-text';

interface IdNumberFieldProps extends TextFieldProps {
  idType: string;
}

export const maskNricNumber = {
  type: 'custom',
  pattern: '999999-99-9999',
} as const;

export const NEW_NRIC_LENGTH = 14;

const IdNumberField = forwardRef<TextFieldRef, IdNumberFieldProps>(
  ({ idType, onChange, onSubmitEditing, ...textFieldProps }, ref) => {
    const isNricNew = idType == NEW_NRIC;

    return (
      <TextField
        ref={ref}
        mask={isNricNew ? maskNricNumber : undefined}
        maxLength={isNricNew ? NEW_NRIC_LENGTH : undefined}
        onChange={(value, rawValue = '') => {
          if (isNricNew) {
            onChange?.(rawValue);
          } else {
            onChange?.(value);
          }
        }}
        onSubmitEditing={e => {
          if (isNricNew) {
            onSubmitEditing?.({
              ...e,
              nativeEvent: {
                text: unMask(e.nativeEvent.text),
              },
            });
          } else {
            onSubmitEditing?.(e);
          }
        }}
        pointerEvents={isNricNew ? 'box-only' : undefined}
        {...textFieldProps}
      />
    );
  },
);

export default IdNumberField;
