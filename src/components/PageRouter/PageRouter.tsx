import React, { useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import Animated, {
  LinearTransition,
  SlideInLeft,
  SlideInRight,
  SlideOutLeft,
  SlideOutRight,
} from 'react-native-reanimated';

type PageRouterProps<T> = {
  pages: PageRoute<T>[];
  initialPage?: T;
};

export type PageRoute<T> = {
  name: T;
  page: ({ navigation }: PageProps<T>) => JSX.Element;
};

export type PageProps<T> = {
  navigation: {
    navigate: (pageName: T) => void;
    goBack: () => void;
  };
};

export default function PageRouter<T extends number | string>({
  pages,
  initialPage,
}: PageRouterProps<T>) {
  const pageRoutes = useMemo(() => {
    return pages.reduce((acc, cur, index) => {
      const pageName = String(cur.name);
      return { ...acc, [index]: pageName, [pageName]: index };
    }, {});
  }, [pages]) as Record<T, number>;

  const defaultPageName = pages[0].name;
  const initialPageIndex: number = pageRoutes[initialPage ?? defaultPageName];

  const [pageHistory, setPageHistory] = useState([initialPageIndex]);
  const [animationType, setAnimationType] = useState<PageAnimationType>('none');

  const curPage = pageHistory[0];
  const lastPage = pageHistory[1];

  const navigate = (pageName: T) => {
    setPageHistory(prev => {
      setAnimationType('push');
      const targetIndex = pageRoutes[pageName];
      return getPageHistory(targetIndex, prev);
    });
  };

  const goBack = () => {
    setPageHistory(prev => {
      setAnimationType('pop');
      const targetIndex = pageHistory[1];
      return getPageHistory(targetIndex, prev);
    });
  };

  const navigation = {
    navigate,
    goBack,
  };

  useEffect(() => {
    setAnimationType('none');
  }, [pageHistory]);

  return (
    <PageRenderer
      curPage={pages[curPage]?.page({ navigation })}
      lastPage={pages[lastPage]?.page({ navigation })}
      animationType={animationType}
    />
  );
}

const PageRenderer = ({
  curPage,
  lastPage,
  animationType,
}: PageRendererProps) => {
  const duration = 500;
  const [enteringPage, setEnteringPage] = useState<
    React.ReactNode | undefined
  >();
  const [displayedPage, setDisplayedPage] = useState(curPage);
  const [exitingPage, setExitingPage] = useState<React.ReactNode | undefined>();

  useEffect(() => {
    if (curPage !== enteringPage && animationType != 'none') {
      const EnteringAnimation =
        animationType === 'push'
          ? SlideInRight
          : animationType === 'pop'
          ? SlideInLeft
          : undefined;

      setEnteringPage(
        <Animated.View entering={EnteringAnimation?.duration(duration)}>
          {curPage}
        </Animated.View>,
      );

      setTimeout(() => {
        setEnteringPage(undefined);
        setDisplayedPage(curPage);
      }, duration);
    }

    if (animationType != 'none') {
      const ExitingAnimation =
        animationType === 'push'
          ? SlideOutLeft
          : animationType === 'pop'
          ? SlideOutRight
          : undefined;

      setExitingPage(
        <Animated.View exiting={ExitingAnimation?.duration(duration)}>
          {lastPage}
        </Animated.View>,
      );
    }
  }, [curPage, lastPage, animationType]);

  useEffect(() => {
    if (exitingPage) {
      // Prevent batched state update
      setTimeout(() => {
        setExitingPage(undefined);
      }, 0);
    }
  }, [exitingPage]);

  return (
    <Animated.View
      style={{ position: 'relative', width: '100%' }}
      layout={LinearTransition}>
      {enteringPage ? enteringPage : displayedPage}
      <View style={{ position: 'absolute', width: '100%' }}>{exitingPage}</View>
    </Animated.View>
  );
};

const getPageHistory = (target: number, history: number[]) => {
  if (!isNaN(target)) {
    return [target, ...history].slice(0, 2);
  } else {
    return [...history];
  }
};

type PageAnimationType = 'push' | 'pop' | 'none';

type PageRendererProps = {
  curPage: React.ReactNode;
  lastPage: React.ReactNode;
  animationType: PageAnimationType;
};
