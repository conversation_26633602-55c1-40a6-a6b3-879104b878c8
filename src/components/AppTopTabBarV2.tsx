import React from 'react';
import styled from '@emotion/native';
import { Column, H6, Row } from 'cube-ui-components';
import { MaterialTopTabBarProps } from '@react-navigation/material-top-tabs';
import { ViewStyle } from '@expo/html-elements/build/primitives/View';
import { useTheme } from '@emotion/react';
import { TouchableOpacity } from 'react-native';

export default function AppTopTabBarV2({
  state,
  descriptors,
  navigation,
  onPressCallBack,
}: MaterialTopTabBarProps & { onPressCallBack?: (routeName: string) => void }) {
  return (
    <BarContainer>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];

        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel.toString()
            : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;
        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            if (onPressCallBack) {
              onPressCallBack(route.name);
            }
            navigation.navigate({
              name: route.name,
              params: route?.params,
              merge: true,
            });
          }
        };
        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TabButton
            key={route.key}
            label={label}
            isFocused={isFocused}
            onPress={onPress}
            onLongPress={onLongPress}
          />
        );
      })}
    </BarContainer>
  );
}

const TabButton = ({
  label,
  isFocused,
  onPress,
  onLongPress,
}: {
  label: string;

  isFocused: boolean;
  onPress: () => void;
  onLongPress: () => void;
}) => {
  const { colors } = useTheme();
  return (
    <Button onPress={onPress} onLongPress={onLongPress} isFocused={isFocused}>
      <H6
        fontWeight={isFocused ? 'bold' : 'normal'}
        color={isFocused ? colors.palette.white : colors.secondary}>
        {label}
      </H6>
    </Button>
  );
};

const BarContainer = styled(Row)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[50],
  paddingVertical: space[6],
  paddingLeft: space[8],
  gap: space[4],
}));

const Button = styled(TouchableOpacity)<ViewStyle & { isFocused: boolean }>(
  ({ theme: { sizes, colors, borderRadius }, isFocused }) => ({
    paddingHorizontal: sizes[6],
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: isFocused ? colors.primary : colors.palette.white,
    borderRadius: borderRadius['x-large'],
  }),
);
