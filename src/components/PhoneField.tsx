import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { TextField, TextFieldProps, TextFieldRef } from 'cube-ui-components';
import { country } from 'utils/context';
import { formatIncompletePhoneNumber } from 'libphonenumber-js';
import {
  PHONE_COUNTRY_CODE,
  SHOULD_PREPEND_LEADING_ZERO_IN_FORMAT,
} from 'constants/phone';
import { prependZero, trimLeadingZero } from 'utils';

type PhoneFieldProps = {
  shouldTrimLeadingZero?: boolean;
  value?: string;
} & Omit<TextFieldProps, 'value'>;

const SHOULD_TRIM_LEADING_ZERO =
  {
    my: true,
    ph: false, // TODO: not yet confirmed
    ib: true,
    id: true,
  }[country] || false;

const PhoneField = React.forwardRef<TextFieldRef, PhoneFieldProps>(
  (
    {
      shouldTrimLeadingZero = SHOULD_TRIM_LEADING_ZERO ?? false,
      value = '',
      onChange,
      ...textFieldProps
    }: PhoneFieldProps,
    ref,
  ) => {
    const [formattedPhoneNumber, setFormattedPhoneNumber] = useState(() =>
      formatInputPhoneNumber(value, shouldTrimLeadingZero),
    );

    useEffect(() => {
      setFormattedPhoneNumber(
        formatInputPhoneNumber(value, shouldTrimLeadingZero),
      );
    }, [value]);

    return (
      <TextField
        ref={ref}
        keyboardType="number-pad"
        returnKeyType="done"
        value={formattedPhoneNumber}
        onChange={val => {
          const newFormattedValue = formatInputPhoneNumber(
            val,
            shouldTrimLeadingZero,
          );
          setFormattedPhoneNumber(newFormattedValue);
          onChange && onChange(removeInputPhoneNumberFormat(newFormattedValue));
        }}
        size="large"
        {...textFieldProps}
      />
    );
  },
);

const formatInputPhoneNumber = (input = '', shouldTrimLeadingZero = false) => {
  const rawInput = removeInputPhoneNumberFormat(input);
  const formattedValue = formatIncompletePhoneNumber(
    SHOULD_PREPEND_LEADING_ZERO_IN_FORMAT ? prependZero(rawInput) : rawInput,
    PHONE_COUNTRY_CODE,
  );
  if (shouldTrimLeadingZero) {
    return trimLeadingZero(formattedValue);
  }
  return formattedValue;
};

const removeInputPhoneNumberFormat = (input = '') => {
  return input.replace(/\W/g, '');
};

export default PhoneField;
