import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H7, Row } from 'cube-ui-components';
import React from 'react';

interface IProps {
  title: string;
  children?: React.ReactNode;
}

const SectionContent = (props: IProps) => {
  const { title, children } = props;
  const { colors } = useTheme();
  return (
    <Wrapper>
      <TitleContainer>
        <H7 fontWeight="bold" color={colors.palette.white} style={{ flex: 1 }}>
          {title}
        </H7>
      </TitleContainer>
      {children}
    </Wrapper>
  );
};

export default SectionContent;

const Wrapper = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    marginBottom: space[5],
    backgroundColor: colors.background,
    borderRadius: borderRadius.large,
    overflow: 'hidden',
  }),
);

const TitleContainer = styled(Row)(
  ({ theme: { space, colors, borderRadius } }) => ({
    gap: space[1],
    alignItems: 'center',
    backgroundColor: colors.secondary,
    justifyContent: 'space-between',
    paddingHorizontal: space[6],
    paddingVertical: space[3],
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
  }),
);
