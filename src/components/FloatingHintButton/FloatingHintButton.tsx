import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import React from 'react';
import { Pressable, StyleProp, TextStyle, ViewStyle } from 'react-native';
import Animated, {
  AnimateStyle,
  SlideInDown,
  SlideOutDown,
  SlideOutUp,
} from 'react-native-reanimated';

type FloatingHintButtonProps = {
  containerStyle?: StyleProp<AnimateStyle<StyleProp<ViewStyle>>>;
  textStyle?: TextStyle;
  visible?: boolean;
  text: string;
  leftIcon?: React.ReactNode;
  onPress?: () => void;
};

function FloatingHintButton({
  containerStyle,
  textStyle,
  visible = true,
  text = '',
  leftIcon,
  onPress,
}: FloatingHintButtonProps) {
  const { space, colors, animation } = useTheme();

  if (visible) {
    return (
      <Container
        onPress={onPress}
        style={containerStyle}
        entering={SlideInDown.duration(animation.duration)}
        exiting={SlideOutDown.duration(animation.duration)}>
        {leftIcon ? (
          <>{leftIcon}</>
        ) : (
          <Icon.ArrowDown size={space[6]} fill={colors.palette.white} />
        )}
        <Label textStyle={textStyle}>{text}</Label>
      </Container>
    );
  }
  return null;
}

export default FloatingHintButton;

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);
const Container = styled(AnimatedPressable)(
  ({ theme: { space, colors, borderRadius, getElevation } }) => ({
    backgroundColor: colors.palette.fwdDarkGreen[50],
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: borderRadius.small,
    ...getElevation(5),
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: space[5],
    paddingHorizontal: space[5],
    alignSelf: 'center',
  }),
);

const Label = styled.Text<{ textStyle?: TextStyle }>(
  ({ theme: { space, colors }, textStyle }) => ({
    color: colors.palette.white,
    fontFamily: 'FWDCircularTT-Book',
    fontSize: space[4],
    marginLeft: space[2],
    ...textStyle,
  }),
);
