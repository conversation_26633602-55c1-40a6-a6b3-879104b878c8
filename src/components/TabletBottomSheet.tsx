import { View, Modal, StyleSheet } from 'react-native';
import React from 'react';
import styled from '@emotion/native';
import { ViewStyle } from '@expo/html-elements/build/primitives/View';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  children?: React.ReactNode;
  visible?: boolean;
  backdropColor?: string;
};

export default function BottomSheet({
  children,
  visible,
  backdropColor,
}: Props) {
  return (
    <CFFModal
      visible={visible}
      backdropColor={backdropColor}
      style={styles.flexEnd}
      contentContainerStyle={styles.flexEnd}>
      <Container>{children}</Container>
    </CFFModal>
  );
}

const Container = styled(View)(
  ({ theme: { space, borderRadius, colors } }) => ({
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingTop: space[2],
    backgroundColor: colors.palette.fwdOrange[100],
  }),
);

const styles = StyleSheet.create({
  flexEnd: {
    justifyContent: 'flex-end',
  },
});
