import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Circle, G, Path, Defs, ClipPath } from 'react-native-svg';
function WhatsApp(props: SvgIconProps) {
  return (
    <Svg width={40} height={40} viewBox="0 0 40 40" fill="none" {...props}>
      <Circle cx={20} cy={20} r={20} fill="#FAE4D3" />
      <G clipPath="url(#clip0_34858_86696)" fill="#E87722">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M27.074 12.926A9.962 9.962 0 0130 20.044 9.976 9.976 0 0120.002 30h-.047a10.03 10.03 0 01-4.462-1.071h-3.395c-.567 0-1.027-.46-1.027-1.027v-3.393a10.032 10.032 0 01-1.07-4.462 10.006 10.006 0 01.765-3.904 9.91 9.91 0 012.125-3.188A9.978 9.978 0 0119.956 10H20c1.344 0 2.647.261 3.875.777a9.92 9.92 0 013.199 2.15zM20 27.888c2.1 0 4.071-.81 5.558-2.281a7.858 7.858 0 002.334-5.571 7.855 7.855 0 00-2.309-5.615 7.85 7.85 0 00-5.581-2.31h-.036a7.829 7.829 0 00-5.573 2.331 7.84 7.84 0 00-2.281 5.594 7.917 7.917 0 00.922 3.67l.096.179v2.985h2.985l.178.096a7.916 7.916 0 003.671.922H20z"
        />
        <Path d="M21.61 23.951c.437.127.909 0 1.23-.322l.976-.974a.63.63 0 000-.89l-1.383-1.383a.63.63 0 00-.89 0l-.549.548c-.242-.045-.728-.196-1.228-.696-.5-.501-.651-.988-.697-1.228l.549-.549a.63.63 0 000-.89l-1.383-1.383a.63.63 0 00-.89 0l-.974.975c-.322.322-.45.794-.322 1.231a8.237 8.237 0 005.561 5.561z" />
      </G>
      <Defs>
        <ClipPath id="clip0_34858_86696">
          <Path fill="#fff" transform="translate(10 10)" d="M0 0H20V20H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default WhatsApp;
