import { SvgIconProps } from "cube-ui-components"
import * as React from "react"
import Svg, { Circle, Path } from "react-native-svg"

function Email(props:SvgIconProps) {
  return (
    <Svg
      width={41}
      height={40}
      viewBox="0 0 41 40"
      fill="none"
      {...props}
    >
      <Circle cx={20.3359} cy={20} r={20} fill="#FAE4D3" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.336 25.154h-16V16.5l7.09 5.42a1.499 1.499 0 001.821 0l7.09-5.42v8.654zM26.772 15l-6.468 4.946L13.836 15h12.936zm2.064-2h-17a1.5 1.5 0 00-1.5 1.5v11a1.5 1.5 0 001.5 1.5h17a1.5 1.5 0 001.5-1.5v-11a1.5 1.5 0 00-1.5-1.5z"
        fill="#E87722"
      />
    </Svg>
  )
}

export default Email
