import { Linking, Modal, Platform, TouchableOpacity } from 'react-native';
import React, { useMemo } from 'react';
import { Portal } from '@gorhom/portal';
import styled from '@emotion/native';
import {
  Body,
  Button,
  Column,
  H6,
  Icon,
  Row,
  SvgIconProps,
} from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import Email from './Email';
import WhatsApp from './WhatsApp';
import Phone from './Phone';

type ContactMethod = 'call' | 'whatsapp' | 'email';
type ContactDetail = {
  email?: string;
  phoneNumber?: string;
  message?: string;
};

const getContactLink = (
  method: ContactMethod,
  contactDetail: ContactDetail,
) => {
  const formattedPhoneNumber = contactDetail?.phoneNumber?.replace(/\+/g, '');
  switch (method) {
    case 'call':
      return (
        (Platform.OS === 'android' ? 'tel' : 'telprompt') +
        `:${contactDetail?.phoneNumber}`
      );
    case 'email':
      return `mailto:${contactDetail?.email}?body=${contactDetail?.message}`;
    case 'whatsapp':
      return `http://api.whatsapp.com/send?phone=${formattedPhoneNumber}&&text=${contactDetail?.message}`;
    default:
      return '';
  }
};

export const contact = (
  method: ContactMethod,
  contactDetail: ContactDetail,
) => {
  const contactLink = getContactLink(method, contactDetail);

  Linking.canOpenURL(contactLink)
    .then(supported => {
      if (!supported) {
        if (Platform.OS === 'android' && method === 'call') {
          return Linking.openURL(contactLink); // canOpenURL() fails in android
        }
      } else {
        return Linking.openURL(contactLink);
      }
    })
    .catch(err => console.log(err));
};

const MediaButton = ({
  Icon,
  label,
  onPress,
}: {
  Icon: (props: SvgIconProps) => JSX.Element;
  label: string;
  onPress: () => void;
}) => {
  const { sizes, colors, space } = useTheme();
  return (
    <MediaOption>
      <TouchableOpacity onPress={onPress}>
        <Icon size={sizes[10]} />
      </TouchableOpacity>

      <Body color={colors.secondary}>{label}</Body>
    </MediaOption>
  );
};

export default function ApplicationShareModal({
  visible,
  onClose,
  phoneNumber,
  email,
  message,
  onEndContact,
}: {
  visible: boolean;
  onClose: () => void;
  phoneNumber?: string;
  email?: string;
  message?: string;
  onEndContact?: () => void;
}) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation(['common']);
  const possibleButtonConfigs = [
    {
      isAvailable: Boolean(phoneNumber),
      Icon: Phone,
      label: 'Call',
      type: 'call',
      onPress: () => {
        contact('call', { phoneNumber });
        onEndContact && onEndContact();
      },
    },
    {
      isAvailable: Boolean(phoneNumber),
      Icon: WhatsApp,
      label: 'WhatsApp',
      type: 'whatsapp',
      onPress: () => {
        contact('whatsapp', { phoneNumber, message });
        onEndContact && onEndContact();
      },
    },
    {
      isAvailable: Boolean(email),
      Icon: Email,
      label: 'Email',
      type: 'email',
      onPress: () => {
        contact('email', { email, message });
        onEndContact && onEndContact();
      },
    },
  ] satisfies Array<{
    isAvailable: boolean;
    Icon: (props: SvgIconProps) => React.JSX.Element;
    label: string;
    type: string;
    onPress: () => void;
  }>;

  const buttonConfig = possibleButtonConfigs.filter(
    ({ isAvailable }) => isAvailable,
  );

  const belowThreeOptions = buttonConfig.length < 3;

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalBackground>
          <InnerModal>
            <H6 fontWeight="bold" color={colors.palette.black}>
              {t('application')}
            </H6>
            <Row
              justifyContent={belowThreeOptions ? 'center' : 'space-between'}
              gap={belowThreeOptions ? space[10] : 0}>
              {buttonConfig.map(config => {
                if (!config) return null;

                const { type, ...btnConfig } = config;
                return <MediaButton key={type} {...btnConfig} />;
              })}
            </Row>

            <CloseBtnContainer>
              <Button
                style={{ width: sizes[50] }}
                text={t('close')}
                onPress={() => onClose()}
              />
            </CloseBtnContainer>
          </InnerModal>
        </ModalBackground>
      </Modal>
    </Portal>
  );
}
const ModalBackground = styled(Column)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.6)',
  }),
);

const InnerModal = styled(Column)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({
    borderRadius: borderRadius.large,
    backgroundColor: colors.background,
    paddingVertical: space[12],
    paddingHorizontal: space[12],
    gap: space[6],
  }),
);

const MediaOption = styled(Column)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({
    justifyContent: 'center',
    alignItems: 'center',
    gap: space[1],
  }),
);

const CloseBtnContainer = styled(Column)(
  ({ theme: { sizes, space, borderRadius, colors } }) => ({
    alignItems: 'center',
  }),
);
