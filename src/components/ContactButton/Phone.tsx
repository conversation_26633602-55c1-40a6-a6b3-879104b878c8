import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Circle, Path } from 'react-native-svg';

function Phone(props: SvgIconProps) {
  return (
    <Svg width={41} height={40} viewBox="0 0 41 40" fill="none" {...props}>
      <Circle cx={20.668} cy={20} r={20} fill="#FAE4D3" />
      <Path
        d="M27.7 28.386c-.766.765-1.89 1.07-2.93.765a19.57 19.57 0 01-8.306-4.94 19.628 19.628 0 01-4.945-8.31c-.305-1.04 0-2.165.765-2.935l2.32-2.32a1.5 1.5 0 012.12 0l3.295 3.295a1.5 1.5 0 010 2.12l-1.305 1.305c.11.57.465 1.73 1.66 2.925 1.19 1.19 2.35 1.55 2.925 1.66l1.305-1.305a1.5 1.5 0 012.12 0l3.295 3.295a1.5 1.5 0 010 2.12l-2.32 2.325zm-13.846-14.16c-.39.39-.54.96-.375 1.49.48 1.575 1.655 4.33 4.4 7.08 2.745 2.745 5.5 3.915 7.075 4.395.53.16 1.1.01 1.49-.38l1.81-1.81-2.585-2.585-1.08 1.08c-.345.345-.83.5-1.305.42-1.01-.17-2.8-.685-4.32-2.205-1.525-1.525-2.035-3.31-2.205-4.32-.08-.48.075-.965.42-1.305L18.254 15l-2.585-2.585-1.815 1.81z"
        fill="#E87722"
      />
    </Svg>
  );
}

export default Phone;
