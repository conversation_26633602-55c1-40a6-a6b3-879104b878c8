import { TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { useTheme } from '@emotion/react';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import GeneralContactModal from 'components/ContactModal/GeneralContactModal';

export default function GeneralContactButton({
  phoneNumber,
  email,
  defaultMessage = '',
  onContacted,
  icon,
  customStyle,
}: {
  phoneNumber?: string | null;
  email?: string;
  defaultMessage?: string;
  onContacted?: () => void;
  icon: JSX.Element;
  customStyle?: any;
}) {
  const { colors, sizes } = useTheme();
  const [visible, setVisible] = useState(false);
  const HIT_SLOP_SPACE_ONE = HIT_SLOP_SPACE(1);
  return (
    <>
      <TouchableOpacity
        style={customStyle ? customStyle : {}}
        onPress={() => {
          setVisible(true);
        }}
        hitSlop={HIT_SLOP_SPACE_ONE}>
        {icon}
      </TouchableOpacity>

      <GeneralContactModal
        showModal={visible}
        setShowModal={setVisible}
        phoneMobile={phoneNumber ?? ''}
        emailAddress={email ?? ''}
      />
    </>
  );
}
