import { StyleProp, StyleSheet, TextStyle } from 'react-native';
import MarkdownText, { useMarkdownStyle } from './MarkdownText';
import { useTheme } from '@emotion/react';
import { LargeLabel } from 'cube-ui-components';
import { useState } from 'react';

interface Props {
  fullText: string;
  shortText: string;
  style?: StyleProp<TextStyle>;
}

export default function MarkdownTextMore({
  fullText,
  shortText,
  style,
}: Props) {
  const { colors, typography } = useTheme();
  const mdStyle = useMarkdownStyle();
  const [expanded, setExpanded] = useState(false);
  return (
    <MarkdownText
      style={{
        body: style
          ? StyleSheet.flatten([
              {
                ...mdStyle.body,
                fontSize: typography.largeBody.size,
                lineHeight: typography.largeBody.lineHeight,
              },
              style,
            ])
          : {
              ...mdStyle.body,
              fontSize: typography.largeBody.size,
              lineHeight: typography.largeBody.lineHeight,
            },
        paragraph: {
          marginBottom: 0,
          marginTop: 0,
        },
      }}
      rules={{
        link: node => {
          return (
            <LargeLabel
              suppressHighlighting
              fontWeight="bold"
              onPress={() => setExpanded(last => !last)}
              color={colors.palette.fwdAlternativeOrange[100]}>
              {node.children[0].content}
            </LargeLabel>
          );
        },
      }}>
      {expanded ? fullText : shortText}
    </MarkdownText>
  );
}
