import { LayoutRectangle } from "react-native";
import Point from "./point";

export default class Pen {
  strokes: Point[][];
  _offsetX: number;
  _offsetY: number;
  constructor(strokes: Point[][] = []) {
    this.strokes = strokes || [];
    this._offsetX = 0;
    this._offsetY = 0;
  }

  addStroke(points: Point[]) {
    if (points.length > 0) {
      this.strokes.push(points);
    }
  }

  rewindStroke() {
    if (this.strokes.length < 1) return;
    this.strokes.pop();
  }

  setOffset(options: LayoutRectangle) {
    if (!options) return;
    this._offsetX = options.x;
    this._offsetY = options.y;
  }

  pointsToSvg(points: Point[]) {
    const offsetX = this._offsetX;
    const offsetY = this._offsetY;
    if (points.length > 0) {
      let path = `M ${points[0].x},${points[0].y}`;
      points.forEach(point => {
        path = path + ` L ${point.x},${point.y}`;
      });
      return path;
    } else {
      return '';
    }
  }

  clear = () => {
    this.strokes = [];
  };

  copy() {
    // @ts-expect-error Cannot find name 'Reaction'
    return new Reaction(this.strokes.slice());
  }
}
