import React from 'react';
import {
  View,
  PanResponder,
  StyleSheet,
  PanResponderInstance,
  GestureResponderEvent,
  LayoutChangeEvent,
  ViewStyle,
  PixelRatio,
} from 'react-native';
import Svg, { G, Path } from 'react-native-svg';
import Pen from './pen';
import Point from './point';

import humps from 'humps';
import ViewShot, { CaptureOptions } from 'react-native-view-shot';
import { LayoutRectangle } from 'react-native';
import { StyleProp } from 'react-native';

export interface Stroke {
  type: 'Path';
  attributes: {
    d: string;
    stroke: string;
    strokeWidth: number;
    fill: 'none';
    strokeLinecap: 'round';
    strokeLinejoin: 'round';
  };
}

export const convertStrokesToSvg = (
  strokes: Stroke[],
  layout: { width: number; height: number },
) => {
  return `
    <svg xmlns="http://www.w3.org/2000/svg" width="${layout.width}" height="${
    layout.height
  }" version="1.1">
      <g>
        ${strokes
          .map(e => {
            return `<${e.type.toLowerCase()} ${Object.keys(e.attributes)
              .map(a => {
                return `${humps.decamelize(a, { separator: '-' })}="${
                  e.attributes[a as keyof typeof e.attributes]
                }"`;
              })
              .join(' ')}/>`;
          })
          .join('\n')}
      </g>
    </svg>
  `;
};
interface Props {
  strokes: Stroke[];
  strokeWidth: number;
  color: string;
  onChangeStrokes: (strokes: Stroke[]) => void;
  containerStyle?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}

interface State {
  currentPoints: Point[];
  previousStrokes: Stroke[];
  newStroke: Stroke[];
  pen: Pen;
  scaleFactor: number;
  layout: LayoutRectangle;
  options: CaptureOptions;
}

export default class Whiteboard extends React.Component<Props, State> {
  viewShot = React.createRef<ViewShot>();

  _panResponder: PanResponderInstance;
  _layout: LayoutRectangle = { height: 0, width: 0, x: 0, y: 0 };
  constructor(props: Props) {
    super(props);
    this.state = {
      currentPoints: [],
      previousStrokes: this.props.strokes || [],
      newStroke: [],
      pen: new Pen(),
      scaleFactor: 1,
      layout: { height: 0, width: 0, x: 0, y: 0 },
      options: { result: 'base64' },
    };

    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: evt => this.onResponderGrant(evt),
      onPanResponderMove: evt => this.onResponderMove(evt),
      onPanResponderRelease: () => this.onResponderRelease(),
    });
  }

  componentWillReceiveProps(newProps: Props) {
    if (
      this.props.strokes &&
      newProps.strokes &&
      JSON.stringify(this.props.strokes) !== JSON.stringify(newProps.strokes)
    ) {
      this.setState({
        previousStrokes: newProps.strokes,
        newStroke: [],
      });
    }
  }

  rewind = () => {
    if (
      this.state.currentPoints.length > 0 ||
      this.state.previousStrokes.length < 1
    )
      return;
    const strokes = this.state.previousStrokes;
    strokes.pop();

    this.state.pen.rewindStroke();

    this.setState(
      {
        previousStrokes: [...strokes],
        currentPoints: [],
      },
      () => {
        this._onChangeStrokes([...strokes]);
      },
    );
  };

  clear = () => {
    this.setState(
      {
        previousStrokes: [],
        currentPoints: [],
        newStroke: [],
      },
      () => {
        this._onChangeStrokes([]);
      },
    );

    this.state.pen.clear();
  };

  onTouch(evt: GestureResponderEvent) {
    const [x, y, timestamp] = [
      evt.nativeEvent.locationX / this.state.scaleFactor,
      evt.nativeEvent.locationY / this.state.scaleFactor,
      evt.nativeEvent.timestamp,
    ];
    const newPoint = new Point(x, y, timestamp);
    const newCurrentPoints = this.state.currentPoints;
    newCurrentPoints.push(newPoint);

    this.setState({
      previousStrokes: this.state.previousStrokes,
      currentPoints: newCurrentPoints,
    });
  }

  onResponderGrant(evt: GestureResponderEvent) {
    this.onTouch(evt);
  }

  onResponderMove(evt: GestureResponderEvent) {
    this.onTouch(evt);
  }

  onResponderRelease() {
    const strokes = this.state.previousStrokes;
    if (this.state.currentPoints.length < 1) return;

    const points = this.state.currentPoints;
    if (points.length === 1) {
      const p = points[0];
      const distance = Number(Math.sqrt(this.props.strokeWidth || 4) / 2);
      points.push(new Point(p.x + distance, p.y + distance, p.time));
    }

    const newElement: Stroke = {
      type: 'Path',
      attributes: {
        d: this.state.pen.pointsToSvg(points),
        stroke: this.props.color || '#000000',
        strokeWidth: this.props.strokeWidth || 4,
        fill: 'none',
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
      },
    };

    this.state.pen.addStroke(points);

    this.setState(
      {
        previousStrokes: [...this.state.previousStrokes, newElement],
        currentPoints: [],
      },
      () => {
        this._onChangeStrokes(this.state.previousStrokes);
      },
    );
  }

  _onChangeStrokes = (strokes: Stroke[]) => {
    if (this.props.onChangeStrokes) {
      this.props.onChangeStrokes(strokes);
    }
  };

  _onLayoutContainer = (e: LayoutChangeEvent) => {
    const pixelRatio = PixelRatio.get();
    this.setState({
      layout: e.nativeEvent.layout,
      options: {
        ...this.state.options,
        width: e.nativeEvent.layout.width / pixelRatio,
        height: e.nativeEvent.layout.height / pixelRatio,
      },
    });
    this.state.pen.setOffset(e.nativeEvent.layout);
    this._layout = e.nativeEvent.layout;
  };

  _renderSvgElement = (e: Stroke, tracker: number) => {
    if (e.type === 'Path') {
      return <Path {...e.attributes} key={tracker} />;
    }

    return null;
  };

  exportToSVG = () => {
    const strokes = [...this.state.previousStrokes];
    return convertStrokesToSvg(strokes, this._layout);
  };

  capture = async () => {
    if (this.viewShot.current) {
      return await this.viewShot.current?.capture?.();
    }
  };

  render() {
    return (
      <View
        onLayout={this._onLayoutContainer}
        style={[styles.drawContainer, this.props.containerStyle]}>
        {this.state.layout.width !== 0 && this.state.layout.height !== 0 && (
          <ViewShot ref={this.viewShot} options={this.state.options}>
            <View {...this._panResponder.panHandlers}>
              <Svg
                width={this.state.layout.width}
                height={this.state.layout.height}
                viewBox={`0 0 ${this.state.layout.width} ${this.state.layout.height}`}>
                <G>
                  {this.state.previousStrokes.map((stroke, index) => {
                    return this._renderSvgElement(stroke, index);
                  })}
                  <Path
                    key={this.state.previousStrokes.length}
                    d={this.state.pen.pointsToSvg(this.state.currentPoints)}
                    stroke={this.props.color || '#000000'}
                    strokeWidth={this.props.strokeWidth || 4}
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </G>
              </Svg>

              {this.props.children}
            </View>
          </ViewShot>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  drawContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
