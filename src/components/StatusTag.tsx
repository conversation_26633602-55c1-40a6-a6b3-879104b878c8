import styled from '@emotion/native';

import ResponsiveText from 'components/ResponsiveTypography';
import { Row, Typography } from 'cube-ui-components';
import { StyleProp, ViewStyle } from 'react-native';
import { country } from 'utils/context';

interface TagProps {
  text: string;
  backgroundColor: string;
  textColor: string;
  left?: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
}

const StatusTag = ({
  text,
  backgroundColor,
  textColor,
  left,
  containerStyle,
}: TagProps) => {
  return (
    <TagContainer backgroundColor={backgroundColor} style={containerStyle}>
      <Row alignItems="center">
        {left}
        <ResponsiveText
          TypographyDefault={Typography.SmallLabel}
          TypographyWide={
            country === 'id' ? Typography.SmallLabel : Typography.Label
          }
          color={textColor}
          fontWeight="medium">
          {text}
        </ResponsiveText>
      </Row>
    </TagContainer>
  );
};

const TagContainer = styled.View<{ backgroundColor: string }>(
  ({ theme, backgroundColor }) => ({
    backgroundColor: backgroundColor,
    paddingHorizontal: country === 'id' ? theme.space[1] : theme.space[2],
    paddingVertical: country === 'id' ? theme.space[1] / 2 : theme.space[1],
    borderRadius: 2,
  }),
);

export default StatusTag;
