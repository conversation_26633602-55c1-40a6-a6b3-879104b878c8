import { useBottomSheetInternal } from '@gorhom/bottom-sheet';
import { TextField, TextFieldProps, TextFieldRef } from 'cube-ui-components';
import { useCurrencyInputValue } from 'features/proposal/hooks/useCurrencyInputValue';
import { forwardRef, useRef, useState } from 'react';
import { formatCurrencyWithMask } from 'utils';

type Props = Omit<TextFieldProps, 'mask' | 'value' | 'onChange'> & {
  precision?: number;
  value?: number | null;
  onChange?: (value: number | null) => void;
  max?: number;
};
type Ref = TextFieldRef;

const DEFAULT_PRECISION = 2;

const isCurrency = (v?: unknown): v is number => v !== null && v !== undefined;

const DecimalTextField = forwardRef<Ref, Props>((props, ref) => {
  const {
    precision = DEFAULT_PRECISION,
    value: pValue,
    max,
    onChange,
    onBlur,
    onFocus,
    ...rest
  } = props;

  if (precision && precision < 0) {
    throw new Error('Negative precision ' + precision);
  }

  if (typeof max === 'number' && max < 0) {
    throw new Error('Negative maximum value ' + max);
  }

  const [value, setValue] = useState(
    isCurrency(pValue) ? formatCurrencyWithMask(pValue, precision) : '',
  );

  const prevValue = useRef(pValue);
  const isBlur = useRef(true);
  if (prevValue.current !== pValue && isBlur.current) {
    setValue(
      isCurrency(pValue) ? formatCurrencyWithMask(pValue, precision) : '',
    );
  }
  prevValue.current = pValue;

  const bottomSheetContext = useBottomSheetInternal(true);

  const handleBlur = () => {
    isBlur.current = true;

    if (bottomSheetContext?.shouldHandleKeyboardEvents) {
      bottomSheetContext.shouldHandleKeyboardEvents.value = false;
    }

    onBlur?.();

    setValue(
      isCurrency(pValue) ? formatCurrencyWithMask(pValue, precision) : '',
    );
  };

  const handleFocus = () => {
    isBlur.current = false;

    if (bottomSheetContext?.shouldHandleKeyboardEvents) {
      bottomSheetContext.shouldHandleKeyboardEvents.value = true;
    }

    onFocus?.();
  };

  const { onChange: handleChange } = useCurrencyInputValue({
    onChange,
    precision: precision,
    setShadowValue: setValue,
    max,
  });

  return (
    <TextField
      ref={ref}
      keyboardType="decimal-pad"
      returnKeyType="done"
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      {...rest}
    />
  );
});

export default DecimalTextField;
