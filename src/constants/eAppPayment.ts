export const paymentOfflineMethods = [
  {
    value: 'otcBillsPayment',
    label: 'eApp:payment.otcBillsPayment',
    listMethod: [
      {
        value: 'otcBillsPayment.securityBank',
        label: 'eApp:payment.securityBank',
      },
      {
        value: 'otcBillsPayment.metroBank',
        label: 'eApp:payment.metroBank',
      },
      {
        value: 'otcBillsPayment.bdo',
        label: 'eApp:payment.bdo',
      },
      {
        value: 'otcBillsPayment.bpi',
        label: 'eApp:payment.bpi',
      },
      {
        value: 'otcBillsPayment.lbc',
        label: 'eApp:payment.lbc',
      },
      {
        value: 'otcBillsPayment.unionBank',
        label: 'eApp:payment.unionBank',
      },
      {
        value: 'otcBillsPayment.cebuanaLhuillierBranches',
        label: 'eApp:payment.cebuanaLhuillierBranches',
      },
    ],
  },
  {
    value: 'gcash',
    label: 'eApp:payment.gcash',
    listMethod: [],
  },
  {
    value: 'onlineBanking',
    label: 'eApp:payment.onlineBanking',
    listMethod: [
      {
        value: 'onlineBanking.securityBank',
        label: 'eApp:payment.securityBank',
      },
      {
        value: 'onlineBanking.bdo',
        label: 'eApp:payment.bdo',
      },
      {
        value: 'onlineBanking.bpi',
        label: 'eApp:payment.bpi',
      },
      {
        value: 'onlineBanking.metroBankDirect',
        label: 'eApp:payment.metroBankDirect',
      },
      {
        value: 'onlineBanking.bancNetOnline',
        label: 'eApp:payment.bancNetOnline',
      },
      {
        value: 'onlineBanking.landBank',
        label: 'eApp:payment.landBank',
      },
    ],
  },
  {
    value: 'creditCardOption',
    label: 'eApp:payment.dragonPayCreditCardOption',
    listMethod: [],
  },
  {
    value: 'fwdPosMobile',
    label: 'eApp:payment.fwdPosMobile',
    listMethod: [],
  },
  {
    value: 'paymaya',
    label: 'eApp:payment.paymaya',
    listMethod: [],
  },
] as const;

export const paymentMethods = [
  {
    value: 'dragonPayOnlineBanking',
    label: 'eApp:payment.dragonPayOnlineBanking',
    isOfflinePayment: false,
  },
  {
    value: 'dragonPayCards',
    label: 'eApp:payment.dragonPayCards',
    isOfflinePayment: false,
  },
  {
    value: 'offline',
    label: 'eApp:payment.cash',
    isOfflinePayment: true,
  },
] as const;

export const sbcProductList = [
  { label: 'eApp:payment.sbcProduct.allAccessAccount', value: 'AAA' },
  { label: 'eApp:payment.sbcProduct.homeLoan', value: 'HL' },
  { label: 'eApp:payment.sbcProduct.businessMortgageLoan', value: 'BML' },
  { label: 'eApp:payment.sbcProduct.businessPlusAccount', value: 'BPA' },
  { label: 'eApp:payment.sbcProduct.creditCard', value: 'CC' },
  { label: 'eApp:payment.sbcProduct.autoCarLoan', value: 'AL' },
  {
    label: 'eApp:payment.sbcProduct.timeDeposit',
    value: 'TD',
  },
  { label: 'eApp:payment.sbcProduct.businessExpressLoan', value: 'BEL' },
  { label: 'eApp:payment.sbcProduct.personal', value: 'PL' },
];
