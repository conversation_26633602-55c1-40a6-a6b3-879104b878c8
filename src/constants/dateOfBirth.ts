import { RelationshipValue } from 'features/proposal/types';
import { BuildCountry } from 'types';
import { CHANNELS } from 'types/channel';
import { country } from 'utils/context';
import { TimeUnit } from 'utils/helper/dateUtil';

export type AgeConfig = {
  [key in BuildCountry]: {
    [key: string]: {
      [key in
        | RelationshipValue
        | 'DEFAULT'
        | 'STUDENT'
        | 'CHILD_OCCUPATION']?: {
        min?: {
          value: number;
          unit: TimeUnit;
        };
        max?: {
          value: number;
          unit: TimeUnit;
        };
      };
    };
  };
};

const ageLimitConfig: AgeConfig = {
  my: {
    [CHANNELS.BANCA]: {
      CHILD: {
        min: {
          value: 0,
          unit: 'day',
        },
        max: {
          value: 18,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 17,
          unit: 'years',
        },
        max: {
          value: 76,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 17,
          unit: 'years',
        },
        max: {
          value: 76,
          unit: 'years',
        },
      },

      DEFAULT: {
        min: {
          value: 17,
          unit: 'years',
        },
      },
      CHILD_OCCUPATION: {
        max: {
          value: 17,
          unit: 'years',
        },
      },
    },
    [CHANNELS.AGENCY]: {
      CHILD: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 65,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 17,
          unit: 'years',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 17,
          unit: 'years',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      EMPLOYEE: {
        min: {
          value: 17,
          unit: 'years',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      DEFAULT: {
        min: {
          value: 17,
          unit: 'years',
        },
      },
      CHILD_OCCUPATION: {
        max: {
          value: 17,
          unit: 'years',
        },
      },
    },
  },
  /* TBD */
  ph: {
    [CHANNELS.AGENCY]: {
      CHILD: {
        min: {
          value: 0,
          unit: 'day',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 0,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      EMPLOYEE: {
        min: {
          value: 0,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 0,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
    },
    [CHANNELS.BANCA]: {
      CHILD: {
        min: {
          value: 0,
          unit: 'day',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 0,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 0,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
    },
  },
  id: {
    [CHANNELS.AGENCY]: {
      CHILD: {
        min: {
          value: 30,
          unit: 'days',
        },
        max: {
          value: 80,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 30,
          unit: 'days',
        },
        max: {
          value: 80,
          unit: 'years',
        },
      },
      EMPLOYEE: {
        min: {
          value: 18,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 18,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },

      DEFAULT: {
        min: {
          value: 18,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
    },
    [CHANNELS.BANCA]: {
      CHILD: {
        min: {
          value: 30,
          unit: 'days',
        },
        max: {
          value: 80,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 30,
          unit: 'days',
        },
        max: {
          value: 80,
          unit: 'years',
        },
      },
      EMPLOYEE: {
        min: {
          value: 18,
          unit: 'days',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 18,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },

      DEFAULT: {
        min: {
          value: 18,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
    },
  },
  // TODO: TBD
  ib: {
    [CHANNELS.BANCA]: {
      CHILD: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 10,
          unit: 'years',
        },
        max: {
          value: 70,
          unit: 'years',
        },
      },
      DEFAULT: {
        min: {
          value: 16,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      STUDENT: {
        min: {
          value: 16,
          unit: 'years',
        },
        max: {
          value: 23,
          unit: 'years',
        },
      },
      CHILD_OCCUPATION: {
        max: {
          value: 16,
          unit: 'years',
        },
      },
    },
    [CHANNELS.AGENCY]: {
      CHILD: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 23,
          unit: 'years',
        },
      },
      SPOUSE: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 75,
          unit: 'years',
        },
      },
      SELF: {
        min: {
          value: 10,
          unit: 'years',
        },
        max: {
          value: 75,
          unit: 'years',
        },
      },
      EMPLOYEE: {
        min: {
          value: 14,
          unit: 'days',
        },
        max: {
          value: 75,
          unit: 'years',
        },
      },
      DEFAULT: {
        min: {
          value: 16,
          unit: 'years',
        },
        max: {
          value: 100,
          unit: 'years',
        },
      },
      STUDENT: {
        min: {
          value: 16,
          unit: 'years',
        },
        max: {
          value: 23,
          unit: 'years',
        },
      },
      CHILD_OCCUPATION: {
        max: {
          value: 16,
          unit: 'years',
        },
      },
    },
  },
};

export const ageLimit = ageLimitConfig[country as BuildCountry];
