import { BuildCountry, BuildType } from 'types';
import { country, build } from 'utils/context';

export const currentCountryCode = getCountryCode(country);
export const defaultBancaReferenceInfo = getDefaultBancaReferenceInfo(country);
export const defaultAffinityReferenceInfo = getDefaultAffinityReferenceInfo(
  country,
  build,
);

function getCountryCode(country: BuildCountry) {
  switch (country) {
    case 'ph':
      return '63';

    default:
      return '';
  }
}

function getDefaultBancaReferenceInfo(country: BuildCountry) {
  switch (country) {
    case 'ph':
      return {
        referrerCode: 'SBC02561',
        serviceBranch: 'SBC00842',
      };
  }
}

function getDefaultAffinityReferenceInfo(
  country: BuildCountry,
  build: BuildType,
) {
  switch (country) {
    case 'ph':
      return {
        referrerCode: build == 'prd' ? 'INT00531' : 'INT000038',
        serviceBranch: null,
      };
  }
}

export const DEFAULT_COUNTRY_PHONE_CODE_MY = '+60';
export const DEFAULT_COUNTRY_PHONE_CODE_IDN = '+62';
export const DEFAULT_COUNTRY_PHONE_CODE_PH = '+63';

const defaultCountryCodeMap: Record<BuildCountry, string> = {
  my: DEFAULT_COUNTRY_PHONE_CODE_MY,
  ph: DEFAULT_COUNTRY_PHONE_CODE_PH,
  ib: DEFAULT_COUNTRY_PHONE_CODE_MY,
  id: DEFAULT_COUNTRY_PHONE_CODE_IDN,
};
export const defaultCountryCode = defaultCountryCodeMap?.[country] ?? '';
