export const mobilePhoneRegex = /^[1-9][0-9]*$/;
export const emailRegex = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
export const urlRegex =
  /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;

/**
 * CUBEMY-3275: name regex
 * (?! ) to block leading space
 * (?!.* {2}) to block double space
 * (?!.*( -|- )) to block space before and after hyphen
 * (?!.*,\s*[\/\\.&()<>@,'-]) to block comma followed by special characters
 * (?!\d+$) to block string containing only numbers
 * [a-zA-Z\/\\.&()<>@' -]+ to allow letters, and special characters
 */
export const nameRegex =
  /^(?! )(?!.* {2})(?!.*( -|- ))(?!.*,\s*[\/\\.&()<>@,'-])(?!\d+$)[a-zA-Z\/\\.&()<>@,' -]+$/;

const companyNameRegex =
  /^(?! )(?!.* {2})(?!.*( -|- ))(?!.*,\s*[\/\\.&()<>@,'-])[a-zA-Z0-9\/\\.&()<>@,' -]+$/;
export const entityNameRegex =
  /^[a-zA-Zñ0-9]+((([- ’']*[a-zA-Zñ0-9])|([.]?[a-zA-Zñ0-9]?))*)?$/;
export const leadNameRegex = /^[^\\~`!@#$%^&*()\\_+={}\\|\\/:;<>?,1-9]+$/;

export const IB_NAME_VALIDATION = /^[A-Za-z]+(?:[ .'’/@][A-Za-z]+)*$/;
export const PH_NAME_VALIDATION = /^(?! )(?!.* {2})(?!.*[-]{2})(?!.*,\s*[\/\\.&()<>@,'-])(?!\d+$)(?!.* $)(?!.*(^|[^a-zA-Zñ])[- ](?![a-zA-Zñ]))(?!.*(^|[^a-zA-Zñ])\.)(?!.*[- ]$)[a-zA-Zñ0-9][a-zA-Zñ0-9 .'-]*$/;
export const MY_NAME_VALIDATION = nameRegex;

export const MY_COMPANY_NAME_VALIDATION = companyNameRegex;
export const IB_COMPANY_NAME_VALIDATION =
  /^[A-Za-z0-9&.()'’]+(?:[ -][A-Za-z0-9&.()'’]+)*$/; 

export const IB_TAX_IDENTIFICATION_VALIDATION = /^(C|CS|D|F|FA|PT|TA|TC|TN|TR|TP|J|LE)[^0A-Za-z][0-9]*0$/;

export const IB_LEAD_NAME_VALIDATION = IB_NAME_VALIDATION;
// export const MY_LEAD_NAME_VALIDATION = /^[a-zA-Z\/\\.&()<>@-]+$/;
export const MY_LEAD_NAME_VALIDATION = MY_NAME_VALIDATION;
export const PH_LEAD_NAME_VALIDATION = PH_NAME_VALIDATION;

export const ID_NAME_VALIDATION = /^[a-zA-Z]+( [a-zA-Z]+)*$/;

export const MY_ADDRESS_VALIDATION = /^[a-zA-Z0-9/\\.&()<>@\-!#%*;?~{}',\s]+$/;

export const TAX_NUMBER_VALIDATION = /^[A-Za-z][0-9]{2}[0-9]{2}(?:0[1-9]|1[0-2])[0-9]{8}$/;