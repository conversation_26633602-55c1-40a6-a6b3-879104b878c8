import { TFunction } from 'i18next';
import { BuildCountry } from 'types';
import { CHANNELS } from 'types/channel';
import { IB_SI_EMAIL_TEMPLATE } from './ib/agency';
import { IB_SI_BANCA_EMAIL_TEMPLATE } from './ib/banca';
import { ID_SI_EMAIL_TEMPLATE } from './id/agency';
import { MY_SI_EMAIL_TEMPLATE } from './my/agency';
import { MY_SI_BANCA_EMAIL_TEMPLATE } from './my/banca';
import { PH_SI_TL_EMAIL_TEMPLATE, PH_SI_VUL_EMAIL_TEMPLATE } from './ph/agency';
import {
  PH_SI_BANCA_TL_EMAIL_TEMPLATE,
  PH_SI_BANCA_VUL_EMAIL_TEMPLATE,
} from './ph/banca';

type EmailTemplate = {
  subject: string;
  content: string;
  html: string;
  templateName?: string;
};

export const getEmailTemplate = ({
  country,
  channel,
  isVul,
  t,
}: {
  country: BuildCountry;
  channel: string;
  isVul?: boolean;
  t?: TFunction;
}): EmailTemplate => {
  switch (country) {
    case 'ph':
      if (channel === CHANNELS.AGENCY) {
        return isVul ? PH_SI_VUL_EMAIL_TEMPLATE : PH_SI_TL_EMAIL_TEMPLATE;
      } else {
        return isVul
          ? PH_SI_BANCA_VUL_EMAIL_TEMPLATE
          : PH_SI_BANCA_TL_EMAIL_TEMPLATE;
      }
    case 'my':
      if (channel === CHANNELS.AGENCY) {
        return MY_SI_EMAIL_TEMPLATE;
      } else {
        return MY_SI_BANCA_EMAIL_TEMPLATE;
      }
    case 'ib':
      if (channel === CHANNELS.AGENCY) {
        return IB_SI_EMAIL_TEMPLATE;
      } else {
        return IB_SI_BANCA_EMAIL_TEMPLATE;
      }
    case 'id':
      return ID_SI_EMAIL_TEMPLATE(t);
  }
};
