import { TFunction } from 'i18next';

export const ID_SI_EMAIL_TEMPLATE = (t?: TFunction) => {
  return {
    subject: 'FWD Insurance - {{productName}}',
    content: `
    ${t?.('proposal:emailTemplate.dear', {
      ownerName: '{{ownerName}}',
      defaultValue: '',
    })}
  
    ${t?.('proposal:emailTemplate.thankYouForInterest', {
      productName: '{{productName}}',
      defaultValue: '',
    })}
  
    ${t?.('proposal:emailTemplate.pleaseStudy', {
      quoteNo: '{{quoteNo}}',
      defaultValue: '',
    })}
    
    Thank you
  
    <PERSON><PERSON>,
    FWD Insurance
    `,
    html: `
    <html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office'
      xmlns:w='urn:schemas-microsoft-com:office:word' xmlns:m='http://schemas.microsoft.com/office/2004/12/omml'
      xmlns='http://www.w3.org/TR/REC-html40'>
  
    <head>
      <meta http-equiv=Content-Type content='text/html; charset=unicode'>
      <meta name=ProgId content=Word.Document>
      <meta name=Generator content='Microsoft Word 15'>
      <meta name=Originator content='Microsoft Word 15'>
      <link rel=File-List href='EXTERNAL%20Payment%20Gateway%20Link_files/filelist.xml'>
      <link rel=Edit-Time-Data href='EXTERNAL%20Payment%20Gateway%20Link_files/editdata.mso'>
      <link rel=themeData href='EXTERNAL%20Payment%20Gateway%20Link_files/themedata.thmx'>
      <link rel=colorSchemeMapping href='EXTERNAL%20Payment%20Gateway%20Link_files/colorschememapping.xml'>
      <style> </style>
    </head>
  
    <body lang=EN-HK link='#0563C1' vlink='#954F72' style='tab-interval:36.0pt'>
      <div class=WordSection1>
          <p class=MsoNormal>
              <o:p>&nbsp;</o:p>
          </p>
          <table class=MsoNormalTable border=0 cellspacing=5 cellpadding=0 width='80%'
              style='width:80.0%;mso-cellspacing:2.5pt;mso-yfti-tbllook:1184;mso-padding-alt: 0cm 5.4pt 0cm 5.4pt'>
              <tr style='mso-yfti-irow:2'>
                  <td style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>${t?.(
                        'proposal:emailTemplate.dear',
                        {
                          ownerName: '{{ownerName}}',
                          defaultValue: '',
                        },
                      )}
                              <o:p></o:p></span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:3'>
                  <td style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>&nbsp; <o:p></o:p>
                              </span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:4'>
                  <td colspan=3 style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'> ${t?.(
                        'proposal:emailTemplate.thankYouForInterest',
                        {
                          productName: '{{productName}}',
                          defaultValue: '',
                        },
                      )} <o:p></o:p></span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:5'>
                  <td colspan=3 style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>${t?.(
                        'proposal:emailTemplate.pleaseStudy',
                        {
                          quoteNo: '{{quoteNo}}',
                          defaultValue: '',
                        },
                      )}
                  </td>
              </tr>
             
             
              <tr style='mso-yfti-irow:14'>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
              </tr>
              <tr style='mso-yfti-irow:15'>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
                  <td style='padding:.75pt .75pt .75pt .75pt'></td>
              </tr>
              <tr style='mso-yfti-irow:16'>
                  <td style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>Thank you<o:p>
                              </o:p></span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:17'>
                  <td style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>Salam Bebaskan Langkah, <o:p>
                              </o:p></span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:17'>
                  <td style='padding:.75pt .75pt .75pt .75pt'>
                      <p class=MsoNormal><span style='mso-fareast-font-family:Times New Roman'>FWD Insurance <o:p>
                              </o:p></span></p>
                  </td>
              </tr>
              <tr style='mso-yfti-irow:18'>
                  <hr size=1 width='100%' align=left>
                  <div style='text-align:center'><span align=center style='text-align:center'>This is a system generated
                          message. Please do not reply to this email</span></div>
                  <hr size=1 width='100%' align=left>
              </tr>
          </table>
      </div>
  </body>
  
  </html>
    `,
    templateName: 'CUBE_send_email_SI_english',
  };
};
