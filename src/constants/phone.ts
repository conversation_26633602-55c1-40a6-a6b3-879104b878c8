import { CountryCode, getCountryCallingCode } from 'libphonenumber-js';
import { BuildCountry } from 'types';
import { country } from 'utils/context';

const MARKET_TO_COUNTRY_CODE: Record<BuildCountry, CountryCode> = {
  my: 'MY',
  ph: 'PH',
  ib: 'MY',
  id: 'ID', //TODO: TBD
};

export const SHOULD_PREPEND_LEADING_ZERO_IN_FORMAT =
  {
    //define for each country
    my: true,
    ph: false,
    ib: true,
    id: false, //TODO: TBD
  }[country as string] || false;

export const PHONE_COUNTRY_CODE = MARKET_TO_COUNTRY_CODE[
  country
] as CountryCode;

export const PHONE_CALLING_CODE = getCountryCallingCode(
  MARKET_TO_COUNTRY_CODE[country],
);

export const INTL_PHONE_PREFIX = `+${PHONE_CALLING_CODE}`;

export const phoneNumberLengthByCountry: {
  [key in BuildCountry]: { min: number | undefined; max: number | undefined };
} = {
  my: {
    min: 8,
    max: 10,
  },
  ib: {
    min: 8,
    max: 10,
  },
  id: {
    min: 9,
    max: 16,
  },
  ph: {
    min: undefined,
    max: undefined,
  },
};

export const phoneNumberLengthConfig =
  phoneNumberLengthByCountry[country as BuildCountry];
